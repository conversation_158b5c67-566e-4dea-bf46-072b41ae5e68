{
    // Auto-fix imports on save
    "editor.codeActionsOnSave": {
        "source.organizeImports": "explicit",
        "source.removeUnusedImports": "explicit",
        "source.fixAll.eslint": "explicit"
    },
    // Auto-save settings
    "editor.formatOnSave": true,
    "editor.formatOnPaste": true,
    // TypeScript specific settings
    "typescript.preferences.organizeImportsIgnoreCase": "auto",
    "typescript.preferences.includePackageJsonAutoImports": "auto",
    "typescript.suggest.autoImports": true,
    "typescript.updateImportsOnFileMove.enabled": "always",
    // JavaScript specific settings  
    "javascript.preferences.organizeImportsIgnoreCase": "auto",
    "javascript.preferences.includePackageJsonAutoImports": "auto",
    "javascript.suggest.autoImports": true,
    "javascript.updateImportsOnFileMove.enabled": "always",
    // ESLint integration
    "eslint.validate": [
        "javascript",
        "javascriptreact",
        "typescript",
        "typescriptreact"
    ],
    "eslint.format.enable": true,
    // Additional helpful settings
    "editor.quickSuggestions": {
        "other": true,
        "comments": false,
        "strings": true
    }
}