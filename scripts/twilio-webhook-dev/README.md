# Webhook Development Scripts

This directory contains scripts for automated webhook development with ngrok and <PERSON>wi<PERSON>.

## Quick Start

```bash
# Test if your environment is ready
make test-webhooks

# Start complete webhook development environment
make dev-webhooks

# Stop and restore production webhooks
make stop-webhooks
```

## Individual Scripts

### `test-setup.sh`
Tests if your environment is ready for webhook development:
- Checks required tools (ngrok, docker-compose, curl)
- Verifies Docker services are running
- Validates database has organization 1 with Twilio app
- Tests Twilio credentials are available

### `start-ngrok.sh`
Starts ngrok tunnel and extracts the public URL:
- Starts ngrok tunnel on port 9084
- Extracts and saves the ngrok URL to `.ngrok_url`
- Sets `COMMS_SERVER_PUBLIC_DOMAIN` environment variable
- Handles cleanup on exit

### `update-twilio-webhooks.sh`
Updates Twilio application webhooks to use ngrok:
- Targets organization 1 only (local development)
- Gets org API credentials from database
- Updates Twilio app's VoiceUrl and StatusCallback
- Backs up original configuration to `.twilio_backup.json`

### `restore-production-webhooks.sh`
Restores Twilio webhooks to production URLs:
- Resets organization 1's webhooks to production
- Uses same org API credentials
- Cleans up development artifacts

## Make Targets

| Target | Description |
|--------|-------------|
| `make test-webhooks` | Test webhook development prerequisites |
| `make start-ngrok` | Start ngrok tunnel only |
| `make update-webhooks` | Update Twilio webhooks to ngrok |
| `make restore-webhooks` | Restore production webhook URLs |
| `make stop-webhooks` | Complete cleanup and restore |
| `make dev-webhooks` | **Full automated setup** |

## Workflow

### Starting Development
```bash
make dev-webhooks
```
This will:
1. Test prerequisites
2. Start ngrok tunnel
3. Update Twilio webhooks
4. Start services with ngrok environment

### During Development
- Webhooks now point to your local ngrok tunnel
- Both existing and new webhook calls reach your local environment
- No manual Twilio console changes needed

### Stopping Development
```bash
make stop-webhooks
```
This will:
1. Kill ngrok process
2. Restore production webhook URLs
3. Clean up temporary files

## Files Created

| File | Purpose |
|------|---------|
| `.ngrok_url` | Stores current ngrok domain |
| `.ngrok.log` | Ngrok process logs |
| `.twilio_backup.json` | Backup of original Twilio config |

## Troubleshooting

### "No ngrok URL found"
- Make sure ngrok is installed and authenticated
- Run `make start-ngrok` separately to debug

### "Organization 1 not found"
- Create a test organization through the admin interface
- Ensure it has Twilio integration configured

### "Twilio credentials not available"
- Make sure services are running: `make run`
- Check that Twilio secrets are properly configured

### "Failed to update Twilio application"
- Check Twilio Account SID and Auth Token
- Verify org API credentials in database
- Check network connectivity to Twilio API

## Development Notes

- Only targets organization 1 (local development org)
- Automatically handles authentication with org-specific credentials
- Preserves production configurations through backup/restore
- Safe to run multiple times - idempotent operations