#!/bin/bash
set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
NGROK_PORT=9084
NGROK_LOG_FILE=".ngrok.log"
NGROK_URL_FILE=".ngrok_url"
MAX_RETRIES=30
RETRY_DELAY=1

echo -e "${BLUE}🚀 Starting ngrok tunnel for webhook development...${NC}"

# Clean up any existing ngrok processes and files
echo -e "${YELLOW}🧹 Cleaning up existing ngrok processes...${NC}"
pkill ngrok 2>/dev/null || true
rm -f "$NGROK_LOG_FILE" "$NGROK_URL_FILE" 2>/dev/null || true

# Check if ngrok is installed
if ! command -v ngrok &> /dev/null; then
    echo -e "${RED}❌ ngrok is not installed. Please install ngrok first.${NC}"
    echo -e "${YELLOW}💡 Visit: https://ngrok.com/download${NC}"
    exit 1
fi

# Start ngrok in background
echo -e "${YELLOW}⏳ Starting ngrok tunnel on port $NGROK_PORT...${NC}"
ngrok http $NGROK_PORT --log=stdout > "$NGROK_LOG_FILE" 2>&1 &
NGROK_PID=$!

# Function to cleanup on exit
cleanup() {
    echo -e "${YELLOW}🧹 Cleaning up ngrok process...${NC}"
    kill $NGROK_PID 2>/dev/null || true
    rm -f "$NGROK_LOG_FILE" "$NGROK_URL_FILE" 2>/dev/null || true
}

# Set trap for cleanup
trap cleanup EXIT INT TERM

# Wait for ngrok to start and extract URL
echo -e "${YELLOW}⏳ Waiting for ngrok to initialize...${NC}"
for i in $(seq 1 $MAX_RETRIES); do
    if [ -f "$NGROK_LOG_FILE" ]; then
        # Extract the public URL from ngrok logs
        NGROK_URL=$(grep -o 'https://[^.]*\.ngrok-free\.app' "$NGROK_LOG_FILE" 2>/dev/null | head -1)
        
        if [ -n "$NGROK_URL" ]; then
            # Remove https:// prefix for environment variable
            NGROK_DOMAIN=$(echo "$NGROK_URL" | sed 's/https:\/\///')
            echo "$NGROK_DOMAIN" > "$NGROK_URL_FILE"
            echo -e "${GREEN}✅ Ngrok tunnel established!${NC}"
            echo -e "${GREEN}🌐 Public URL: ${NGROK_URL}${NC}"
            echo -e "${GREEN}📝 Domain saved to: $NGROK_URL_FILE${NC}"
            
            # Export environment variable for current session
            export COMMS_SERVER_PUBLIC_DOMAIN="$NGROK_DOMAIN"
            echo -e "${BLUE}📦 Environment variable set: COMMS_SERVER_PUBLIC_DOMAIN=$NGROK_DOMAIN${NC}"
            
            # Verify ngrok is accessible
            echo -e "${YELLOW}🔍 Verifying ngrok tunnel...${NC}"
            if curl -s --max-time 10 "$NGROK_URL/health" > /dev/null 2>&1; then
                echo -e "${GREEN}✅ Ngrok tunnel is accessible and ready!${NC}"
            else
                echo -e "${YELLOW}⚠️  Ngrok tunnel created but service not yet accessible. This is normal if services aren't running.${NC}"
            fi
            
            # Keep ngrok running in background and exit successfully
            echo -e "${BLUE}🔄 Ngrok is running in background (PID: $NGROK_PID)${NC}"
            echo -e "${YELLOW}💡 Use 'make stop-webhooks' to stop ngrok and cleanup${NC}"
            
            # Disable cleanup trap since we want ngrok to keep running
            trap - EXIT INT TERM
            
            # Exit successfully, leaving ngrok running
            exit 0
        fi
    fi
    
    echo -e "${YELLOW}⏳ Waiting for ngrok... (attempt $i/$MAX_RETRIES)${NC}"
    sleep $RETRY_DELAY
done

# If we get here, ngrok failed to start
echo -e "${RED}❌ Failed to start ngrok tunnel after $MAX_RETRIES attempts${NC}"
echo -e "${YELLOW}🔍 Check ngrok logs:${NC}"
[ -f "$NGROK_LOG_FILE" ] && tail -10 "$NGROK_LOG_FILE"
exit 1