#!/bin/bash
set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
NGROK_URL_FILE=".ngrok_url"
BACKUP_FILE=".twilio_backup.json"

echo -e "${BLUE}📞 Updating Twilio webhook URLs for development...${NC}"

# Check if ngrok URL file exists
if [ ! -f "$NGROK_URL_FILE" ]; then
    echo -e "${RED}❌ No ngrok URL found. Please run 'make start-ngrok' first.${NC}"
    exit 1
fi

# Read ngrok domain
NGROK_DOMAIN=$(cat "$NGROK_URL_FILE")
if [ -z "$NGROK_DOMAIN" ]; then
    echo -e "${RED}❌ Empty ngrok URL in $NGROK_URL_FILE${NC}"
    exit 1
fi

echo -e "${BLUE}🌐 Using ngrok domain: $NGROK_DOMAIN${NC}"

# Check if required environment variables are set
if [ -z "$TWILIO_ACCOUNT_SID" ] || [ -z "$TWILIO_AUTH_TOKEN" ]; then
    echo -e "${YELLOW}⚠️  Twilio credentials not found in environment. Attempting to load from Docker...${NC}"
    
    # Try to get credentials from Docker environment
    if docker-compose ps communications-service | grep -q "Up"; then
        echo -e "${BLUE}🐳 Getting Twilio credentials from communications service...${NC}"
        TWILIO_ACCOUNT_SID=$(docker-compose exec -T communications-service printenv TWILIO_ACCOUNT_SID 2>/dev/null || echo "")
        TWILIO_AUTH_TOKEN=$(docker-compose exec -T communications-service printenv TWILIO_AUTH_TOKEN 2>/dev/null || echo "")
    fi
    
    if [ -z "$TWILIO_ACCOUNT_SID" ] || [ -z "$TWILIO_AUTH_TOKEN" ]; then
        echo -e "${RED}❌ Twilio credentials not available. Please ensure services are running.${NC}"
        exit 1
    fi
fi

echo -e "${BLUE}🔑 Twilio Account SID: ${TWILIO_ACCOUNT_SID:0:20}...${NC}"

# Function to get database value
get_db_value() {
    local query="$1"
    docker-compose exec -T postgres psql -U postgres -d mydatabase -t -A -c "$query" 2>/dev/null | tr -d ' ' || echo ""
}

# Get organization data from database
echo -e "${YELLOW}🗄️  Fetching organization data from database...${NC}"

# Get organization 1 data (local development org)
ORG_DATA=$(get_db_value "SELECT id, name, twiml_app_sid FROM orgs WHERE id = 1 AND twiml_app_sid IS NOT NULL AND twiml_app_sid != '';")

if [ -z "$ORG_DATA" ]; then
    echo -e "${RED}❌ Organization 1 not found or has no Twilio application configured.${NC}"
    echo -e "${YELLOW}💡 Make sure you have a local test organization created.${NC}"
    exit 1
fi

# Function to backup current Twilio app configuration
backup_twilio_config() {
    local app_sid="$1"
    local org_id="$2"
    
    echo -e "${YELLOW}💾 Backing up current Twilio app configuration...${NC}"
    
    local backup_data=$(curl -s -u "$TWILIO_ACCOUNT_SID:$TWILIO_AUTH_TOKEN" \
        "https://api.twilio.com/2010-04-01/Accounts/$TWILIO_ACCOUNT_SID/Applications/$app_sid.json")
    
    if [ $? -eq 0 ]; then
        echo "$backup_data" | jq ".org_id = $org_id" >> "$BACKUP_FILE" 2>/dev/null || echo "$backup_data" >> "$BACKUP_FILE"
        echo -e "${GREEN}✅ Backup saved to $BACKUP_FILE${NC}"
    else
        echo -e "${YELLOW}⚠️  Could not backup Twilio app configuration${NC}"
    fi
}

# Function to update Twilio application webhooks
update_twilio_app() {
    local org_id="$1"
    local org_name="$2"
    local app_sid="$3"
    
    echo -e "${BLUE}🏢 Updating organization: $org_name (ID: $org_id)${NC}"
    echo -e "${BLUE}📱 Twilio App SID: $app_sid${NC}"
    
    # Get org API credentials from database
    local username=$(get_db_value "SELECT username FROM org_api_users WHERE org_id = $org_id LIMIT 1;")
    local user_id=$(get_db_value "SELECT id FROM org_api_users WHERE org_id = $org_id LIMIT 1;")
    
    if [ -z "$username" ] || [ -z "$user_id" ]; then
        echo -e "${RED}❌ No API credentials found for organization $org_id${NC}"
        return 1
    fi
    
    echo -e "${BLUE}👤 Using API credentials: ${username:0:8}...${NC}"
    
    # Get the raw password dynamically from the orgs service
    echo -e "${YELLOW}🔐 Fetching decrypted password from orgs service...${NC}"
    local password_response=$(curl -s -X POST \
        "http://localhost:9087/hero.orgs.v1.OrgsService/GetOrgAPIUserPrivateById" \
        -H "Content-Type: application/json" \
        -H "Authorization4: OHBBOJHBLHJBLHJVLHJVLJTESTEVLJVL" \
        -d "{\"user_id\":\"$user_id\"}")
    
    if [ $? -ne 0 ]; then
        echo -e "${RED}❌ Failed to fetch password from orgs service${NC}"
        return 1
    fi
    
    local password=$(echo "$password_response" | jq -r '.orgApiUser.rawPassword' 2>/dev/null)
    
    if [ -z "$password" ] || [ "$password" = "null" ]; then
        echo -e "${RED}❌ Failed to extract password from service response${NC}"
        echo -e "${RED}Response: $password_response${NC}"
        return 1
    fi
    
    echo -e "${GREEN}✅ Successfully retrieved dynamic password${NC}"
    
    # Backup current configuration
    backup_twilio_config "$app_sid" "$org_id"
    
    # Build webhook URLs
    local voice_url="https://$username:$password@$NGROK_DOMAIN/hero.communications.v1.TwilioWebhookService/voice"
    local status_url="https://$username:$password@$NGROK_DOMAIN/hero.communications.v1.TwilioWebhookService/callstatus"
    
    echo -e "${YELLOW}🔗 Voice URL: $voice_url${NC}"
    echo -e "${YELLOW}🔗 Status URL: $status_url${NC}"
    
    # Update Twilio application
    local response=$(curl -s -w "%{http_code}" -X POST \
        "https://api.twilio.com/2010-04-01/Accounts/$TWILIO_ACCOUNT_SID/Applications/$app_sid.json" \
        --data-urlencode "VoiceUrl=$voice_url" \
        --data-urlencode "StatusCallback=$status_url" \
        -u "$TWILIO_ACCOUNT_SID:$TWILIO_AUTH_TOKEN")
    
    local http_code="${response: -3}"
    local body="${response%???}"
    
    if [ "$http_code" = "200" ]; then
        echo -e "${GREEN}✅ Successfully updated Twilio application for $org_name${NC}"
        return 0
    else
        echo -e "${RED}❌ Failed to update Twilio application for $org_name${NC}"
        echo -e "${RED}HTTP Code: $http_code${NC}"
        echo -e "${RED}Response: $body${NC}"
        return 1
    fi
}

# Process organization 1
echo -e "${YELLOW}🔄 Processing organization 1...${NC}"

# Clear previous backup
> "$BACKUP_FILE"

IFS='|' read -r org_id org_name app_sid <<< "$ORG_DATA"

if [ -n "$org_id" ] && [ -n "$app_sid" ]; then
    echo -e "\n${BLUE}═══════════════════════════════════════${NC}"
    if update_twilio_app "$org_id" "$org_name" "$app_sid"; then
        updated_count=1
        failed_count=0
    else
        updated_count=0
        failed_count=1
    fi
else
    echo -e "${RED}❌ Invalid organization data${NC}"
    updated_count=0
    failed_count=1
fi

echo -e "\n${GREEN}🎉 Webhook update complete!${NC}"
echo -e "${GREEN}✅ Updated: $updated_count applications${NC}"
[ $failed_count -gt 0 ] && echo -e "${RED}❌ Failed: $failed_count applications${NC}"

echo -e "\n${YELLOW}💡 Your Twilio applications now point to: https://$NGROK_DOMAIN${NC}"
echo -e "${YELLOW}🔄 Use 'make stop-webhooks' to clean up when done${NC}"