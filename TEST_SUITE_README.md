# Pre-Registration User Mapping Test Suite

This test suite validates the complete pre-registration user mapping flow and postConfirmation Lambda integration.

## Overview

The test suite covers:
1. **Pre-registration mapping CRUD operations** via API
2. **Bulk creation** of mappings
3. **postConfirmation Lambda** simulation and testing
4. **Database verification** of the complete flow

## Test Files Created

### 1. `test_pre_registration_api.sh`
- **Purpose**: Tests all pre-registration mapping API endpoints
- **What it tests**:
  - Individual mapping creation for your test users
  - Bulk mapping creation
  - Mapping retrieval (individual and list)
  - API error handling

### 2. `test_lambda_invoke.js`
- **Purpose**: Node.js script to test the postConfirmation Lambda
- **What it tests**:
  - Lambda event processing
  - Pre-registration mapping lookup and usage
  - Asset creation with correct cognito_jwt_sub
  - Fallback behavior for unmapped users

### 3. `test_lambda_locally.go`
- **Purpose**: Go simulation of Lambda logic
- **What it tests**:
  - Event structure validation
  - Logic flow simulation
  - Expected behavior documentation

### 4. `run_all_tests.sh`
- **Purpose**: Comprehensive test runner
- **What it does**:
  - Checks if required services are running
  - Runs API tests
  - Executes Lambda tests
  - Provides verification instructions

### 5. `verify_database.sql`
- **Purpose**: Database verification queries
- **What it checks**:
  - Pre-registration mappings created correctly
  - Mappings marked as used after Lambda execution
  - Assets created with correct cognito_jwt_sub values
  - Role assignments and org memberships

## Your Test Data

Based on your Cognito user attributes and assets table, the tests use:

### Test Users
- **<EMAIL>** → `<EMAIL>`
  - Expected: Admin role, Dispatcher asset type (2)
  - cognito_jwt_sub: `<EMAIL>`

- **<EMAIL>** → `<EMAIL>`
  - Expected: Supervisor role, Supervisor asset type (6)
  - cognito_jwt_sub: `<EMAIL>`

- **<EMAIL>** → `<EMAIL>`
  - Expected: Dispatcher role, Dispatcher asset type (2)
  - cognito_jwt_sub: `<EMAIL>`

### Bearer Token
The tests use your provided bearer token:
```
eyJraWQiOiJCS20yRjNPcjZVMDI2cDVUc24xSjlpUkJ2cDNJMldydzlQMWQ2dVh1UDJVPSIsImFsZyI6IlJTMjU2In0...
```

## How to Run the Tests

### Prerequisites
1. **Database migration** completed (✅ you mentioned this is done)
2. **Local services running**:
   - Orgs service on `:8080`
   - Assets/Workflow service on `:8081`
   - Permissions service on `:8082`

### Quick Start
```bash
# Run the complete test suite
./run_all_tests.sh
```

### Individual Test Components
```bash
# 1. Test API endpoints only
./test_pre_registration_api.sh

# 2. Test Lambda with Node.js (if Lambda handler exists)
node test_lambda_invoke.js

# 3. Run Go simulation
go run test_lambda_locally.go

# 4. Verify database state
# Run queries from verify_database.sql in your database client
```

## Expected Flow

### 1. Pre-Registration Phase
- API creates mappings for your test users
- Mappings stored with `used_at = NULL`
- Each mapping specifies: email, org_id, role_name, asset_type

### 2. Lambda Execution Phase
- User signs up via Cognito
- postConfirmation Lambda triggered
- Lambda looks up pre-registration mapping by email + org_id
- If found: uses specified role and asset type
- If not found: falls back to default Responder role
- Marks mapping as used (`used_at = NOW()`)
- Creates asset with `cognito_jwt_sub = event.UserName`

### 3. Verification Phase
- Check mappings are marked as used
- Verify assets created with correct cognito_jwt_sub
- Confirm role assignments in permissions service

## Key Implementation Details

### cognito_jwt_sub Field
The Lambda correctly uses `event.UserName` as the `cognito_jwt_sub`:
```go
// Line 257 in postConfirmationLambda/main.go
CognitoJwtSub: event.UserName,
```

For your Okta users, `event.UserName` will be:
- `<EMAIL>`
- `<EMAIL>`
- `<EMAIL>`

This matches the pattern in your existing assets table.

### Org Determination
The Lambda uses SAML issuer to determine org:
```go
// Environment variables set in Lambda
OKTA_SANDBOX_ISSUER = "http://www.okta.com/exk1wzdmtadxej3up1d8"
SANDBOX_ORG_ID = "2"
```

Your users have issuer `http://www.okta.com/exk1wzdmtadxej3up1d8`, so they'll be assigned to org 2.

## Troubleshooting

### Common Issues
1. **Services not running**: Check that all required services are accessible
2. **Bearer token expired**: Update the token in test scripts if needed
3. **Database connection**: Ensure your local database is accessible
4. **Lambda environment**: Verify environment variables are set correctly

### Debug Steps
1. Check service logs for API call errors
2. Verify database state with provided SQL queries
3. Test individual API endpoints with curl
4. Review Lambda logs for execution details

## Success Criteria

✅ **API Tests Pass**: All CRUD operations work correctly
✅ **Lambda Processes Events**: No errors during event processing
✅ **Mappings Used**: Pre-registration mappings marked as used
✅ **Assets Created**: Assets have correct cognito_jwt_sub values
✅ **Roles Assigned**: Users get correct roles based on mappings
✅ **Fallback Works**: Unknown users get default Responder role

## Next Steps

After running the tests:
1. **Verify results** using the database queries
2. **Test real Cognito signup** if possible
3. **Monitor production** Lambda logs
4. **Document** any issues or improvements needed
