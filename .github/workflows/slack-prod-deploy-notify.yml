name: Prod Deploy Slack Notification

on:
  workflow_run:
    workflows: ["Deploy"]
    types:
      - completed

permissions:
  contents: read
  pull-requests: read

jobs:
  slack-prod-notify:
    if: github.event.workflow_run.conclusion == 'success' || github.event.workflow_run.conclusion == 'failure'
    runs-on: ubuntu-latest
    steps:
      - name: Get PR info for deployed commit
        id: pr
        run: |
          PR_JSON=$(curl -s -H "Authorization: Bearer ${{ secrets.GITHUB_TOKEN }}" \
            "https://api.github.com/repos/${{ github.repository }}/commits/${{ github.event.workflow_run.head_sha }}/pulls" \
            -H "Accept: application/vnd.github.groot-preview+json")
          PR_TITLE=$(echo "$PR_JSON" | jq -r '.[0].title // empty')
          PR_NUMBER=$(echo "$PR_JSON" | jq -r '.[0].number // empty')
          PR_URL=$(echo "$PR_JSON" | jq -r '.[0].html_url // empty')
          echo "pr_title=$PR_TITLE" >> $GITHUB_OUTPUT
          echo "pr_number=$PR_NUMBER" >> $GITHUB_OUTPUT
          echo "pr_url=$PR_URL" >> $GITHUB_OUTPUT

      - name: Send prod deploy Slack message
        env:
          REPO: ${{ github.repository }}
          ACTOR: ${{ github.event.workflow_run.actor.login }}
          RUN_URL: ${{ github.event.workflow_run.html_url }}
          PR_TITLE: ${{ steps.pr.outputs.pr_title }}
          PR_NUMBER: ${{ steps.pr.outputs.pr_number }}
          PR_URL: ${{ steps.pr.outputs.pr_url }}
          COMMIT_MSG: ${{ github.event.workflow_run.head_commit.message }}
          CONCLUSION: ${{ github.event.workflow_run.conclusion }}
          SLACK_DEPLOY_WEBHOOK: ${{ secrets.SLACK_DEPLOY_WEBHOOK }}
        run: |
          set -e
          
          # Set status-specific variables
          if [ "$CONCLUSION" = "success" ]; then
            EMOJI=":rocket:"
            STATUS_TEXT="Prod Deploy Succeeded!"
          else
            EMOJI=":x:"
            STATUS_TEXT="Prod Deploy Failed!"
          fi
          
          if [ -n "$PR_TITLE" ] && [ -n "$PR_NUMBER" ] && [ -n "$PR_URL" ]; then
            TEXT=$(cat <<EOF
          ${EMOJI} *${STATUS_TEXT}*

          Repo: \`${REPO}\`
          By: \`${ACTOR}\`
          PR: <${PR_URL}|#${PR_NUMBER} - ${PR_TITLE}>
          See run: <${RUN_URL}|View Workflow Run>
          EOF
          )
          else
            TEXT=$(cat <<EOF
          ${EMOJI} *${STATUS_TEXT}*

          Repo: \`${REPO}\`
          By: \`${ACTOR}\`
          Commit: \`${COMMIT_MSG}\`
          See run: <${RUN_URL}|View Workflow Run>
          EOF
          )
          fi

          ESCAPED_TEXT=$(echo "$TEXT" | tr -d '\000' | jq -Rs .)
          PAYLOAD="{\"text\": ${ESCAPED_TEXT}, \"mrkdwn\": true}"

          RESPONSE=$(curl -s -o response.txt -w "%{http_code}" \
            -H 'Content-type: application/json' \
            --data "$PAYLOAD" "$SLACK_DEPLOY_WEBHOOK")

          if [ "$RESPONSE" -ne 200 ]; then
            echo "Slack notification failed (HTTP $RESPONSE)"
            cat response.txt
            exit 1
          fi