name: Build
on:
  pull_request:
    paths:
      - 'services/**'
      - 'infra/**'
      - 'lib/**'
      - 'apps/**'
      - 'docker-compose.yml'
      - '.github/workflows/**'

# Add concurrency control for PR builds
concurrency:
  group: build-${{ github.event.pull_request.number }}
  cancel-in-progress: true  # Cancel previous builds for the same PR

jobs:
  build-and-push:
    name: Build Docker Images
    runs-on: ubuntu-latest

    steps:
    # Step 1: Checkout the code
    - name: Checkout code
      uses: actions/checkout@v3
      with:
        fetch-depth: 0  # Fetch all history for checking changes
    
    # Step 1.1: Free disk space
    - name: Free disk space
      uses: jlumbroso/free-disk-space@v1.3.1
      with:
        tool-cache: true
        android: true
        dotnet: true
        haskell: true
        large-packages: true

    # Step 2: Check for changes in sensors service and its dependencies
    - name: Check for changes in sensors service
      id: check_changes
      run: |
        # Check for changes in sensors service and shared dependencies
        if git diff --quiet ${{ github.event.pull_request.base.sha }} ${{ github.event.pull_request.head.sha }} -- ./services/sensors/; then
          echo "No changes detected in sensors service"
          echo "sensors_changed=false" >> $GITHUB_OUTPUT
        else
          echo "Changes detected in sensors service"
          echo "sensors_changed=true" >> $GITHUB_OUTPUT
        fi

    # Step 3: Set up Docker Buildx
    - name: Set up Docker Buildx
      uses: docker/setup-buildx-action@v2

    # Step 4: Configure AWS credentials
    - name: Configure AWS Credentials
      uses: aws-actions/configure-aws-credentials@v2
      with:
        aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
        aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
        aws-region: us-west-2

    # Step 5: Log in to Amazon ECR
    - name: Log in to Amazon ECR
      uses: aws-actions/amazon-ecr-login@v1

    # Step 6: Build Docker images
    - name: Build Docker images
      run: |
        echo "Checking for changes in sensors service"
        echo "${{ steps.check_changes.outputs.sensors_changed }}"
        if [ "$(echo "${{ steps.check_changes.outputs.sensors_changed }}" | xargs | tr '[:upper:]' '[:lower:]')" = "false" ]; then
          echo "Skipping sensors service build - no changes detected"
          # Build all services except sensors
          SERVICES_TO_SKIP=sensors ./infra/scripts/build-services.sh
        else
          ./infra/scripts/build-services.sh
        fi

    - name: Set up Go
      uses: actions/setup-go@v5
      with:
        go-version: '1.23'

    - name: Set up Node.js
      uses: actions/setup-node@v4
      with:
        node-version: '22'

    - name: Prepare CDK
      run: ./infra/scripts/prepare-cdk.sh