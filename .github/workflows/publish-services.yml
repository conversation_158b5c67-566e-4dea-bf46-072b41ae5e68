name: Deploy

on:
  push:
    branches:
      - main
    paths:
      - 'services/**'
      - 'infra/**'
      - 'lib/**'
      - 'apps/**'
      - '.github/workflows/**'

# Add concurrency control to queue deployments
concurrency:
  group: production-deployment
  cancel-in-progress: false  # Don't cancel running deployments, queue them instead

permissions:
  contents: write

jobs:
  build-and-push:
    name: Build and Push Docker Images
    runs-on: ubuntu-latest
    # Add timeout to prevent stuck deployments
    timeout-minutes: 60

    steps:
    # Step 0: Notify deployment start
    - name: Notify deployment start
      run: |
        echo "::notice::🚀 Starting deployment for commit ${{ github.sha }}"
        echo "::notice::📋 Deployment will be queued if another deployment is running"
        
    # Step 1: Checkout the code
    - name: Checkout code
      uses: actions/checkout@v3
      with:
        fetch-depth: 0  # Ensures all commits and tags are fetched

    # Step 2: Check for changes in sensors service and its dependencies
    - name: Check for changes in sensors service
      id: check_changes
      run: |
        # Retrieve the second most recent tag matching the pattern 'v*'
        # 1. List all tags matching 'v*'
        # 2. Sort them in version order (-V)
        # 3. Get the last two tags (tail -n 2)
        # 4. Select the second-to-last tag (head -n 1)
        PREV_TAG=$(git tag -l 'v*' | sort -V | tail -n 2 | head -n 1)
        if [ -z "$PREV_TAG" ]; then
          echo "No previous tag found, will build all services"
          echo "sensors_changed=true" >> $GITHUB_OUTPUT
        else
          echo "Comparing against previous deployment tag: $PREV_TAG"
          # Check for changes in sensors service and shared dependencies
          if git diff --quiet $PREV_TAG HEAD -- ./services/sensors/; then
            echo "sensors_changed=false" >> $GITHUB_OUTPUT
          else
            echo "sensors_changed=true" >> $GITHUB_OUTPUT
          fi
        fi

    # Step 3: Set up Docker Buildx
    - name: Set up Docker Buildx
      uses: docker/setup-buildx-action@v2

    # Step 4: Configure AWS credentials
    - name: Configure AWS Credentials
      uses: aws-actions/configure-aws-credentials@v2
      with:
        aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
        aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
        aws-region: us-west-2

    # Step 5: Log in to Amazon ECR
    - name: Log in to Amazon ECR
      uses: aws-actions/amazon-ecr-login@v1

    # Step 6: Build and push Docker images for all services
    - name: Build and push Docker images
      run: |
        if [ "$(echo "${{ steps.check_changes.outputs.sensors_changed }}" | xargs | tr '[:upper:]' '[:lower:]')" = "false" ]; then
          echo "Skipping sensors service build - no changes detected"
          SERVICES_TO_SKIP=sensors ./infra/scripts/build-and-publish-services.sh
        else
          ./infra/scripts/build-and-publish-services.sh
        fi
      env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}

    # Clean up Docker resources to free disk space
    - name: Clean up Docker resources
      run: |
        echo "Disk usage before cleanup:"
        df -h
        echo "Cleaning up Docker resources..."
        
        # Remove only the service images we just built (more targeted)
        echo "Removing built service images..."
        docker images --format "table {{.Repository}}:{{.Tag}}" | grep -E "(command-service|communications-service|filerepository-service|orgs-service|perms-service|sensors-service|workflow-service)" | head -20 || true
        docker images --format "{{.Repository}}:{{.Tag}}" | grep -E "(command-service|communications-service|filerepository-service|orgs-service|perms-service|sensors-service|workflow-service)" | xargs -r docker rmi -f || true
        
        # Remove dangling images and unused containers (safer than full prune)
        echo "Removing dangling images and stopped containers..."
        docker container prune -f || true
        docker image prune -f || true
        
        # Only remove unused build cache older than 24h (preserve recent cache)
        echo "Cleaning old build cache..."
        docker builder prune -f --filter "until=24h" || true
        
        echo "Disk usage after cleanup:"
        df -h

    - name: Set up Go
      uses: actions/setup-go@v5
      with:
        go-version: '1.23'

    - name: Set up Node.js
      uses: actions/setup-node@v4
      with:
        node-version: '22'

    - name: Prepare CDK
      run: ./infra/scripts/prepare-cdk.sh

    - name: Run CDK Deploy
      run: ./infra/scripts/deploy-services.sh

    # E2TDDCR4SH3TD4 is the command.gethero.com distribution id
    - name: Invalidate CloudFront cache
      run: |
        aws cloudfront create-invalidation \
          --distribution-id E2TDDCR4SH3TD4 \
          --paths "/*"

    # Step: Notify deployment success
    - name: Notify deployment success
      run: |
        echo "::notice::✅ Deployment completed successfully for commit ${{ github.sha }}"