name: Frontend Lint & Type Check

on:
  pull_request:

# Add concurrency control for PR builds
concurrency:
  group: frontend-lint-${{ github.event.pull_request.number }}
  cancel-in-progress: true

jobs:
  lint-and-typecheck:
    name: Lint & Type Check Command & Control Web
    runs-on: ubuntu-latest

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '22'

      - name: Install dependencies
        working-directory: apps/hero-command-and-control-web
        run: npm ci

      - name: Run ESLint
        working-directory: apps/hero-command-and-control-web
        run: npm run lint 