# Deployment Guide

This document explains how deployments work in the hero-core repository and how to handle deployment queuing.

## How Deployment Queuing Works

### Automatic Deployments
- Deployments are triggered automatically when code is pushed to the `main` branch
- Only **one deployment can run at a time** to prevent conflicts
- If a deployment is already running, subsequent deployments will be **queued automatically**
- Queued deployments will start as soon as the current deployment completes

### Concurrency Control
We use GitHub Actions concurrency control with these settings:
- **Concurrency Group**: `production-deployment`
- **Cancel in Progress**: `false` (deployments are queued, not cancelled)
- **Timeout**: 60 minutes per deployment

## Deployment Status Notifications

### Slack Notifications
You'll receive Slack notifications for:
- 🚀 **Deployment Success**: When a deployment completes successfully
- ❌ **Deployment Failed**: When a deployment fails

**Note**: Queue notifications are not available due to GitHub Actions limitations. Monitor the Actions tab to see queued deployments.

### GitHub Actions UI
Check the Actions tab in GitHub to see:
- Currently running deployments
- Queued deployments waiting to start
- Deployment history and logs

## Manual Deployments

Manual deployments have been **removed for security and simplicity**. All deployments now happen automatically when code is merged to the main branch.

### Deployment Process
- **Automatic only**: Deployments trigger automatically on merge to main
- **Single queue**: All deployments use the same concurrency group
- **No manual triggers**: Reduces risk of deploying wrong code or from wrong branch

## Troubleshooting

### Deployment Stuck or Taking Too Long
1. Check the current deployment status in Actions tab
2. If stuck for more than 60 minutes, the deployment will timeout automatically
3. If deployment fails, fix the issue and push a new commit to main to retry

### Multiple Deployments Failing
1. Check the deployment logs for error details
2. Common issues:
   - AWS credential problems
   - Docker build failures
   - CDK deployment errors
   - CloudFront invalidation issues

### Emergency: Cancel All Deployments
If you need to stop all deployments:
1. Go to Actions tab
2. Cancel the currently running deployment
3. This will also cancel any queued deployments
4. Push a new commit to main when ready to restart deployments

## Best Practices

### For Developers
- ✅ **Merge PRs one at a time** when possible to reduce queue length
- ✅ **Check deployment status** before merging additional PRs
- ✅ **Test thoroughly** in PR builds to avoid deployment failures
- ✅ **Monitor Slack notifications** for deployment status

### For Releases
- ✅ **Plan releases** during low-traffic periods when possible
- ✅ **Batch related changes** into single PRs to reduce deployment frequency
- ✅ **Merge hotfixes to main** for automatic deployment (will queue safely)
- ✅ **Verify deployment success** before considering the release complete
- ✅ **Monitor queue length** in Actions tab during busy periods

## Monitoring and Alerting

### What to Monitor
- **Deployment queue length**: Check Actions tab for queued workflows (🟡 clock icons)
- **Deployment success rate**: Monitor Slack success/failure notifications
- **Deployment duration trends**: Track time between merge and success notification
- **Failed deployment alerts**: Immediate Slack notifications with links to logs

### Key Metrics
- **Normal deployment time**: 15-30 minutes
- **Timeout threshold**: 60 minutes
- **Queue warning**: More than 3 deployments queued

## Configuration

### Workflow Files
- `publish-services.yml`: Main deployment workflow (concurrency group: `production-deployment`)
- `slack-prod-deploy-notify.yml`: Success/failure Slack notifications
- `build-services.yml`: PR build validation (separate per-PR concurrency)

### Environment Variables
- `AWS_ACCESS_KEY_ID`: AWS credentials for deployment
- `AWS_SECRET_ACCESS_KEY`: AWS credentials for deployment
- `SLACK_DEPLOY_WEBHOOK`: Webhook URL for Slack notifications
- `GITHUB_TOKEN`: Automatically provided by GitHub Actions

## Getting Help

If you encounter deployment issues:
1. Check this guide first
2. Look at recent deployment logs in Actions tab
3. Check Slack notifications for deployment status
4. Monitor queue status in GitHub Actions UI
5. Contact the DevOps team for persistent problems

## Current System Limitations

### Queue Visibility
- **No Slack notifications** for queued deployments (GitHub Actions limitation)
- **Monitor via GitHub UI**: Check Actions tab for 🟡 queued workflows
- **Time gaps indicate queuing**: Long delays between merge and success notification

### Emergency Deployment Process
- **No manual triggers**: All deployments happen automatically via git push to main
- **For hotfixes**: Create emergency branch, fix issue, merge to main immediately  
- **Automatic queuing**: Emergency deployments queue safely with existing deployments

---

*Last updated: January 2025* 