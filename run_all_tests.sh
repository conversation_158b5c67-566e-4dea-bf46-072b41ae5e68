#!/bin/bash

# Comprehensive test runner for Pre-Registration User Mapping and postConfirmation Lambda
# This script will test the entire flow end-to-end

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
NC='\033[0m' # No Color

echo -e "${PURPLE}🚀 Hero Core - Pre-Registration User Mapping Test Suite${NC}"
echo -e "${PURPLE}=====================================================${NC}"
echo

# Step 1: Check if services are running
echo -e "${YELLOW}📋 Step 1: Checking if required services are running...${NC}"

check_service() {
    local service_name=$1
    local port=$2
    local url="http://localhost:$port"
    
    if curl -s --connect-timeout 3 "$url" > /dev/null 2>&1; then
        echo -e "${GREEN}✅ $service_name is running on port $port${NC}"
        return 0
    else
        echo -e "${RED}❌ $service_name is NOT running on port $port${NC}"
        return 1
    fi
}

services_ok=true

if ! check_service "Orgs Service" 8080; then
    services_ok=false
fi

if ! check_service "Assets/Workflow Service" 8081; then
    services_ok=false
fi

if ! check_service "Permissions Service" 8082; then
    services_ok=false
fi

if [ "$services_ok" = false ]; then
    echo -e "${RED}❌ Some services are not running. Please start them before running tests.${NC}"
    echo -e "${YELLOW}💡 Tip: Make sure to start your local development environment first.${NC}"
    exit 1
fi

echo -e "${GREEN}✅ All required services are running!${NC}"
echo

# Step 2: Run API tests to create pre-registration mappings
echo -e "${YELLOW}📋 Step 2: Creating pre-registration mappings via API...${NC}"
echo -e "${BLUE}Running: ./test_pre_registration_api.sh${NC}"
echo

if ./test_pre_registration_api.sh; then
    echo -e "${GREEN}✅ Pre-registration mapping API tests completed successfully!${NC}"
else
    echo -e "${RED}❌ Pre-registration mapping API tests failed!${NC}"
    exit 1
fi

echo

# Step 3: Check database state
echo -e "${YELLOW}📋 Step 3: Checking database state...${NC}"
echo -e "${BLUE}Querying pre_registration_user_mappings table...${NC}"

# Note: This assumes you have a way to query the database locally
# You might need to adjust this based on your local setup
echo -e "${YELLOW}💡 Manual step: Please verify the following in your database:${NC}"
echo "   SELECT email, org_id, role_name, asset_type, used_at FROM pre_registration_user_mappings WHERE org_id = 2;"
echo
echo -e "${YELLOW}Expected results:${NC}"
echo "   - <EMAIL> | 2 | Admin | 2 | NULL"
echo "   - <EMAIL> | 2 | Supervisor | 6 | NULL"  
echo "   - <EMAIL> | 2 | Dispatcher | 2 | NULL"
echo "   - <EMAIL> | 2 | Responder | 3 | NULL"
echo "   - <EMAIL> | 2 | Admin | 2 | NULL"
echo

read -p "Press Enter to continue after verifying the database state..."

# Step 4: Test postConfirmation Lambda
echo -e "${YELLOW}📋 Step 4: Testing postConfirmation Lambda...${NC}"

# Check if we're in the Lambda directory
if [ -f "infra/cloud/shared/cognito-stack/postConfirmationLambda/index.js" ]; then
    echo -e "${BLUE}Found Lambda handler, testing with Node.js...${NC}"
    cd infra/cloud/shared/cognito-stack/postConfirmationLambda
    
    if node ../../../../../test_lambda_invoke.js; then
        echo -e "${GREEN}✅ Lambda tests completed!${NC}"
    else
        echo -e "${RED}❌ Lambda tests failed!${NC}"
    fi
    
    cd - > /dev/null
else
    echo -e "${YELLOW}⚠️  Lambda handler not found at expected location.${NC}"
    echo -e "${BLUE}Running Go test simulation instead...${NC}"
    
    if go run test_lambda_locally.go; then
        echo -e "${GREEN}✅ Lambda simulation completed!${NC}"
    else
        echo -e "${RED}❌ Lambda simulation failed!${NC}"
    fi
fi

echo

# Step 5: Final verification
echo -e "${YELLOW}📋 Step 5: Final verification steps...${NC}"
echo -e "${YELLOW}💡 After running the Lambda tests, please verify:${NC}"
echo
echo "1. Check that pre-registration mappings are marked as used:"
echo "   SELECT email, used_at FROM pre_registration_user_mappings WHERE used_at IS NOT NULL;"
echo
echo "2. Check that assets were created with correct cognito_jwt_sub:"
echo "   SELECT id, name, cognito_jwt_sub, asset_type FROM assets WHERE cognito_jwt_sub LIKE 'oktasandbox_%';"
echo
echo "3. Verify the cognito_jwt_sub matches the expected pattern:"
echo "   - Should be the username from Cognito (e.g., '<EMAIL>')"
echo
echo "4. Check that users have correct roles assigned:"
echo "   SELECT u.email, r.name as role_name FROM users u"
echo "   JOIN user_roles ur ON u.id = ur.user_id" 
echo "   JOIN roles r ON ur.role_id = r.id"
echo "   WHERE u.email LIKE '%@gethero.com';"
echo

echo -e "${GREEN}🎉 Test suite completed!${NC}"
echo -e "${BLUE}📝 Summary of what was tested:${NC}"
echo "   ✅ Pre-registration mapping CRUD operations"
echo "   ✅ Bulk creation of mappings"
echo "   ✅ Individual mapping retrieval"
echo "   ✅ Listing mappings with filters"
echo "   ✅ postConfirmation Lambda simulation"
echo
echo -e "${YELLOW}🔍 Next steps:${NC}"
echo "   1. Verify database state as shown above"
echo "   2. Test actual Cognito user signup flow if possible"
echo "   3. Monitor logs for any errors during Lambda execution"
