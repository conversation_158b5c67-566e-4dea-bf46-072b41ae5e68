package main

import (
	clients "common/clients/services"
	"context"
	"encoding/json"
	"fmt"
	"log"
	"os"
	assetpb "proto/hero/assets/v2"
	orgspb "proto/hero/orgs/v1"
	permspb "proto/hero/permissions/v1"
	"slices"
	"strconv"
	"strings"

	"connectrpc.com/connect"

	"github.com/aws/aws-lambda-go/lambda"
	"github.com/aws/aws-sdk-go/aws"
	"github.com/aws/aws-sdk-go/aws/session"
	"github.com/aws/aws-sdk-go/service/cognitoidentityprovider"
)

// CognitoUserEvent represents the event passed to the Lambda function
type CognitoUserEvent struct {
	Version       string `json:"version"`
	TriggerSource string `json:"triggerSource"`
	Region        string `json:"region"`
	UserName      string `json:"userName"`
	UserPoolID    string `json:"userPoolId"`
	CallerContext struct {
		ClientID string `json:"clientId"`
	} `json:"callerContext"`
	Request struct {
		UserAttributes map[string]string `json:"userAttributes"`
	} `json:"request"`
}

// EmailDomainConfig holds configuration for each email domain
type EmailDomainConfig struct {
	OrgID string
}

const secretID = "ADKHPJHPJHPKJjUIOPPGOYGOYGOUYGOG"

// parseIdentitiesForIssuer extracts issuer from identities JSON
func parseIdentitiesForIssuer(identitiesJSON string) (string, error) {
	var identities []map[string]interface{}
	err := json.Unmarshal([]byte(identitiesJSON), &identities)
	if err != nil {
		return "", err
	}

	if len(identities) > 0 {
		if issuer, exists := identities[0]["issuer"]; exists {
			return issuer.(string), nil
		}
	}
	return "", fmt.Errorf("issuer not found in identities")
}

// getOrgFromIssuer maps issuer to org ID using environment variables
func getOrgFromIssuer(issuer string) int32 {
	oktaProdIssuer := os.Getenv("OKTA_PROD_ISSUER")
	oktaSandboxIssuer := os.Getenv("OKTA_SANDBOX_ISSUER")
	prodOrgID := os.Getenv("PROD_ORG_ID")
	sandboxOrgID := os.Getenv("SANDBOX_ORG_ID")

	switch issuer {
	case oktaProdIssuer:
		if prodOrgIDInt, err := strconv.Atoi(prodOrgID); err == nil {
			return int32(prodOrgIDInt)
		}
		return 1 // Fallback to production
	case oktaSandboxIssuer:
		if sandboxOrgIDInt, err := strconv.Atoi(sandboxOrgID); err == nil {
			return int32(sandboxOrgIDInt)
		}
		return 2 // Fallback to sandbox
	default:
		log.Printf("Unknown issuer: %s, defaulting to org 1", issuer)
		return 1 // Default to production
	}
}

func handler(ctx context.Context, event CognitoUserEvent) (CognitoUserEvent, error) {
	log.Printf("Received event: %+v\n", event)
	log.Printf("TriggerSource: %s", event.TriggerSource)

	// Only run user setup logic for initial signup, not password reset
	// Common trigger sources: "PostConfirmation_ConfirmSignUp" and "PostConfirmation_ConfirmForgotPassword"
	if event.TriggerSource == "PostConfirmation_ConfirmForgotPassword" {
		log.Printf("Skipping user setup for password reset trigger: %s", event.TriggerSource)
		return event, nil
	}

	orgsServiceURL := os.Getenv("ORGS_SERVICE_URL")
	orgsClient := clients.NewOrgsClient(
		orgsServiceURL,
		clients.AuthHeaderSecretKeyInterceptor(secretID))

	// list orgs
	orgs, err := orgsClient.ListOrgs(ctx, connect.NewRequest(&orgspb.ListOrgsRequest{}))
	if err != nil {
		return event, err
	}

	// create a mapping of domain to org id
	domainToOrgID := make(map[string]int32)
	for _, org := range orgs.Msg.Orgs {
		for _, domain := range org.Domains {
			domainToOrgID[domain] = org.Id
		}
	}

	availableOrgIds := []int32{}
	for _, org := range orgs.Msg.Orgs {
		availableOrgIds = append(availableOrgIds, org.Id)
	}

	userEmail := event.Request.UserAttributes["email"]
	userEmailPrefix := ""
	userEmailDomain := ""
	orgID := int32(0)

	// First try to get org from SAML issuer
	identitiesJSON := event.Request.UserAttributes["identities"]
	if identitiesJSON != "" {
		issuer, err := parseIdentitiesForIssuer(identitiesJSON)
		if err != nil {
			log.Printf("Error parsing issuer: %v", err)
		} else {
			log.Printf("Extracted issuer: %s", issuer)
			orgID = getOrgFromIssuer(issuer)
			log.Printf("Mapped to org ID: %d", orgID)
		}
	}

	// Fallback to email domain mapping if issuer-based routing failed
	if orgID == 0 && userEmail != "" {
		if atIndex := strings.Index(userEmail, "@"); atIndex != -1 {
			userEmailPrefix = userEmail[:atIndex]
			userEmailDomain = userEmail[atIndex+1:] // +1 to skip @
			orgID = domainToOrgID[userEmailDomain]
		}
		// allow @gethero.com emails to choose their own org (for testing purposes)
		// example: johndoe+org:<EMAIL> should get org:2
		if userEmailDomain == "gethero.com" {
			if orgIndex := strings.Index(userEmailPrefix, "+org"); orgIndex != -1 {
				requestedOrgID := userEmailPrefix[orgIndex+4:]
				requestedOrgIDInt, err := strconv.Atoi(requestedOrgID)
				if err != nil {
					return event, fmt.Errorf("invalid organization ID format: %s", requestedOrgID)
				}
				// if the requested org id is in the list of orgs, use it
				if slices.Contains(availableOrgIds, int32(requestedOrgIDInt)) {
					orgID = int32(requestedOrgIDInt)
				} else {
					return event, fmt.Errorf("invalid organization ID: %s", requestedOrgID)
				}
				log.Printf("injecting responder asset for orgID: %d\n", orgID)
			}
		}
	}

	// Set userEmailPrefix if not already set
	if userEmailPrefix == "" && userEmail != "" {
		if atIndex := strings.Index(userEmail, "@"); atIndex != -1 {
			userEmailPrefix = userEmail[:atIndex]
		}
	}

	assetServiceURL := os.Getenv("WORKFLOW_SERVICE_URL")
	assetClient := clients.NewAssetsClient(
		assetServiceURL,
		clients.AuthHeaderSecretKeyInterceptor(secretID))

	permsServiceURL := os.Getenv("PERMS_SERVICE_URL")
	permsClient := clients.NewPermissionClient(
		permsServiceURL,
		clients.AuthHeaderSecretKeyInterceptor(secretID))

	// Check for pre-registration mapping first
	var roleName string
	var assetType assetpb.AssetType = assetpb.AssetType_ASSET_TYPE_RESPONDER // default

	mappingResp, err := orgsClient.GetPreRegistrationMapping(ctx, connect.NewRequest(&orgspb.GetPreRegistrationMappingRequest{
		Email: userEmail,
		OrgId: orgID,
	}))

	if err == nil && mappingResp.Msg.Mapping != nil {
		// Use pre-registered role
		roleName = mappingResp.Msg.Mapping.RoleName
		if mappingResp.Msg.Mapping.AssetType != assetpb.AssetType_ASSET_TYPE_UNSPECIFIED {
			assetType = mappingResp.Msg.Mapping.AssetType
		}
		log.Printf("Found pre-registration mapping for %s: role=%s, assetType=%v", userEmail, roleName, assetType)

		// Mark as used
		_, markErr := orgsClient.MarkMappingAsUsed(ctx, connect.NewRequest(&orgspb.MarkMappingAsUsedRequest{
			Email: userEmail,
			OrgId: orgID,
		}))
		if markErr != nil {
			log.Printf("Warning: failed to mark mapping as used: %v", markErr)
		}
	} else {
		// Fallback to environment variable
		roleName = os.Getenv("RESPONDER_ROLE_NAME")
		log.Printf("No pre-registration mapping found for %s, using default role: %s", userEmail, roleName)
	}

	// Get environment variables
	userPoolID := os.Getenv("USER_POOL_ID")
	if roleName == "" {
		log.Println("No matching role found, skipping user assignment")
		return event, nil
	}

	// Create Cognito service client
	sess := session.Must(session.NewSession())
	svc := cognitoidentityprovider.New(sess)

	// Assign user to the appropriate organization
	orgGroupName := "org:" + strconv.Itoa(int(orgID))
	_, err = svc.AdminAddUserToGroup(&cognitoidentityprovider.AdminAddUserToGroupInput{
		UserPoolId: aws.String(userPoolID),
		Username:   aws.String(event.UserName),
		GroupName:  aws.String(orgGroupName),
	})
	if err != nil {
		log.Printf("Error adding user to org group: %v", err)
		return event, err
	}

	// Always assign role and create asset (no longer checking against specific role name)
	// Assign user to the appropriate role
	_, err = permsClient.AddCognitoUserToRole(ctx, connect.NewRequest(&permspb.AddCognitoUserToRoleRequest{
		RoleName:      roleName,
		CognitoSubId:  event.UserName,
		OrgIdOverride: orgID,
	}))
	if err != nil {
		log.Printf("Error assigning role: %v", err)
		return event, err
	}

	// create an asset (our internal user record) in the assets service
	_, err = assetClient.CreateResponderAsset(
		ctx,
		connect.NewRequest(&assetpb.CreateAssetRequest{
			Asset: &assetpb.Asset{
				Type:          assetType,
				Name:          userEmailPrefix,
				CognitoJwtSub: event.UserName,
				OrgId:         orgID,
			},
		}),
	)
	if err != nil {
		log.Printf("Error creating asset for user: %v", err)
		return event, err
	}

	if err != nil {
		log.Printf("Error adding user to group: %v", err)
		return event, err
	}

	log.Printf("User %s successfully added to role %s", event.UserName, roleName)
	return event, nil
}

func main() {
	lambda.Start(handler)
}
