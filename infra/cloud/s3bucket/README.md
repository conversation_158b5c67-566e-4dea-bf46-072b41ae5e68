# S3 Bucket Configuration System

This directory contains the configuration-driven S3 bucket management system for the Hero infrastructure.

## Table of Contents

- [Overview](#overview)
- [Files](#files)
  - [TypeScript Files](#typescript-files)
  - [Shell Scripts](#shell-scripts)
- [Configuration](#configuration)
  - [Adding a New Bucket](#adding-a-new-bucket)
  - [Enabling/Disabling Buckets](#enablingdisabling-buckets)
- [Usage](#usage)
  - [Check Bucket Status (default - safe)](#check-bucket-status-default---safe)
  - [Deploy Buckets](#deploy-buckets)
  - [Show Current Configuration](#show-current-configuration)
  - [Analyze Existing Buckets](#analyze-existing-buckets)
- [Configuration Options](#configuration-options)
  - [S3BucketConfig Interface](#s3bucketconfig-interface)
  - [Common Configurations](#common-configurations)
- [How It Works](#how-it-works)
- [Code Execution Flow](#code-execution-flow)
  - [Visual Flow Diagram](#visual-flow-diagram)
  - [Detailed Step-by-Step Execution](#detailed-step-by-step-execution)
  - [Key Configuration Flow](#key-configuration-flow)
- [System Architecture](#system-architecture)
- [Safety Features](#safety-features)
- [Migration from Console-Created Buckets](#migration-from-console-created-buckets)
  - [Option 1: Import Existing Bucket (Recommended)](#option-1-import-existing-bucket-recommended)
  - [Option 2: Create New CDK-Managed Bucket](#option-2-create-new-cdk-managed-bucket)
- [Removing or Disabling Buckets](#removing-or-disabling-buckets)
  - [What Happens When You Comment Out a Deployed Bucket?](#what-happens-when-you-comment-out-a-deployed-bucket)
  - [Behavior Summary](#behavior-summary)
  - [Safe Removal Strategies](#safe-removal-strategies)
  - [Best Practices](#best-practices)
- [Troubleshooting](#troubleshooting)
  - [Common Issues](#common-issues)
  - [Development vs Production](#development-vs-production)
  - [Debugging Tips](#debugging-tips)
- [Key Development Insights](#key-development-insights)
  - [Why We Use ts-node Instead of Compilation](#why-we-use-ts-node-instead-of-compilation)
  - [Safe-by-Default Design](#safe-by-default-design)
  - [Configuration-Driven Approach](#configuration-driven-approach)

## Overview

Instead of manually creating S3 buckets through the AWS console, this system allows you to:
- ✅ Define multiple S3 buckets in a single configuration file
- ✅ Deploy buckets conditionally (skip if already exists)
- ✅ Update bucket configurations automatically
- ✅ Manage all bucket settings through code

## Files

### TypeScript Files

- **`s3-buckets.ts`** - Main configuration file where you define your S3 buckets
  - Contains the `S3_BUCKETS_CONFIG` object with all bucket definitions
  - Exports `getEnabledBuckets()` function to control which buckets are deployed
  - Defines the `S3BucketConfig` interface for type safety

- **`s3-stack.ts`** - CDK stack definition for S3 buckets
  - Creates the actual AWS CloudFormation stack
  - Reads configuration from `s3-buckets.ts` and creates S3 buckets accordingly
  - Handles bucket properties like encryption, CORS, lifecycle rules, and tags
  - Exports stack outputs (bucket names, ARNs, domain names) for other stacks to reference
  - **Independent stack** - No dependencies on other infrastructure components for clean deployment

- **`deploy-s3-buckets.ts`** - Intelligent deployment script with AWS SDK integration
  - Compares local configuration with existing AWS bucket settings
  - Detects which buckets need to be created or updated
  - Provides three modes: `check` (dry-run), `deploy` (apply changes), `info` (show config)
  - Uses AWS SDK to inspect current bucket configurations
  - Orchestrates CDK deployment when changes are needed

### Shell Scripts

- **`../../scripts/deploy-s3.sh`** - Shell wrapper for easy execution
  - Validates prerequisites (Node.js, AWS CLI, credentials)
  - Installs dependencies automatically
  - Provides a simple interface to the TypeScript deployment script

- **`../../scripts/s3_bucket_info.sh`** - Bucket analysis utility
  - Provides comprehensive information about any existing S3 bucket
  - Shows location, policy, ACL, encryption, versioning, CORS, tags, and website configuration
  - Useful for understanding existing buckets before importing them
  - Helps troubleshoot configuration drift

## Configuration

### Adding a New Bucket

Edit `s3-buckets.ts` and add your bucket configuration to the `S3_BUCKETS_CONFIG` object:

```typescript
export const S3_BUCKETS_CONFIG: { [key: string]: S3BucketConfig } = {
  // Existing buckets...
  
  myNewBucket: {
    bucketName: 'my-new-bucket-name',
    description: 'Description of what this bucket is for',
    encryption: s3.BucketEncryption.S3_MANAGED,
    bucketKeyEnabled: true,
    versioned: true,
    blockPublicAccess: s3.BlockPublicAccess.BLOCK_ALL,
    cors: [
      {
        allowedHeaders: ['*'],
        allowedMethods: [s3.HttpMethods.GET, s3.HttpMethods.POST],
        allowedOrigins: ['https://mydomain.com'],
        maxAge: 3000,
      },
    ],
    tags: {
      Environment: 'production',
      Service: 'my-service'
    }
  }
};
```

### Enabling/Disabling Buckets

Control which buckets are deployed by modifying the `getEnabledBuckets()` function:

```typescript
export function getEnabledBuckets(): { [key: string]: S3BucketConfig } {
  return {
    fileRepository: S3_BUCKETS_CONFIG.fileRepository,
    myNewBucket: S3_BUCKETS_CONFIG.myNewBucket, // Enable this bucket
    // tempUploads: S3_BUCKETS_CONFIG.tempUploads, // Disabled (commented out)
  };
}
```

## Usage

### Check Bucket Status (default - safe)

```bash
# Default behavior - shows what would happen without deploying
./scripts/deploy-s3.sh

# Or explicitly specify check command
./scripts/deploy-s3.sh check
```

This will show you:
- Which buckets exist vs. need to be created
- What configuration changes are needed
- But won't actually deploy anything

### Deploy Buckets

```bash
./scripts/deploy-s3.sh deploy
```

This will:
1. Check each configured bucket's status
2. Create new buckets that don't exist
3. Update existing buckets with configuration changes
4. Deploy using CDK

### Show Current Configuration

```bash
./scripts/deploy-s3.sh info
```

This displays all currently configured buckets and their settings.

### Analyze Existing Buckets

If you need to understand the configuration of any existing S3 bucket:

```bash
./scripts/s3_bucket_info.sh bucket-name
```

This utility script provides comprehensive information about a bucket:
- **Location**: Which AWS region the bucket is in
- **Policy**: Bucket policy (if any)
- **ACL**: Access Control List settings
- **Public Access Block**: Public access restrictions
- **Encryption**: Server-side encryption configuration
- **Lifecycle**: Lifecycle management rules
- **Versioning**: Whether object versioning is enabled
- **CORS**: Cross-Origin Resource Sharing configuration
- **Tags**: Resource tags applied to the bucket
- **Website**: Static website hosting configuration (if enabled)

**Example output:**
```bash
$ ./scripts/s3_bucket_info.sh hero-file-repository

Location:
{
    "LocationConstraint": "us-west-2"
}

Encryption:
{
    "ServerSideEncryptionConfiguration": {
        "Rules": [
            {
                "ApplyServerSideEncryptionByDefault": {
                    "SSEAlgorithm": "AES256"
                }
            }
        ]
    }
}

Versioning:
{
    "Status": "Enabled"
}
```

This is particularly useful when:
- Planning to import existing buckets into CDK
- Troubleshooting configuration drift
- Documenting current bucket settings
- Comparing multiple bucket configurations

## Configuration Options

### S3BucketConfig Interface

```typescript
interface S3BucketConfig {
  bucketName: string;              // The actual S3 bucket name
  description: string;             // Human-readable description
  encryption?: s3.BucketEncryption; // Encryption type (default: S3_MANAGED)
  bucketKeyEnabled?: boolean;      // Enable S3 Bucket Key (default: true)
  versioned?: boolean;             // Enable versioning (default: false)
  blockPublicAccess?: s3.BlockPublicAccess; // Public access settings
  cors?: s3.CorsRule[];           // CORS configuration
  lifecycleRules?: s3.LifecycleRule[]; // Lifecycle management
  removalPolicy?: cdk.RemovalPolicy;   // What happens when stack is deleted
  autoDeleteObjects?: boolean;     // Auto-delete objects with stack
  tags?: { [key: string]: string }; // Resource tags
}
```

### Common Configurations

**Basic File Storage:**
```typescript
{
  bucketName: 'my-files',
  description: 'Basic file storage',
  versioned: true,
  blockPublicAccess: s3.BlockPublicAccess.BLOCK_ALL,
}
```

**Web Assets with CORS:**
```typescript
{
  bucketName: 'my-web-assets',
  description: 'Web assets with CORS',
  cors: [{
    allowedHeaders: ['*'],
    allowedMethods: [s3.HttpMethods.GET],
    allowedOrigins: ['https://mywebsite.com'],
    maxAge: 86400,
  }],
}
```

**Temporary Storage with Lifecycle:**
```typescript
{
  bucketName: 'my-temp-storage',
  description: 'Temporary files (auto-delete after 30 days)',
  lifecycleRules: [{
    id: 'DeleteOldFiles',
    enabled: true,
    expiration: cdk.Duration.days(30),
  }],
  removalPolicy: cdk.RemovalPolicy.DESTROY,
  autoDeleteObjects: true,
}
```

## How It Works

1. **Configuration Check**: The script compares your local configuration with the actual AWS bucket settings
2. **Conditional Deployment**: Only deploys/updates buckets that have changes
3. **CDK Integration**: Uses AWS CDK for reliable, repeatable deployments
4. **State Management**: CDK tracks the state of your infrastructure

## Code Execution Flow

When S3Stack is deployed (either through CI/CD or manually), here's the complete execution flow that takes your configuration and creates actual AWS resources:

### Visual Flow Diagram

```mermaid
graph TD
    A["GitHub CI Push to main"] --> B["publish-services.yml workflow"]
    B --> C["./infra/scripts/deploy-services.sh"]
    C --> D["cdk deploy S3Stack --require-approval never"]
    D --> E["CDK App Entry Point<br/>infra/bin/deploy.ts"]
    E --> F["new EntryStack()"]
    F --> G["new S3Stack()"]
    G --> H["getEnabledBuckets()"]
    H --> I["S3_BUCKETS_CONFIG from s3-buckets.ts"]
    I --> J["Object.entries(bucketsConfig).forEach()"]
    J --> K["createBucket() for each config"]
    K --> L["new s3.Bucket() with CDK"]
    L --> M["AWS CloudFormation Stack"]
    M --> N["Physical S3 Bucket Created/Updated"]
    
    style A fill:#e1f5fe
    style N fill:#c8e6c9
    style I fill:#fff3e0
```

### Detailed Step-by-Step Execution

#### 1. CI/CD Trigger
```bash
# GitHub CI calls:
./infra/scripts/deploy-services.sh
```

#### 2. CDK Deploy Command
```bash
# deploy-services.sh executes:
cd infra
cdk deploy S3Stack --require-approval never
```

#### 3. CDK App Entry Point
**File: `infra/bin/deploy.ts`**
```typescript
const app = new cdk.App();
new EntryStack(app, 'CDKEntryStack', { ... });
```

#### 4. EntryStack Constructor
**File: `infra/cloud/entry-stack.ts` (line 28)**
```typescript
const s3Stack = new S3Stack(scope, 'S3Stack', { 
  env: { region: 'us-west-2' }, 
  crossRegionReferences: true 
});
```

**✅ Architecture Design**: S3Stack is designed to operate independently without external dependencies, making deployments reliable and the architecture clean.

#### 5. S3Stack Constructor
**File: `infra/cloud/s3bucket/s3-stack.ts` (line 11-19)**
```typescript
constructor(scope: Construct, id: string, props?: cdk.StackProps) {
  super(scope, id, props);

  // Get enabled buckets from configuration
  const bucketsConfig = getEnabledBuckets();  // 👈 This calls the config

  // Create each configured bucket
  Object.entries(bucketsConfig).forEach(([bucketKey, config]) => {
    this.buckets[bucketKey] = this.createBucket(bucketKey, config);  // 👈 Create each bucket
  });
}
```

#### 6. Configuration Loading
**File: `infra/cloud/s3bucket/s3-buckets.ts` (line 44-54)**
```typescript
export function getEnabledBuckets(): { [key: string]: S3BucketConfig } {
  const enabledBuckets = {
    fileRepository: S3_BUCKETS_CONFIG.fileRepository,  // 👈 Loads config
  };
  return enabledBuckets;
}
```

#### 7. Bucket Configuration Source
**File: `infra/cloud/s3bucket/s3-buckets.ts` (line 16-42)**
```typescript
export const S3_BUCKETS_CONFIG: { [key: string]: S3BucketConfig } = {
  fileRepository: {
    bucketName: 'hero-file-repository',          // 👈 Bucket name
    description: 'Main file repository...',      // 👈 Description
    encryption: s3.BucketEncryption.S3_MANAGED,  // 👈 Encryption settings
    bucketKeyEnabled: true,                      // 👈 Encryption key settings
    versioned: true,                             // 👈 Versioning
    blockPublicAccess: s3.BlockPublicAccess.BLOCK_ALL,  // 👈 Security
    cors: [...],                                 // 👈 CORS rules
    removalPolicy: cdk.RemovalPolicy.RETAIN,    // 👈 Deletion protection
    tags: { ... }                               // 👈 Resource tags
  },
};
```

#### 8. Bucket Creation Loop
**File: `infra/cloud/s3bucket/s3-stack.ts` (line 17-19)**
```typescript
Object.entries(bucketsConfig).forEach(([bucketKey, config]) => {
  // bucketKey = "fileRepository"  
  // config = { bucketName: "hero-file-repository", ... }
  this.buckets[bucketKey] = this.createBucket(bucketKey, config);
});
```

#### 9. Individual Bucket Creation
**File: `infra/cloud/s3bucket/s3-stack.ts` (line 22-50)**
```typescript
private createBucket(bucketKey: string, config: S3BucketConfig): s3.Bucket {
  const bucket = new s3.Bucket(this, `${bucketKey}Bucket`, {
    bucketName: config.bucketName,                    // "hero-file-repository"
    encryption: config.encryption,                    // S3_MANAGED
    bucketKeyEnabled: config.bucketKeyEnabled,        // true
    versioned: config.versioned,                      // true
    blockPublicAccess: config.blockPublicAccess,      // BLOCK_ALL
    cors: config.cors,                               // [{ allowedMethods: [...] }]
    lifecycleRules: config.lifecycleRules,           // []
    removalPolicy: config.removalPolicy,             // RETAIN
    autoDeleteObjects: config.autoDeleteObjects,     // false
  });
  
  // Add tags if specified
  if (config.tags) {
    Object.entries(config.tags).forEach(([key, value]) => {
      cdk.Tags.of(bucket).add(key, value);
    });
  }
  
  return bucket;
}
```

#### 10. CloudFormation Stack Generation
CDK converts the `s3.Bucket` construct into CloudFormation template:
```json
{
  "Resources": {
    "fileRepositoryBucket": {
      "Type": "AWS::S3::Bucket",
      "Properties": {
        "BucketName": "hero-file-repository",
        "BucketEncryption": { ... },
        "VersioningConfiguration": { "Status": "Enabled" },
        "CorsConfiguration": { ... },
        "Tags": [...]
      }
    }
  }
}
```

#### 11. AWS Resource Creation
CloudFormation deploys the template, creating the actual S3 bucket in AWS with all specified configurations.

### Key Configuration Flow

The beauty of this system is that **all bucket configuration flows from a single source**:

1. **Configuration Source**: `s3-buckets.ts` → `S3_BUCKETS_CONFIG` object
2. **Filtering**: `getEnabledBuckets()` function controls which buckets deploy
3. **CDK Processing**: Each enabled bucket becomes a `s3.Bucket` construct
4. **CloudFormation**: CDK generates AWS CloudFormation template
5. **AWS Resources**: CloudFormation creates actual S3 buckets

### To Modify Bucket Configuration:

1. **Edit** `infra/cloud/s3bucket/s3-buckets.ts`
2. **Change** properties in `S3_BUCKETS_CONFIG.fileRepository`
3. **Push to main** branch (or deploy manually)
4. **CI automatically deploys** the changes

### To Add New Buckets:

1. **Add** new entry to `S3_BUCKETS_CONFIG`
2. **Include** it in `getEnabledBuckets()` return object
3. **Push to main** branch

**Example:**
```typescript
export const S3_BUCKETS_CONFIG = {
  fileRepository: { ... },
  // Add new bucket:
  userUploads: {
    bucketName: 'hero-user-uploads',
    description: 'User uploaded files',
    versioned: false,
    // ... other config
  }
};

export function getEnabledBuckets() {
  return {
    fileRepository: S3_BUCKETS_CONFIG.fileRepository,
    userUploads: S3_BUCKETS_CONFIG.userUploads,  // 👈 Enable new bucket
  };
}
```

This configuration-driven approach ensures that **all bucket settings are version-controlled, reviewable, and automatically deployed** through your existing CI/CD pipeline! 🎯

### System Architecture

```
┌─────────────────┐    ┌──────────────────┐    ┌─────────────────┐
│   s3-buckets.ts │───▶│ deploy-s3-buckets.ts │───▶│   s3-stack.ts   │
│   (Config)      │    │   (Intelligence)     │    │   (CDK Stack)   │
└─────────────────┘    └──────────────────────┘    └─────────────────┘
                                    │
                                    ▼
                       ┌──────────────────────┐
                       │     AWS S3 API       │
                       │  (Check existing)    │
                       └──────────────────────┘
                                    │
                                    ▼
                       ┌──────────────────────┐
                       │   CDK Deployment     │
                       │  (CloudFormation)    │
                       └──────────────────────┘
```

## Architecture Design 

### ✅ **Independent Stack Pattern**

**Design Decision**: The S3Stack follows a clean, dependency-free architecture pattern:

```typescript
// ✅ Clean Implementation:
const s3Stack = new S3Stack(scope, 'S3Stack', props);
//                                            ^^^^^
//                                         No external dependencies!

// S3Stack operates independently and can be deployed standalone
```

**Why This Pattern**: We chose this approach to avoid common infrastructure anti-patterns like circular dependencies and type assertion bypasses that can lead to runtime failures.

### 🎯 **Benefits of This Architecture**

1. **Independent Deployment**: S3Stack can be deployed without waiting for other infrastructure
2. **Type Safety**: Clean constructor signatures without dangerous type assertions
3. **Clear Dependencies**: S3 buckets are created first, then services that depend on them
4. **Fast CI/CD**: S3Stack deployment is not blocked by external dependencies
5. **Maintainable**: No risk of runtime errors from accessing undefined shared resources

### 🔄 **Service Integration Pattern**

**How Services Access S3 Buckets**:

```typescript
// ✅ Environment Variable Pattern:
{
  serviceName: "FileRepository",
  env: {
    "AWS_S3_BUCKET_NAME": "hero-file-repository",  // ← Direct configuration
  }
}
```

**Integration Flow**:
1. **S3Stack** creates `hero-file-repository` bucket (independent)
2. **Services** get bucket name via environment variables
3. **AWS SDK** uses IAM roles for bucket access (clean separation of concerns)

### 📋 **Deployment Order**

```bash
# CI/CD deployment order (from deploy-services.sh):
S3Stack                        # ← Creates buckets FIRST
WorkflowFargateServiceStack    # ← Services deploy AFTER buckets exist
CommunicationsFargateServiceStack
FileRepositoryFargateServiceStack  # ← Gets bucket name from env vars
# ... other services
```

This ensures buckets exist before any services that need them are deployed.

### 🚀 **CI/CD Integration**

The S3Stack is fully integrated into the GitHub Actions deployment pipeline:

```yaml
# From .github/workflows/publish-services.yml
- name: Run CDK Deploy
  run: ./infra/scripts/deploy-services.sh
```

**Deployment Script** (`infra/scripts/deploy-services.sh`):
```bash
cdk deploy --concurrency 10 --require-approval never --exclusively \
S3Stack \                        # ← First in deployment order
WorkflowFargateServiceStack \
# ... other services
FileRepositoryFargateServiceStack  # ← Uses S3 bucket created above
```

**Key Benefits**:
- **Automated deployment** on every push to main branch
- **Parallel execution** with `--concurrency 10` for faster deployments
- **No manual approval** required with `--require-approval never`
- **Dependency order** ensures S3 buckets exist before services deploy
- **Independent testing** - S3Stack can be deployed and tested separately

### Safety Features

- **Default to Check Mode**: Script shows what would happen without applying changes
- **Retain Policy**: Buckets are preserved even if removed from code
- **Configuration Validation**: TypeScript ensures configuration correctness
- **Drift Detection**: Automatically detects manual changes in AWS Console

## Migration from Console-Created Buckets

If you have existing buckets created through the AWS console, you have two options:

### Option 1: Import Existing Bucket (Recommended)

This preserves your existing bucket name and data:

1. **Analyze the existing bucket** to understand its current configuration:
   ```bash
   ./scripts/s3_bucket_info.sh your-existing-bucket-name
   ```
2. **Add bucket to configuration** with the same name and matching settings
3. **Remove any conflicting CDK buckets** first:
   ```typescript
   // Temporarily comment out in getEnabledBuckets()
   // fileRepository: S3_BUCKETS_CONFIG.fileRepository,
   ```
4. **Deploy to remove old CDK bucket**: `./scripts/deploy-s3.sh deploy`
5. **Create import mapping file** (`s3-import-mapping.json`):
   ```json
   [{
     "resourceType": "AWS::S3::Bucket",
     "logicalId": "fileRepositoryBucketBB7CD2A6",
     "resourceIdentifier": {
       "BucketName": "your-existing-bucket-name"
     }
   }]
   ```
6. **Import the bucket**: `cdk import S3Stack --resource-mapping s3-import-mapping.json`
7. **Re-enable in configuration** and deploy

### Option 2: Create New CDK-Managed Bucket

Use different bucket names and migrate data manually:

1. Configure with a new name (e.g., `my-bucket-cdk`)
2. Deploy the new bucket
3. Migrate data from old to new bucket
4. Update applications to use new bucket
5. Delete old bucket when ready

## Removing or Disabling Buckets

### What Happens When You Comment Out a Deployed Bucket?

When you comment out an already deployed bucket from `getEnabledBuckets()`, the bucket becomes **"orphaned"**:

```typescript
export function getEnabledBuckets(): { [key: string]: S3BucketConfig } {
  return {
    // fileRepository: S3_BUCKETS_CONFIG.fileRepository, // Commented out
  };
}
```

**CDK Diff Output:**
```
Resources
[-] AWS::S3::Bucket fileRepositoryBucket fileRepositoryBucketBB7CD2A6 orphan
```

### Behavior Summary

| Aspect | What Happens |
|--------|-------------|
| **AWS Console** | ✅ Bucket remains visible and functional |
| **CDK Management** | ❌ No longer managed by infrastructure as code |
| **Data Safety** | ✅ All data preserved (due to `RETAIN` policy) |
| **Cost** | ⚠️ Still incurring storage costs |
| **Application Access** | ✅ Apps can still access if they know the bucket name |
| **Future Updates** | ❌ Can't update bucket settings through CDK anymore |

### Safe Removal Strategies

#### Option 1: Temporary Disable (Recommended)
```typescript
export function getEnabledBuckets() {
  return {
    // fileRepository: S3_BUCKETS_CONFIG.fileRepository, // Temporarily disabled
    // Uncomment when needed again
  };
}
```
**Result:** Bucket becomes orphaned but preserved safely

#### Option 2: Permanent Deletion (DANGEROUS)
```typescript
// First, change the removal policy
fileRepository: {
  // ... other config ...
  removalPolicy: cdk.RemovalPolicy.DESTROY,
  autoDeleteObjects: true, // Required for DESTROY
}
```
Then deploy, then comment out. **Result:** Bucket and ALL contents permanently deleted

#### Option 3: Manual Cleanup
1. Comment out the bucket configuration
2. Deploy to orphan it (bucket preserved)
3. Manually delete from AWS Console when ready

### ⚠️ Important Warnings

- **Data Protection**: Our default `removalPolicy: RETAIN` protects your data
- **Cost Awareness**: Orphaned buckets continue charging for storage
- **Documentation**: Keep track of orphaned buckets for future cleanup
- **No Rollback**: Once deleted with DESTROY policy, data cannot be recovered

### Best Practices

1. **Always use `RETAIN` policy for production buckets** (our default)
2. **Test removal in development environment first**
3. **Backup important data before any removal operations**
4. **Monitor orphaned buckets and clean up periodically**
5. **Use versioning for critical buckets** to protect against accidental deletion

## Troubleshooting

### Common Issues

**"Bucket already exists" Error**
```
Resource handler returned message: "hero-file-repository already exists"
```
- **Cause**: CDK is trying to create a bucket that already exists
- **Solution**: Use the bucket import process (see Migration section above)

**Permission Errors**
- **Cause**: Insufficient AWS permissions
- **Solution**: Ensure your AWS credentials have S3 and CloudFormation permissions

**TypeScript Compilation Conflicts**
```
error TS5055: Cannot write file 'build/...' because it would overwrite input file
```
- **Cause**: TypeScript declaration files conflicting with source structure
- **Solution**: This system uses `ts-node` for development (no compilation needed)
- **Note**: Only production deployment uses `npm run build`

**Avoiding Circular Dependency Issues**
```
TypeError: Cannot read properties of undefined (reading 'vpc')
```
- **Common Issue**: When stacks try to access SharedStacks properties that are undefined
- **Prevention**: S3Stack is designed without SharedStacks dependency
- **Design Benefit**: ✅ **Clean Architecture** - S3Stack operates independently

**Bucket Name Conflicts**
- **Cause**: S3 bucket names must be globally unique across all AWS accounts
- **Solution**: Choose a more specific name (e.g., add company prefix)

**Configuration Drift**
- **Cause**: Manual changes to bucket settings in AWS Console
- **Solution**: The script will detect and report configuration differences

**Deployment Failures**
- **Cause**: Various CDK or AWS issues
- **Solution**: Check the CDK logs for detailed error information

**Orphaned Buckets**
- **Cause**: Buckets commented out from configuration but still exist in AWS
- **Solution**: Use `aws s3 ls` to list all buckets and identify orphaned ones

### Development vs Production

- **Development**: Uses `ts-node` (in-memory compilation, no disk files)
- **Production**: Uses `npm run build` then runs compiled JavaScript
- **Key Point**: You rarely need to run `npm run build` during development

### Debugging Tips

1. **Check bucket status**: `./scripts/deploy-s3.sh` (safe check mode)
2. **View CDK diff**: `cdk diff S3Stack` (shows planned changes)
3. **List all buckets**: `aws s3 ls` (see what exists in AWS)
4. **Analyze specific bucket**: `./scripts/s3_bucket_info.sh bucket-name`
5. **Check CloudFormation**: AWS Console → CloudFormation → S3Stack
6. **Verify configuration**: `./scripts/deploy-s3.sh info`

## Key Development Insights

### Why We Use ts-node Instead of Compilation

This project uses `ts-node` for development, which means:
- ✅ **No build step needed** for development work
- ✅ **In-memory compilation** - faster iteration
- ✅ **No file conflicts** between source and compiled files
- ⚠️ **Production deployments** still use `npm run build` for CDK

### Safe-by-Default Design

- **Check mode is default**: Running the script without arguments shows you what would happen
- **Explicit deployment**: You must specifically run `deploy` to make changes
- **Retain policy**: Buckets are preserved even if removed from configuration
- **Import-friendly**: Easy to adopt existing manually-created buckets

### Configuration-Driven Approach

Instead of creating buckets manually:
1. **Define once** in `s3-buckets.ts`
2. **Deploy everywhere** with consistent settings
3. **Update centrally** when configuration changes
4. **Track changes** through version control

### Independent Stack Architecture

The S3Stack follows a **dependency-free design pattern**:
- ✅ **No external dependencies**: S3Stack doesn't require other infrastructure to be deployed first
- ✅ **Environment variable integration**: Services get bucket configuration through env vars, not CDK references
- ✅ **Deployment reliability**: Can be deployed standalone or as part of the full infrastructure
- ✅ **Type safety**: Clean constructor signatures without type assertion bypasses

This architectural design makes the system maintainable and reduces deployment complexity. 