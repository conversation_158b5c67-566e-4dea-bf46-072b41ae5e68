import * as cdk from 'aws-cdk-lib';
import * as s3 from 'aws-cdk-lib/aws-s3';
import { Construct } from 'constructs';
import { getEnabledBuckets, S3BucketConfig } from './s3-buckets';

export class S3Stack extends cdk.Stack {
  public readonly buckets: { [key: string]: s3.Bucket } = {};

  constructor(scope: Construct, id: string, props?: cdk.StackProps) {
    super(scope, id, props);

    // Get enabled buckets from configuration
    const bucketsConfig = getEnabledBuckets();

    // Create each configured bucket
    Object.entries(bucketsConfig).forEach(([bucketKey, config]) => {
      this.buckets[bucketKey] = this.createBucket(bucketKey, config);
    });
  }

  private createBucket(bucketKey: string, config: S3BucketConfig): s3.Bucket {
    const bucket = new s3.Bucket(this, `${bucketKey}Bucket`, {
      bucketName: config.bucketName,
      
      // Encryption configuration
      encryption: config.encryption || s3.BucketEncryption.S3_MANAGED,
      bucketKeyEnabled: config.bucketKeyEnabled ?? true,
      
      // Versioning
      versioned: config.versioned ?? false,
      
      // Public access block
      blockPublicAccess: config.blockPublicAccess || s3.BlockPublicAccess.BLOCK_ALL,
      
      // CORS configuration
      cors: config.cors || [],
      
      // Lifecycle rules
      lifecycleRules: config.lifecycleRules || [],
      
      // Removal policy
      removalPolicy: config.removalPolicy || cdk.RemovalPolicy.RETAIN,
      autoDeleteObjects: config.autoDeleteObjects ?? false,
    });

    // Add tags if specified
    if (config.tags) {
      Object.entries(config.tags).forEach(([key, value]) => {
        cdk.Tags.of(bucket).add(key, value);
      });
    }

    // Create outputs for each bucket
    new cdk.CfnOutput(this, `${bucketKey}BucketName`, {
      value: bucket.bucketName,
      description: `${config.description} - Bucket Name`,
    });

    new cdk.CfnOutput(this, `${bucketKey}BucketArn`, {
      value: bucket.bucketArn,
      description: `${config.description} - Bucket ARN`,
    });

    new cdk.CfnOutput(this, `${bucketKey}BucketDomainName`, {
      value: bucket.bucketDomainName,
      description: `${config.description} - Bucket Domain Name`,
    });

    return bucket;
  }

  // Helper method to get a specific bucket
  public getBucket(bucketKey: string): s3.Bucket | undefined {
    return this.buckets[bucketKey];
  }

  // Helper method to get the main file repository bucket
  public getFileRepositoryBucket(): s3.Bucket | undefined {
    return this.getBucket('fileRepository');
  }
} 