import * as s3 from 'aws-cdk-lib/aws-s3';
import * as cdk from 'aws-cdk-lib';

export interface S3BucketConfig {
  bucketName: string;
  description: string;
  encryption?: s3.BucketEncryption;
  bucketKeyEnabled?: boolean;
  versioned?: boolean;
  blockPublicAccess?: s3.BlockPublicAccess;
  cors?: s3.CorsRule[];
  lifecycleRules?: s3.LifecycleRule[];
  removalPolicy?: cdk.RemovalPolicy;
  autoDeleteObjects?: boolean;
  tags?: { [key: string]: string };
}

export const S3_BUCKETS_CONFIG: { [key: string]: S3BucketConfig } = {
  fileRepository: {
    bucketName: 'hero-file-repository',
    description: 'Main file repository for Hero application',
    encryption: s3.BucketEncryption.S3_MANAGED,
    bucketKeyEnabled: true,
    versioned: true,
    blockPublicAccess: s3.BlockPublicAccess.BLOCK_ALL,
    cors: [
      {
        allowedHeaders: ['*'],
        allowedMethods: [
          s3.HttpMethods.GET,
          s3.HttpMethods.POST,
          s3.HttpMethods.PUT,
          s3.HttpMethods.DELETE,
          s3.HttpMethods.HEAD,
        ],
        allowedOrigins: ['*'],
        exposedHeaders: ['ETag'],
        maxAge: 3000,
      },
    ],
    removalPolicy: cdk.RemovalPolicy.RETAIN,
    autoDeleteObjects: false,
    tags: {
      Environment: 'production',
      Service: 'hero-core',
      Component: 'file-repository'
    }
  },
  fileRepositoryDev: {
    bucketName: 'hero-file-repository-dev',
    description: 'Dev file repository for Hero application, this will be used in the local docker-compose.yml',
    encryption: s3.BucketEncryption.S3_MANAGED,
    bucketKeyEnabled: true,
    versioned: true,
    blockPublicAccess: s3.BlockPublicAccess.BLOCK_ALL,
    cors: [
      {
        allowedHeaders: ['*'],
        allowedMethods: [
          s3.HttpMethods.GET,
          s3.HttpMethods.POST,
          s3.HttpMethods.PUT,
          s3.HttpMethods.DELETE,
          s3.HttpMethods.HEAD,
        ],
        allowedOrigins: ['*'],
        exposedHeaders: ['ETag'],
        maxAge: 3000,
      },
    ],
    removalPolicy: cdk.RemovalPolicy.RETAIN,
    autoDeleteObjects: false,
    tags: {
      Environment: 'development',
      Service: 'hero-core',
      Component: 'file-repository'
    }
  }
};

// Helper function to get enabled buckets (you can modify this logic)
export function getEnabledBuckets(): { [key: string]: S3BucketConfig } {
  // For now, return all buckets. You can add logic here to enable/disable specific buckets
  // based on environment variables, feature flags, etc.
  const enabledBuckets = {
    fileRepository: S3_BUCKETS_CONFIG.fileRepository,
    fileRepositoryDev: S3_BUCKETS_CONFIG.fileRepositoryDev,
  };
  
  return enabledBuckets;
} 