#!/usr/bin/env ts-node

import { S3Client, HeadBucketCommand, GetBucketEncryptionCommand, GetBucketVersioningCommand, GetBucketCorsCommand, GetBucketTaggingCommand } from '@aws-sdk/client-s3';
import { getEnabledBuckets, S3BucketConfig } from './s3-buckets';
import { execSync } from 'child_process';
import * as fs from 'fs';
import * as path from 'path';

interface BucketStatus {
  exists: boolean;
  needsUpdate: boolean;
  configDifferences: string[];
}

class S3BucketDeployer {
  private s3Client: S3Client;
  private region: string;

  constructor(region: string = 'us-west-2') {
    this.region = region;
    this.s3Client = new S3Client({ region: this.region });
  }

  async checkAllBuckets(deployIfNeeded: boolean = true): Promise<{ deploymentNeeded: string[], updatesNeeded: string[] }> {
    console.log('🔍 Checking S3 bucket configurations...\n');
    
    const bucketsConfig = getEnabledBuckets();
    const deploymentNeeded: string[] = [];
    const updatesNeeded: string[] = [];
    
    for (const [bucketKey, config] of Object.entries(bucketsConfig)) {
      console.log(`📦 Checking bucket: ${config.bucketName}`);
      
      const status = await this.checkBucketStatus(bucketKey, config);
      
      if (!status.exists) {
        console.log(`   ✨ New bucket - ${deployIfNeeded ? 'will be created' : 'would be created'}`);
        deploymentNeeded.push(bucketKey);
      } else if (status.needsUpdate) {
        console.log(`   🔄 Configuration changes detected:`);
        status.configDifferences.forEach(diff => {
          console.log(`      - ${diff}`);
        });
        updatesNeeded.push(bucketKey);
      } else {
        console.log(`   ✅ No changes needed`);
      }
      console.log('');
    }

    // Summary
    console.log('📋 Summary:');
    console.log(`   - New buckets to ${deployIfNeeded ? 'create' : 'be created'}: ${deploymentNeeded.length}`);
    console.log(`   - Existing buckets to ${deployIfNeeded ? 'update' : 'be updated'}: ${updatesNeeded.length}`);
    console.log('');

    if (deploymentNeeded.length > 0 || updatesNeeded.length > 0) {
      if (deployIfNeeded) {
        await this.deployBuckets();
      } else {
        console.log('💡 Run with "deploy" command to apply these changes.');
      }
    } else {
      console.log('🎉 All buckets are up to date!');
    }

    return { deploymentNeeded, updatesNeeded };
  }

  async checkOnly(): Promise<void> {
    await this.checkAllBuckets(false);
  }

  async deployAllBuckets(): Promise<void> {
    await this.checkAllBuckets(true);
  }

  private async checkBucketStatus(bucketKey: string, config: S3BucketConfig): Promise<BucketStatus> {
    try {
      // Check if bucket exists
      await this.s3Client.send(new HeadBucketCommand({ Bucket: config.bucketName }));
      
      // Bucket exists, check configuration
      const differences = await this.compareConfiguration(config);
      
      return {
        exists: true,
        needsUpdate: differences.length > 0,
        configDifferences: differences
      };
    } catch (error: any) {
      if (error.name === 'NotFound' || error.$metadata?.httpStatusCode === 404) {
        return {
          exists: false,
          needsUpdate: false,
          configDifferences: []
        };
      }
      
      console.error(`❌ Error checking bucket ${config.bucketName}:`, error.message);
      return {
        exists: false,
        needsUpdate: false,
        configDifferences: []
      };
    }
  }

  private async compareConfiguration(config: S3BucketConfig): Promise<string[]> {
    const differences: string[] = [];
    
    try {
      // Check encryption
      const encryption = await this.s3Client.send(
        new GetBucketEncryptionCommand({ Bucket: config.bucketName })
      );
      
      // Check versioning
      const versioning = await this.s3Client.send(
        new GetBucketVersioningCommand({ Bucket: config.bucketName })
      );
      
      // Check CORS
      try {
        const cors = await this.s3Client.send(
          new GetBucketCorsCommand({ Bucket: config.bucketName })
        );
        
        // Simple CORS comparison (you can make this more sophisticated)
        if (config.cors && config.cors.length > 0 && !cors.CORSRules) {
          differences.push('CORS configuration missing');
        } else if (!config.cors && cors.CORSRules) {
          differences.push('CORS configuration should be removed');
        }
      } catch (corsError: any) {
        if (config.cors && config.cors.length > 0) {
          differences.push('CORS configuration needs to be added');
        }
      }
      
      // Check versioning
      const versioningEnabled = versioning.Status === 'Enabled';
      if (config.versioned !== versioningEnabled) {
        differences.push(`Versioning should be ${config.versioned ? 'enabled' : 'disabled'}`);
      }
      
      // Check tags
      try {
        const tags = await this.s3Client.send(
          new GetBucketTaggingCommand({ Bucket: config.bucketName })
        );
        
        if (config.tags && Object.keys(config.tags).length > 0 && !tags.TagSet) {
          differences.push('Tags need to be added');
        }
      } catch (tagError: any) {
        if (config.tags && Object.keys(config.tags).length > 0) {
          differences.push('Tags need to be added');
        }
      }
      
    } catch (error: any) {
      console.warn(`⚠️  Could not fully compare configuration for ${config.bucketName}: ${error.message}`);
    }
    
    return differences;
  }

  private async deployBuckets(): Promise<void> {
    console.log('🚀 Deploying S3 buckets...\n');
    
    try {
      // Check if we're in the right directory
      const currentDir = process.cwd();
      if (!currentDir.includes('infra')) {
        console.log('📁 Changing to infra directory...');
        process.chdir(path.join(currentDir, 'infra'));
      }
      
      // Build the project first
      console.log('🔨 Building CDK project...');
      execSync('npm run build', { stdio: 'inherit' });
      
      // Deploy the S3 stack
      console.log('🚀 Deploying S3Stack...');
      execSync('npx cdk deploy S3Stack --require-approval never', { stdio: 'inherit' });
      
      console.log('\n✅ S3 buckets deployed successfully!');
      
      // Show the outputs
      console.log('\n📋 Deployment outputs:');
      execSync('npx cdk ls --long', { stdio: 'inherit' });
      
    } catch (error: any) {
      console.error('❌ Deployment failed:', error.message);
      process.exit(1);
    }
  }

  async listBucketInfo(): Promise<void> {
    console.log('📊 Current bucket configurations:\n');
    
    const bucketsConfig = getEnabledBuckets();
    
    for (const [bucketKey, config] of Object.entries(bucketsConfig)) {
      console.log(`🪣 ${bucketKey} (${config.bucketName}):`);
      console.log(`   Description: ${config.description}`);
      console.log(`   Versioning: ${config.versioned ? 'Enabled' : 'Disabled'}`);
      console.log(`   Encryption: ${config.encryption || 'S3_MANAGED'}`);
      console.log(`   CORS: ${config.cors && config.cors.length > 0 ? 'Configured' : 'Not configured'}`);
      console.log(`   Lifecycle: ${config.lifecycleRules && config.lifecycleRules.length > 0 ? 'Configured' : 'Not configured'}`);
      console.log(`   Tags: ${config.tags ? Object.keys(config.tags).join(', ') : 'None'}`);
      console.log('');
    }
  }
}

// CLI handling
async function main() {
  const args = process.argv.slice(2);
  const command = args[0] || 'deploy';
  
  const deployer = new S3BucketDeployer();
  
  switch (command) {
    case 'check':
      await deployer.checkOnly();
      break;
    case 'info':
      await deployer.listBucketInfo();
      break;
    case 'deploy':
    default:
      await deployer.deployAllBuckets();
      break;
  }
}

// Run if called directly
if (require.main === module) {
  main().catch(console.error);
}

export { S3BucketDeployer }; 