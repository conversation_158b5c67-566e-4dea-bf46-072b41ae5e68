#!/bin/bash
BUCKET=$1

echo "Location:"
aws s3api get-bucket-location --bucket "$BUCKET"

echo -e "\nPolicy:"
aws s3api get-bucket-policy --bucket "$BUCKET"

echo -e "\nACL:"
aws s3api get-bucket-acl --bucket "$BUCKET"

echo -e "\nPublic Access Block:"
aws s3api get-public-access-block --bucket "$BUCKET"

echo -e "\nEncryption:"
aws s3api get-bucket-encryption --bucket "$BUCKET"

echo -e "\nLifecycle:"
aws s3api get-bucket-lifecycle-configuration --bucket "$BUCKET"

echo -e "\nVersioning:"
aws s3api get-bucket-versioning --bucket "$BUCKET"

echo -e "\nCORS:"
aws s3api get-bucket-cors --bucket "$BUCKET"

echo -e "\nTags:"
aws s3api get-bucket-tagging --bucket "$BUCKET"

echo -e "\nWebsite:"
aws s3api get-bucket-website --bucket "$BUCKET"
