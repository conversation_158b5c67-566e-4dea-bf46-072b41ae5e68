#!/bin/bash

# S3 Bucket Deployment Script
# Usage: ./deploy-s3.sh [command]
# Commands:
#   check (default)  - Only check bucket status, don't deploy
#   deploy           - Check and deploy buckets as needed
#   info             - Show current bucket configurations

set -e

# Get the directory of this script
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
INFRA_DIR="$(cd "$SCRIPT_DIR/.." && pwd)"

# Change to infra directory
cd "$INFRA_DIR"

# Get the command (default to 'check')
COMMAND=${1:-check}

echo "🚀 Hero S3 Bucket Deployment Tool"
echo "================================="
echo "Command: $COMMAND"
echo "Working directory: $INFRA_DIR"
echo ""

# Check if required dependencies are installed
if ! command -v npx &> /dev/null; then
    echo "❌ Error: npx is not installed. Please install Node.js and npm."
    exit 1
fi

if ! command -v aws &> /dev/null; then
    echo "❌ Error: AWS CLI is not installed or not in PATH."
    exit 1
fi

# Check if AWS credentials are configured
if ! aws sts get-caller-identity &> /dev/null; then
    echo "❌ Error: AWS credentials not configured. Please run 'aws configure' first."
    exit 1
fi

# Install dependencies if node_modules doesn't exist
if [ ! -d "node_modules" ]; then
    echo "📦 Installing dependencies..."
    npm install
fi

# Add AWS SDK S3 client if not already installed
if ! npm list @aws-sdk/client-s3 &> /dev/null; then
    echo "📦 Installing AWS SDK S3 client..."
    npm install @aws-sdk/client-s3
fi

# Run the TypeScript deployment script
echo "🎯 Running S3 deployment script..."
npx ts-node cloud/s3bucket/deploy-s3-buckets.ts "$COMMAND" 