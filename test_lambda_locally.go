package main

import (
	"context"
	"encoding/json"
	"fmt"
	"log"
	"os"
)

// Mock the CognitoUserEvent structure from the Lambda
type CognitoUserEvent struct {
	Version       string `json:"version"`
	TriggerSource string `json:"triggerSource"`
	Region        string `json:"region"`
	UserName      string `json:"userName"`
	UserPoolID    string `json:"userPoolId"`
	CallerContext struct {
		ClientID string `json:"clientId"`
	} `json:"callerContext"`
	Request struct {
		UserAttributes map[string]string `json:"userAttributes"`
	} `json:"request"`
}

func main() {
	// Set environment variables for testing
	os.Setenv("ORGS_SERVICE_URL", "http://localhost:8080")
	os.Setenv("WORKFLOW_SERVICE_URL", "http://localhost:8081")
	os.Setenv("PERMS_SERVICE_URL", "http://localhost:8082")
	os.Setenv("USER_POOL_ID", "us-west-2_7SyrT2GBd")
	os.Setenv("RESPONDER_ROLE_NAME", "Responder")
	os.Setenv("OKTA_SANDBOX_ISSUER", "http://www.okta.com/exk1wzdmtadxej3up1d8")
	os.Setenv("SANDBOX_ORG_ID", "2")

	// Test cases based on your real users
	testCases := []struct {
		name        string
		event       CognitoUserEvent
		description string
	}{
		{
			name: "andrei_with_preregistration",
			event: CognitoUserEvent{
				Version:       "1",
				TriggerSource: "PostConfirmation_ConfirmSignUp",
				Region:        "us-west-2",
				UserName:      "<EMAIL>",
				UserPoolID:    "us-west-2_7SyrT2GBd",
				CallerContext: struct {
					ClientID string `json:"clientId"`
				}{
					ClientID: "52s9qm0anac1sohgioj2upgp1s",
				},
				Request: struct {
					UserAttributes map[string]string `json:"userAttributes"`
				}{
					UserAttributes: map[string]string{
						"email": "<EMAIL>",
						"identities": `[{"dateCreated":"1752610858878","userId":"<EMAIL>","providerName":"OktaSandbox","providerType":"SAML","issuer":"http://www.okta.com/exk1wzdmtadxej3up1d8","primary":"true"}]`,
						"sub":        "6851f340-60a1-70ea-15e0-dcdd6f4c4284",
					},
				},
			},
			description: "Test <EMAIL> with pre-registration mapping (should get Admin role + Dispatcher asset type)",
		},
		{
			name: "travis_with_preregistration",
			event: CognitoUserEvent{
				Version:       "1",
				TriggerSource: "PostConfirmation_ConfirmSignUp",
				Region:        "us-west-2",
				UserName:      "<EMAIL>",
				UserPoolID:    "us-west-2_7SyrT2GBd",
				CallerContext: struct {
					ClientID string `json:"clientId"`
				}{
					ClientID: "52s9qm0anac1sohgioj2upgp1s",
				},
				Request: struct {
					UserAttributes map[string]string `json:"userAttributes"`
				}{
					UserAttributes: map[string]string{
						"email": "<EMAIL>",
						"identities": `[{"dateCreated":"1752610858878","userId":"<EMAIL>","providerName":"OktaSandbox","providerType":"SAML","issuer":"http://www.okta.com/exk1wzdmtadxej3up1d8","primary":"true"}]`,
						"sub":        "test-travis-sub-123",
					},
				},
			},
			description: "Test <EMAIL> with pre-registration mapping (should get Supervisor role + Supervisor asset type)",
		},
		{
			name: "unknown_user_fallback",
			event: CognitoUserEvent{
				Version:       "1",
				TriggerSource: "PostConfirmation_ConfirmSignUp",
				Region:        "us-west-2",
				UserName:      "<EMAIL>",
				UserPoolID:    "us-west-2_7SyrT2GBd",
				CallerContext: struct {
					ClientID string `json:"clientId"`
				}{
					ClientID: "52s9qm0anac1sohgioj2upgp1s",
				},
				Request: struct {
					UserAttributes map[string]string `json:"userAttributes"`
				}{
					UserAttributes: map[string]string{
						"email": "<EMAIL>",
						"identities": `[{"dateCreated":"1752610858878","userId":"<EMAIL>","providerName":"OktaSandbox","providerType":"SAML","issuer":"http://www.okta.com/exk1wzdmtadxej3up1d8","primary":"true"}]`,
						"sub":        "test-unknown-sub-456",
					},
				},
			},
			description: "Test unknown user without pre-registration (should fallback to default Responder role + Responder asset type)",
		},
	}

	fmt.Println("🧪 Testing postConfirmation Lambda Logic")
	fmt.Println("========================================")

	for _, tc := range testCases {
		fmt.Printf("\n🔍 Test Case: %s\n", tc.name)
		fmt.Printf("Description: %s\n", tc.description)
		fmt.Println("---")

		// Convert event to JSON for logging
		eventJSON, _ := json.MarshalIndent(tc.event, "", "  ")
		fmt.Printf("Event: %s\n", eventJSON)

		// Here you would call your actual Lambda handler
		// For now, we'll just simulate the logic
		fmt.Printf("✅ Event prepared for Lambda testing\n")
		fmt.Printf("Expected behavior: %s\n", tc.description)
	}

	fmt.Println("\n📋 Manual Testing Instructions:")
	fmt.Println("================================")
	fmt.Println("1. First, run the API test script to create pre-registration mappings:")
	fmt.Println("   chmod +x test_pre_registration_api.sh")
	fmt.Println("   ./test_pre_registration_api.sh")
	fmt.Println()
	fmt.Println("2. Start your services locally:")
	fmt.Println("   - Orgs service on :8080")
	fmt.Println("   - Assets service on :8081") 
	fmt.Println("   - Permissions service on :8082")
	fmt.Println()
	fmt.Println("3. Test the Lambda by invoking it with the events above")
	fmt.Println("4. Check the database to verify:")
	fmt.Println("   - Pre-registration mappings are marked as used")
	fmt.Println("   - Assets are created with correct types")
	fmt.Println("   - Users are assigned correct roles")
	fmt.Println()
	fmt.Println("5. Database queries to verify:")
	fmt.Println("   SELECT * FROM pre_registration_user_mappings WHERE used_at IS NOT NULL;")
	fmt.Println("   SELECT * FROM assets WHERE cognito_jwt_sub LIKE 'oktasandbox_%';")
}
