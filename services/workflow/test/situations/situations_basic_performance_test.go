package test

import (
	"context"
	"fmt"
	"net/http"
	"strings"
	"testing"
	"time"

	situations "proto/hero/situations/v2"
	situationsConnect "proto/hero/situations/v2/situationsconnect"

	assets "proto/hero/assets/v2"
	assetsConnect "proto/hero/assets/v2/assetsconnect"

	"connectrpc.com/connect"
)

const (
	performanceTestSituationCount = 100
	performanceTestAssetCount     = 10
	maxLatencyThreshold           = 200 * time.Millisecond
	bulkOperationThreshold        = 2000 * time.Millisecond
)

type PerformanceMetrics struct {
	Operation    string
	Duration     time.Duration
	Success      bool
	ErrorMessage string
	RecordCount  int
}

func TestSituations_BasicPerformance(t *testing.T) {
	if testing.Short() {
		t.Skip("skipping performance tests in short mode")
	}

	t.Logf("🚀 Starting Situations Basic Performance Test with %d situations, %d assets",
		performanceTestSituationCount, performanceTestAssetCount)

	httpClient := http.DefaultClient
	AddAuthHeader(httpClient)

	ctx, cancel := context.WithTimeout(context.Background(), 600*time.Second)
	defer cancel()

	// Initialize clients
	situationsClient := situationsConnect.NewSituationServiceClient(httpClient, ServiceBaseURL)
	assetsClient := assetsConnect.NewAssetRegistryServiceClient(httpClient, ServiceBaseURL)

	var metrics []PerformanceMetrics
	var createdSituationIDs []string
	var createdAssetIDs []string

	// Cleanup function
	defer func() {
		t.Log("🧹 Starting cleanup...")

		// Delete situations first
		for _, situationID := range createdSituationIDs {
			if _, err := situationsClient.DeleteSituation(ctx, connect.NewRequest(&situations.DeleteSituationRequest{Id: situationID})); err != nil {
				t.Logf("cleanup: failed to delete situation %s: %v", situationID, err)
			}
		}

		// Delete assets
		for _, assetID := range createdAssetIDs {
			if _, err := assetsClient.DeleteAsset(ctx, connect.NewRequest(&assets.DeleteAssetRequest{Id: assetID})); err != nil {
				t.Logf("cleanup: failed to delete asset %s: %v", assetID, err)
			}
		}

		t.Log("✅ Cleanup completed")

		// Print performance summary
		printPerformanceSummary(t, metrics)
	}()

	// Step 1: Create supporting assets
	t.Log("📋 Step 1: Creating performance test assets...")
	start := time.Now()
	for i := 0; i < performanceTestAssetCount; i++ {
		assetReq := &assets.CreateAssetRequest{
			Asset: &assets.Asset{
				Name:               fmt.Sprintf("Performance Test Asset %d", i),
				Type:               assets.AssetType_ASSET_TYPE_MEMBER,
				Status:             assets.AssetStatus_ASSET_STATUS_AVAILABLE,
				CognitoJwtSub:      fmt.Sprintf("perf-test-asset-%d", i),
				ContactNo:          fmt.Sprintf("+1555%07d", i),
				ContactEmail:       fmt.Sprintf("<EMAIL>", i),
				Latitude:           37.7749 + float64(i)*0.001,
				Longitude:          -122.4194 + float64(i)*0.001,
				OrgId:              1,
				AdditionalInfoJson: fmt.Sprintf(`{"address": "%d Performance Test Street, Test City, TS 12345"}`, i),
			},
		}

		resp, err := assetsClient.CreateAsset(ctx, connect.NewRequest(assetReq))
		if err != nil {
			t.Fatalf("Failed to create asset %d: %v", i, err)
		}
		createdAssetIDs = append(createdAssetIDs, resp.Msg.Asset.Id)

		// Progress logging
		if (i+1)%5 == 0 {
			t.Logf("   Created %d/%d assets...", i+1, performanceTestAssetCount)
		}
	}
	assetCreateDuration := time.Since(start)
	t.Logf("✅ Created %d assets in %v (avg: %v per asset)", performanceTestAssetCount, assetCreateDuration, assetCreateDuration/time.Duration(performanceTestAssetCount))

	// Step 2: Create performance test situations
	t.Log("📋 Step 2: Creating performance test situations...")
	start = time.Now()

	situationTypes := []situations.SituationType{
		situations.SituationType_SITUATION_TYPE_VANDALISM,
		situations.SituationType_SITUATION_TYPE_INTRUSION,
		situations.SituationType_SITUATION_TYPE_MEDICAL_EMERGENCY,
		situations.SituationType_SITUATION_TYPE_THEFT,
		situations.SituationType_SITUATION_TYPE_SUSPICIOUS_ACTIVITY,
		situations.SituationType_SITUATION_TYPE_FIRE,
		situations.SituationType_SITUATION_TYPE_OTHER,
	}

	situationStatuses := []situations.SituationStatus{
		situations.SituationStatus_SITUATION_STATUS_CREATED,
		situations.SituationStatus_SITUATION_STATUS_TRIAGING,
		situations.SituationStatus_SITUATION_STATUS_DISPATCHING,
		situations.SituationStatus_SITUATION_STATUS_ADDRESSING,
		situations.SituationStatus_SITUATION_STATUS_RESOLVED,
	}

	triggerSources := []situations.TriggerSource{
		situations.TriggerSource_TRIGGER_SOURCE_WEB_PORTAL,
		situations.TriggerSource_TRIGGER_SOURCE_MOBILE_APP,
		situations.TriggerSource_TRIGGER_SOURCE_PHONE_CALL,
		situations.TriggerSource_TRIGGER_SOURCE_CAMERA_EVENT,
		situations.TriggerSource_TRIGGER_SOURCE_SOS,
	}

	for i := 0; i < performanceTestSituationCount; i++ {
		assetIndex := i % len(createdAssetIDs)
		situationType := situationTypes[i%len(situationTypes)]
		situationStatus := situationStatuses[i%len(situationStatuses)]
		triggerSource := triggerSources[i%len(triggerSources)]

		situationReq := &situations.CreateSituationRequest{
			Situation: &situations.Situation{
				Title:              fmt.Sprintf("Performance Test Situation %d", i),
				Description:        fmt.Sprintf("Performance testing situation number %d with comprehensive data", i),
				Status:             situationStatus,
				Type:               situationType,
				Priority:           int32(1 + (i % 5)), //nolint:gosec // Safe conversion for test data
				TriggerSource:      triggerSource,
				ReporterId:         createdAssetIDs[assetIndex],
				ReporterName:       fmt.Sprintf("Performance Test Reporter %d", i),
				ContactNo:          fmt.Sprintf("+1555%07d", i+1000),
				ContactEmail:       fmt.Sprintf("<EMAIL>", i),
				Address:            fmt.Sprintf("%d Situation Test Avenue, Test City, TS %05d", i, 20000+i),
				Latitude:           37.7749 + float64(i)*0.002,
				Longitude:          -122.4194 + float64(i)*0.002,
				DueTime:            time.Now().Add(time.Duration(i+1) * time.Hour).Format(time.RFC3339),
				IncidentTime:       time.Now().Add(-time.Duration(i+1) * time.Minute).Format(time.RFC3339),
				AdditionalInfoJson: fmt.Sprintf(`{"test_situation_number": %d, "batch": "performance_test", "priority_level": %d}`, i, 1+(i%5)),
				Tags:               []string{fmt.Sprintf("performance-test-%d", i), "automated-test", fmt.Sprintf("priority-%d", 1+(i%5))},
			},
		}

		resp, err := situationsClient.CreateSituation(ctx, connect.NewRequest(situationReq))
		if err != nil {
			metrics = append(metrics, PerformanceMetrics{
				Operation:    "CreateSituation",
				Duration:     0,
				Success:      false,
				ErrorMessage: err.Error(),
				RecordCount:  i,
			})
			t.Fatalf("Failed to create situation %d: %v", i, err)
		}
		createdSituationIDs = append(createdSituationIDs, resp.Msg.Situation.Id)

		// Progress logging
		if (i+1)%25 == 0 {
			t.Logf("   Created %d/%d situations...", i+1, performanceTestSituationCount)
		}
	}
	createDuration := time.Since(start)
	metrics = append(metrics, PerformanceMetrics{
		Operation:   "CreateSituation_Bulk",
		Duration:    createDuration,
		Success:     true,
		RecordCount: performanceTestSituationCount,
	})
	t.Logf("✅ Created %d situations in %v (avg: %v per situation)", performanceTestSituationCount, createDuration, createDuration/time.Duration(performanceTestSituationCount))

	// Wait for database indexing
	t.Log("⏳ Waiting for database indexing...")
	time.Sleep(2 * time.Second)

	// Performance Tests
	t.Log("🔥 Running Performance Tests...")

	// Test 1: GetSituation performance
	t.Run("GetSituation_Performance", func(t *testing.T) {
		testSituations := createdSituationIDs[:10]
		var totalDuration time.Duration
		var successCount int

		for _, situationID := range testSituations {
			start := time.Now()
			_, err := situationsClient.GetSituation(ctx, connect.NewRequest(&situations.GetSituationRequest{Id: situationID}))
			duration := time.Since(start)
			totalDuration += duration

			success := err == nil
			if success {
				successCount++
			}

			metrics = append(metrics, PerformanceMetrics{
				Operation: "GetSituation",
				Duration:  duration,
				Success:   success,
				ErrorMessage: func() string {
					if err != nil {
						return err.Error()
					}
					return ""
				}(),
				RecordCount: 1,
			})

			if duration > maxLatencyThreshold {
				t.Errorf("❌ GetSituation latency too high: %v > %v", duration, maxLatencyThreshold)
			}
		}

		avgDuration := totalDuration / time.Duration(len(testSituations))
		t.Logf("✅ GetSituation average latency: %v (success rate: %d/%d)", avgDuration, successCount, len(testSituations))
	})

	// Test 2: ListSituations performance
	t.Run("ListSituations_Performance", func(t *testing.T) {
		start := time.Now()
		resp, err := situationsClient.ListSituations(ctx, connect.NewRequest(&situations.ListSituationsRequest{
			PageSize: 50,
		}))
		duration := time.Since(start)

		success := err == nil && resp != nil
		metrics = append(metrics, PerformanceMetrics{
			Operation: "ListSituations",
			Duration:  duration,
			Success:   success,
			ErrorMessage: func() string {
				if err != nil {
					return err.Error()
				}
				return ""
			}(),
			RecordCount: func() int {
				if resp != nil {
					return len(resp.Msg.Situations)
				}
				return 0
			}(),
		})

		if duration > maxLatencyThreshold {
			t.Errorf("❌ ListSituations latency too high: %v > %v", duration, maxLatencyThreshold)
		} else {
			t.Logf("✅ ListSituations latency: %v (returned %d situations)", duration, len(resp.Msg.Situations))
		}
	})

	// Test 3: UpdateSituation performance
	t.Run("UpdateSituation_Performance", func(t *testing.T) {
		testSituations := createdSituationIDs[:5]
		var totalDuration time.Duration
		var successCount int

		for i, situationID := range testSituations {
			// Get current situation first
			getResp, err := situationsClient.GetSituation(ctx, connect.NewRequest(&situations.GetSituationRequest{Id: situationID}))
			if err != nil {
				t.Errorf("Failed to get situation for update: %v", err)
				continue
			}

			// Update the situation
			updatedSituation := getResp.Msg
			updatedSituation.Title = fmt.Sprintf("Updated Performance Test Situation %d", i)
			updatedSituation.Description = fmt.Sprintf("Updated performance testing situation number %d", i)
			updatedSituation.Priority = int32(3 + (i % 3)) //nolint:gosec // Safe conversion for test data

			start := time.Now()
			_, err = situationsClient.UpdateSituation(ctx, connect.NewRequest(&situations.UpdateSituationRequest{
				Situation: updatedSituation,
			}))
			duration := time.Since(start)
			totalDuration += duration

			success := err == nil
			if success {
				successCount++
			}

			metrics = append(metrics, PerformanceMetrics{
				Operation: "UpdateSituation",
				Duration:  duration,
				Success:   success,
				ErrorMessage: func() string {
					if err != nil {
						return err.Error()
					}
					return ""
				}(),
				RecordCount: 1,
			})

			if duration > maxLatencyThreshold {
				t.Errorf("❌ UpdateSituation latency too high: %v > %v", duration, maxLatencyThreshold)
			}
		}

		avgDuration := totalDuration / time.Duration(len(testSituations))
		t.Logf("✅ UpdateSituation average latency: %v (success rate: %d/%d)", avgDuration, successCount, len(testSituations))
	})

	// Test 4: SearchSituations performance
	t.Run("SearchSituations_Performance", func(t *testing.T) {
		searchTests := []struct {
			name    string
			request *situations.SearchSituationsRequest
		}{
			{
				name: "BasicTextSearch",
				request: &situations.SearchSituationsRequest{
					Query:    "Performance Test",
					PageSize: 25,
					OrderBy:  situations.SearchOrderBy_SEARCH_ORDER_BY_RELEVANCE,
				},
			},
			{
				name: "FilterByStatus",
				request: &situations.SearchSituationsRequest{
					Status:   []situations.SituationStatus{situations.SituationStatus_SITUATION_STATUS_CREATED},
					PageSize: 25,
					OrderBy:  situations.SearchOrderBy_SEARCH_ORDER_BY_CREATE_TIME,
				},
			},
			{
				name: "FilterByType",
				request: &situations.SearchSituationsRequest{
					Type:     []situations.SituationType{situations.SituationType_SITUATION_TYPE_VANDALISM, situations.SituationType_SITUATION_TYPE_THEFT},
					PageSize: 25,
					OrderBy:  situations.SearchOrderBy_SEARCH_ORDER_BY_PRIORITY,
				},
			},
			{
				name: "FilterByTriggerSource",
				request: &situations.SearchSituationsRequest{
					TriggerSource: []situations.TriggerSource{situations.TriggerSource_TRIGGER_SOURCE_WEB_PORTAL},
					PageSize:      25,
					OrderBy:       situations.SearchOrderBy_SEARCH_ORDER_BY_UPDATE_TIME,
				},
			},
			{
				name: "FilterByPriority",
				request: &situations.SearchSituationsRequest{
					Priority: []int32{1, 2, 3},
					PageSize: 25,
					OrderBy:  situations.SearchOrderBy_SEARCH_ORDER_BY_PRIORITY,
				},
			},
			{
				name: "FilterByTags",
				request: &situations.SearchSituationsRequest{
					Tags:     []string{"automated-test"},
					PageSize: 25,
					OrderBy:  situations.SearchOrderBy_SEARCH_ORDER_BY_RELEVANCE,
				},
			},
			{
				name: "GeographicBoundingBox",
				request: &situations.SearchSituationsRequest{
					MinLatitude:  37.7,
					MaxLatitude:  37.8,
					MinLongitude: -122.5,
					MaxLongitude: -122.4,
					PageSize:     25,
					OrderBy:      situations.SearchOrderBy_SEARCH_ORDER_BY_CREATE_TIME,
				},
			},
			{
				name: "ComplexCombinedFilter",
				request: &situations.SearchSituationsRequest{
					Query:         "Performance",
					Status:        []situations.SituationStatus{situations.SituationStatus_SITUATION_STATUS_CREATED, situations.SituationStatus_SITUATION_STATUS_TRIAGING},
					Type:          []situations.SituationType{situations.SituationType_SITUATION_TYPE_VANDALISM},
					TriggerSource: []situations.TriggerSource{situations.TriggerSource_TRIGGER_SOURCE_WEB_PORTAL},
					Tags:          []string{"automated-test"},
					PageSize:      25,
					OrderBy:       situations.SearchOrderBy_SEARCH_ORDER_BY_RELEVANCE,
				},
			},
		}

		for _, test := range searchTests {
			t.Run(test.name, func(t *testing.T) {
				start := time.Now()
				resp, err := situationsClient.SearchSituations(ctx, connect.NewRequest(test.request))
				duration := time.Since(start)

				success := err == nil && resp != nil
				metrics = append(metrics, PerformanceMetrics{
					Operation: fmt.Sprintf("SearchSituations_%s", test.name),
					Duration:  duration,
					Success:   success,
					ErrorMessage: func() string {
						if err != nil {
							return err.Error()
						}
						return ""
					}(),
					RecordCount: func() int {
						if resp != nil {
							return len(resp.Msg.Situations)
						}
						return 0
					}(),
				})

				if duration > maxLatencyThreshold {
					t.Errorf("❌ SearchSituations_%s latency too high: %v > %v", test.name, duration, maxLatencyThreshold)
				} else {
					t.Logf("✅ SearchSituations_%s latency: %v (returned %d situations)", test.name, duration, func() int {
						if resp != nil {
							return len(resp.Msg.Situations)
						}
						return 0
					}())
				}
			})
		}
	})

	// Test 5: ListSituationsForAsset performance
	t.Run("ListSituationsForAsset_Performance", func(t *testing.T) {
		if len(createdAssetIDs) > 0 {
			assetID := createdAssetIDs[0]

			start := time.Now()
			resp, err := situationsClient.ListSituationsForAsset(ctx, connect.NewRequest(&situations.ListSituationsForAssetRequest{
				AssetId:  assetID,
				PageSize: 25,
			}))
			duration := time.Since(start)

			success := err == nil && resp != nil
			metrics = append(metrics, PerformanceMetrics{
				Operation: "ListSituationsForAsset",
				Duration:  duration,
				Success:   success,
				ErrorMessage: func() string {
					if err != nil {
						return err.Error()
					}
					return ""
				}(),
				RecordCount: func() int {
					if resp != nil {
						return len(resp.Msg.Situations)
					}
					return 0
				}(),
			})

			if duration > maxLatencyThreshold {
				t.Errorf("❌ ListSituationsForAsset latency too high: %v > %v", duration, maxLatencyThreshold)
			} else {
				t.Logf("✅ ListSituationsForAsset latency: %v (returned %d situations)", duration, func() int {
					if resp != nil {
						return len(resp.Msg.Situations)
					}
					return 0
				}())
			}
		}
	})

	// Test 6: Situation update operations performance
	t.Run("SituationUpdates_Performance", func(t *testing.T) {
		if len(createdSituationIDs) > 0 {
			situationID := createdSituationIDs[0]

			// Test AddSituationUpdate
			t.Run("AddSituationUpdate", func(t *testing.T) {
				updateEntry := &situations.UpdateEntry{
					Message:      "Performance test update",
					Timestamp:    time.Now().Format(time.RFC3339),
					UpdateSource: situations.UpdateSource_UPDATE_SOURCE_HUMAN_OPERATOR,
					DisplayName:  "Performance Test Operator",
					EventType:    "PerformanceTest",
					UpdaterId:    "perf-test-001",
				}

				start := time.Now()
				_, err := situationsClient.AddSituationUpdate(ctx, connect.NewRequest(&situations.AddSituationUpdateRequest{
					Id:     situationID,
					Update: updateEntry,
				}))
				duration := time.Since(start)

				success := err == nil
				metrics = append(metrics, PerformanceMetrics{
					Operation: "AddSituationUpdate",
					Duration:  duration,
					Success:   success,
					ErrorMessage: func() string {
						if err != nil {
							return err.Error()
						}
						return ""
					}(),
					RecordCount: 1,
				})

				if duration > maxLatencyThreshold {
					t.Errorf("❌ AddSituationUpdate latency too high: %v > %v", duration, maxLatencyThreshold)
				} else {
					t.Logf("✅ AddSituationUpdate latency: %v", duration)
				}
			})

			// Test AddSituationTag
			t.Run("AddSituationTag", func(t *testing.T) {
				start := time.Now()
				_, err := situationsClient.AddSituationTag(ctx, connect.NewRequest(&situations.AddSituationTagRequest{
					Id:  situationID,
					Tag: "performance-test-tag",
				}))
				duration := time.Since(start)

				success := err == nil
				metrics = append(metrics, PerformanceMetrics{
					Operation: "AddSituationTag",
					Duration:  duration,
					Success:   success,
					ErrorMessage: func() string {
						if err != nil {
							return err.Error()
						}
						return ""
					}(),
					RecordCount: 1,
				})

				if duration > maxLatencyThreshold {
					t.Errorf("❌ AddSituationTag latency too high: %v > %v", duration, maxLatencyThreshold)
				} else {
					t.Logf("✅ AddSituationTag latency: %v", duration)
				}
			})

			// Test AddRelatedSituation
			t.Run("AddRelatedSituation", func(t *testing.T) {
				if len(createdSituationIDs) > 1 {
					relatedSituationID := createdSituationIDs[1]

					start := time.Now()
					_, err := situationsClient.AddRelatedSituation(ctx, connect.NewRequest(&situations.AddRelatedSituationRequest{
						Id:                 situationID,
						RelatedSituationId: relatedSituationID,
					}))
					duration := time.Since(start)

					success := err == nil
					metrics = append(metrics, PerformanceMetrics{
						Operation: "AddRelatedSituation",
						Duration:  duration,
						Success:   success,
						ErrorMessage: func() string {
							if err != nil {
								return err.Error()
							}
							return ""
						}(),
						RecordCount: 1,
					})

					if duration > maxLatencyThreshold {
						t.Errorf("❌ AddRelatedSituation latency too high: %v > %v", duration, maxLatencyThreshold)
					} else {
						t.Logf("✅ AddRelatedSituation latency: %v", duration)
					}
				}
			})

			// Test AddMediaAttachmentForSituation
			t.Run("AddMediaAttachmentForSituation", func(t *testing.T) {
				mediaAttachment := &situations.MediaAttachment{
					AttachmentId: "perf-test-attachment-001",
					Url:          "https://example.com/performance-test-media.jpg",
					ContentType:  "image/jpeg",
				}

				start := time.Now()
				_, err := situationsClient.AddMediaAttachmentForSituation(ctx, connect.NewRequest(&situations.AddMediaAttachmentForSituationRequest{
					Id:              situationID,
					MediaAttachment: mediaAttachment,
				}))
				duration := time.Since(start)

				success := err == nil
				metrics = append(metrics, PerformanceMetrics{
					Operation: "AddMediaAttachmentForSituation",
					Duration:  duration,
					Success:   success,
					ErrorMessage: func() string {
						if err != nil {
							return err.Error()
						}
						return ""
					}(),
					RecordCount: 1,
				})

				if duration > maxLatencyThreshold {
					t.Errorf("❌ AddMediaAttachmentForSituation latency too high: %v > %v", duration, maxLatencyThreshold)
				} else {
					t.Logf("✅ AddMediaAttachmentForSituation latency: %v", duration)
				}
			})

			// Test AddAdditionalInfo
			t.Run("AddAdditionalInfo", func(t *testing.T) {
				start := time.Now()
				_, err := situationsClient.AddAdditionalInfo(ctx, connect.NewRequest(&situations.AddAdditionalInfoRequest{
					Id:                 situationID,
					AdditionalInfoJson: `{"performance_test": "additional_info", "test_timestamp": "` + time.Now().Format(time.RFC3339) + `"}`,
				}))
				duration := time.Since(start)

				success := err == nil
				metrics = append(metrics, PerformanceMetrics{
					Operation: "AddAdditionalInfo",
					Duration:  duration,
					Success:   success,
					ErrorMessage: func() string {
						if err != nil {
							return err.Error()
						}
						return ""
					}(),
					RecordCount: 1,
				})

				if duration > maxLatencyThreshold {
					t.Errorf("❌ AddAdditionalInfo latency too high: %v > %v", duration, maxLatencyThreshold)
				} else {
					t.Logf("✅ AddAdditionalInfo latency: %v", duration)
				}
			})
		}
	})

	// Test 7: Filtered listing performance
	t.Run("FilteredListing_Performance", func(t *testing.T) {
		filterTests := []struct {
			name    string
			request *situations.ListSituationsRequest
		}{
			{
				name: "ListByStatus_CREATED",
				request: &situations.ListSituationsRequest{
					PageSize: 50,
					Status:   situations.SituationStatus_SITUATION_STATUS_CREATED,
				},
			},
			{
				name: "ListByType_VANDALISM",
				request: &situations.ListSituationsRequest{
					PageSize: 50,
					Type:     situations.SituationType_SITUATION_TYPE_VANDALISM,
				},
			},
			{
				name: "ListByTriggerSource_WEB_PORTAL",
				request: &situations.ListSituationsRequest{
					PageSize:      50,
					TriggerSource: situations.TriggerSource_TRIGGER_SOURCE_WEB_PORTAL,
				},
			},
			{
				name: "ListWithOrderBy_CREATE_TIME",
				request: &situations.ListSituationsRequest{
					PageSize: 50,
					OrderBy:  "create_time desc",
				},
			},
		}

		for _, test := range filterTests {
			t.Run(test.name, func(t *testing.T) {
				start := time.Now()
				resp, err := situationsClient.ListSituations(ctx, connect.NewRequest(test.request))
				duration := time.Since(start)

				success := err == nil && resp != nil
				metrics = append(metrics, PerformanceMetrics{
					Operation: fmt.Sprintf("ListSituations_%s", test.name),
					Duration:  duration,
					Success:   success,
					ErrorMessage: func() string {
						if err != nil {
							return err.Error()
						}
						return ""
					}(),
					RecordCount: func() int {
						if resp != nil {
							return len(resp.Msg.Situations)
						}
						return 0
					}(),
				})

				if duration > maxLatencyThreshold {
					t.Errorf("❌ %s latency too high: %v > %v", test.name, duration, maxLatencyThreshold)
				} else {
					t.Logf("✅ %s latency: %v (returned %d situations)", test.name, duration, func() int {
						if resp != nil {
							return len(resp.Msg.Situations)
						}
						return 0
					}())
				}
			})
		}
	})

	t.Log("🎯 Situations Basic Performance Test Completed")
}

func printPerformanceSummary(t *testing.T, metrics []PerformanceMetrics) {
	t.Log("\n" + strings.Repeat("=", 80))
	t.Log("📊 PERFORMANCE SUMMARY")
	t.Log(strings.Repeat("=", 80))

	operationStats := make(map[string][]time.Duration)
	var totalOperations, successfulOperations int

	for _, metric := range metrics {
		if metric.Success {
			operationStats[metric.Operation] = append(operationStats[metric.Operation], metric.Duration)
			successfulOperations++
		}
		totalOperations++
	}

	t.Logf("Overall Success Rate: %d/%d (%.1f%%)", successfulOperations, totalOperations, float64(successfulOperations)/float64(totalOperations)*100)
	t.Log("")

	for operation, durations := range operationStats {
		if len(durations) == 0 {
			continue
		}

		var total time.Duration
		min := durations[0]
		max := durations[0]

		for _, d := range durations {
			total += d
			if d < min {
				min = d
			}
			if d > max {
				max = d
			}
		}

		avg := total / time.Duration(len(durations))
		status := "✅"
		if avg > maxLatencyThreshold {
			status = "❌"
		}

		t.Logf("%s %-35s | Count: %3d | Avg: %8v | Min: %8v | Max: %8v",
			status, operation, len(durations), avg, min, max)
	}

	t.Log(strings.Repeat("=", 80))
	t.Logf("Latency Threshold: %v", maxLatencyThreshold)
	t.Log(strings.Repeat("=", 80))
}
