package test

import (
	"context"
	"fmt"
	"net/http"
	"strings"
	"testing"
	"time"

	entities "proto/hero/entity/v1"
	entitiesConnect "proto/hero/entity/v1/entityconnect"

	"connectrpc.com/connect"
	"google.golang.org/protobuf/types/known/structpb"
)

const (
	performanceTestEntityCount = 100
	performanceTestSchemaCount = 10
	maxLatencyThreshold        = 200 * time.Millisecond
	bulkOperationThreshold     = 2000 * time.Millisecond
)

type PerformanceMetrics struct {
	Operation    string
	Duration     time.Duration
	Success      bool
	ErrorMessage string
	RecordCount  int
}

func TestEntity_BasicPerformance(t *testing.T) {
	if testing.Short() {
		t.Skip("skipping performance tests in short mode")
	}

	t.Logf("🚀 Starting Entity Basic Performance Test with %d entities and %d schemas", performanceTestEntityCount, performanceTestSchemaCount)

	httpClient := http.DefaultClient
	AddAuthHeader(httpClient)

	ctx, cancel := context.WithTimeout(context.Background(), 600*time.Second)
	defer cancel()

	// Initialize client
	entitiesClient := entitiesConnect.NewEntityServiceClient(httpClient, ServiceBaseURL)

	var metrics []PerformanceMetrics
	var createdEntityIDs []string
	var createdSchemaIDs []string

	// Cleanup function
	defer func() {
		t.Log("🧹 Starting cleanup...")

		// Delete entities first (they depend on schemas)
		for _, entityID := range createdEntityIDs {
			if _, err := entitiesClient.DeleteAllVersionsOfEntity(ctx, connect.NewRequest(&entities.DeleteAllVersionsOfEntityRequest{Id: entityID})); err != nil {
				t.Logf("cleanup: failed to delete entity %s: %v", entityID, err)
			}
		}

		// Delete schemas
		for _, schemaID := range createdSchemaIDs {
			if _, err := entitiesClient.DeleteAllVersionsOfEntitySchema(ctx, connect.NewRequest(&entities.DeleteAllVersionsOfEntitySchemaRequest{Id: schemaID})); err != nil {
				t.Logf("cleanup: failed to delete schema %s: %v", schemaID, err)
			}
		}

		t.Log("✅ Cleanup completed")

		// Print performance summary
		printPerformanceSummary(t, metrics)
	}()

	// Step 1: Create entity schemas for testing
	t.Log("📋 Step 1: Creating entity schemas...")
	start := time.Now()

	schemaTypes := []entities.EntityType{
		entities.EntityType_ENTITY_TYPE_PERSON,
		entities.EntityType_ENTITY_TYPE_VEHICLE,
		entities.EntityType_ENTITY_TYPE_PROPERTY,
		entities.EntityType_ENTITY_TYPE_ORGANIZATION,
		entities.EntityType_ENTITY_TYPE_OTHER,
	}

	for i := 0; i < performanceTestSchemaCount; i++ {
		schemaType := schemaTypes[i%len(schemaTypes)]

		var schemaDefinition *structpb.Struct
		var err error

		switch schemaType {
		case entities.EntityType_ENTITY_TYPE_PERSON:
			schemaDefinition, err = structpb.NewStruct(map[string]interface{}{
				"type": "object",
				"properties": map[string]interface{}{
					"name":  map[string]interface{}{"type": "string"},
					"age":   map[string]interface{}{"type": "integer", "minimum": 0},
					"email": map[string]interface{}{"type": "string", "format": "email"},
					"phone": map[string]interface{}{"type": "string"},
				},
				"required": []interface{}{"name"},
			})
		case entities.EntityType_ENTITY_TYPE_VEHICLE:
			schemaDefinition, err = structpb.NewStruct(map[string]interface{}{
				"type": "object",
				"properties": map[string]interface{}{
					"make":          map[string]interface{}{"type": "string"},
					"model":         map[string]interface{}{"type": "string"},
					"year":          map[string]interface{}{"type": "integer", "minimum": 1900},
					"license_plate": map[string]interface{}{"type": "string"},
				},
				"required": []interface{}{"make", "model"},
			})
		case entities.EntityType_ENTITY_TYPE_PROPERTY:
			schemaDefinition, err = structpb.NewStruct(map[string]interface{}{
				"type": "object",
				"properties": map[string]interface{}{
					"address": map[string]interface{}{"type": "string"},
					"size":    map[string]interface{}{"type": "number", "minimum": 0},
					"type":    map[string]interface{}{"type": "string"},
				},
				"required": []interface{}{"address"},
			})
		case entities.EntityType_ENTITY_TYPE_ORGANIZATION:
			schemaDefinition, err = structpb.NewStruct(map[string]interface{}{
				"type": "object",
				"properties": map[string]interface{}{
					"name":      map[string]interface{}{"type": "string"},
					"industry":  map[string]interface{}{"type": "string"},
					"employees": map[string]interface{}{"type": "integer", "minimum": 0},
				},
				"required": []interface{}{"name"},
			})
		default:
			schemaDefinition, err = structpb.NewStruct(map[string]interface{}{
				"type": "object",
				"properties": map[string]interface{}{
					"name":        map[string]interface{}{"type": "string"},
					"description": map[string]interface{}{"type": "string"},
				},
				"required": []interface{}{"name"},
			})
		}

		if err != nil {
			t.Fatalf("Failed to create schema definition %d: %v", i, err)
		}

		schemaReq := &entities.CreateEntitySchemaRequest{
			Schema: &entities.EntitySchema{
				OrgId:            1,
				Name:             fmt.Sprintf("Performance Test Schema %d", i),
				Description:      fmt.Sprintf("Performance testing schema number %d", i),
				SchemaDefinition: schemaDefinition,
				EntityType:       schemaType,
				Status:           entities.RecordStatus_RECORD_STATUS_ACTIVE,
				Tags:             []string{"performance", "test", fmt.Sprintf("schema-%d", i)},
			},
		}

		resp, err := entitiesClient.CreateEntitySchema(ctx, connect.NewRequest(schemaReq))
		if err != nil {
			metrics = append(metrics, PerformanceMetrics{
				Operation:    "CreateEntitySchema",
				Duration:     0,
				Success:      false,
				ErrorMessage: err.Error(),
				RecordCount:  i,
			})
			t.Fatalf("Failed to create schema %d: %v", i, err)
		}
		createdSchemaIDs = append(createdSchemaIDs, resp.Msg.Schema.Id)

		// Progress logging
		if (i+1)%5 == 0 {
			t.Logf("   Created %d/%d schemas...", i+1, performanceTestSchemaCount)
		}
	}
	schemaCreateDuration := time.Since(start)
	metrics = append(metrics, PerformanceMetrics{
		Operation:   "CreateEntitySchema_Bulk",
		Duration:    schemaCreateDuration,
		Success:     true,
		RecordCount: performanceTestSchemaCount,
	})
	t.Logf("✅ Created %d schemas in %v (avg: %v per schema)", performanceTestSchemaCount, schemaCreateDuration, schemaCreateDuration/time.Duration(performanceTestSchemaCount))

	// Step 2: Create performance test entities
	t.Log("📋 Step 2: Creating performance test entities...")
	start = time.Now()
	for i := 0; i < performanceTestEntityCount; i++ {
		schemaIndex := i % len(createdSchemaIDs)
		schemaID := createdSchemaIDs[schemaIndex]
		schemaType := schemaTypes[schemaIndex%len(schemaTypes)]

		var entityData *structpb.Struct
		var err error

		switch schemaType {
		case entities.EntityType_ENTITY_TYPE_PERSON:
			entityData, err = structpb.NewStruct(map[string]interface{}{
				"name":  fmt.Sprintf("Performance Test Person %d", i),
				"age":   25 + (i % 50),
				"email": fmt.Sprintf("<EMAIL>", i),
				"phone": fmt.Sprintf("+1555%07d", i),
			})
		case entities.EntityType_ENTITY_TYPE_VEHICLE:
			makes := []string{"Toyota", "Honda", "Ford", "BMW", "Mercedes"}
			models := []string{"Sedan", "SUV", "Truck", "Coupe", "Hatchback"}
			entityData, err = structpb.NewStruct(map[string]interface{}{
				"make":          makes[i%len(makes)],
				"model":         models[i%len(models)],
				"year":          2020 + (i % 5),
				"license_plate": fmt.Sprintf("TEST%03d", i),
			})
		case entities.EntityType_ENTITY_TYPE_PROPERTY:
			propertyTypes := []string{"Residential", "Commercial", "Industrial", "Retail"}
			entityData, err = structpb.NewStruct(map[string]interface{}{
				"address": fmt.Sprintf("%d Performance Test Street, Test City, TS 12345", i),
				"size":    1000.0 + float64(i*10),
				"type":    propertyTypes[i%len(propertyTypes)],
			})
		case entities.EntityType_ENTITY_TYPE_ORGANIZATION:
			industries := []string{"Technology", "Healthcare", "Finance", "Education", "Manufacturing"}
			entityData, err = structpb.NewStruct(map[string]interface{}{
				"name":      fmt.Sprintf("Performance Test Organization %d", i),
				"industry":  industries[i%len(industries)],
				"employees": 10 + (i % 1000),
			})
		default:
			entityData, err = structpb.NewStruct(map[string]interface{}{
				"name":        fmt.Sprintf("Performance Test Entity %d", i),
				"description": fmt.Sprintf("Performance testing entity number %d", i),
			})
		}

		if err != nil {
			t.Fatalf("Failed to create entity data %d: %v", i, err)
		}

		entityReq := &entities.CreateEntityRequest{
			Entity: &entities.Entity{
				OrgId:         1,
				SchemaId:      schemaID,
				SchemaVersion: 1,
				Data:          entityData,
				EntityType:    schemaType,
				Status:        entities.RecordStatus_RECORD_STATUS_ACTIVE,
				Tags:          []string{"performance", "test", fmt.Sprintf("entity-%d", i)},
				References: []*entities.Reference{
					{
						Id:           fmt.Sprintf("ref-%d", i),
						Type:         "test_reference",
						Version:      1,
						DisplayName:  fmt.Sprintf("Test Reference %d", i),
						RelationType: "test_relation",
					},
				},
			},
		}

		resp, err := entitiesClient.CreateEntity(ctx, connect.NewRequest(entityReq))
		if err != nil {
			metrics = append(metrics, PerformanceMetrics{
				Operation:    "CreateEntity",
				Duration:     0,
				Success:      false,
				ErrorMessage: err.Error(),
				RecordCount:  i,
			})
			t.Fatalf("Failed to create entity %d: %v", i, err)
		}
		createdEntityIDs = append(createdEntityIDs, resp.Msg.Entity.Id)

		// Progress logging
		if (i+1)%25 == 0 {
			t.Logf("   Created %d/%d entities...", i+1, performanceTestEntityCount)
		}
	}
	createDuration := time.Since(start)
	metrics = append(metrics, PerformanceMetrics{
		Operation:   "CreateEntity_Bulk",
		Duration:    createDuration,
		Success:     true,
		RecordCount: performanceTestEntityCount,
	})
	t.Logf("✅ Created %d entities in %v (avg: %v per entity)", performanceTestEntityCount, createDuration, createDuration/time.Duration(performanceTestEntityCount))

	// Wait for database indexing
	t.Log("⏳ Waiting for database indexing...")
	time.Sleep(2 * time.Second)

	// Performance Tests
	t.Log("🔥 Running Performance Tests...")

	// Test 1: GetLatestEntity performance
	t.Run("GetLatestEntity_Performance", func(t *testing.T) {
		testEntities := createdEntityIDs[:10]
		var totalDuration time.Duration
		var successCount int

		for _, entityID := range testEntities {
			start := time.Now()
			_, err := entitiesClient.GetLatestEntity(ctx, connect.NewRequest(&entities.GetLatestEntityRequest{Id: entityID}))
			duration := time.Since(start)
			totalDuration += duration

			success := err == nil
			if success {
				successCount++
			}

			metrics = append(metrics, PerformanceMetrics{
				Operation: "GetLatestEntity",
				Duration:  duration,
				Success:   success,
				ErrorMessage: func() string {
					if err != nil {
						return err.Error()
					}
					return ""
				}(),
				RecordCount: 1,
			})

			if duration > maxLatencyThreshold {
				t.Errorf("❌ GetLatestEntity latency too high: %v > %v", duration, maxLatencyThreshold)
			}
		}

		avgDuration := totalDuration / time.Duration(len(testEntities))
		t.Logf("✅ GetLatestEntity average latency: %v (success rate: %d/%d)", avgDuration, successCount, len(testEntities))
	})

	// Test 2: ListLatestEntities performance
	t.Run("ListLatestEntities_Performance", func(t *testing.T) {
		start := time.Now()
		resp, err := entitiesClient.ListLatestEntities(ctx, connect.NewRequest(&entities.ListLatestEntitiesRequest{
			PageSize: 50,
		}))
		duration := time.Since(start)

		success := err == nil && resp != nil
		metrics = append(metrics, PerformanceMetrics{
			Operation: "ListLatestEntities",
			Duration:  duration,
			Success:   success,
			ErrorMessage: func() string {
				if err != nil {
					return err.Error()
				}
				return ""
			}(),
			RecordCount: func() int {
				if resp != nil {
					return len(resp.Msg.Entities)
				}
				return 0
			}(),
		})

		if duration > maxLatencyThreshold {
			t.Errorf("❌ ListLatestEntities latency too high: %v > %v", duration, maxLatencyThreshold)
		} else {
			t.Logf("✅ ListLatestEntities latency: %v (returned %d entities)", duration, len(resp.Msg.Entities))
		}
	})

	// Test 3: BatchGetLatestEntities performance
	t.Run("BatchGetLatestEntities_Performance", func(t *testing.T) {
		testEntities := createdEntityIDs[:10]
		start := time.Now()
		resp, err := entitiesClient.BatchGetLatestEntities(ctx, connect.NewRequest(&entities.BatchGetLatestEntitiesRequest{
			Ids: testEntities,
		}))
		duration := time.Since(start)

		success := err == nil && resp != nil
		metrics = append(metrics, PerformanceMetrics{
			Operation: "BatchGetLatestEntities",
			Duration:  duration,
			Success:   success,
			ErrorMessage: func() string {
				if err != nil {
					return err.Error()
				}
				return ""
			}(),
			RecordCount: func() int {
				if resp != nil {
					return len(resp.Msg.Entities)
				}
				return 0
			}(),
		})

		if duration > maxLatencyThreshold {
			t.Errorf("❌ BatchGetLatestEntities latency too high: %v > %v", duration, maxLatencyThreshold)
		} else {
			t.Logf("✅ BatchGetLatestEntities latency: %v (returned %d entities)", duration, len(resp.Msg.Entities))
		}
	})

	// Test 4: UpdateEntity performance
	t.Run("UpdateEntity_Performance", func(t *testing.T) {
		testEntities := createdEntityIDs[:5]
		var totalDuration time.Duration
		var successCount int

		for i, entityID := range testEntities {
			// Get current entity first
			getResp, err := entitiesClient.GetLatestEntity(ctx, connect.NewRequest(&entities.GetLatestEntityRequest{Id: entityID}))
			if err != nil {
				t.Errorf("Failed to get entity for update: %v", err)
				continue
			}

			// Update the entity data
			updatedData, err := structpb.NewStruct(map[string]interface{}{
				"name":        fmt.Sprintf("Updated Performance Test Entity %d", i),
				"description": fmt.Sprintf("Updated performance testing entity number %d", i),
			})
			if err != nil {
				t.Errorf("Failed to create updated entity data: %v", err)
				continue
			}

			updatedEntity := getResp.Msg
			updatedEntity.Data = updatedData

			start := time.Now()
			_, err = entitiesClient.UpdateEntity(ctx, connect.NewRequest(&entities.UpdateEntityRequest{
				Entity: updatedEntity,
			}))
			duration := time.Since(start)
			totalDuration += duration

			success := err == nil
			if success {
				successCount++
			}

			metrics = append(metrics, PerformanceMetrics{
				Operation: "UpdateEntity",
				Duration:  duration,
				Success:   success,
				ErrorMessage: func() string {
					if err != nil {
						return err.Error()
					}
					return ""
				}(),
				RecordCount: 1,
			})

			if duration > maxLatencyThreshold {
				t.Errorf("❌ UpdateEntity latency too high: %v > %v", duration, maxLatencyThreshold)
			}
		}

		avgDuration := totalDuration / time.Duration(len(testEntities))
		t.Logf("✅ UpdateEntity average latency: %v (success rate: %d/%d)", avgDuration, successCount, len(testEntities))
	})

	// Test 5: SearchEntities performance
	t.Run("SearchEntities_Performance", func(t *testing.T) {
		searchTests := []struct {
			name    string
			request *entities.SearchEntitiesRequest
		}{
			{
				name: "SearchByEntityType",
				request: &entities.SearchEntitiesRequest{
					EntityTypes: []entities.EntityType{entities.EntityType_ENTITY_TYPE_PERSON},
					PageSize:    50,
				},
			},
			{
				name: "SearchByStatus",
				request: &entities.SearchEntitiesRequest{
					Status:   []entities.RecordStatus{entities.RecordStatus_RECORD_STATUS_ACTIVE},
					PageSize: 50,
				},
			},
			{
				name: "SearchByQuery",
				request: &entities.SearchEntitiesRequest{
					Query:    "Performance Test",
					PageSize: 50,
				},
			},
			{
				name: "SearchByTags",
				request: &entities.SearchEntitiesRequest{
					Tags:     []string{"performance", "test"},
					PageSize: 50,
				},
			},
		}

		for _, test := range searchTests {
			t.Run(test.name, func(t *testing.T) {
				start := time.Now()
				resp, err := entitiesClient.SearchEntities(ctx, connect.NewRequest(test.request))
				duration := time.Since(start)

				success := err == nil && resp != nil
				metrics = append(metrics, PerformanceMetrics{
					Operation: fmt.Sprintf("SearchEntities_%s", test.name),
					Duration:  duration,
					Success:   success,
					ErrorMessage: func() string {
						if err != nil {
							return err.Error()
						}
						return ""
					}(),
					RecordCount: func() int {
						if resp != nil {
							return len(resp.Msg.Entities)
						}
						return 0
					}(),
				})

				if duration > maxLatencyThreshold {
					t.Errorf("❌ %s latency too high: %v > %v", test.name, duration, maxLatencyThreshold)
				} else {
					t.Logf("✅ %s latency: %v (returned %d entities)", test.name, duration, func() int {
						if resp != nil {
							return len(resp.Msg.Entities)
						}
						return 0
					}())
				}
			})
		}
	})

	// Test 6: GetLatestEntitySchema performance
	t.Run("GetLatestEntitySchema_Performance", func(t *testing.T) {
		testSchemas := createdSchemaIDs[:5]
		var totalDuration time.Duration
		var successCount int

		for _, schemaID := range testSchemas {
			start := time.Now()
			_, err := entitiesClient.GetLatestEntitySchema(ctx, connect.NewRequest(&entities.GetLatestEntitySchemaRequest{Id: schemaID}))
			duration := time.Since(start)
			totalDuration += duration

			success := err == nil
			if success {
				successCount++
			}

			metrics = append(metrics, PerformanceMetrics{
				Operation: "GetLatestEntitySchema",
				Duration:  duration,
				Success:   success,
				ErrorMessage: func() string {
					if err != nil {
						return err.Error()
					}
					return ""
				}(),
				RecordCount: 1,
			})

			if duration > maxLatencyThreshold {
				t.Errorf("❌ GetLatestEntitySchema latency too high: %v > %v", duration, maxLatencyThreshold)
			}
		}

		avgDuration := totalDuration / time.Duration(len(testSchemas))
		t.Logf("✅ GetLatestEntitySchema average latency: %v (success rate: %d/%d)", avgDuration, successCount, len(testSchemas))
	})

	// Test 7: ListLatestEntitySchemas performance
	t.Run("ListLatestEntitySchemas_Performance", func(t *testing.T) {
		start := time.Now()
		resp, err := entitiesClient.ListLatestEntitySchemas(ctx, connect.NewRequest(&entities.ListLatestEntitySchemasRequest{
			PageSize: 50,
		}))
		duration := time.Since(start)

		success := err == nil && resp != nil
		metrics = append(metrics, PerformanceMetrics{
			Operation: "ListLatestEntitySchemas",
			Duration:  duration,
			Success:   success,
			ErrorMessage: func() string {
				if err != nil {
					return err.Error()
				}
				return ""
			}(),
			RecordCount: func() int {
				if resp != nil {
					return len(resp.Msg.Schemas)
				}
				return 0
			}(),
		})

		if duration > maxLatencyThreshold {
			t.Errorf("❌ ListLatestEntitySchemas latency too high: %v > %v", duration, maxLatencyThreshold)
		} else {
			t.Logf("✅ ListLatestEntitySchemas latency: %v (returned %d schemas)", duration, len(resp.Msg.Schemas))
		}
	})

	// Test 8: BulkCreateEntities performance
	t.Run("BulkCreateEntities_Performance", func(t *testing.T) {
		bulkEntityData := make([]*entities.Entity, 5)
		for i := 0; i < 5; i++ {
			entityData, err := structpb.NewStruct(map[string]interface{}{
				"name":        fmt.Sprintf("Bulk Performance Test Entity %d", i),
				"description": fmt.Sprintf("Bulk performance testing entity number %d", i),
			})
			if err != nil {
				t.Errorf("Failed to create bulk entity data: %v", err)
				return
			}

			bulkEntityData[i] = &entities.Entity{
				OrgId:         1,
				SchemaId:      createdSchemaIDs[0],
				SchemaVersion: 1,
				Data:          entityData,
				EntityType:    entities.EntityType_ENTITY_TYPE_OTHER,
				Status:        entities.RecordStatus_RECORD_STATUS_ACTIVE,
				Tags:          []string{"bulk", "performance", "test"},
			}
		}

		start := time.Now()
		resp, err := entitiesClient.BulkCreateEntities(ctx, connect.NewRequest(&entities.BulkCreateEntitiesRequest{
			Entities: bulkEntityData,
		}))
		duration := time.Since(start)

		success := err == nil && resp != nil
		recordCount := func() int {
			if resp != nil {
				return len(resp.Msg.Entities)
			}
			return 0
		}()

		// Add created entities to cleanup list
		if success {
			for _, entity := range resp.Msg.Entities {
				createdEntityIDs = append(createdEntityIDs, entity.Id)
			}
		}

		metrics = append(metrics, PerformanceMetrics{
			Operation: "BulkCreateEntities",
			Duration:  duration,
			Success:   success,
			ErrorMessage: func() string {
				if err != nil {
					return err.Error()
				}
				return ""
			}(),
			RecordCount: recordCount,
		})

		if duration > bulkOperationThreshold {
			t.Errorf("❌ BulkCreateEntities latency too high: %v > %v", duration, bulkOperationThreshold)
		} else {
			t.Logf("✅ BulkCreateEntities latency: %v (created %d entities)", duration, recordCount)
		}
	})

	// Test 9: GetEntityByVersion performance
	t.Run("GetEntityByVersion_Performance", func(t *testing.T) {
		testEntities := createdEntityIDs[:5]
		var totalDuration time.Duration
		var successCount int

		for _, entityID := range testEntities {
			start := time.Now()
			_, err := entitiesClient.GetEntityByVersion(ctx, connect.NewRequest(&entities.GetEntityByVersionRequest{
				Id:      entityID,
				Version: 1,
			}))
			duration := time.Since(start)
			totalDuration += duration

			success := err == nil
			if success {
				successCount++
			}

			metrics = append(metrics, PerformanceMetrics{
				Operation: "GetEntityByVersion",
				Duration:  duration,
				Success:   success,
				ErrorMessage: func() string {
					if err != nil {
						return err.Error()
					}
					return ""
				}(),
				RecordCount: 1,
			})

			if duration > maxLatencyThreshold {
				t.Errorf("❌ GetEntityByVersion latency too high: %v > %v", duration, maxLatencyThreshold)
			}
		}

		avgDuration := totalDuration / time.Duration(len(testEntities))
		t.Logf("✅ GetEntityByVersion average latency: %v (success rate: %d/%d)", avgDuration, successCount, len(testEntities))
	})

	// Test 10: ListAllVersionsOfEntity performance
	t.Run("ListAllVersionsOfEntity_Performance", func(t *testing.T) {
		testEntities := createdEntityIDs[:5]
		var totalDuration time.Duration
		var successCount int

		for _, entityID := range testEntities {
			start := time.Now()
			resp, err := entitiesClient.ListAllVersionsOfEntity(ctx, connect.NewRequest(&entities.ListAllVersionsOfEntityRequest{
				EntityId: entityID,
			}))
			duration := time.Since(start)
			totalDuration += duration

			success := err == nil
			if success {
				successCount++
			}

			metrics = append(metrics, PerformanceMetrics{
				Operation: "ListAllVersionsOfEntity",
				Duration:  duration,
				Success:   success,
				ErrorMessage: func() string {
					if err != nil {
						return err.Error()
					}
					return ""
				}(),
				RecordCount: func() int {
					if resp != nil {
						return len(resp.Msg.Versions)
					}
					return 0
				}(),
			})

			if duration > maxLatencyThreshold {
				t.Errorf("❌ ListAllVersionsOfEntity latency too high: %v > %v", duration, maxLatencyThreshold)
			}
		}

		avgDuration := totalDuration / time.Duration(len(testEntities))
		t.Logf("✅ ListAllVersionsOfEntity average latency: %v (success rate: %d/%d)", avgDuration, successCount, len(testEntities))
	})

	// Test 11: CheckEntityPermissions performance
	t.Run("CheckEntityPermissions_Performance", func(t *testing.T) {
		testEntities := createdEntityIDs[:5]
		var totalDuration time.Duration
		var successCount int

		for _, entityID := range testEntities {
			start := time.Now()
			_, err := entitiesClient.CheckEntityPermissions(ctx, connect.NewRequest(&entities.CheckEntityPermissionsRequest{
				EntityId: entityID,
				UserId:   "test-user-123",
				Action:   "read",
			}))
			duration := time.Since(start)
			totalDuration += duration

			success := err == nil
			if success {
				successCount++
			}

			metrics = append(metrics, PerformanceMetrics{
				Operation: "CheckEntityPermissions",
				Duration:  duration,
				Success:   success,
				ErrorMessage: func() string {
					if err != nil {
						return err.Error()
					}
					return ""
				}(),
				RecordCount: 1,
			})

			if duration > maxLatencyThreshold {
				t.Errorf("❌ CheckEntityPermissions latency too high: %v > %v", duration, maxLatencyThreshold)
			}
		}

		avgDuration := totalDuration / time.Duration(len(testEntities))
		t.Logf("✅ CheckEntityPermissions average latency: %v (success rate: %d/%d)", avgDuration, successCount, len(testEntities))
	})

	t.Log("🎯 Entity Basic Performance Test Completed")
}

func printPerformanceSummary(t *testing.T, metrics []PerformanceMetrics) {
	t.Log("\n" + strings.Repeat("=", 80))
	t.Log("📊 PERFORMANCE SUMMARY")
	t.Log(strings.Repeat("=", 80))

	operationStats := make(map[string][]time.Duration)
	var totalOperations, successfulOperations int

	for _, metric := range metrics {
		if metric.Success {
			operationStats[metric.Operation] = append(operationStats[metric.Operation], metric.Duration)
			successfulOperations++
		}
		totalOperations++
	}

	t.Logf("Overall Success Rate: %d/%d (%.1f%%)", successfulOperations, totalOperations, float64(successfulOperations)/float64(totalOperations)*100)
	t.Log("")

	for operation, durations := range operationStats {
		if len(durations) == 0 {
			continue
		}

		var total time.Duration
		min := durations[0]
		max := durations[0]

		for _, d := range durations {
			total += d
			if d < min {
				min = d
			}
			if d > max {
				max = d
			}
		}

		avg := total / time.Duration(len(durations))
		status := "✅"
		if avg > maxLatencyThreshold {
			status = "❌"
		}

		t.Logf("%s %-35s | Count: %3d | Avg: %8v | Min: %8v | Max: %8v",
			status, operation, len(durations), avg, min, max)
	}

	t.Log(strings.Repeat("=", 80))
	t.Logf("Latency Threshold: %v", maxLatencyThreshold)
	t.Log(strings.Repeat("=", 80))
}
