package test

import (
	"context"
	"fmt"
	"net/http"
	"strings"
	"testing"
	"time"

	assets "proto/hero/assets/v2"
	assetsConnect "proto/hero/assets/v2/assetsconnect"

	"connectrpc.com/connect"
)

const (
	performanceTestAssetCount = 100
	maxLatencyThreshold       = 200 * time.Millisecond
	bulkOperationThreshold    = 2000 * time.Millisecond
)

type PerformanceMetrics struct {
	Operation    string
	Duration     time.Duration
	Success      bool
	ErrorMessage string
	RecordCount  int
}

func TestAssets_BasicPerformance(t *testing.T) {
	if testing.Short() {
		t.Skip("skipping performance tests in short mode")
	}

	t.Logf("🚀 Starting Assets Basic Performance Test with %d records", performanceTestAssetCount)

	httpClient := http.DefaultClient
	AddAuthHeader(httpClient)

	ctx, cancel := context.WithTimeout(context.Background(), 600*time.Second)
	defer cancel()

	// Initialize client
	assetsClient := assetsConnect.NewAssetRegistryServiceClient(httpClient, ServiceBaseURL)

	var metrics []PerformanceMetrics
	var createdAssetIDs []string

	// Cleanup function
	defer func() {
		t.Log("🧹 Starting cleanup...")
		for _, assetID := range createdAssetIDs {
			if _, err := assetsClient.DeleteAsset(ctx, connect.NewRequest(&assets.DeleteAssetRequest{Id: assetID})); err != nil {
				t.Logf("cleanup: failed to delete asset %s: %v", assetID, err)
			}
		}
		t.Log("✅ Cleanup completed")

		// Print performance summary
		printPerformanceSummary(t, metrics)
	}()

	// Step 1: Create performance test assets
	t.Log("📋 Step 1: Creating performance test assets...")
	start := time.Now()
	for i := 0; i < performanceTestAssetCount; i++ {
		assetReq := &assets.CreateAssetRequest{
			Asset: &assets.Asset{
				Type:               assets.AssetType_ASSET_TYPE_TEST,
				Status:             assets.AssetStatus_ASSET_STATUS_AVAILABLE,
				Name:               fmt.Sprintf("Performance Test Asset %d", i),
				CognitoJwtSub:      fmt.Sprintf("perf-test-sub-%d", i),
				OrgId:              1,
				Latitude:           37.7749 + float64(i)*0.001,
				Longitude:          -122.4194 + float64(i)*0.001,
				ContactNo:          fmt.Sprintf("+1555000%04d", i),
				ContactEmail:       fmt.Sprintf("<EMAIL>", i),
				AdditionalInfoJson: fmt.Sprintf(`{"test_index": %d, "performance_test": true}`, i),
			},
		}

		resp, err := assetsClient.CreateAsset(ctx, connect.NewRequest(assetReq))
		if err != nil {
			metrics = append(metrics, PerformanceMetrics{
				Operation:    "CreateAsset",
				Duration:     0,
				Success:      false,
				ErrorMessage: err.Error(),
				RecordCount:  i,
			})
			t.Fatalf("Failed to create asset %d: %v", i, err)
		}
		createdAssetIDs = append(createdAssetIDs, resp.Msg.Asset.Id)

		// Progress logging
		if (i+1)%25 == 0 {
			t.Logf("   Created %d/%d assets...", i+1, performanceTestAssetCount)
		}
	}
	createDuration := time.Since(start)
	metrics = append(metrics, PerformanceMetrics{
		Operation:   "CreateAsset_Bulk",
		Duration:    createDuration,
		Success:     true,
		RecordCount: performanceTestAssetCount,
	})
	t.Logf("✅ Created %d assets in %v (avg: %v per asset)", performanceTestAssetCount, createDuration, createDuration/time.Duration(performanceTestAssetCount))

	// Wait for database indexing
	t.Log("⏳ Waiting for database indexing...")
	time.Sleep(2 * time.Second)

	// Performance Tests
	t.Log("🔥 Running Performance Tests...")

	// Test 1: GetAsset performance
	t.Run("GetAsset_Performance", func(t *testing.T) {
		testAssets := createdAssetIDs[:10] // Test first 10 assets
		var totalDuration time.Duration
		var successCount int

		for _, assetID := range testAssets {
			start := time.Now()
			_, err := assetsClient.GetAsset(ctx, connect.NewRequest(&assets.GetAssetRequest{Id: assetID}))
			duration := time.Since(start)
			totalDuration += duration

			success := err == nil
			if success {
				successCount++
			}

			metrics = append(metrics, PerformanceMetrics{
				Operation: "GetAsset",
				Duration:  duration,
				Success:   success,
				ErrorMessage: func() string {
					if err != nil {
						return err.Error()
					}
					return ""
				}(),
				RecordCount: 1,
			})

			if duration > maxLatencyThreshold {
				t.Errorf("❌ GetAsset latency too high: %v > %v", duration, maxLatencyThreshold)
			}
		}

		avgDuration := totalDuration / time.Duration(len(testAssets))
		t.Logf("✅ GetAsset average latency: %v (success rate: %d/%d)", avgDuration, successCount, len(testAssets))
	})

	// Test 2: GetAssetByCognitoSub performance
	t.Run("GetAssetByCognitoSub_Performance", func(t *testing.T) {
		testCount := 10
		var totalDuration time.Duration
		var successCount int

		for i := 0; i < testCount; i++ {
			cognitoSub := fmt.Sprintf("perf-test-sub-%d", i)
			start := time.Now()
			_, err := assetsClient.GetAssetByCognitoSub(ctx, connect.NewRequest(&assets.GetAssetByCognitoSubRequest{
				CognitoJwtSub: cognitoSub,
			}))
			duration := time.Since(start)
			totalDuration += duration

			success := err == nil
			if success {
				successCount++
			}

			metrics = append(metrics, PerformanceMetrics{
				Operation: "GetAssetByCognitoSub",
				Duration:  duration,
				Success:   success,
				ErrorMessage: func() string {
					if err != nil {
						return err.Error()
					}
					return ""
				}(),
				RecordCount: 1,
			})

			if duration > maxLatencyThreshold {
				t.Errorf("❌ GetAssetByCognitoSub latency too high: %v > %v", duration, maxLatencyThreshold)
			}
		}

		avgDuration := totalDuration / time.Duration(testCount)
		t.Logf("✅ GetAssetByCognitoSub average latency: %v (success rate: %d/%d)", avgDuration, successCount, testCount)
	})

	// Test 3: ListAssets performance
	t.Run("ListAssets_Performance", func(t *testing.T) {
		start := time.Now()
		resp, err := assetsClient.ListAssets(ctx, connect.NewRequest(&assets.ListAssetsRequest{
			PageSize: 50,
		}))
		duration := time.Since(start)

		success := err == nil && resp != nil
		metrics = append(metrics, PerformanceMetrics{
			Operation: "ListAssets",
			Duration:  duration,
			Success:   success,
			ErrorMessage: func() string {
				if err != nil {
					return err.Error()
				}
				return ""
			}(),
			RecordCount: func() int {
				if resp != nil {
					return len(resp.Msg.Assets)
				}
				return 0
			}(),
		})

		if duration > maxLatencyThreshold {
			t.Errorf("❌ ListAssets latency too high: %v > %v", duration, maxLatencyThreshold)
		} else {
			t.Logf("✅ ListAssets latency: %v (returned %d assets)", duration, len(resp.Msg.Assets))
		}
	})

	// Test 4: UpdateAsset performance
	t.Run("UpdateAsset_Performance", func(t *testing.T) {
		testAssets := createdAssetIDs[:5]
		var totalDuration time.Duration
		var successCount int

		for i, assetID := range testAssets {
			// Get current asset first
			getResp, err := assetsClient.GetAsset(ctx, connect.NewRequest(&assets.GetAssetRequest{Id: assetID}))
			if err != nil {
				t.Errorf("Failed to get asset for update: %v", err)
				continue
			}

			// Update the asset
			updatedAsset := getResp.Msg.Asset
			updatedAsset.Name = fmt.Sprintf("Updated Performance Test Asset %d", i)
			updatedAsset.ContactEmail = fmt.Sprintf("<EMAIL>", i)

			start := time.Now()
			_, err = assetsClient.UpdateAsset(ctx, connect.NewRequest(&assets.UpdateAssetRequest{
				Asset: updatedAsset,
			}))
			duration := time.Since(start)
			totalDuration += duration

			success := err == nil
			if success {
				successCount++
			}

			metrics = append(metrics, PerformanceMetrics{
				Operation: "UpdateAsset",
				Duration:  duration,
				Success:   success,
				ErrorMessage: func() string {
					if err != nil {
						return err.Error()
					}
					return ""
				}(),
				RecordCount: 1,
			})

			if duration > maxLatencyThreshold {
				t.Errorf("❌ UpdateAsset latency too high: %v > %v", duration, maxLatencyThreshold)
			}
		}

		avgDuration := totalDuration / time.Duration(len(testAssets))
		t.Logf("✅ UpdateAsset average latency: %v (success rate: %d/%d)", avgDuration, successCount, len(testAssets))
	})

	// Test 5: AddAdditionalInfo performance
	t.Run("AddAdditionalInfo_Performance", func(t *testing.T) {
		testAssets := createdAssetIDs[:5]
		var totalDuration time.Duration
		var successCount int

		for i, assetID := range testAssets {
			start := time.Now()
			_, err := assetsClient.AddAdditionalInfo(ctx, connect.NewRequest(&assets.AddAdditionalInfoRequest{
				Id:                 assetID,
				AdditionalInfoJson: fmt.Sprintf(`{"performance_update": %d, "timestamp": "%s"}`, i, time.Now().Format(time.RFC3339)),
			}))
			duration := time.Since(start)
			totalDuration += duration

			success := err == nil
			if success {
				successCount++
			}

			metrics = append(metrics, PerformanceMetrics{
				Operation: "AddAdditionalInfo",
				Duration:  duration,
				Success:   success,
				ErrorMessage: func() string {
					if err != nil {
						return err.Error()
					}
					return ""
				}(),
				RecordCount: 1,
			})

			if duration > maxLatencyThreshold {
				t.Errorf("❌ AddAdditionalInfo latency too high: %v > %v", duration, maxLatencyThreshold)
			}
		}

		avgDuration := totalDuration / time.Duration(len(testAssets))
		t.Logf("✅ AddAdditionalInfo average latency: %v (success rate: %d/%d)", avgDuration, successCount, len(testAssets))
	})

	// Test 6: SearchAssets performance
	t.Run("SearchAssets_Performance", func(t *testing.T) {
		searchTests := []struct {
			name    string
			request *assets.SearchAssetsRequest
		}{
			{
				name: "SearchByType",
				request: &assets.SearchAssetsRequest{
					Type:     []assets.AssetType{assets.AssetType_ASSET_TYPE_TEST},
					PageSize: 50,
				},
			},
			{
				name: "SearchByStatus",
				request: &assets.SearchAssetsRequest{
					Status:   []assets.AssetStatus{assets.AssetStatus_ASSET_STATUS_AVAILABLE},
					PageSize: 50,
				},
			},
			{
				name: "SearchByQuery",
				request: &assets.SearchAssetsRequest{
					Query:    "Performance Test",
					PageSize: 50,
				},
			},
		}

		for _, test := range searchTests {
			t.Run(test.name, func(t *testing.T) {
				start := time.Now()
				resp, err := assetsClient.SearchAssets(ctx, connect.NewRequest(test.request))
				duration := time.Since(start)

				success := err == nil && resp != nil
				metrics = append(metrics, PerformanceMetrics{
					Operation: fmt.Sprintf("SearchAssets_%s", test.name),
					Duration:  duration,
					Success:   success,
					ErrorMessage: func() string {
						if err != nil {
							return err.Error()
						}
						return ""
					}(),
					RecordCount: func() int {
						if resp != nil {
							return len(resp.Msg.Assets)
						}
						return 0
					}(),
				})

				if duration > maxLatencyThreshold {
					t.Errorf("❌ %s latency too high: %v > %v", test.name, duration, maxLatencyThreshold)
				} else {
					t.Logf("✅ %s latency: %v (returned %d assets)", test.name, duration, func() int {
						if resp != nil {
							return len(resp.Msg.Assets)
						}
						return 0
					}())
				}
			})
		}
	})

	// Test 7: ListAssetsByPhoneNumber performance
	t.Run("ListAssetsByPhoneNumber_Performance", func(t *testing.T) {
		testPhoneNumbers := []string{"+15550000001", "+15550000002", "+15550000003"}
		var totalDuration time.Duration
		var successCount int

		for _, phoneNumber := range testPhoneNumbers {
			start := time.Now()
			resp, err := assetsClient.ListAssetsByPhoneNumber(ctx, connect.NewRequest(&assets.ListAssetsByPhoneNumberRequest{
				PhoneNumber: phoneNumber,
			}))
			duration := time.Since(start)
			totalDuration += duration

			success := err == nil
			if success {
				successCount++
			}

			metrics = append(metrics, PerformanceMetrics{
				Operation: "ListAssetsByPhoneNumber",
				Duration:  duration,
				Success:   success,
				ErrorMessage: func() string {
					if err != nil {
						return err.Error()
					}
					return ""
				}(),
				RecordCount: func() int {
					if resp != nil {
						return len(resp.Msg.Assets)
					}
					return 0
				}(),
			})

			if duration > maxLatencyThreshold {
				t.Errorf("❌ ListAssetsByPhoneNumber latency too high: %v > %v", duration, maxLatencyThreshold)
			}
		}

		avgDuration := totalDuration / time.Duration(len(testPhoneNumbers))
		t.Logf("✅ ListAssetsByPhoneNumber average latency: %v (success rate: %d/%d)", avgDuration, successCount, len(testPhoneNumbers))
	})

	t.Log("🎯 Assets Basic Performance Test Completed")
}

func printPerformanceSummary(t *testing.T, metrics []PerformanceMetrics) {
	t.Log("\n" + strings.Repeat("=", 80))
	t.Log("📊 PERFORMANCE SUMMARY")
	t.Log(strings.Repeat("=", 80))

	operationStats := make(map[string][]time.Duration)
	var totalOperations, successfulOperations int

	for _, metric := range metrics {
		if metric.Success {
			operationStats[metric.Operation] = append(operationStats[metric.Operation], metric.Duration)
			successfulOperations++
		}
		totalOperations++
	}

	t.Logf("Overall Success Rate: %d/%d (%.1f%%)", successfulOperations, totalOperations, float64(successfulOperations)/float64(totalOperations)*100)
	t.Log("")

	for operation, durations := range operationStats {
		if len(durations) == 0 {
			continue
		}

		var total time.Duration
		min := durations[0]
		max := durations[0]

		for _, d := range durations {
			total += d
			if d < min {
				min = d
			}
			if d > max {
				max = d
			}
		}

		avg := total / time.Duration(len(durations))
		status := "✅"
		if avg > maxLatencyThreshold {
			status = "❌"
		}

		t.Logf("%s %-35s | Count: %3d | Avg: %8v | Min: %8v | Max: %8v",
			status, operation, len(durations), avg, min, max)
	}

	t.Log(strings.Repeat("=", 80))
	t.Logf("Latency Threshold: %v", maxLatencyThreshold)
	t.Log(strings.Repeat("=", 80))
}
