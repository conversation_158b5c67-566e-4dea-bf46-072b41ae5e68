package test

import (
	"context"
	"fmt"
	"net/http"
	"strings"
	"testing"
	"time"

	orders "proto/hero/orders/v2"
	ordersConnect "proto/hero/orders/v2/ordersconnect"

	situations "proto/hero/situations/v2"
	situationsConnect "proto/hero/situations/v2/situationsconnect"

	assets "proto/hero/assets/v2"
	assetsConnect "proto/hero/assets/v2/assetsconnect"

	"connectrpc.com/connect"
)

const (
	performanceTestOrderCount     = 100
	performanceTestAssetCount     = 10
	performanceTestSituationCount = 10
	maxLatencyThreshold           = 200 * time.Millisecond
	bulkOperationThreshold        = 2000 * time.Millisecond
)

type PerformanceMetrics struct {
	Operation    string
	Duration     time.Duration
	Success      bool
	ErrorMessage string
	RecordCount  int
}

func TestOrders_BasicPerformance(t *testing.T) {
	if testing.Short() {
		t.Skip("skipping performance tests in short mode")
	}

	t.Logf("🚀 Starting Orders Basic Performance Test with %d orders, %d assets, %d situations",
		performanceTestOrderCount, performanceTestAssetCount, performanceTestSituationCount)

	httpClient := http.DefaultClient
	AddAuthHeader(httpClient)

	ctx, cancel := context.WithTimeout(context.Background(), 600*time.Second)
	defer cancel()

	// Initialize clients
	ordersClient := ordersConnect.NewOrderServiceClient(httpClient, ServiceBaseURL)
	situationsClient := situationsConnect.NewSituationServiceClient(httpClient, ServiceBaseURL)
	assetsClient := assetsConnect.NewAssetRegistryServiceClient(httpClient, ServiceBaseURL)

	var metrics []PerformanceMetrics
	var createdOrderIDs []string
	var createdSituationIDs []string
	var createdAssetIDs []string

	// Cleanup function
	defer func() {
		t.Log("🧹 Starting cleanup...")

		// Delete orders first (they depend on situations and assets)
		for _, orderID := range createdOrderIDs {
			if _, err := ordersClient.DeleteOrder(ctx, connect.NewRequest(&orders.DeleteOrderRequest{Id: orderID})); err != nil {
				t.Logf("cleanup: failed to delete order %s: %v", orderID, err)
			}
		}

		// Delete situations
		for _, situationID := range createdSituationIDs {
			if _, err := situationsClient.DeleteSituation(ctx, connect.NewRequest(&situations.DeleteSituationRequest{Id: situationID})); err != nil {
				t.Logf("cleanup: failed to delete situation %s: %v", situationID, err)
			}
		}

		// Delete assets
		for _, assetID := range createdAssetIDs {
			if _, err := assetsClient.DeleteAsset(ctx, connect.NewRequest(&assets.DeleteAssetRequest{Id: assetID})); err != nil {
				t.Logf("cleanup: failed to delete asset %s: %v", assetID, err)
			}
		}

		t.Log("✅ Cleanup completed")

		// Print performance summary
		printPerformanceSummary(t, metrics)
	}()

	// Step 1: Create supporting assets
	t.Log("📋 Step 1: Creating performance test assets...")
	start := time.Now()
	for i := 0; i < performanceTestAssetCount; i++ {
		assetReq := &assets.CreateAssetRequest{
			Asset: &assets.Asset{
				Name:               fmt.Sprintf("Performance Test Asset %d", i),
				Type:               assets.AssetType_ASSET_TYPE_MEMBER,
				Status:             assets.AssetStatus_ASSET_STATUS_AVAILABLE,
				CognitoJwtSub:      fmt.Sprintf("perf-test-asset-%d", i),
				ContactNo:          fmt.Sprintf("+1555%07d", i),
				ContactEmail:       fmt.Sprintf("<EMAIL>", i),
				Latitude:           37.7749 + float64(i)*0.001,
				Longitude:          -122.4194 + float64(i)*0.001,
				OrgId:              1,
				AdditionalInfoJson: fmt.Sprintf(`{"address": "%d Performance Test Street, Test City, TS 12345"}`, i),
			},
		}

		resp, err := assetsClient.CreateAsset(ctx, connect.NewRequest(assetReq))
		if err != nil {
			t.Fatalf("Failed to create asset %d: %v", i, err)
		}
		createdAssetIDs = append(createdAssetIDs, resp.Msg.Asset.Id)

		// Progress logging
		if (i+1)%5 == 0 {
			t.Logf("   Created %d/%d assets...", i+1, performanceTestAssetCount)
		}
	}
	assetCreateDuration := time.Since(start)
	t.Logf("✅ Created %d assets in %v (avg: %v per asset)", performanceTestAssetCount, assetCreateDuration, assetCreateDuration/time.Duration(performanceTestAssetCount))

	// Step 2: Create supporting situations
	t.Log("📋 Step 2: Creating performance test situations...")
	start = time.Now()
	for i := 0; i < performanceTestSituationCount; i++ {
		situationReq := &situations.CreateSituationRequest{
			Situation: &situations.Situation{
				Title:       fmt.Sprintf("Performance Test Situation %d", i),
				Description: fmt.Sprintf("Performance testing situation number %d", i),
				Priority:    2, // Medium priority
				Status:      situations.SituationStatus_SITUATION_STATUS_CREATED,
				Type:        situations.SituationType_SITUATION_TYPE_OTHER,
				Address:     fmt.Sprintf("%d Situation Test Avenue, Test City, TS 12345", i),
				Latitude:    37.7749 + float64(i)*0.002,
				Longitude:   -122.4194 + float64(i)*0.002,
			},
		}

		resp, err := situationsClient.CreateSituation(ctx, connect.NewRequest(situationReq))
		if err != nil {
			t.Fatalf("Failed to create situation %d: %v", i, err)
		}
		createdSituationIDs = append(createdSituationIDs, resp.Msg.Situation.Id)

		// Progress logging
		if (i+1)%5 == 0 {
			t.Logf("   Created %d/%d situations...", i+1, performanceTestSituationCount)
		}
	}
	situationCreateDuration := time.Since(start)
	t.Logf("✅ Created %d situations in %v (avg: %v per situation)", performanceTestSituationCount, situationCreateDuration, situationCreateDuration/time.Duration(performanceTestSituationCount))

	// Step 3: Create performance test orders
	t.Log("📋 Step 3: Creating performance test orders...")
	start = time.Now()

	orderTypes := []orders.OrderType{
		orders.OrderType_ORDER_TYPE_TRIAGE_MEMBER_REPORT,
		orders.OrderType_ORDER_TYPE_ASSIST_MEMBER,
		orders.OrderType_ORDER_TYPE_SUBMIT_FINAL_REPORT,
		orders.OrderType_ORDER_TYPE_ASSIGN_AGENT,
		orders.OrderType_ORDER_TYPE_WRITE_REPORT,
	}

	orderStatuses := []orders.OrderStatus{
		orders.OrderStatus_ORDER_STATUS_CREATED,
		orders.OrderStatus_ORDER_STATUS_ACKNOWLEDGED,
		orders.OrderStatus_ORDER_STATUS_IN_PROGRESS,
		orders.OrderStatus_ORDER_STATUS_COMPLETED,
		orders.OrderStatus_ORDER_STATUS_SNOOZED,
	}

	for i := 0; i < performanceTestOrderCount; i++ {
		assetIndex := i % len(createdAssetIDs)
		situationIndex := i % len(createdSituationIDs)
		orderType := orderTypes[i%len(orderTypes)]
		orderStatus := orderStatuses[i%len(orderStatuses)]

		orderReq := &orders.CreateOrderRequest{
			Order: &orders.Order{
				SituationId:        createdSituationIDs[situationIndex],
				AssetId:            createdAssetIDs[assetIndex],
				Type:               orderType,
				Status:             orderStatus,
				Instructions:       fmt.Sprintf("Performance test instructions for order %d", i),
				Priority:           int32(1 + (i % 5)), //nolint:gosec // Safe conversion for test data
				AdditionalInfoJson: fmt.Sprintf(`{"test_order_number": %d, "batch": "performance_test"}`, i),
				Notes:              fmt.Sprintf("Performance testing order number %d", i),
				Title:              fmt.Sprintf("Performance Test Order %d", i),
				CreateTime:         time.Now().Format(time.RFC3339),
				UpdateTime:         time.Now().Format(time.RFC3339),
				AllowedAssetTypes:  []assets.AssetType{assets.AssetType_ASSET_TYPE_MEMBER, assets.AssetType_ASSET_TYPE_RESPONDER},
			},
		}

		resp, err := ordersClient.CreateOrder(ctx, connect.NewRequest(orderReq))
		if err != nil {
			metrics = append(metrics, PerformanceMetrics{
				Operation:    "CreateOrder",
				Duration:     0,
				Success:      false,
				ErrorMessage: err.Error(),
				RecordCount:  i,
			})
			t.Fatalf("Failed to create order %d: %v", i, err)
		}
		createdOrderIDs = append(createdOrderIDs, resp.Msg.Order.Id)

		// Progress logging
		if (i+1)%25 == 0 {
			t.Logf("   Created %d/%d orders...", i+1, performanceTestOrderCount)
		}
	}
	createDuration := time.Since(start)
	metrics = append(metrics, PerformanceMetrics{
		Operation:   "CreateOrder_Bulk",
		Duration:    createDuration,
		Success:     true,
		RecordCount: performanceTestOrderCount,
	})
	t.Logf("✅ Created %d orders in %v (avg: %v per order)", performanceTestOrderCount, createDuration, createDuration/time.Duration(performanceTestOrderCount))

	// Wait for database indexing
	t.Log("⏳ Waiting for database indexing...")
	time.Sleep(2 * time.Second)

	// Performance Tests
	t.Log("🔥 Running Performance Tests...")

	// Test 1: GetOrder performance
	t.Run("GetOrder_Performance", func(t *testing.T) {
		testOrders := createdOrderIDs[:10]
		var totalDuration time.Duration
		var successCount int

		for _, orderID := range testOrders {
			start := time.Now()
			_, err := ordersClient.GetOrder(ctx, connect.NewRequest(&orders.GetOrderRequest{Id: orderID}))
			duration := time.Since(start)
			totalDuration += duration

			success := err == nil
			if success {
				successCount++
			}

			metrics = append(metrics, PerformanceMetrics{
				Operation: "GetOrder",
				Duration:  duration,
				Success:   success,
				ErrorMessage: func() string {
					if err != nil {
						return err.Error()
					}
					return ""
				}(),
				RecordCount: 1,
			})

			if duration > maxLatencyThreshold {
				t.Errorf("❌ GetOrder latency too high: %v > %v", duration, maxLatencyThreshold)
			}
		}

		avgDuration := totalDuration / time.Duration(len(testOrders))
		t.Logf("✅ GetOrder average latency: %v (success rate: %d/%d)", avgDuration, successCount, len(testOrders))
	})

	// Test 2: ListOrders performance
	t.Run("ListOrders_Performance", func(t *testing.T) {
		start := time.Now()
		resp, err := ordersClient.ListOrders(ctx, connect.NewRequest(&orders.ListOrdersRequest{
			PageSize: 50,
		}))
		duration := time.Since(start)

		success := err == nil && resp != nil
		metrics = append(metrics, PerformanceMetrics{
			Operation: "ListOrders",
			Duration:  duration,
			Success:   success,
			ErrorMessage: func() string {
				if err != nil {
					return err.Error()
				}
				return ""
			}(),
			RecordCount: func() int {
				if resp != nil {
					return len(resp.Msg.Orders)
				}
				return 0
			}(),
		})

		if duration > maxLatencyThreshold {
			t.Errorf("❌ ListOrders latency too high: %v > %v", duration, maxLatencyThreshold)
		} else {
			t.Logf("✅ ListOrders latency: %v (returned %d orders)", duration, len(resp.Msg.Orders))
		}
	})

	// Test 3: UpdateOrder performance
	t.Run("UpdateOrder_Performance", func(t *testing.T) {
		testOrders := createdOrderIDs[:5]
		var totalDuration time.Duration
		var successCount int

		for i, orderID := range testOrders {
			// Get current order first
			getResp, err := ordersClient.GetOrder(ctx, connect.NewRequest(&orders.GetOrderRequest{Id: orderID}))
			if err != nil {
				t.Errorf("Failed to get order for update: %v", err)
				continue
			}

			// Update the order
			updatedOrder := getResp.Msg
			updatedOrder.Title = fmt.Sprintf("Updated Performance Test Order %d", i)
			updatedOrder.Notes = fmt.Sprintf("Updated performance testing order number %d", i)

			start := time.Now()
			_, err = ordersClient.UpdateOrder(ctx, connect.NewRequest(&orders.UpdateOrderRequest{
				Order: updatedOrder,
			}))
			duration := time.Since(start)
			totalDuration += duration

			success := err == nil
			if success {
				successCount++
			}

			metrics = append(metrics, PerformanceMetrics{
				Operation: "UpdateOrder",
				Duration:  duration,
				Success:   success,
				ErrorMessage: func() string {
					if err != nil {
						return err.Error()
					}
					return ""
				}(),
				RecordCount: 1,
			})

			if duration > maxLatencyThreshold {
				t.Errorf("❌ UpdateOrder latency too high: %v > %v", duration, maxLatencyThreshold)
			}
		}

		avgDuration := totalDuration / time.Duration(len(testOrders))
		t.Logf("✅ UpdateOrder average latency: %v (success rate: %d/%d)", avgDuration, successCount, len(testOrders))
	})

	// Test 4: Order status transitions performance
	t.Run("StatusTransitions_Performance", func(t *testing.T) {
		statusTests := []struct {
			name      string
			operation func(orderID string) error
		}{
			{
				name: "AcknowledgeOrder",
				operation: func(orderID string) error {
					_, err := ordersClient.AcknowledgeOrder(ctx, connect.NewRequest(&orders.AcknowledgeOrderRequest{Id: orderID}))
					return err
				},
			},
			{
				name: "SnoozeOrder",
				operation: func(orderID string) error {
					_, err := ordersClient.SnoozeOrder(ctx, connect.NewRequest(&orders.SnoozeOrderRequest{
						Id:           orderID,
						SnoozeReason: "Performance test snooze",
						SnoozeUntil:  time.Now().Add(1 * time.Hour).Format(time.RFC3339),
					}))
					return err
				},
			},
			{
				name: "CompleteOrder",
				operation: func(orderID string) error {
					_, err := ordersClient.CompleteOrder(ctx, connect.NewRequest(&orders.CompleteOrderRequest{Id: orderID}))
					return err
				},
			},
		}

		for _, test := range statusTests {
			t.Run(test.name, func(t *testing.T) {
				// Use different orders for each status transition test
				testOrderIndex := len(statusTests) // Start from a different index
				if testOrderIndex < len(createdOrderIDs) {
					orderID := createdOrderIDs[testOrderIndex]

					start := time.Now()
					err := test.operation(orderID)
					duration := time.Since(start)

					success := err == nil
					metrics = append(metrics, PerformanceMetrics{
						Operation: test.name,
						Duration:  duration,
						Success:   success,
						ErrorMessage: func() string {
							if err != nil {
								return err.Error()
							}
							return ""
						}(),
						RecordCount: 1,
					})

					if duration > maxLatencyThreshold {
						t.Errorf("❌ %s latency too high: %v > %v", test.name, duration, maxLatencyThreshold)
					} else {
						t.Logf("✅ %s latency: %v", test.name, duration)
					}
				}
			})
		}
	})

	// Test 5: Specialized listing operations performance
	t.Run("SpecializedListing_Performance", func(t *testing.T) {
		listingTests := []struct {
			name string
			test func() (int, time.Duration, error)
		}{
			{
				name: "ListActiveAssignedOrdersForAsset",
				test: func() (int, time.Duration, error) {
					start := time.Now()
					resp, err := ordersClient.ListActiveAssignedOrdersForAsset(ctx, connect.NewRequest(&orders.ListActiveAssignedOrdersForAssetRequest{
						AssetId:  createdAssetIDs[0],
						PageSize: 50,
					}))
					duration := time.Since(start)
					count := 0
					if resp != nil {
						count = len(resp.Msg.Orders)
					}
					return count, duration, err
				},
			},
			{
				name: "ListNewOrdersForAsset",
				test: func() (int, time.Duration, error) {
					start := time.Now()
					resp, err := ordersClient.ListNewOrdersForAsset(ctx, connect.NewRequest(&orders.ListNewOrdersForAssetRequest{
						AssetId:  createdAssetIDs[0],
						PageSize: 50,
					}))
					duration := time.Since(start)
					count := 0
					if resp != nil {
						count = len(resp.Msg.Orders)
					}
					return count, duration, err
				},
			},
			{
				name: "ListOrdersForAsset",
				test: func() (int, time.Duration, error) {
					start := time.Now()
					resp, err := ordersClient.ListOrdersForAsset(ctx, connect.NewRequest(&orders.ListOrdersForAssetRequest{
						AssetId:  createdAssetIDs[0],
						PageSize: 50,
					}))
					duration := time.Since(start)
					count := 0
					if resp != nil {
						count = len(resp.Msg.Orders)
					}
					return count, duration, err
				},
			},
			{
				name: "ListOrdersForSituation",
				test: func() (int, time.Duration, error) {
					start := time.Now()
					resp, err := ordersClient.ListOrdersForSituation(ctx, connect.NewRequest(&orders.ListOrdersForSituationRequest{
						SituationId: createdSituationIDs[0],
						PageSize:    50,
					}))
					duration := time.Since(start)
					count := 0
					if resp != nil {
						count = len(resp.Msg.Orders)
					}
					return count, duration, err
				},
			},
		}

		for _, test := range listingTests {
			t.Run(test.name, func(t *testing.T) {
				count, duration, err := test.test()

				success := err == nil
				metrics = append(metrics, PerformanceMetrics{
					Operation: test.name,
					Duration:  duration,
					Success:   success,
					ErrorMessage: func() string {
						if err != nil {
							return err.Error()
						}
						return ""
					}(),
					RecordCount: count,
				})

				if duration > maxLatencyThreshold {
					t.Errorf("❌ %s latency too high: %v > %v", test.name, duration, maxLatencyThreshold)
				} else {
					t.Logf("✅ %s latency: %v (returned %d orders)", test.name, duration, count)
				}
			})
		}
	})

	// Test 6: Order update operations performance
	t.Run("OrderUpdates_Performance", func(t *testing.T) {
		if len(createdOrderIDs) > 0 {
			orderID := createdOrderIDs[0]

			// Test AddOrderUpdate
			t.Run("AddOrderUpdate", func(t *testing.T) {
				updateEntry := &orders.OrderUpdateEntry{
					Message:      "Performance test update",
					Timestamp:    time.Now().Format(time.RFC3339),
					UpdateSource: situations.UpdateSource_UPDATE_SOURCE_HUMAN_OPERATOR,
				}

				start := time.Now()
				_, err := ordersClient.AddOrderUpdate(ctx, connect.NewRequest(&orders.AddOrderUpdateRequest{
					Id:     orderID,
					Update: updateEntry,
				}))
				duration := time.Since(start)

				success := err == nil
				metrics = append(metrics, PerformanceMetrics{
					Operation: "AddOrderUpdate",
					Duration:  duration,
					Success:   success,
					ErrorMessage: func() string {
						if err != nil {
							return err.Error()
						}
						return ""
					}(),
					RecordCount: 1,
				})

				if duration > maxLatencyThreshold {
					t.Errorf("❌ AddOrderUpdate latency too high: %v > %v", duration, maxLatencyThreshold)
				} else {
					t.Logf("✅ AddOrderUpdate latency: %v", duration)
				}
			})

			// Test AddAllowedAssetType
			t.Run("AddAllowedAssetType", func(t *testing.T) {
				start := time.Now()
				_, err := ordersClient.AddAllowedAssetType(ctx, connect.NewRequest(&orders.AddAllowedAssetTypeRequest{
					Id:               orderID,
					AllowedAssetType: assets.AssetType_ASSET_TYPE_CAMERA,
				}))
				duration := time.Since(start)

				success := err == nil
				metrics = append(metrics, PerformanceMetrics{
					Operation: "AddAllowedAssetType",
					Duration:  duration,
					Success:   success,
					ErrorMessage: func() string {
						if err != nil {
							return err.Error()
						}
						return ""
					}(),
					RecordCount: 1,
				})

				if duration > maxLatencyThreshold {
					t.Errorf("❌ AddAllowedAssetType latency too high: %v > %v", duration, maxLatencyThreshold)
				} else {
					t.Logf("✅ AddAllowedAssetType latency: %v", duration)
				}
			})

			// Test UpdateOrderPermissions
			t.Run("UpdateOrderPermissions", func(t *testing.T) {
				permissions := &orders.OrderPermissions{
					CanChangeStatus: []assets.AssetType{assets.AssetType_ASSET_TYPE_MEMBER},
					CanAssignAsset:  []assets.AssetType{assets.AssetType_ASSET_TYPE_MEMBER, assets.AssetType_ASSET_TYPE_RESPONDER},
				}

				start := time.Now()
				_, err := ordersClient.UpdateOrderPermissions(ctx, connect.NewRequest(&orders.UpdateOrderPermissionsRequest{
					Id:          orderID,
					Permissions: permissions,
				}))
				duration := time.Since(start)

				success := err == nil
				metrics = append(metrics, PerformanceMetrics{
					Operation: "UpdateOrderPermissions",
					Duration:  duration,
					Success:   success,
					ErrorMessage: func() string {
						if err != nil {
							return err.Error()
						}
						return ""
					}(),
					RecordCount: 1,
				})

				if duration > maxLatencyThreshold {
					t.Errorf("❌ UpdateOrderPermissions latency too high: %v > %v", duration, maxLatencyThreshold)
				} else {
					t.Logf("✅ UpdateOrderPermissions latency: %v", duration)
				}
			})

			// Test AddAdditionalInfo
			t.Run("AddAdditionalInfo", func(t *testing.T) {
				start := time.Now()
				_, err := ordersClient.AddAdditionalInfo(ctx, connect.NewRequest(&orders.AddAdditionalInfoRequest{
					Id:                 orderID,
					AdditionalInfoJson: `{"performance_test": "additional_info"}`,
				}))
				duration := time.Since(start)

				success := err == nil
				metrics = append(metrics, PerformanceMetrics{
					Operation: "AddAdditionalInfo",
					Duration:  duration,
					Success:   success,
					ErrorMessage: func() string {
						if err != nil {
							return err.Error()
						}
						return ""
					}(),
					RecordCount: 1,
				})

				if duration > maxLatencyThreshold {
					t.Errorf("❌ AddAdditionalInfo latency too high: %v > %v", duration, maxLatencyThreshold)
				} else {
					t.Logf("✅ AddAdditionalInfo latency: %v", duration)
				}
			})
		}
	})

	// Test 7: Filtered listing performance
	t.Run("FilteredListing_Performance", func(t *testing.T) {
		filterTests := []struct {
			name    string
			request *orders.ListOrdersRequest
		}{
			{
				name: "ListByStatus_CREATED",
				request: &orders.ListOrdersRequest{
					PageSize: 50,
					Status:   orders.OrderStatus_ORDER_STATUS_CREATED,
				},
			},
			{
				name: "ListByType_TRIAGE",
				request: &orders.ListOrdersRequest{
					PageSize: 50,
					Type:     orders.OrderType_ORDER_TYPE_TRIAGE_MEMBER_REPORT,
				},
			},
			{
				name: "ListByStatusAndType",
				request: &orders.ListOrdersRequest{
					PageSize: 50,
					Status:   orders.OrderStatus_ORDER_STATUS_ACKNOWLEDGED,
					Type:     orders.OrderType_ORDER_TYPE_ASSIST_MEMBER,
				},
			},
		}

		for _, test := range filterTests {
			t.Run(test.name, func(t *testing.T) {
				start := time.Now()
				resp, err := ordersClient.ListOrders(ctx, connect.NewRequest(test.request))
				duration := time.Since(start)

				success := err == nil && resp != nil
				metrics = append(metrics, PerformanceMetrics{
					Operation: fmt.Sprintf("ListOrders_%s", test.name),
					Duration:  duration,
					Success:   success,
					ErrorMessage: func() string {
						if err != nil {
							return err.Error()
						}
						return ""
					}(),
					RecordCount: func() int {
						if resp != nil {
							return len(resp.Msg.Orders)
						}
						return 0
					}(),
				})

				if duration > maxLatencyThreshold {
					t.Errorf("❌ %s latency too high: %v > %v", test.name, duration, maxLatencyThreshold)
				} else {
					t.Logf("✅ %s latency: %v (returned %d orders)", test.name, duration, func() int {
						if resp != nil {
							return len(resp.Msg.Orders)
						}
						return 0
					}())
				}
			})
		}
	})

	t.Log("🎯 Orders Basic Performance Test Completed")
	t.Log("⚠️  Note: Full orders API testing requires proper proto definitions")
}

func printPerformanceSummary(t *testing.T, metrics []PerformanceMetrics) {
	t.Log("\n" + strings.Repeat("=", 80))
	t.Log("📊 PERFORMANCE SUMMARY")
	t.Log(strings.Repeat("=", 80))

	operationStats := make(map[string][]time.Duration)
	var totalOperations, successfulOperations int

	for _, metric := range metrics {
		if metric.Success {
			operationStats[metric.Operation] = append(operationStats[metric.Operation], metric.Duration)
			successfulOperations++
		}
		totalOperations++
	}

	t.Logf("Overall Success Rate: %d/%d (%.1f%%)", successfulOperations, totalOperations, float64(successfulOperations)/float64(totalOperations)*100)
	t.Log("")

	for operation, durations := range operationStats {
		if len(durations) == 0 {
			continue
		}

		var total time.Duration
		min := durations[0]
		max := durations[0]

		for _, d := range durations {
			total += d
			if d < min {
				min = d
			}
			if d > max {
				max = d
			}
		}

		avg := total / time.Duration(len(durations))
		status := "✅"
		if avg > maxLatencyThreshold {
			status = "❌"
		}

		t.Logf("%s %-35s | Count: %3d | Avg: %8v | Min: %8v | Max: %8v",
			status, operation, len(durations), avg, min, max)
	}

	t.Log(strings.Repeat("=", 80))
	t.Logf("Latency Threshold: %v", maxLatencyThreshold)
	t.Log(strings.Repeat("=", 80))
}
