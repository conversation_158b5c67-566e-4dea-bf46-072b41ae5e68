package test

import (
	"context"
	"fmt"
	"net/http"
	"strings"
	"testing"
	"time"

	cases "proto/hero/cases/v1"
	casesConnect "proto/hero/cases/v1/casesconnect"

	assets "proto/hero/assets/v2"
	assetsConnect "proto/hero/assets/v2/assetsconnect"

	situations "proto/hero/situations/v2"
	situationsConnect "proto/hero/situations/v2/situationsconnect"

	reports "proto/hero/reports/v2"
	reportsConnect "proto/hero/reports/v2/reportsconnect"

	"connectrpc.com/connect"
	"google.golang.org/protobuf/types/known/structpb"
)

const (
	performanceTestCaseCount = 100
	maxLatencyThreshold      = 200 * time.Millisecond
	bulkOperationThreshold   = 2000 * time.Millisecond
)

type PerformanceMetrics struct {
	Operation    string
	Duration     time.Duration
	Success      bool
	ErrorMessage string
	RecordCount  int
}

func TestCases_BasicPerformance(t *testing.T) {
	if testing.Short() {
		t.Skip("skipping performance tests in short mode")
	}

	t.Logf("🚀 Starting Cases Basic Performance Test with %d records", performanceTestCaseCount)

	httpClient := http.DefaultClient
	AddAuthHeader(httpClient)

	ctx, cancel := context.WithTimeout(context.Background(), 600*time.Second)
	defer cancel()

	// Initialize clients
	casesClient := casesConnect.NewCaseServiceClient(httpClient, ServiceBaseURL)
	assetsClient := assetsConnect.NewAssetRegistryServiceClient(httpClient, ServiceBaseURL)
	situationsClient := situationsConnect.NewSituationServiceClient(httpClient, ServiceBaseURL)
	reportsClient := reportsConnect.NewReportServiceClient(httpClient, ServiceBaseURL)

	var metrics []PerformanceMetrics
	var createdCaseIDs []string
	var createdAssetIDs []string
	var createdSituationIDs []string
	var createdReportIDs []string

	// Cleanup function
	defer func() {
		t.Log("🧹 Starting cleanup...")

		// Delete cases
		for _, caseID := range createdCaseIDs {
			if _, err := casesClient.DeleteCase(ctx, connect.NewRequest(&cases.DeleteCaseRequest{Id: caseID})); err != nil {
				t.Logf("cleanup: failed to delete case %s: %v", caseID, err)
			}
		}

		// Delete reports
		for _, reportID := range createdReportIDs {
			if _, err := reportsClient.DeleteReport(ctx, connect.NewRequest(&reports.DeleteReportRequest{Id: reportID})); err != nil {
				t.Logf("cleanup: failed to delete report %s: %v", reportID, err)
			}
		}

		// Delete situations
		for _, situationID := range createdSituationIDs {
			if _, err := situationsClient.DeleteSituation(ctx, connect.NewRequest(&situations.DeleteSituationRequest{Id: situationID})); err != nil {
				t.Logf("cleanup: failed to delete situation %s: %v", situationID, err)
			}
		}

		// Delete assets
		for _, assetID := range createdAssetIDs {
			if _, err := assetsClient.DeleteAsset(ctx, connect.NewRequest(&assets.DeleteAssetRequest{Id: assetID})); err != nil {
				t.Logf("cleanup: failed to delete asset %s: %v", assetID, err)
			}
		}

		t.Log("✅ Cleanup completed")

		// Print performance summary
		printPerformanceSummary(t, metrics)
	}()

	// Step 1: Create supporting data (assets, situations, reports)
	t.Log("📋 Step 1: Creating supporting data...")

	// Create assets for case relationships
	for i := 0; i < 50; i++ {
		assetReq := &assets.CreateAssetRequest{
			Asset: &assets.Asset{
				Type:          assets.AssetType_ASSET_TYPE_TEST,
				Status:        assets.AssetStatus_ASSET_STATUS_AVAILABLE,
				Name:          fmt.Sprintf("Performance Test Asset %d", i),
				CognitoJwtSub: fmt.Sprintf("perf-test-asset-sub-%d", i),
				OrgId:         1,
			},
		}
		resp, err := assetsClient.CreateAsset(ctx, connect.NewRequest(assetReq))
		if err != nil {
			t.Fatalf("Failed to create asset %d: %v", i, err)
		}
		createdAssetIDs = append(createdAssetIDs, resp.Msg.Asset.Id)
	}

	// Create situations for case relationships
	for i := 0; i < 50; i++ {
		situationReq := &situations.CreateSituationRequest{
			Situation: &situations.Situation{
				Title:         fmt.Sprintf("Performance Test Situation %d", i),
				Description:   fmt.Sprintf("Performance testing situation number %d", i),
				Status:        situations.SituationStatus_SITUATION_STATUS_CREATED,
				ReporterId:    createdAssetIDs[i%len(createdAssetIDs)],
				TriggerSource: situations.TriggerSource_TRIGGER_SOURCE_WEB_PORTAL,
				Type:          situations.SituationType_SITUATION_TYPE_VANDALISM,
				Priority:      int32(i%5 + 1), //nolint:gosec // Safe conversion for test data
				ReporterName:  fmt.Sprintf("Test Reporter %d", i),
				ContactNo:     fmt.Sprintf("+1555%07d", i),
				ContactEmail:  fmt.Sprintf("<EMAIL>", i),
				Address:       fmt.Sprintf("%d Test Street, Test City, TS 12345", i),
				Latitude:      37.7749 + float64(i)*0.001,
				Longitude:     -122.4194 + float64(i)*0.001,
			},
		}
		resp, err := situationsClient.CreateSituation(ctx, connect.NewRequest(situationReq))
		if err != nil {
			t.Fatalf("Failed to create situation %d: %v", i, err)
		}
		createdSituationIDs = append(createdSituationIDs, resp.Msg.Situation.Id)
	}

	// Create reports for case relationships
	for i := 0; i < 50; i++ {
		reportReq := &reports.CreateReportRequest{
			Report: &reports.Report{
				Title:         fmt.Sprintf("Performance Test Report %d", i),
				Status:        reports.ReportStatus_REPORT_STATUS_IN_PROGRESS,
				AuthorAssetId: createdAssetIDs[i%len(createdAssetIDs)],
				SituationId:   createdSituationIDs[i%len(createdSituationIDs)],
			},
		}
		resp, err := reportsClient.CreateReport(ctx, connect.NewRequest(reportReq))
		if err != nil {
			t.Fatalf("Failed to create report %d: %v", i, err)
		}
		createdReportIDs = append(createdReportIDs, resp.Msg.Report.Id)
	}

	t.Logf("✅ Created %d assets, %d situations, %d reports", len(createdAssetIDs), len(createdSituationIDs), len(createdReportIDs))

	// Step 2: Create performance test cases
	t.Log("📋 Step 2: Creating performance test cases...")
	start := time.Now()
	for i := 0; i < performanceTestCaseCount; i++ {
		additionalInfo, _ := structpb.NewStruct(map[string]interface{}{
			"test_index":          i,
			"performance_test":    true,
			"evidence_collected":  i%2 == 0,
			"witness_count":       i % 5,
			"investigation_notes": fmt.Sprintf("Initial findings for case %d", i),
		})

		caseReq := &cases.CreateCaseRequest{
			Case_: &cases.Case{
				Type:               cases.CaseType_CASE_TYPE_SECURITY_INCIDENT,
				Title:              fmt.Sprintf("Performance Test Case %d", i),
				Description:        fmt.Sprintf("Performance testing case number %d", i),
				Status:             cases.CaseStatus_CASE_STATUS_NEW,
				Priority:           int32(i%5 + 1), //nolint:gosec // Safe conversion for test data
				ReleaseStatus:      cases.ReleaseStatus_RELEASE_STATUS_INTERNAL,
				SituationIds:       []string{createdSituationIDs[i%len(createdSituationIDs)]},
				ReportIds:          []string{createdReportIDs[i%len(createdReportIDs)]},
				Tags:               []string{"performance", "test", fmt.Sprintf("batch-%d", i/10)},
				AdditionalInfoJson: additionalInfo,
				Version:            1,
				CreatedByAssetId:   createdAssetIDs[i%len(createdAssetIDs)],
				UpdatedByAssetId:   createdAssetIDs[i%len(createdAssetIDs)],
				DueDate:            time.Now().Add(24 * time.Hour).Format(time.RFC3339),
				WatcherAssetIds:    []string{createdAssetIDs[(i+1)%len(createdAssetIDs)]},
			},
		}

		resp, err := casesClient.CreateCase(ctx, connect.NewRequest(caseReq))
		if err != nil {
			metrics = append(metrics, PerformanceMetrics{
				Operation:    "CreateCase",
				Duration:     0,
				Success:      false,
				ErrorMessage: err.Error(),
				RecordCount:  i,
			})
			t.Fatalf("Failed to create case %d: %v", i, err)
		}
		createdCaseIDs = append(createdCaseIDs, resp.Msg.Case_.Id)

		// Progress logging
		if (i+1)%25 == 0 {
			t.Logf("   Created %d/%d cases...", i+1, performanceTestCaseCount)
		}
	}
	createDuration := time.Since(start)
	metrics = append(metrics, PerformanceMetrics{
		Operation:   "CreateCase_Bulk",
		Duration:    createDuration,
		Success:     true,
		RecordCount: performanceTestCaseCount,
	})
	t.Logf("✅ Created %d cases in %v (avg: %v per case)", performanceTestCaseCount, createDuration, createDuration/time.Duration(performanceTestCaseCount))

	// Wait for database indexing
	t.Log("⏳ Waiting for database indexing...")
	time.Sleep(2 * time.Second)

	// Performance Tests
	t.Log("🔥 Running Performance Tests...")

	// Test 1: GetCase performance
	t.Run("GetCase_Performance", func(t *testing.T) {
		testCases := createdCaseIDs[:10]
		var totalDuration time.Duration
		var successCount int

		for _, caseID := range testCases {
			start := time.Now()
			_, err := casesClient.GetCase(ctx, connect.NewRequest(&cases.GetCaseRequest{Id: caseID}))
			duration := time.Since(start)
			totalDuration += duration

			success := err == nil
			if success {
				successCount++
			}

			metrics = append(metrics, PerformanceMetrics{
				Operation: "GetCase",
				Duration:  duration,
				Success:   success,
				ErrorMessage: func() string {
					if err != nil {
						return err.Error()
					}
					return ""
				}(),
				RecordCount: 1,
			})

			if duration > maxLatencyThreshold {
				t.Errorf("❌ GetCase latency too high: %v > %v", duration, maxLatencyThreshold)
			}
		}

		avgDuration := totalDuration / time.Duration(len(testCases))
		t.Logf("✅ GetCase average latency: %v (success rate: %d/%d)", avgDuration, successCount, len(testCases))
	})

	// Test 2: ListCases performance
	t.Run("ListCases_Performance", func(t *testing.T) {
		start := time.Now()
		resp, err := casesClient.ListCases(ctx, connect.NewRequest(&cases.ListCasesRequest{
			PageSize: 50,
		}))
		duration := time.Since(start)

		success := err == nil && resp != nil
		metrics = append(metrics, PerformanceMetrics{
			Operation: "ListCases",
			Duration:  duration,
			Success:   success,
			ErrorMessage: func() string {
				if err != nil {
					return err.Error()
				}
				return ""
			}(),
			RecordCount: func() int {
				if resp != nil {
					return len(resp.Msg.Cases)
				}
				return 0
			}(),
		})

		if duration > maxLatencyThreshold {
			t.Errorf("❌ ListCases latency too high: %v > %v", duration, maxLatencyThreshold)
		} else {
			t.Logf("✅ ListCases latency: %v (returned %d cases)", duration, len(resp.Msg.Cases))
		}
	})

	// Test 3: BatchGetCases performance
	t.Run("BatchGetCases_Performance", func(t *testing.T) {
		testCases := createdCaseIDs[:10]
		start := time.Now()
		resp, err := casesClient.BatchGetCases(ctx, connect.NewRequest(&cases.BatchGetCasesRequest{
			Ids: testCases,
		}))
		duration := time.Since(start)

		success := err == nil && resp != nil
		metrics = append(metrics, PerformanceMetrics{
			Operation: "BatchGetCases",
			Duration:  duration,
			Success:   success,
			ErrorMessage: func() string {
				if err != nil {
					return err.Error()
				}
				return ""
			}(),
			RecordCount: func() int {
				if resp != nil {
					return len(resp.Msg.Cases)
				}
				return 0
			}(),
		})

		if duration > maxLatencyThreshold {
			t.Errorf("❌ BatchGetCases latency too high: %v > %v", duration, maxLatencyThreshold)
		} else {
			t.Logf("✅ BatchGetCases latency: %v (returned %d cases)", duration, len(resp.Msg.Cases))
		}
	})

	// Test 4: UpdateCase performance
	t.Run("UpdateCase_Performance", func(t *testing.T) {
		testCases := createdCaseIDs[:5]
		var totalDuration time.Duration
		var successCount int

		for i, caseID := range testCases {
			// Get current case first
			getResp, err := casesClient.GetCase(ctx, connect.NewRequest(&cases.GetCaseRequest{Id: caseID}))
			if err != nil {
				t.Errorf("Failed to get case for update: %v", err)
				continue
			}

			// Update the case
			updatedCase := getResp.Msg
			updatedCase.Title = fmt.Sprintf("Updated Performance Test Case %d", i)
			updatedCase.Description = fmt.Sprintf("Updated performance testing case number %d", i)
			updatedCase.Priority = int32((i+1)%5 + 1) //nolint:gosec // Safe conversion for test data

			start := time.Now()
			_, err = casesClient.UpdateCase(ctx, connect.NewRequest(&cases.UpdateCaseRequest{
				Case_: updatedCase,
			}))
			duration := time.Since(start)
			totalDuration += duration

			success := err == nil
			if success {
				successCount++
			}

			metrics = append(metrics, PerformanceMetrics{
				Operation: "UpdateCase",
				Duration:  duration,
				Success:   success,
				ErrorMessage: func() string {
					if err != nil {
						return err.Error()
					}
					return ""
				}(),
				RecordCount: 1,
			})

			if duration > maxLatencyThreshold {
				t.Errorf("❌ UpdateCase latency too high: %v > %v", duration, maxLatencyThreshold)
			}
		}

		avgDuration := totalDuration / time.Duration(len(testCases))
		t.Logf("✅ UpdateCase average latency: %v (success rate: %d/%d)", avgDuration, successCount, len(testCases))
	})

	// Test 5: UpdateCaseStatus performance
	t.Run("UpdateCaseStatus_Performance", func(t *testing.T) {
		testCases := createdCaseIDs[:5]
		var totalDuration time.Duration
		var successCount int

		for i, caseID := range testCases {
			start := time.Now()
			_, err := casesClient.UpdateCaseStatus(ctx, connect.NewRequest(&cases.UpdateCaseStatusRequest{
				CaseId: caseID,
				Status: cases.CaseStatus_CASE_STATUS_OPEN,
				Note:   fmt.Sprintf("Status updated for performance test %d", i),
			}))
			duration := time.Since(start)
			totalDuration += duration

			success := err == nil
			if success {
				successCount++
			}

			metrics = append(metrics, PerformanceMetrics{
				Operation: "UpdateCaseStatus",
				Duration:  duration,
				Success:   success,
				ErrorMessage: func() string {
					if err != nil {
						return err.Error()
					}
					return ""
				}(),
				RecordCount: 1,
			})

			if duration > maxLatencyThreshold {
				t.Errorf("❌ UpdateCaseStatus latency too high: %v > %v", duration, maxLatencyThreshold)
			}
		}

		avgDuration := totalDuration / time.Duration(len(testCases))
		t.Logf("✅ UpdateCaseStatus average latency: %v (success rate: %d/%d)", avgDuration, successCount, len(testCases))
	})

	// Test 6: AddCaseUpdate performance
	t.Run("AddCaseUpdate_Performance", func(t *testing.T) {
		testCases := createdCaseIDs[:5]
		var totalDuration time.Duration
		var successCount int

		for i, caseID := range testCases {
			updateEntry := &cases.CaseUpdateEntry{
				Message:   fmt.Sprintf("Performance test update %d", i),
				EventTime: time.Now().Format(time.RFC3339),
				UpdaterId: createdAssetIDs[i%len(createdAssetIDs)],
				EventType: "PERFORMANCE_TEST",
			}

			start := time.Now()
			_, err := casesClient.AddCaseUpdate(ctx, connect.NewRequest(&cases.AddCaseUpdateRequest{
				CaseId: caseID,
				Update: updateEntry,
			}))
			duration := time.Since(start)
			totalDuration += duration

			success := err == nil
			if success {
				successCount++
			}

			metrics = append(metrics, PerformanceMetrics{
				Operation: "AddCaseUpdate",
				Duration:  duration,
				Success:   success,
				ErrorMessage: func() string {
					if err != nil {
						return err.Error()
					}
					return ""
				}(),
				RecordCount: 1,
			})

			if duration > maxLatencyThreshold {
				t.Errorf("❌ AddCaseUpdate latency too high: %v > %v", duration, maxLatencyThreshold)
			}
		}

		avgDuration := totalDuration / time.Duration(len(testCases))
		t.Logf("✅ AddCaseUpdate average latency: %v (success rate: %d/%d)", avgDuration, successCount, len(testCases))
	})

	// Test 7: AddCaseTag performance
	t.Run("AddCaseTag_Performance", func(t *testing.T) {
		testCases := createdCaseIDs[:5]
		var totalDuration time.Duration
		var successCount int

		for i, caseID := range testCases {
			start := time.Now()
			_, err := casesClient.AddCaseTag(ctx, connect.NewRequest(&cases.AddCaseTagRequest{
				CaseId: caseID,
				Tag:    fmt.Sprintf("performance-tag-%d", i),
			}))
			duration := time.Since(start)
			totalDuration += duration

			success := err == nil
			if success {
				successCount++
			}

			metrics = append(metrics, PerformanceMetrics{
				Operation: "AddCaseTag",
				Duration:  duration,
				Success:   success,
				ErrorMessage: func() string {
					if err != nil {
						return err.Error()
					}
					return ""
				}(),
				RecordCount: 1,
			})

			if duration > maxLatencyThreshold {
				t.Errorf("❌ AddCaseTag latency too high: %v > %v", duration, maxLatencyThreshold)
			}
		}

		avgDuration := totalDuration / time.Duration(len(testCases))
		t.Logf("✅ AddCaseTag average latency: %v (success rate: %d/%d)", avgDuration, successCount, len(testCases))
	})

	// Test 8: AssociateAssetToCase performance
	t.Run("AssociateAssetToCase_Performance", func(t *testing.T) {
		testCases := createdCaseIDs[:5]
		var totalDuration time.Duration
		var successCount int

		for i, caseID := range testCases {
			association := &cases.CaseAssetAssociation{
				CaseId:          caseID,
				AssetId:         createdAssetIDs[i%len(createdAssetIDs)],
				AssociationType: cases.CaseAssetAssociationType_ASSET_ASSOCIATION_TYPE_INVESTIGATOR,
				Notes:           fmt.Sprintf("Performance test association %d", i),
				AssignerAssetId: createdAssetIDs[(i+1)%len(createdAssetIDs)],
			}

			start := time.Now()
			_, err := casesClient.AssociateAssetToCase(ctx, connect.NewRequest(&cases.AssociateAssetToCaseRequest{
				CaseId:      caseID,
				Association: association,
			}))
			duration := time.Since(start)
			totalDuration += duration

			success := err == nil
			if success {
				successCount++
			}

			metrics = append(metrics, PerformanceMetrics{
				Operation: "AssociateAssetToCase",
				Duration:  duration,
				Success:   success,
				ErrorMessage: func() string {
					if err != nil {
						return err.Error()
					}
					return ""
				}(),
				RecordCount: 1,
			})

			if duration > maxLatencyThreshold {
				t.Errorf("❌ AssociateAssetToCase latency too high: %v > %v", duration, maxLatencyThreshold)
			}
		}

		avgDuration := totalDuration / time.Duration(len(testCases))
		t.Logf("✅ AssociateAssetToCase average latency: %v (success rate: %d/%d)", avgDuration, successCount, len(testCases))
	})

	// Test 9: SearchCases performance
	t.Run("SearchCases_Performance", func(t *testing.T) {
		searchTests := []struct {
			name    string
			request *cases.SearchCasesRequest
		}{
			{
				name: "SearchByType",
				request: &cases.SearchCasesRequest{
					Type:     []cases.CaseType{cases.CaseType_CASE_TYPE_SECURITY_INCIDENT},
					PageSize: 50,
				},
			},
			{
				name: "SearchByStatus",
				request: &cases.SearchCasesRequest{
					Status:   []cases.CaseStatus{cases.CaseStatus_CASE_STATUS_NEW, cases.CaseStatus_CASE_STATUS_OPEN},
					PageSize: 50,
				},
			},
			{
				name: "SearchByQuery",
				request: &cases.SearchCasesRequest{
					Query:    "Performance Test",
					PageSize: 50,
				},
			},
			{
				name: "SearchByTags",
				request: &cases.SearchCasesRequest{
					Tags:     []string{"performance", "test"},
					PageSize: 50,
				},
			},
		}

		for _, test := range searchTests {
			t.Run(test.name, func(t *testing.T) {
				start := time.Now()
				resp, err := casesClient.SearchCases(ctx, connect.NewRequest(test.request))
				duration := time.Since(start)

				success := err == nil && resp != nil
				metrics = append(metrics, PerformanceMetrics{
					Operation: fmt.Sprintf("SearchCases_%s", test.name),
					Duration:  duration,
					Success:   success,
					ErrorMessage: func() string {
						if err != nil {
							return err.Error()
						}
						return ""
					}(),
					RecordCount: func() int {
						if resp != nil {
							return len(resp.Msg.Cases)
						}
						return 0
					}(),
				})

				if duration > maxLatencyThreshold {
					t.Errorf("❌ %s latency too high: %v > %v", test.name, duration, maxLatencyThreshold)
				} else {
					t.Logf("✅ %s latency: %v (returned %d cases)", test.name, duration, func() int {
						if resp != nil {
							return len(resp.Msg.Cases)
						}
						return 0
					}())
				}
			})
		}
	})

	// Test 10: ListCasesBySituationId performance
	t.Run("ListCasesBySituationId_Performance", func(t *testing.T) {
		testSituations := createdSituationIDs[:5]
		var totalDuration time.Duration
		var successCount int

		for _, situationID := range testSituations {
			start := time.Now()
			resp, err := casesClient.ListCasesBySituationId(ctx, connect.NewRequest(&cases.ListCasesBySituationIdRequest{
				SituationId: situationID,
				PageSize:    50,
			}))
			duration := time.Since(start)
			totalDuration += duration

			success := err == nil
			if success {
				successCount++
			}

			metrics = append(metrics, PerformanceMetrics{
				Operation: "ListCasesBySituationId",
				Duration:  duration,
				Success:   success,
				ErrorMessage: func() string {
					if err != nil {
						return err.Error()
					}
					return ""
				}(),
				RecordCount: func() int {
					if resp != nil {
						return len(resp.Msg.Cases)
					}
					return 0
				}(),
			})

			if duration > maxLatencyThreshold {
				t.Errorf("❌ ListCasesBySituationId latency too high: %v > %v", duration, maxLatencyThreshold)
			}
		}

		avgDuration := totalDuration / time.Duration(len(testSituations))
		t.Logf("✅ ListCasesBySituationId average latency: %v (success rate: %d/%d)", avgDuration, successCount, len(testSituations))
	})

	// Test 11: AddWatcher performance
	t.Run("AddWatcher_Performance", func(t *testing.T) {
		testCases := createdCaseIDs[:5]
		var totalDuration time.Duration
		var successCount int

		for i, caseID := range testCases {
			start := time.Now()
			_, err := casesClient.AddWatcher(ctx, connect.NewRequest(&cases.AddWatcherRequest{
				CaseId:  caseID,
				AssetId: createdAssetIDs[(i+2)%len(createdAssetIDs)],
			}))
			duration := time.Since(start)
			totalDuration += duration

			success := err == nil
			if success {
				successCount++
			}

			metrics = append(metrics, PerformanceMetrics{
				Operation: "AddWatcher",
				Duration:  duration,
				Success:   success,
				ErrorMessage: func() string {
					if err != nil {
						return err.Error()
					}
					return ""
				}(),
				RecordCount: 1,
			})

			if duration > maxLatencyThreshold {
				t.Errorf("❌ AddWatcher latency too high: %v > %v", duration, maxLatencyThreshold)
			}
		}

		avgDuration := totalDuration / time.Duration(len(testCases))
		t.Logf("✅ AddWatcher average latency: %v (success rate: %d/%d)", avgDuration, successCount, len(testCases))
	})

	// Test 12: ListCaseStatusHistory performance
	t.Run("ListCaseStatusHistory_Performance", func(t *testing.T) {
		testCases := createdCaseIDs[:5]
		var totalDuration time.Duration
		var successCount int

		for _, caseID := range testCases {
			start := time.Now()
			resp, err := casesClient.ListCaseStatusHistory(ctx, connect.NewRequest(&cases.ListCaseStatusHistoryRequest{
				CaseId:   caseID,
				PageSize: 100,
			}))
			duration := time.Since(start)
			totalDuration += duration

			success := err == nil
			if success {
				successCount++
			}

			metrics = append(metrics, PerformanceMetrics{
				Operation: "ListCaseStatusHistory",
				Duration:  duration,
				Success:   success,
				ErrorMessage: func() string {
					if err != nil {
						return err.Error()
					}
					return ""
				}(),
				RecordCount: func() int {
					if resp != nil {
						return len(resp.Msg.StatusUpdates)
					}
					return 0
				}(),
			})

			if duration > maxLatencyThreshold {
				t.Errorf("❌ ListCaseStatusHistory latency too high: %v > %v", duration, maxLatencyThreshold)
			}
		}

		avgDuration := totalDuration / time.Duration(len(testCases))
		t.Logf("✅ ListCaseStatusHistory average latency: %v (success rate: %d/%d)", avgDuration, successCount, len(testCases))
	})

	t.Log("🎯 Cases Basic Performance Test Completed")
}

func printPerformanceSummary(t *testing.T, metrics []PerformanceMetrics) {
	t.Log("\n" + strings.Repeat("=", 80))
	t.Log("📊 PERFORMANCE SUMMARY")
	t.Log(strings.Repeat("=", 80))

	operationStats := make(map[string][]time.Duration)
	var totalOperations, successfulOperations int

	for _, metric := range metrics {
		if metric.Success {
			operationStats[metric.Operation] = append(operationStats[metric.Operation], metric.Duration)
			successfulOperations++
		}
		totalOperations++
	}

	t.Logf("Overall Success Rate: %d/%d (%.1f%%)", successfulOperations, totalOperations, float64(successfulOperations)/float64(totalOperations)*100)
	t.Log("")

	for operation, durations := range operationStats {
		if len(durations) == 0 {
			continue
		}

		var total time.Duration
		min := durations[0]
		max := durations[0]

		for _, d := range durations {
			total += d
			if d < min {
				min = d
			}
			if d > max {
				max = d
			}
		}

		avg := total / time.Duration(len(durations))
		status := "✅"
		if avg > maxLatencyThreshold {
			status = "❌"
		}

		t.Logf("%s %-35s | Count: %3d | Avg: %8v | Min: %8v | Max: %8v",
			status, operation, len(durations), avg, min, max)
	}

	t.Log(strings.Repeat("=", 80))
	t.Logf("Latency Threshold: %v", maxLatencyThreshold)
	t.Log(strings.Repeat("=", 80))
}
