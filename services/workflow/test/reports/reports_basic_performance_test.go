package test

import (
	"context"
	"fmt"
	"net/http"
	"strings"
	"testing"
	"time"

	reports "proto/hero/reports/v2"
	reportsConnect "proto/hero/reports/v2/reportsconnect"

	assets "proto/hero/assets/v2"
	assetsConnect "proto/hero/assets/v2/assetsconnect"

	situations "proto/hero/situations/v2"
	situationsConnect "proto/hero/situations/v2/situationsconnect"

	"connectrpc.com/connect"
)

const (
	performanceTestReportCount     = 100
	performanceTestAssetCount      = 10
	performanceTestSupervisorCount = 3
	performanceTestSituationCount  = 5
	maxLatencyThreshold            = 200 * time.Millisecond
	bulkOperationThreshold         = 2000 * time.Millisecond
)

type PerformanceMetrics struct {
	Operation    string
	Duration     time.Duration
	Success      bool
	ErrorMessage string
	RecordCount  int
}

func TestReports_BasicPerformance(t *testing.T) {
	if testing.Short() {
		t.Skip("skipping performance tests in short mode")
	}

	t.Logf("🚀 Starting Reports Basic Performance Test with %d reports, %d assets, %d supervisors, %d situations",
		performanceTestReportCount, performanceTestAssetCount, performanceTestSupervisorCount, performanceTestSituationCount)

	httpClient := http.DefaultClient
	AddAuthHeader(httpClient)

	ctx, cancel := context.WithTimeout(context.Background(), 600*time.Second)
	defer cancel()

	// Initialize clients
	reportsClient := reportsConnect.NewReportServiceClient(httpClient, ServiceBaseURL)
	assetsClient := assetsConnect.NewAssetRegistryServiceClient(httpClient, ServiceBaseURL)
	situationsClient := situationsConnect.NewSituationServiceClient(httpClient, ServiceBaseURL)

	var metrics []PerformanceMetrics
	var createdReportIDs []string
	var createdAssetIDs []string
	var createdSupervisorIDs []string
	var createdSituationIDs []string
	var createdSectionIDs []string
	var createdCommentIDs []string
	var createdRelationIDs []string

	// Cleanup function
	defer func() {
		t.Log("🧹 Starting cleanup...")

		// Delete comments first
		for _, commentID := range createdCommentIDs {
			if _, err := reportsClient.DeleteComment(ctx, connect.NewRequest(&reports.DeleteCommentRequest{CommentId: commentID})); err != nil {
				t.Logf("cleanup: failed to delete comment %s: %v", commentID, err)
			}
		}

		// Delete relations
		for i, relationID := range createdRelationIDs {
			if i < len(createdReportIDs) {
				if _, err := reportsClient.DeleteRelation(ctx, connect.NewRequest(&reports.DeleteRelationRequest{
					ReportId:   createdReportIDs[i],
					RelationId: relationID,
				})); err != nil {
					t.Logf("cleanup: failed to delete relation %s: %v", relationID, err)
				}
			}
		}

		// Delete sections
		for i, sectionID := range createdSectionIDs {
			if i < len(createdReportIDs) {
				if _, err := reportsClient.DeleteReportSection(ctx, connect.NewRequest(&reports.DeleteReportSectionRequest{
					ReportId:  createdReportIDs[i],
					SectionId: sectionID,
				})); err != nil {
					t.Logf("cleanup: failed to delete section %s: %v", sectionID, err)
				}
			}
		}

		// Delete reports
		for _, reportID := range createdReportIDs {
			if _, err := reportsClient.DeleteReport(ctx, connect.NewRequest(&reports.DeleteReportRequest{Id: reportID})); err != nil {
				t.Logf("cleanup: failed to delete report %s: %v", reportID, err)
			}
		}

		// Delete situations
		for _, situationID := range createdSituationIDs {
			if _, err := situationsClient.DeleteSituation(ctx, connect.NewRequest(&situations.DeleteSituationRequest{Id: situationID})); err != nil {
				t.Logf("cleanup: failed to delete situation %s: %v", situationID, err)
			}
		}

		// Delete assets
		for _, assetID := range createdAssetIDs {
			if _, err := assetsClient.DeleteAsset(ctx, connect.NewRequest(&assets.DeleteAssetRequest{Id: assetID})); err != nil {
				t.Logf("cleanup: failed to delete asset %s: %v", assetID, err)
			}
		}

		// Delete supervisor assets
		for _, supervisorID := range createdSupervisorIDs {
			if _, err := assetsClient.DeleteAsset(ctx, connect.NewRequest(&assets.DeleteAssetRequest{Id: supervisorID})); err != nil {
				t.Logf("cleanup: failed to delete supervisor %s: %v", supervisorID, err)
			}
		}

		t.Log("✅ Cleanup completed")

		// Print performance summary
		printPerformanceSummary(t, metrics)
	}()

	// Step 1: Create supporting assets
	t.Log("📋 Step 1: Creating performance test assets...")
	start := time.Now()
	for i := 0; i < performanceTestAssetCount; i++ {
		assetReq := &assets.CreateAssetRequest{
			Asset: &assets.Asset{
				Name:               fmt.Sprintf("Performance Test Asset %d", i),
				Type:               assets.AssetType_ASSET_TYPE_MEMBER,
				Status:             assets.AssetStatus_ASSET_STATUS_AVAILABLE,
				CognitoJwtSub:      fmt.Sprintf("perf-test-asset-%d", i),
				ContactNo:          fmt.Sprintf("+1555%07d", i),
				ContactEmail:       fmt.Sprintf("<EMAIL>", i),
				Latitude:           37.7749 + float64(i)*0.001,
				Longitude:          -122.4194 + float64(i)*0.001,
				OrgId:              1,
				AdditionalInfoJson: fmt.Sprintf(`{"address": "%d Performance Test Street, Test City, TS 12345"}`, i),
			},
		}

		resp, err := assetsClient.CreateAsset(ctx, connect.NewRequest(assetReq))
		if err != nil {
			t.Fatalf("Failed to create asset %d: %v", i, err)
		}
		createdAssetIDs = append(createdAssetIDs, resp.Msg.Asset.Id)

		// Progress logging
		if (i+1)%5 == 0 {
			t.Logf("   Created %d/%d assets...", i+1, performanceTestAssetCount)
		}
	}
	assetCreateDuration := time.Since(start)
	t.Logf("✅ Created %d assets in %v (avg: %v per asset)", performanceTestAssetCount, assetCreateDuration, assetCreateDuration/time.Duration(performanceTestAssetCount))

	// Step 1.5: Create supervisor assets for review workflow
	t.Log("📋 Step 1.5: Creating performance test supervisor assets...")
	start = time.Now()
	for i := 0; i < performanceTestSupervisorCount; i++ {
		supervisorReq := &assets.CreateAssetRequest{
			Asset: &assets.Asset{
				Name:               fmt.Sprintf("Performance Test Supervisor %d", i),
				Type:               assets.AssetType_ASSET_TYPE_SUPERVISOR,
				Status:             assets.AssetStatus_ASSET_STATUS_AVAILABLE,
				CognitoJwtSub:      fmt.Sprintf("perf-test-supervisor-%d", i),
				ContactNo:          fmt.Sprintf("+1555%07d", i+1000),
				ContactEmail:       fmt.Sprintf("<EMAIL>", i),
				Latitude:           37.7749 + float64(i)*0.001,
				Longitude:          -122.4194 + float64(i)*0.001,
				OrgId:              1,
				AdditionalInfoJson: fmt.Sprintf(`{"address": "%d Supervisor Test Street, Test City, TS 12345", "role": "supervisor"}`, i),
			},
		}

		resp, err := assetsClient.CreateAsset(ctx, connect.NewRequest(supervisorReq))
		if err != nil {
			t.Fatalf("Failed to create supervisor %d: %v", i, err)
		}
		createdSupervisorIDs = append(createdSupervisorIDs, resp.Msg.Asset.Id)
	}
	supervisorCreateDuration := time.Since(start)
	t.Logf("✅ Created %d supervisors in %v (avg: %v per supervisor)", performanceTestSupervisorCount, supervisorCreateDuration, supervisorCreateDuration/time.Duration(performanceTestSupervisorCount))

	// Step 2: Create supporting situations
	t.Log("📋 Step 2: Creating performance test situations...")
	start = time.Now()
	for i := 0; i < performanceTestSituationCount; i++ {
		assetIndex := i % len(createdAssetIDs)
		situationReq := &situations.CreateSituationRequest{
			Situation: &situations.Situation{
				Title:              fmt.Sprintf("Performance Test Situation for Reports %d", i),
				Description:        fmt.Sprintf("Performance testing situation for reports number %d", i),
				Status:             situations.SituationStatus_SITUATION_STATUS_CREATED,
				Type:               situations.SituationType_SITUATION_TYPE_OTHER,
				Priority:           int32(1 + (i % 3)), //nolint:gosec // Safe conversion for test data
				TriggerSource:      situations.TriggerSource_TRIGGER_SOURCE_WEB_PORTAL,
				ReporterId:         createdAssetIDs[assetIndex],
				ReporterName:       fmt.Sprintf("Performance Test Reporter %d", i),
				ContactNo:          fmt.Sprintf("+1555%07d", i+2000),
				ContactEmail:       fmt.Sprintf("<EMAIL>", i),
				Address:            fmt.Sprintf("%d Report Test Avenue, Test City, TS %05d", i, 30000+i),
				Latitude:           37.7749 + float64(i)*0.003,
				Longitude:          -122.4194 + float64(i)*0.003,
				DueTime:            time.Now().Add(time.Duration(i+1) * time.Hour).Format(time.RFC3339),
				IncidentTime:       time.Now().Add(-time.Duration(i+1) * time.Minute).Format(time.RFC3339),
				AdditionalInfoJson: fmt.Sprintf(`{"test_situation_number": %d, "batch": "reports_performance_test"}`, i),
				Tags:               []string{fmt.Sprintf("reports-performance-test-%d", i), "automated-test"},
			},
		}

		resp, err := situationsClient.CreateSituation(ctx, connect.NewRequest(situationReq))
		if err != nil {
			t.Fatalf("Failed to create situation %d: %v", i, err)
		}
		createdSituationIDs = append(createdSituationIDs, resp.Msg.Situation.Id)
	}
	situationCreateDuration := time.Since(start)
	t.Logf("✅ Created %d situations in %v (avg: %v per situation)", performanceTestSituationCount, situationCreateDuration, situationCreateDuration/time.Duration(performanceTestSituationCount))

	// Step 3: Create performance test reports
	t.Log("📋 Step 3: Creating performance test reports...")
	start = time.Now()

	reportStatuses := []reports.ReportStatus{
		reports.ReportStatus_REPORT_STATUS_ASSIGNED,
		reports.ReportStatus_REPORT_STATUS_IN_PROGRESS,
		reports.ReportStatus_REPORT_STATUS_ASSIGNED, // Avoid SUBMITTED_FOR_REVIEW to prevent auto-review assignment during creation
		reports.ReportStatus_REPORT_STATUS_IN_PROGRESS,
	}

	reportTypes := []reports.ReportType{
		reports.ReportType_REPORT_TYPE_INCIDENT_PRIMARY,
		reports.ReportType_REPORT_TYPE_INCIDENT_SUPPLEMENTAL,
		reports.ReportType_REPORT_TYPE_INCIDENT_SUPPLEMENTAL_INVESTIGATIVE,
	}

	for i := 0; i < performanceTestReportCount; i++ {
		assetIndex := i % len(createdAssetIDs)
		situationIndex := i % len(createdSituationIDs)
		reportStatus := reportStatuses[i%len(reportStatuses)]
		reportType := reportTypes[i%len(reportTypes)]

		currentTime := time.Now().Format(time.RFC3339)
		reportReq := &reports.CreateReportRequest{
			Report: &reports.Report{
				OrgId:            1,
				AuthorAssetId:    createdAssetIDs[assetIndex],
				Title:            fmt.Sprintf("Performance Test Report %d", i),
				Status:           reportStatus,
				ReportType:       reportType,
				SituationId:      createdSituationIDs[situationIndex],
				WatcherAssetIds:  []string{createdAssetIDs[assetIndex]},
				ResourceType:     "REPORT",
				CreatedByAssetId: createdAssetIDs[assetIndex],
				AssignedAt:       currentTime,
			},
		}

		resp, err := reportsClient.CreateReport(ctx, connect.NewRequest(reportReq))
		if err != nil {
			metrics = append(metrics, PerformanceMetrics{
				Operation:    "CreateReport",
				Duration:     0,
				Success:      false,
				ErrorMessage: err.Error(),
				RecordCount:  i,
			})
			t.Fatalf("Failed to create report %d: %v", i, err)
		}
		createdReportIDs = append(createdReportIDs, resp.Msg.Report.Id)

		// Progress logging
		if (i+1)%10 == 0 {
			t.Logf("   Created %d/%d reports...", i+1, performanceTestReportCount)
		}
	}
	createDuration := time.Since(start)
	metrics = append(metrics, PerformanceMetrics{
		Operation:   "CreateReport_Bulk",
		Duration:    createDuration,
		Success:     true,
		RecordCount: performanceTestReportCount,
	})
	t.Logf("✅ Created %d reports in %v (avg: %v per report)", performanceTestReportCount, createDuration, createDuration/time.Duration(performanceTestReportCount))

	// Wait for database indexing
	t.Log("⏳ Waiting for database indexing...")
	time.Sleep(2 * time.Second)

	// Performance Tests
	t.Log("🔥 Running Performance Tests...")

	// Test 1: GetReport performance
	t.Run("GetReport_Performance", func(t *testing.T) {
		testReports := createdReportIDs[:10]
		var totalDuration time.Duration
		var successCount int

		for _, reportID := range testReports {
			start := time.Now()
			_, err := reportsClient.GetReport(ctx, connect.NewRequest(&reports.GetReportRequest{Id: reportID}))
			duration := time.Since(start)
			totalDuration += duration

			success := err == nil
			if success {
				successCount++
			}

			metrics = append(metrics, PerformanceMetrics{
				Operation: "GetReport",
				Duration:  duration,
				Success:   success,
				ErrorMessage: func() string {
					if err != nil {
						return err.Error()
					}
					return ""
				}(),
				RecordCount: 1,
			})

			if duration > maxLatencyThreshold {
				t.Errorf("❌ GetReport latency too high: %v > %v", duration, maxLatencyThreshold)
			}
		}

		avgDuration := totalDuration / time.Duration(len(testReports))
		t.Logf("✅ GetReport average latency: %v (success rate: %d/%d)", avgDuration, successCount, len(testReports))
	})

	// Test 2: ListReports performance
	t.Run("ListReports_Performance", func(t *testing.T) {
		start := time.Now()
		resp, err := reportsClient.ListReports(ctx, connect.NewRequest(&reports.ListReportsRequest{
			PageSize: 25,
			OrgId:    1,
		}))
		duration := time.Since(start)

		success := err == nil && resp != nil
		metrics = append(metrics, PerformanceMetrics{
			Operation: "ListReports",
			Duration:  duration,
			Success:   success,
			ErrorMessage: func() string {
				if err != nil {
					return err.Error()
				}
				return ""
			}(),
			RecordCount: func() int {
				if resp != nil {
					return len(resp.Msg.Reports)
				}
				return 0
			}(),
		})

		if duration > maxLatencyThreshold {
			t.Errorf("❌ ListReports latency too high: %v > %v", duration, maxLatencyThreshold)
		} else {
			t.Logf("✅ ListReports latency: %v (returned %d reports)", duration, len(resp.Msg.Reports))
		}
	})

	// Test 3: UpdateReport performance
	t.Run("UpdateReport_Performance", func(t *testing.T) {
		testReports := createdReportIDs[:5]
		var totalDuration time.Duration
		var successCount int

		for i, reportID := range testReports {
			// Get current report first
			getResp, err := reportsClient.GetReport(ctx, connect.NewRequest(&reports.GetReportRequest{Id: reportID}))
			if err != nil {
				t.Errorf("Failed to get report for update: %v", err)
				continue
			}

			// Update the report
			updatedReport := getResp.Msg
			updatedReport.Title = fmt.Sprintf("Updated Performance Test Report %d", i)

			start := time.Now()
			_, err = reportsClient.UpdateReport(ctx, connect.NewRequest(&reports.UpdateReportRequest{
				Report: updatedReport,
			}))
			duration := time.Since(start)
			totalDuration += duration

			success := err == nil
			if success {
				successCount++
			}

			metrics = append(metrics, PerformanceMetrics{
				Operation: "UpdateReport",
				Duration:  duration,
				Success:   success,
				ErrorMessage: func() string {
					if err != nil {
						return err.Error()
					}
					return ""
				}(),
				RecordCount: 1,
			})

			if duration > maxLatencyThreshold {
				t.Errorf("❌ UpdateReport latency too high: %v > %v", duration, maxLatencyThreshold)
			}
		}

		avgDuration := totalDuration / time.Duration(len(testReports))
		t.Logf("✅ UpdateReport average latency: %v (success rate: %d/%d)", avgDuration, successCount, len(testReports))
	})

	// Test 4: SearchReports performance
	t.Run("SearchReports_Performance", func(t *testing.T) {
		searchTests := []struct {
			name    string
			request *reports.SearchReportsRequest
		}{
			{
				name: "BasicTextSearch",
				request: &reports.SearchReportsRequest{
					Query:     "Performance Test",
					PageSize:  25,
					OrderBy:   reports.SearchOrderBy_SEARCH_ORDER_BY_RELEVANCE,
					Ascending: false,
				},
			},
			{
				name: "FilterByStatus",
				request: &reports.SearchReportsRequest{
					Status:    []reports.ReportStatus{reports.ReportStatus_REPORT_STATUS_ASSIGNED},
					PageSize:  25,
					OrderBy:   reports.SearchOrderBy_SEARCH_ORDER_BY_CREATED_AT,
					Ascending: false,
				},
			},
			{
				name: "FilterByReportType",
				request: &reports.SearchReportsRequest{
					ReportTypes: []reports.ReportType{reports.ReportType_REPORT_TYPE_INCIDENT_PRIMARY},
					PageSize:    25,
					OrderBy:     reports.SearchOrderBy_SEARCH_ORDER_BY_UPDATED_AT,
					Ascending:   false,
				},
			},
			{
				name: "FilterBySituationId",
				request: &reports.SearchReportsRequest{
					SituationIds: []string{createdSituationIDs[0]},
					PageSize:     25,
					OrderBy:      reports.SearchOrderBy_SEARCH_ORDER_BY_CREATED_AT,
					Ascending:    false,
				},
			},
			{
				name: "ComplexCombinedFilter",
				request: &reports.SearchReportsRequest{
					Query:       "Performance",
					Status:      []reports.ReportStatus{reports.ReportStatus_REPORT_STATUS_ASSIGNED, reports.ReportStatus_REPORT_STATUS_IN_PROGRESS},
					ReportTypes: []reports.ReportType{reports.ReportType_REPORT_TYPE_INCIDENT_PRIMARY},
					PageSize:    25,
					OrderBy:     reports.SearchOrderBy_SEARCH_ORDER_BY_RELEVANCE,
					Ascending:   false,
				},
			},
		}

		for _, test := range searchTests {
			t.Run(test.name, func(t *testing.T) {
				start := time.Now()
				resp, err := reportsClient.SearchReports(ctx, connect.NewRequest(test.request))
				duration := time.Since(start)

				success := err == nil && resp != nil
				metrics = append(metrics, PerformanceMetrics{
					Operation: fmt.Sprintf("SearchReports_%s", test.name),
					Duration:  duration,
					Success:   success,
					ErrorMessage: func() string {
						if err != nil {
							return err.Error()
						}
						return ""
					}(),
					RecordCount: func() int {
						if resp != nil {
							return len(resp.Msg.Reports)
						}
						return 0
					}(),
				})

				if duration > maxLatencyThreshold {
					t.Errorf("❌ %s latency too high: %v > %v", test.name, duration, maxLatencyThreshold)
				} else {
					t.Logf("✅ %s latency: %v (returned %d reports)", test.name, duration, func() int {
						if resp != nil {
							return len(resp.Msg.Reports)
						}
						return 0
					}())
				}
			})
		}
	})

	// Test 5: BatchGetReports performance
	t.Run("BatchGetReports_Performance", func(t *testing.T) {
		batchSize := 10
		testReportIDs := createdReportIDs[:batchSize]

		start := time.Now()
		resp, err := reportsClient.BatchGetReports(ctx, connect.NewRequest(&reports.BatchGetReportsRequest{
			ReportIds: testReportIDs,
		}))
		duration := time.Since(start)

		success := err == nil && resp != nil
		metrics = append(metrics, PerformanceMetrics{
			Operation: "BatchGetReports",
			Duration:  duration,
			Success:   success,
			ErrorMessage: func() string {
				if err != nil {
					return err.Error()
				}
				return ""
			}(),
			RecordCount: func() int {
				if resp != nil {
					return len(resp.Msg.Reports)
				}
				return 0
			}(),
		})

		if duration > maxLatencyThreshold {
			t.Errorf("❌ BatchGetReports latency too high: %v > %v", duration, maxLatencyThreshold)
		} else {
			t.Logf("✅ BatchGetReports latency: %v (returned %d reports)", duration, func() int {
				if resp != nil {
					return len(resp.Msg.Reports)
				}
				return 0
			}())
		}
	})

	// Test 6: Report Section CRUD performance
	t.Run("ReportSection_CRUD_Performance", func(t *testing.T) {
		for i, reportID := range createdReportIDs[:5] {
			// Test CreateReportSection
			t.Run(fmt.Sprintf("CreateReportSection_%d", i), func(t *testing.T) {
				narrativeSection := &reports.ReportSection{
					Type: reports.SectionType_SECTION_TYPE_NARRATIVE,
					Content: &reports.ReportSection_Narrative{
						Narrative: &reports.NarrativeContent{
							RichText: fmt.Sprintf("<p>Performance test narrative section %d with comprehensive content for testing purposes.</p>", i),
						},
					},
				}

				start := time.Now()
				resp, err := reportsClient.CreateReportSection(ctx, connect.NewRequest(&reports.CreateReportSectionRequest{
					ReportId: reportID,
					Section:  narrativeSection,
				}))
				duration := time.Since(start)

				success := err == nil && resp != nil
				if success && resp != nil {
					createdSectionIDs = append(createdSectionIDs, resp.Msg.Id)
				}

				metrics = append(metrics, PerformanceMetrics{
					Operation: "CreateReportSection",
					Duration:  duration,
					Success:   success,
					ErrorMessage: func() string {
						if err != nil {
							return err.Error()
						}
						return ""
					}(),
					RecordCount: 1,
				})

				if duration > maxLatencyThreshold {
					t.Errorf("❌ CreateReportSection latency too high: %v > %v", duration, maxLatencyThreshold)
				} else {
					t.Logf("✅ CreateReportSection latency: %v", duration)
				}
			})

			// Test GetReportSection
			if len(createdSectionIDs) > i {
				t.Run(fmt.Sprintf("GetReportSection_%d", i), func(t *testing.T) {
					start := time.Now()
					_, err := reportsClient.GetReportSection(ctx, connect.NewRequest(&reports.GetReportSectionRequest{
						ReportId:  reportID,
						SectionId: createdSectionIDs[i],
					}))
					duration := time.Since(start)

					success := err == nil
					metrics = append(metrics, PerformanceMetrics{
						Operation: "GetReportSection",
						Duration:  duration,
						Success:   success,
						ErrorMessage: func() string {
							if err != nil {
								return err.Error()
							}
							return ""
						}(),
						RecordCount: 1,
					})

					if duration > maxLatencyThreshold {
						t.Errorf("❌ GetReportSection latency too high: %v > %v", duration, maxLatencyThreshold)
					} else {
						t.Logf("✅ GetReportSection latency: %v", duration)
					}
				})
			}
		}

		// Test ListReportSections
		if len(createdReportIDs) > 0 {
			t.Run("ListReportSections", func(t *testing.T) {
				start := time.Now()
				resp, err := reportsClient.ListReportSections(ctx, connect.NewRequest(&reports.ListReportSectionsRequest{
					ReportId: createdReportIDs[0],
				}))
				duration := time.Since(start)

				success := err == nil && resp != nil
				metrics = append(metrics, PerformanceMetrics{
					Operation: "ListReportSections",
					Duration:  duration,
					Success:   success,
					ErrorMessage: func() string {
						if err != nil {
							return err.Error()
						}
						return ""
					}(),
					RecordCount: func() int {
						if resp != nil {
							return len(resp.Msg.Sections)
						}
						return 0
					}(),
				})

				if duration > maxLatencyThreshold {
					t.Errorf("❌ ListReportSections latency too high: %v > %v", duration, maxLatencyThreshold)
				} else {
					t.Logf("✅ ListReportSections latency: %v (returned %d sections)", duration, func() int {
						if resp != nil {
							return len(resp.Msg.Sections)
						}
						return 0
					}())
				}
			})
		}
	})

	// Test 7: Comment CRUD performance
	t.Run("Comment_CRUD_Performance", func(t *testing.T) {
		for i, reportID := range createdReportIDs[:3] {
			// Test AddComment
			t.Run(fmt.Sprintf("AddComment_%d", i), func(t *testing.T) {
				assetIndex := i % len(createdAssetIDs)
				comment := &reports.Comment{
					ReportId:      reportID,
					AuthorAssetId: createdAssetIDs[assetIndex],
					Text:          fmt.Sprintf("Performance test comment %d with detailed content for testing purposes.", i),
					ResourceType:  "COMMENT",
					DisplayName:   fmt.Sprintf("Performance Test User %d", i),
				}

				if len(createdSectionIDs) > i {
					comment.SectionId = createdSectionIDs[i]
				}

				start := time.Now()
				resp, err := reportsClient.AddComment(ctx, connect.NewRequest(&reports.AddCommentRequest{
					Comment: comment,
				}))
				duration := time.Since(start)

				success := err == nil && resp != nil
				if success && resp != nil {
					createdCommentIDs = append(createdCommentIDs, resp.Msg.Id)
				}

				metrics = append(metrics, PerformanceMetrics{
					Operation: "AddComment",
					Duration:  duration,
					Success:   success,
					ErrorMessage: func() string {
						if err != nil {
							return err.Error()
						}
						return ""
					}(),
					RecordCount: 1,
				})

				if duration > maxLatencyThreshold {
					t.Errorf("❌ AddComment latency too high: %v > %v", duration, maxLatencyThreshold)
				} else {
					t.Logf("✅ AddComment latency: %v", duration)
				}
			})
		}

		// Test GetComments
		if len(createdReportIDs) > 0 {
			t.Run("GetComments", func(t *testing.T) {
				start := time.Now()
				resp, err := reportsClient.GetComments(ctx, connect.NewRequest(&reports.GetCommentsRequest{
					ReportId: createdReportIDs[0],
					PageSize: 10,
				}))
				duration := time.Since(start)

				success := err == nil && resp != nil
				metrics = append(metrics, PerformanceMetrics{
					Operation: "GetComments",
					Duration:  duration,
					Success:   success,
					ErrorMessage: func() string {
						if err != nil {
							return err.Error()
						}
						return ""
					}(),
					RecordCount: func() int {
						if resp != nil {
							return len(resp.Msg.Comments)
						}
						return 0
					}(),
				})

				if duration > maxLatencyThreshold {
					t.Errorf("❌ GetComments latency too high: %v > %v", duration, maxLatencyThreshold)
				} else {
					t.Logf("✅ GetComments latency: %v (returned %d comments)", duration, func() int {
						if resp != nil {
							return len(resp.Msg.Comments)
						}
						return 0
					}())
				}
			})
		}
	})

	// Test 8: Relations CRUD performance
	t.Run("Relations_CRUD_Performance", func(t *testing.T) {
		for i, reportID := range createdReportIDs[:3] {
			// Test CreateRelation
			t.Run(fmt.Sprintf("CreateRelation_%d", i), func(t *testing.T) {
				assetIndex := i % len(createdAssetIDs)
				situationIndex := i % len(createdSituationIDs)
				relation := &reports.Relation{
					ReportId:         reportID,
					RelationType:     "ASSOCIATED_WITH",
					Description:      fmt.Sprintf("Performance test relation %d between report and situation", i),
					CreatedByAssetId: createdAssetIDs[assetIndex],
					ObjectA: &reports.ObjectReference{
						ObjectType:  "REPORT",
						GlobalId:    reportID,
						DisplayName: fmt.Sprintf("Performance Test Report %d", i),
					},
					ObjectB: &reports.ObjectReference{
						ObjectType:  "SITUATION",
						GlobalId:    createdSituationIDs[situationIndex],
						DisplayName: fmt.Sprintf("Performance Test Situation %d", i),
					},
				}

				start := time.Now()
				resp, err := reportsClient.CreateRelation(ctx, connect.NewRequest(&reports.CreateRelationRequest{
					ReportId: reportID,
					Relation: relation,
				}))
				duration := time.Since(start)

				success := err == nil && resp != nil
				if success && resp != nil {
					createdRelationIDs = append(createdRelationIDs, resp.Msg.Id)
				}

				metrics = append(metrics, PerformanceMetrics{
					Operation: "CreateRelation",
					Duration:  duration,
					Success:   success,
					ErrorMessage: func() string {
						if err != nil {
							return err.Error()
						}
						return ""
					}(),
					RecordCount: 1,
				})

				if duration > maxLatencyThreshold {
					t.Errorf("❌ CreateRelation latency too high: %v > %v", duration, maxLatencyThreshold)
				} else {
					t.Logf("✅ CreateRelation latency: %v", duration)
				}
			})
		}

		// Test ListRelations
		if len(createdReportIDs) > 0 {
			t.Run("ListRelations", func(t *testing.T) {
				start := time.Now()
				resp, err := reportsClient.ListRelations(ctx, connect.NewRequest(&reports.ListRelationsRequest{
					ReportId: createdReportIDs[0],
					PageSize: 10,
				}))
				duration := time.Since(start)

				success := err == nil && resp != nil
				metrics = append(metrics, PerformanceMetrics{
					Operation: "ListRelations",
					Duration:  duration,
					Success:   success,
					ErrorMessage: func() string {
						if err != nil {
							return err.Error()
						}
						return ""
					}(),
					RecordCount: func() int {
						if resp != nil {
							return len(resp.Msg.Relations)
						}
						return 0
					}(),
				})

				if duration > maxLatencyThreshold {
					t.Errorf("❌ ListRelations latency too high: %v > %v", duration, maxLatencyThreshold)
				} else {
					t.Logf("✅ ListRelations latency: %v (returned %d relations)", duration, func() int {
						if resp != nil {
							return len(resp.Msg.Relations)
						}
						return 0
					}())
				}
			})
		}
	})

	// Test 9: Filtered listing performance
	t.Run("FilteredListing_Performance", func(t *testing.T) {
		filterTests := []struct {
			name    string
			request interface{}
		}{
			{
				name: "ListByStatus_ASSIGNED",
				request: &reports.ListReportsRequest{
					PageSize: 25,
					Status:   reports.ReportStatus_REPORT_STATUS_ASSIGNED,
					OrgId:    1,
				},
			},
			{
				name: "ListByStatus_IN_PROGRESS",
				request: &reports.ListReportsRequest{
					PageSize: 25,
					Status:   reports.ReportStatus_REPORT_STATUS_IN_PROGRESS,
					OrgId:    1,
				},
			},
			{
				name: "ListBySituationId",
				request: &reports.ListReportsBySituationIdRequest{
					SituationId: createdSituationIDs[0],
					PageSize:    25,
				},
			},
		}

		for _, test := range filterTests {
			t.Run(test.name, func(t *testing.T) {
				start := time.Now()
				var resp interface{}
				var err error

				switch req := test.request.(type) {
				case *reports.ListReportsRequest:
					resp, err = reportsClient.ListReports(ctx, connect.NewRequest(req))
				case *reports.ListReportsBySituationIdRequest:
					resp, err = reportsClient.ListReportsBySituationId(ctx, connect.NewRequest(req))
				}

				duration := time.Since(start)

				success := err == nil && resp != nil
				recordCount := 0
				if success {
					if r, ok := resp.(*connect.Response[reports.ListReportsResponse]); ok {
						recordCount = len(r.Msg.Reports)
					}
				}

				metrics = append(metrics, PerformanceMetrics{
					Operation: fmt.Sprintf("ListReports_%s", test.name),
					Duration:  duration,
					Success:   success,
					ErrorMessage: func() string {
						if err != nil {
							return err.Error()
						}
						return ""
					}(),
					RecordCount: recordCount,
				})

				if duration > maxLatencyThreshold {
					t.Errorf("❌ %s latency too high: %v > %v", test.name, duration, maxLatencyThreshold)
				} else {
					t.Logf("✅ %s latency: %v (returned %d reports)", test.name, duration, recordCount)
				}
			})
		}
	})

	// Test 10: JSON metadata operations performance
	t.Run("JSONMetadata_Performance", func(t *testing.T) {
		testReportID := createdReportIDs[0]
		testMetadata := `{"performance_test": true, "test_timestamp": "` + time.Now().Format(time.RFC3339) + `", "batch_number": 1}`

		// Test UpdateAdditionalInfoJson
		t.Run("UpdateAdditionalInfoJson", func(t *testing.T) {
			start := time.Now()
			_, err := reportsClient.UpdateAdditionalInfoJson(ctx, connect.NewRequest(&reports.UpdateAdditionalInfoJsonRequest{
				ReportId:           testReportID,
				AdditionalInfoJson: testMetadata,
			}))
			duration := time.Since(start)

			success := err == nil
			metrics = append(metrics, PerformanceMetrics{
				Operation: "UpdateAdditionalInfoJson",
				Duration:  duration,
				Success:   success,
				ErrorMessage: func() string {
					if err != nil {
						return err.Error()
					}
					return ""
				}(),
				RecordCount: 1,
			})

			if duration > maxLatencyThreshold {
				t.Errorf("❌ UpdateAdditionalInfoJson latency too high: %v > %v", duration, maxLatencyThreshold)
			} else {
				t.Logf("✅ UpdateAdditionalInfoJson latency: %v", duration)
			}
		})

		// Test GetAdditionalInfo
		t.Run("GetAdditionalInfo", func(t *testing.T) {
			start := time.Now()
			_, err := reportsClient.GetAdditionalInfo(ctx, connect.NewRequest(&reports.GetAdditionalInfoRequest{
				ReportId: testReportID,
			}))
			duration := time.Since(start)

			success := err == nil
			metrics = append(metrics, PerformanceMetrics{
				Operation: "GetAdditionalInfo",
				Duration:  duration,
				Success:   success,
				ErrorMessage: func() string {
					if err != nil {
						return err.Error()
					}
					return ""
				}(),
				RecordCount: 1,
			})

			if duration > maxLatencyThreshold {
				t.Errorf("❌ GetAdditionalInfo latency too high: %v > %v", duration, maxLatencyThreshold)
			} else {
				t.Logf("✅ GetAdditionalInfo latency: %v", duration)
			}
		})
	})

	t.Log("🎯 Reports Basic Performance Test Completed")
}

func printPerformanceSummary(t *testing.T, metrics []PerformanceMetrics) {
	t.Log("\n" + strings.Repeat("=", 80))
	t.Log("📊 PERFORMANCE SUMMARY")
	t.Log(strings.Repeat("=", 80))

	operationStats := make(map[string][]time.Duration)
	var totalOperations, successfulOperations int

	for _, metric := range metrics {
		if metric.Success {
			operationStats[metric.Operation] = append(operationStats[metric.Operation], metric.Duration)
			successfulOperations++
		}
		totalOperations++
	}

	t.Logf("Overall Success Rate: %d/%d (%.1f%%)", successfulOperations, totalOperations, float64(successfulOperations)/float64(totalOperations)*100)
	t.Log("")

	for operation, durations := range operationStats {
		if len(durations) == 0 {
			continue
		}

		var total time.Duration
		min := durations[0]
		max := durations[0]

		for _, d := range durations {
			total += d
			if d < min {
				min = d
			}
			if d > max {
				max = d
			}
		}

		avg := total / time.Duration(len(durations))
		status := "✅"
		if avg > maxLatencyThreshold {
			status = "❌"
		}

		t.Logf("%s %-35s | Count: %3d | Avg: %8v | Min: %8v | Max: %8v",
			status, operation, len(durations), avg, min, max)
	}

	t.Log(strings.Repeat("=", 80))
	t.Logf("Latency Threshold: %v", maxLatencyThreshold)
	t.Log(strings.Repeat("=", 80))
}
