package usecase

import (
	"context"
	"database/sql"
	"fmt"

	orderpb "proto/hero/orders/v2"
	reportpb "proto/hero/reports/v2"
	assetRepository "workflow/internal/assets/data"
	workflowUtils "workflow/internal/common/utils"
	orderRepository "workflow/internal/orders/data"
	reportRepository "workflow/internal/reports/data"

	"google.golang.org/protobuf/types/known/structpb"
)

const defaultPageSize = 100

// ReportUseCase groups business‑level operations for the report domain and
// delegates data persistence to a ReportRepository implementation.
type ReportUseCase struct {
	databaseConnection *sql.DB
	assetRepository    assetRepository.AssetRepository
	reportRepository   reportRepository.ReportRepository
	orderRepository    orderRepository.OrderRepository
}

// NewReportUseCase constructs a fully‑initialised ReportUseCase instance.
func NewReportUseCase(databaseConnection *sql.DB, assetRepository assetRepository.AssetRepository, reportRepository reportRepository.ReportRepository, orderRepository orderRepository.OrderRepository) (*ReportUseCase, error) {
	if databaseConnection == nil {
		return nil, fmt.Errorf("database connection is nil")
	}
	if assetRepository == nil {
		return nil, fmt.Errorf("asset repository must not be nil")
	}
	if reportRepository == nil {
		return nil, fmt.Errorf("report repository must not be nil")
	}
	if orderRepository == nil {
		return nil, fmt.Errorf("order repository must not be nil")
	}
	return &ReportUseCase{databaseConnection: databaseConnection, assetRepository: assetRepository, reportRepository: reportRepository, orderRepository: orderRepository}, nil
}

// executeInTx wraps a series of repository calls in a SQL transaction, ensuring
// commit on success and rollback on error or panic.
func (usecase *ReportUseCase) executeInTx(ctx context.Context, transactionalWork func(transaction *sql.Tx) error) error {
	transaction, transactionErr := usecase.databaseConnection.BeginTx(ctx, nil)
	if transactionErr != nil {
		return fmt.Errorf("begin transaction: %w", transactionErr)
	}
	defer func() {
		if recoveredPanic := recover(); recoveredPanic != nil {
			_ = transaction.Rollback()
			panic(recoveredPanic)
		}
	}()

	if workErr := transactionalWork(transaction); workErr != nil {
		_ = transaction.Rollback()
		return workErr
	}
	if commitErr := transaction.Commit(); commitErr != nil {
		return fmt.Errorf("commit transaction: %w", commitErr)
	}
	return nil
}

// -----------------------------------------------------------------------------
// Report CRUD
// -----------------------------------------------------------------------------

// CreateReport creates a report and enqueues a WRITE_REPORT order.
func (usecase *ReportUseCase) CreateReport(ctx context.Context, reportToCreate *reportpb.Report) (*reportpb.Report, error) {

	// If the report is created with an empty author_asset_id, we need to find the first
	// completed ORDER_TYPE_ASSIST_MEMBER order for the situation and set it as the author.
	// We can only do this if the report is not already assigned to an author.
	// And SituationId is not empty.
	// And status is either unspecified or ASSIGNED (though you should not set status assigned without assigning anyone).
	if reportToCreate.AuthorAssetId == "" &&
		reportToCreate.SituationId != "" &&
		(reportToCreate.Status == reportpb.ReportStatus_REPORT_STATUS_UNSPECIFIED || reportToCreate.Status == reportpb.ReportStatus_REPORT_STATUS_ASSIGNED) {

		// Log that we're attempting to find an author for the report
		fmt.Printf("Attempting to find an author for report with situation ID: %s\n", reportToCreate.SituationId)

		var orders []*orderpb.Order
		for token := ""; ; {
			resp, err := usecase.orderRepository.ListOrdersForSituation(ctx, nil, reportToCreate.SituationId, defaultPageSize, token, 0)
			if err != nil {
				return nil, fmt.Errorf("list orders for situation: %w", err)
			}
			orders = append(orders, resp.Orders...)
			if token = resp.PageToken; token == "" {
				break
			}
		}

		for _, order := range orders {
			if order.Type == orderpb.OrderType_ORDER_TYPE_ASSIST_MEMBER &&
				order.AssetId != "" &&
				order.Status != orderpb.OrderStatus_ORDER_STATUS_REJECTED &&
				order.Status != orderpb.OrderStatus_ORDER_STATUS_CANCELLED {
				// We found the first not cancelled/rejected assist member order, set it as the author.
				reportToCreate.AuthorAssetId = order.AssetId
				if reportToCreate.Status == reportpb.ReportStatus_REPORT_STATUS_UNSPECIFIED {
					reportToCreate.Status = reportpb.ReportStatus_REPORT_STATUS_ASSIGNED
				}
				break
			}
		}
	}

	var createdReport *reportpb.Report
	createErr := usecase.executeInTx(ctx, func(transaction *sql.Tx) error {
		var repositoryErr error
		createdReport, repositoryErr = usecase.reportRepository.CreateReport(ctx, transaction, reportToCreate)
		if repositoryErr != nil {
			return repositoryErr
		}

		// Side-effects: on ASSIGNED status, create WRITE_REPORT order
		checker := &ReportSideEffectChecker{}
		executor := NewReportSideEffectExecutor()
		effects := checker.CheckReportStatusChangeSideEffect(createdReport)
		for _, effect := range effects {
			if execErr := executor.ExecuteSideEffect(ctx, transaction, effect, createdReport, nil, usecase, ""); execErr != nil {
				return fmt.Errorf("CreateReport side-effect: %w", execErr)
			}
		}
		return nil
	})
	if createErr != nil {
		return nil, createErr
	}
	return createdReport, nil
}

// GetReport retrieves a report by its ID.
func (usecase *ReportUseCase) GetReport(ctx context.Context, reportID string) (*reportpb.Report, error) {
	return usecase.reportRepository.GetReport(ctx, nil, reportID)
}

// UpdateReport updates an existing report.
func (usecase *ReportUseCase) UpdateReport(ctx context.Context, request *reportpb.UpdateReportRequest) (*reportpb.Report, error) {
	var updatedReport *reportpb.Report
	updateErr := usecase.executeInTx(ctx, func(transaction *sql.Tx) error {
		// First get the existing report
		existingReport, err := usecase.reportRepository.GetReport(ctx, transaction, request.GetReport().Id)
		if err != nil {
			return fmt.Errorf("failed to get existing report: %w", err)
		}

		// Create a merged report by preserving existing values when new ones aren't provided
		mergedReport := existingReport
		updateReport := request.GetReport()

		// Only update fields that are explicitly provided and supported by repository
		// Fields updated in this usecase: title, status, author_asset_id, situation_id, case_id, watcher_asset_ids, additional_info_json
		// Fields handled automatically by repository: updated_at, completed_at, version

		if updateReport.Title != "" {
			mergedReport.Title = updateReport.Title
		}
		if updateReport.Status != reportpb.ReportStatus_REPORT_STATUS_UNSPECIFIED {
			mergedReport.Status = updateReport.Status
		}
		if updateReport.AuthorAssetId != "" {
			mergedReport.AuthorAssetId = updateReport.AuthorAssetId
		}
		if updateReport.SituationId != "" {
			mergedReport.SituationId = updateReport.SituationId
		}
		if updateReport.CaseId != "" {
			mergedReport.CaseId = updateReport.CaseId
		}

		// Handle watcher asset IDs update - if provided in update request, use them; otherwise keep existing
		if updateReport.WatcherAssetIds != nil {
			mergedReport.WatcherAssetIds = updateReport.WatcherAssetIds
		}

		// For additional info, merge with existing if provided
		if updateReport.AdditionalInfoJson != nil {
			if mergedReport.AdditionalInfoJson == nil {
				mergedReport.AdditionalInfoJson = updateReport.AdditionalInfoJson
			} else {
				// Use recursive merge instead of simple overwrite
				existingMap := mergedReport.AdditionalInfoJson.AsMap()
				updateMap := updateReport.AdditionalInfoJson.AsMap()
				workflowUtils.MergeJSON(existingMap, updateMap)
				newStruct, err := structpb.NewStruct(existingMap)
				if err != nil {
					return fmt.Errorf("failed to merge additional info: %w", err)
				}
				mergedReport.AdditionalInfoJson = newStruct
			}
		}

		var repositoryErr error
		updatedReport, repositoryErr = usecase.reportRepository.UpdateReport(ctx, transaction, mergedReport)
		return repositoryErr
	})
	if updateErr != nil {
		return nil, updateErr
	}
	return updatedReport, nil
}

// UpdateReportStatus updates the status of a report and handles side effects.
func (usecase *ReportUseCase) UpdateReportStatus(ctx context.Context, reportID string, newStatus reportpb.ReportStatus) (*reportpb.Report, error) {
	var updatedReport *reportpb.Report
	updateErr := usecase.executeInTx(ctx, func(transaction *sql.Tx) error {
		var repositoryErr error

		// Update the status in the repository
		updatedReport, repositoryErr = usecase.reportRepository.UpdateReportStatus(ctx, transaction, reportID, newStatus)
		if repositoryErr != nil {
			return repositoryErr // Already includes context
		}

		// Side-effects: Check based on the transition from originalReport.Status to updatedReport.Status
		checker := &ReportSideEffectChecker{}
		executor := NewReportSideEffectExecutor()
		effects := checker.CheckReportStatusChangeSideEffect(updatedReport) // Pass the *updated* report
		for _, effect := range effects {
			if execErr := executor.ExecuteSideEffect(ctx, transaction, effect, updatedReport, nil, usecase, ""); execErr != nil {
				// Rollback transaction if side effect fails
				return fmt.Errorf("UpdateReportStatus side-effect execution failed: %w", execErr)
			}
		}
		return nil // Commit transaction
	})

	if updateErr != nil {
		return nil, updateErr
	}
	return updatedReport, nil
}

// ListReports returns a paginated list of reports.
func (usecase *ReportUseCase) ListReports(ctx context.Context, pageSize int32, pageToken string, statusFilter reportpb.ReportStatus, organizationID int32) ([]*reportpb.Report, string, error) {
	effectivePageSize := pageSize
	if effectivePageSize <= 0 {
		effectivePageSize = defaultPageSize
	}
	listResponse, listErr := usecase.reportRepository.ListReports(ctx, nil, int(effectivePageSize), pageToken, statusFilter, organizationID)
	if listErr != nil {
		return nil, "", listErr
	}
	return listResponse.Reports, listResponse.NextPageToken, nil
}

// ListReportsBySituationID returns a page of reports filtered by situation_id.
func (usecase *ReportUseCase) ListReportsBySituationID(
	ctx context.Context,
	situationID string,
	pageSize int32,
	pageToken string,
) ([]*reportpb.Report, string, error) {
	if situationID == "" {
		return nil, "", fmt.Errorf("situationID must be provided")
	}
	effectivePageSize := pageSize
	if effectivePageSize <= 0 {
		effectivePageSize = defaultPageSize
	}
	resp, err := usecase.reportRepository.ListReportsBySituationID(
		ctx, nil,
		situationID,
		int(effectivePageSize),
		pageToken,
	)
	if err != nil {
		return nil, "", err
	}
	return resp.Reports, resp.NextPageToken, nil
}

// ListReportsByCaseID returns a page of reports filtered by case_id.
func (usecase *ReportUseCase) ListReportsByCaseID(
	ctx context.Context,
	caseID string,
	pageSize int32,
	pageToken string,
) ([]*reportpb.Report, string, error) {
	if caseID == "" {
		return nil, "", fmt.Errorf("caseID must be provided")
	}
	effectivePageSize := pageSize
	if effectivePageSize <= 0 {
		effectivePageSize = defaultPageSize
	}
	resp, err := usecase.reportRepository.ListReportsByCaseID(
		ctx, nil,
		caseID,
		int(effectivePageSize),
		pageToken,
	)
	if err != nil {
		return nil, "", err
	}
	return resp.Reports, resp.NextPageToken, nil
}

// BatchGetReports retrieves multiple reports by their IDs.
func (usecase *ReportUseCase) BatchGetReports(ctx context.Context, reportIDs []string) ([]*reportpb.Report, error) {
	if len(reportIDs) == 0 {
		return []*reportpb.Report{}, nil
	}
	return usecase.reportRepository.BatchGetReports(ctx, nil, reportIDs)
}

// DeleteReport deletes a report by its ID.
func (usecase *ReportUseCase) DeleteReport(ctx context.Context, reportID string) error {
	return usecase.executeInTx(ctx, func(transaction *sql.Tx) error {
		return usecase.reportRepository.DeleteReport(ctx, transaction, reportID)
	})
}

// -----------------------------------------------------------------------------
// Section CRUD
// -----------------------------------------------------------------------------

// CreateReportSection adds a new section to a report.
func (usecase *ReportUseCase) CreateReportSection(ctx context.Context, reportId string, sectionToCreate *reportpb.ReportSection) (*reportpb.ReportSection, error) {
	// Validate section type before creation
	if err := validateSectionType(sectionToCreate); err != nil {
		return nil, fmt.Errorf("invalid section: %w", err)
	}

	var createdSection *reportpb.ReportSection
	createErr := usecase.executeInTx(ctx, func(transaction *sql.Tx) error {
		var repositoryErr error
		createdSection, repositoryErr = usecase.reportRepository.CreateReportSection(ctx, transaction, reportId, sectionToCreate)
		return repositoryErr
	})
	if createErr != nil {
		return nil, createErr
	}
	return createdSection, nil
}

// GetReportSection retrieves a single report section.
func (usecase *ReportUseCase) GetReportSection(ctx context.Context, reportID, sectionID string) (*reportpb.ReportSection, error) {
	return usecase.reportRepository.GetReportSection(ctx, nil, reportID, sectionID)
}

// UpdateReportSection updates an existing report section.
func (usecase *ReportUseCase) UpdateReportSection(ctx context.Context, reportID string, sectionToUpdate *reportpb.ReportSection) (*reportpb.ReportSection, error) {
	// Validate section type before update
	if err := validateSectionType(sectionToUpdate); err != nil {
		return nil, fmt.Errorf("invalid section: %w", err)
	}

	var updatedSection *reportpb.ReportSection
	updateErr := usecase.executeInTx(ctx, func(transaction *sql.Tx) error {
		var repositoryErr error
		updatedSection, repositoryErr = usecase.reportRepository.UpdateReportSection(ctx, transaction, reportID, sectionToUpdate)
		return repositoryErr
	})
	if updateErr != nil {
		return nil, updateErr
	}
	return updatedSection, nil
}

// DeleteReportSection removes a section from a report.
func (usecase *ReportUseCase) DeleteReportSection(ctx context.Context, reportID, sectionID string) error {
	return usecase.executeInTx(ctx, func(transaction *sql.Tx) error {
		return usecase.reportRepository.DeleteReportSection(ctx, transaction, reportID, sectionID)
	})
}

// ListReportSections lists all sections in a report.
func (usecase *ReportUseCase) ListReportSections(ctx context.Context, reportID string) ([]*reportpb.ReportSection, error) {
	return usecase.reportRepository.ListReportSections(ctx, nil, reportID)
}

// -----------------------------------------------------------------------------
// Comments
// -----------------------------------------------------------------------------

// AddComment adds a comment to a report or section.
func (usecase *ReportUseCase) AddComment(ctx context.Context, commentRequest *reportpb.AddCommentRequest) (*reportpb.Comment, error) {
	if commentRequest.Comment == nil {
		return nil, fmt.Errorf("comment field is required in AddCommentRequest")
	}

	var createdComment *reportpb.Comment
	createErr := usecase.executeInTx(ctx, func(transaction *sql.Tx) error {
		var repositoryErr error
		createdComment, repositoryErr = usecase.reportRepository.AddComment(ctx, transaction, commentRequest)
		return repositoryErr
	})
	if createErr != nil {
		return nil, createErr
	}
	return createdComment, nil
}

// GetComments retrieves comments for a report or section.
func (usecase *ReportUseCase) GetComments(ctx context.Context, commentsRequest *reportpb.GetCommentsRequest) ([]*reportpb.Comment, string, error) {
	effectivePageSize := int(commentsRequest.PageSize)
	if effectivePageSize <= 0 {
		effectivePageSize = defaultPageSize
	}
	commentsResponse, commentsErr := usecase.reportRepository.GetComments(ctx, nil, commentsRequest.ReportId, commentsRequest.SectionId, effectivePageSize, commentsRequest.PageToken)
	if commentsErr != nil {
		return nil, "", commentsErr
	}
	return commentsResponse.Comments, commentsResponse.NextPageToken, nil
}

// UpdateComment updates an existing comment.
func (usecase *ReportUseCase) UpdateComment(ctx context.Context, commentToUpdate *reportpb.Comment) (*reportpb.Comment, error) {
	var updatedComment *reportpb.Comment
	updateErr := usecase.executeInTx(ctx, func(transaction *sql.Tx) error {
		var repositoryErr error
		updatedComment, repositoryErr = usecase.reportRepository.UpdateComment(ctx, transaction, commentToUpdate)
		return repositoryErr
	})
	if updateErr != nil {
		return nil, updateErr
	}
	return updatedComment, nil
}

// DeleteComment removes a comment.
func (usecase *ReportUseCase) DeleteComment(ctx context.Context, commentID string) error {
	return usecase.executeInTx(ctx, func(transaction *sql.Tx) error {
		return usecase.reportRepository.DeleteComment(ctx, transaction, commentID)
	})
}

// -----------------------------------------------------------------------------
// Review Workflow
// -----------------------------------------------------------------------------

// SubmitForReview sends a report for review and enqueues a REVIEW_REPORT order.
func (usecase *ReportUseCase) SubmitForReview(ctx context.Context, reportID, authorNote string) (*reportpb.Report, error) {
	var submittedReport *reportpb.Report
	submitErr := usecase.executeInTx(ctx, func(transaction *sql.Tx) error {
		var repositoryErr error
		submittedReport, repositoryErr = usecase.reportRepository.SubmitForReview(ctx, transaction, reportID, authorNote)
		if repositoryErr != nil {
			return repositoryErr
		}

		// Side-effects: on SUBMITTED_FOR_REVIEW create REVIEW_REPORT order (within the transaction)
		checker := &ReportSideEffectChecker{}
		executor := NewReportSideEffectExecutor()
		effects := checker.CheckReportStatusChangeSideEffect(submittedReport)
		for _, effect := range effects {
			if execErr := executor.ExecuteSideEffect(ctx, transaction, effect, submittedReport, nil, usecase, authorNote); execErr != nil {
				return fmt.Errorf("SubmitForReview side-effect: %w", execErr)
			}
		}
		return nil
	})
	if submitErr != nil {
		return nil, submitErr // Return the error from executeInTx (could be commit error or side-effect error)
	}

	return submittedReport, nil
}

// AddReviewRound assigns a reviewer and enqueues a REVIEW_REPORT order.
func (usecase *ReportUseCase) AddReviewRound(ctx context.Context, assignmentRequest *reportpb.AddReviewRoundRequest) (*reportpb.ReviewRound, error) {
	var createdReviewRound *reportpb.ReviewRound
	assignErr := usecase.executeInTx(ctx, func(transaction *sql.Tx) error {
		var repositoryErr error
		createdReviewRound, repositoryErr = usecase.reportRepository.AddReviewRound(ctx, transaction, assignmentRequest)
		if repositoryErr != nil {
			return repositoryErr // Return error to rollback transaction
		}

		// Side-effects: on new review round create REVIEW_REPORT order (within the transaction)
		checker := &ReportSideEffectChecker{}
		executor := NewReportSideEffectExecutor()
		effects := checker.CheckReviewRoundStatusChangeSideEffect(createdReviewRound)
		for _, effect := range effects {
			if execErr := executor.ExecuteSideEffect(ctx, transaction, effect, nil, createdReviewRound, usecase, ""); execErr != nil {
				return fmt.Errorf("AddReviewRound side-effect: %w", execErr)
			}
		}
		return nil // Indicate success for this transaction block
	})
	if assignErr != nil {
		return nil, assignErr // Return the error from executeInTx
	}

	return createdReviewRound, nil
}

// ApproveReviewRound marks a review as approved and may escalate or finalize.
func (usecase *ReportUseCase) ApproveReviewRound(ctx context.Context, approvalRequest *reportpb.ApproveReviewRoundRequest) (*reportpb.ReviewRound, error) {
	var approvedRound *reportpb.ReviewRound
	approveErr := usecase.executeInTx(ctx, func(transaction *sql.Tx) error {
		var repositoryErr error
		approvedRound, repositoryErr = usecase.reportRepository.ApproveReviewRound(ctx, transaction, approvalRequest.ReviewRoundId, approvalRequest.Note)
		if repositoryErr != nil {
			return repositoryErr // Return error to rollback transaction
		}

		// Side-effects: on APPROVED escalate or complete (within the transaction)
		checker := &ReportSideEffectChecker{}
		executor := NewReportSideEffectExecutor()
		effects := checker.CheckReviewRoundStatusChangeSideEffect(approvedRound)
		for _, effect := range effects {
			// Pass the transaction to ExecuteSideEffect
			if execErr := executor.ExecuteSideEffect(ctx, transaction, effect, nil, approvedRound, usecase, ""); execErr != nil {
				return fmt.Errorf("ApproveReviewRound side-effect: %w", execErr) // Return error to rollback transaction
			}
		}
		return nil // Indicate success for this transaction block
	})
	if approveErr != nil {
		return nil, approveErr // Return the error from executeInTx
	}

	return approvedRound, nil
}

// RequestChanges marks a review as changes requested and enqueues a REVISE_REPORT order.
func (usecase *ReportUseCase) RequestChanges(ctx context.Context, changeRequest *reportpb.RequestChangesRequest) (*reportpb.ReviewRound, error) {
	var reviewRoundWithChanges *reportpb.ReviewRound
	requestErr := usecase.executeInTx(ctx, func(transaction *sql.Tx) error {
		var repositoryErr error
		reviewRoundWithChanges, repositoryErr = usecase.reportRepository.RequestChanges(ctx, transaction, changeRequest)
		if repositoryErr != nil {
			return repositoryErr
		}

		// Side-effects: on CHANGES_REQUESTED create REVISE_REPORT order (within the transaction)
		checker := &ReportSideEffectChecker{}
		executor := NewReportSideEffectExecutor()
		effects := checker.CheckReviewRoundStatusChangeSideEffect(reviewRoundWithChanges)
		for _, effect := range effects {
			if execErr := executor.ExecuteSideEffect(ctx, transaction, effect, nil, reviewRoundWithChanges, usecase, ""); execErr != nil {
				return fmt.Errorf("RequestChanges side-effect: %w", execErr)
			}
		}
		return nil
	})
	if requestErr != nil {
		return nil, requestErr
	}

	return reviewRoundWithChanges, nil
}

// ListReviewRoundsForReport retrieves review rounds for a specific report.
func (usecase *ReportUseCase) ListReviewRoundsForReport(
	ctx context.Context,
	reportID string,
	pageSize int32,
	pageToken string,
) ([]*reportpb.ReviewRound, string, error) {
	effectivePageSize := pageSize
	if effectivePageSize <= 0 {
		effectivePageSize = defaultPageSize
	}
	resp, err := usecase.reportRepository.ListReviewRoundsForReport(
		ctx, nil,
		reportID,
		int(effectivePageSize),
		pageToken,
	)
	if err != nil {
		return nil, "", fmt.Errorf("failed to list review rounds for report %s: %w", reportID, err)
	}
	return resp.ReviewRounds, resp.NextPageToken, nil
}

// GetReviewRound retrieves a single review round by its ID.
func (usecase *ReportUseCase) GetReviewRound(ctx context.Context, reviewRoundID string) (*reportpb.ReviewRound, error) {
	round, err := usecase.reportRepository.GetReviewRound(ctx, nil, reviewRoundID)
	if err != nil {
		return nil, fmt.Errorf("failed to get review round %s: %w", reviewRoundID, err)
	}
	return round, nil
}

// UpdateReviewRound updates an existing review round.
// Note: This performs a general update. Status changes should use ApproveReviewRound/RequestChanges.
func (usecase *ReportUseCase) UpdateReviewRound(ctx context.Context, inputRound *reportpb.ReviewRound) (*reportpb.ReviewRound, error) {
	var finalUpdatedRound *reportpb.ReviewRound
	updateErr := usecase.executeInTx(ctx, func(tx *sql.Tx) error {
		// 1. Fetch the existing review round to ensure it exists and to merge changes.
		existingRound, getErr := usecase.reportRepository.GetReviewRound(ctx, tx, inputRound.Id)
		if getErr != nil {
			return fmt.Errorf("failed to fetch existing review round %s for update: %w", inputRound.Id, getErr)
		}

		// 2. Apply changes from inputRound to existingRound selectively.
		// We only update fields that are typically user-modifiable in a general update.
		// Status, timestamps related to status changes (requested_at, resolved_at), creator, report_id, snapshot_version are generally immutable or handled by specific actions.

		if inputRound.ReviewerAssetId != "" && inputRound.ReviewerAssetId != existingRound.ReviewerAssetId {
			existingRound.ReviewerAssetId = inputRound.ReviewerAssetId
		}
		if inputRound.Level != 0 && inputRound.Level != existingRound.Level { // Assuming 0 is not a valid level
			existingRound.Level = inputRound.Level
		}
		if inputRound.SentToLevel != 0 && inputRound.SentToLevel != existingRound.SentToLevel { // Assuming 0 is not a valid level
			existingRound.SentToLevel = inputRound.SentToLevel
		}
		if inputRound.SentToAssetId != "" && inputRound.SentToAssetId != existingRound.SentToAssetId {
			existingRound.SentToAssetId = inputRound.SentToAssetId
		}
		if inputRound.RoundNote != "" && inputRound.RoundNote != existingRound.RoundNote {
			existingRound.RoundNote = inputRound.RoundNote
		}
		if inputRound.DueAt != "" && inputRound.DueAt != existingRound.DueAt {
			existingRound.DueAt = inputRound.DueAt
		}
		if inputRound.NoteForReviewer != "" && inputRound.NoteForReviewer != existingRound.NoteForReviewer {
			existingRound.NoteForReviewer = inputRound.NoteForReviewer
		}

		if inputRound.Status != reportpb.ReviewStatus_REVIEW_STATUS_UNSPECIFIED && inputRound.Status != existingRound.Status {
			existingRound.Status = inputRound.Status
		}

		// 4. Pass the merged/updated existingRound to the repository for persistence.
		var repoErr error
		finalUpdatedRound, repoErr = usecase.reportRepository.UpdateReviewRound(ctx, tx, existingRound) // Pass the modified existingRound
		if repoErr != nil {
			return repoErr // Rollback transaction
		}
		// No side effects expected for a general update, only for status changes.
		checker := &ReportSideEffectChecker{}
		executor := NewReportSideEffectExecutor()
		effects := checker.CheckReviewRoundStatusChangeSideEffect(finalUpdatedRound)
		for _, effect := range effects {
			if execErr := executor.ExecuteSideEffect(ctx, tx, effect, nil, finalUpdatedRound, usecase, ""); execErr != nil {
				return fmt.Errorf("UpdateReviewRound side-effect: %w", execErr) // Return error to rollback transaction
			}
		}
		return nil // Indicate success for this transaction block
	})
	if updateErr != nil {
		return nil, fmt.Errorf("failed to update review round %s: %w", inputRound.Id, updateErr)
	}
	return finalUpdatedRound, nil
}

// DeleteReviewRound deletes a review round by its ID.
func (usecase *ReportUseCase) DeleteReviewRound(ctx context.Context, reviewRoundID string) error {
	deleteErr := usecase.executeInTx(ctx, func(tx *sql.Tx) error {

		repoErr := usecase.reportRepository.DeleteReviewRound(ctx, tx, reviewRoundID)
		if repoErr != nil {
			return repoErr
		}
		return nil
	})
	if deleteErr != nil {
		return fmt.Errorf("failed to delete review round %s: %w", reviewRoundID, deleteErr)
	}
	return nil
}

// -----------------------------------------------------------------------------
// JSON Metadata
// -----------------------------------------------------------------------------

func (usecase *ReportUseCase) UpdateAdditionalInfoJSON(ctx context.Context, metadataRequest *reportpb.UpdateAdditionalInfoJsonRequest) (*reportpb.UpdateAdditionalInfoJsonResponse, error) {
	var metadataResponse *reportpb.UpdateAdditionalInfoJsonResponse
	updateErr := usecase.executeInTx(ctx, func(transaction *sql.Tx) error {
		var repositoryErr error
		metadataResponse, repositoryErr = usecase.reportRepository.UpdateAdditionalInfoJSON(ctx, transaction, metadataRequest)
		return repositoryErr
	})
	if updateErr != nil {
		return nil, updateErr
	}
	return metadataResponse, nil
}

func (usecase *ReportUseCase) GetAdditionalInfo(ctx context.Context, reportID string) (*reportpb.GetAdditionalInfoResponse, error) {
	return usecase.reportRepository.GetAdditionalInfo(ctx, nil, reportID)
}

// -----------------------------------------------------------------------------
// Versioning
// -----------------------------------------------------------------------------

func (usecase *ReportUseCase) GetReportVersion(ctx context.Context, reportID string, versionNumber int32) (*reportpb.ReportSnapshot, error) {
	return usecase.reportRepository.GetReportVersion(ctx, nil, reportID, versionNumber)
}

func (usecase *ReportUseCase) ListReportVersions(ctx context.Context, reportID string) ([]int32, error) {
	return usecase.reportRepository.ListReportVersions(ctx, nil, reportID)
}

func (usecase *ReportUseCase) ResolveComment(ctx context.Context, resolveCommentRequest *reportpb.ResolveCommentRequest) (*reportpb.Comment, error) {
	return usecase.reportRepository.ResolveComment(ctx, nil, resolveCommentRequest)
}

// SearchReports provides rich search capabilities with multiple filtering options.
func (usecase *ReportUseCase) SearchReports(
	ctx context.Context,
	searchReq *reportpb.SearchReportsRequest,
) (*reportpb.SearchReportsResponse, error) {
	if searchReq.PageSize <= 0 {
		searchReq.PageSize = defaultPageSize
	}

	return usecase.reportRepository.SearchReports(
		ctx,
		nil, // no transaction
		searchReq,
	)
}

// -----------------------------------------------------------------------------
// Relations CRUD
// -----------------------------------------------------------------------------

// CreateRelation creates a new relation between a report and an object.
func (usecase *ReportUseCase) CreateRelation(ctx context.Context, request *reportpb.CreateRelationRequest) (*reportpb.Relation, error) {
	var createdRelation *reportpb.Relation
	createErr := usecase.executeInTx(ctx, func(transaction *sql.Tx) error {
		var repositoryErr error
		createdRelation, repositoryErr = usecase.reportRepository.CreateRelation(ctx, transaction, request)
		return repositoryErr
	})
	if createErr != nil {
		return nil, createErr
	}
	return createdRelation, nil
}

// GetRelation retrieves a relation by its ID.
func (usecase *ReportUseCase) GetRelation(ctx context.Context, request *reportpb.GetRelationRequest) (*reportpb.Relation, error) {
	return usecase.reportRepository.GetRelation(ctx, nil, request)
}

// UpdateRelation updates an existing relation.
func (usecase *ReportUseCase) UpdateRelation(ctx context.Context, request *reportpb.UpdateRelationRequest) (*reportpb.Relation, error) {
	var updatedRelation *reportpb.Relation
	updateErr := usecase.executeInTx(ctx, func(transaction *sql.Tx) error {
		var repositoryErr error
		updatedRelation, repositoryErr = usecase.reportRepository.UpdateRelation(ctx, transaction, request)
		return repositoryErr
	})
	if updateErr != nil {
		return nil, updateErr
	}
	return updatedRelation, nil
}

// DeleteRelation deletes a relation by its ID.
func (usecase *ReportUseCase) DeleteRelation(ctx context.Context, request *reportpb.DeleteRelationRequest) error {
	return usecase.executeInTx(ctx, func(transaction *sql.Tx) error {
		return usecase.reportRepository.DeleteRelation(ctx, transaction, request)
	})
}

// ListRelations returns a paginated list of relations for a report.
func (usecase *ReportUseCase) ListRelations(ctx context.Context, request *reportpb.ListRelationsRequest) (*reportpb.ListRelationsResponse, error) {
	return usecase.reportRepository.ListRelations(ctx, nil, request)
}

// -----------------------------------------------------------------------------
// Section Validation
// -----------------------------------------------------------------------------

// validateSectionType validates that a section has a valid type specified
func validateSectionType(section *reportpb.ReportSection) error {
	if section == nil {
		return fmt.Errorf("section cannot be nil")
	}

	if section.Type == reportpb.SectionType_SECTION_TYPE_UNSPECIFIED {
		return fmt.Errorf("section type must be specified")
	}

	// Validate that the section type is supported
	validTypes := map[reportpb.SectionType]bool{
		reportpb.SectionType_SECTION_TYPE_NARRATIVE:                 true,
		reportpb.SectionType_SECTION_TYPE_ENTITY_LIST_PEOPLE:        true,
		reportpb.SectionType_SECTION_TYPE_ENTITY_LIST_VEHICLE:       true,
		reportpb.SectionType_SECTION_TYPE_ENTITY_LIST_PROPERTIES:    true,
		reportpb.SectionType_SECTION_TYPE_ENTITY_LIST_ORGANIZATIONS: true,
		reportpb.SectionType_SECTION_TYPE_INCIDENT_DETAILS:          true,
		reportpb.SectionType_SECTION_TYPE_OFFENSE:                   true,
		reportpb.SectionType_SECTION_TYPE_ARREST:                    true,
		reportpb.SectionType_SECTION_TYPE_MEDIA:                     true,
	}

	if !validTypes[section.Type] {
		return fmt.Errorf("unsupported section type: %v", section.Type)
	}

	return nil
}
