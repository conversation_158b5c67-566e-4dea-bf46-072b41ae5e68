package repository

import (
	"context"
	"database/sql"
	"errors"

	filerepository "proto/hero/filerepository/v1"

	_ "github.com/lib/pq"
)

// ErrFileNotFound is returned when a file cannot be found.
var ErrFileNotFound = errors.New("file not found")

// ErrAccessLogNotFound is returned when an access log entry cannot be found.
var ErrAccessLogNotFound = errors.New("access log not found")

const FixedResourceTypeFile = "FILE"

// FileRepositoryRepository defines the operations for managing files and access logs.
type FileRepositoryRepository interface {
	//--- Core file metadata operations
	// CreateFileMetadata stores a new file metadata record (typically with PENDING status).
	CreateFileMetadata(ctx context.Context, transaction *sql.Tx, metadata *filerepository.FileMetadata) error
	// GetFileMetadata returns file metadata by its ID.
	GetFileMetadata(ctx context.Context, transaction *sql.Tx, fileID string) (*filerepository.FileMetadata, error)
	// UpdateFileMetadata updates the file metadata fields.
	UpdateFileMetadata(ctx context.Context, transaction *sql.Tx, fileID string, req *filerepository.UpdateFileMetadataRequest) (*filerepository.FileMetadata, error)

	//--- File listing and search
	// ListFiles returns a paginated list of files with optional filtering.
	ListFiles(ctx context.Context, transaction *sql.Tx, req *filerepository.ListFilesRequest) (*filerepository.ListFilesResponse, error)
	// SearchFiles performs advanced search on files with complex filtering.
	SearchFiles(ctx context.Context, transaction *sql.Tx, req *filerepository.SearchFilesRequest) (*filerepository.SearchFilesResponse, error)

	//--- Upload lifecycle management
	// ConfirmUpload marks a PENDING file as ACTIVE after successful upload.
	ConfirmUpload(ctx context.Context, transaction *sql.Tx, pendingID string, actualFileSize int32) (*filerepository.FileMetadata, error)
	// CleanupPendingUploads removes stale PENDING records older than the specified timestamp.
	CleanupPendingUploads(ctx context.Context, transaction *sql.Tx, olderThan string) (int32, error)

	//--- File lifecycle operations
	// DeleteFile soft-deletes a file by setting status to DELETED.
	DeleteFile(ctx context.Context, transaction *sql.Tx, fileID string) error
	// UndeleteFile restores a soft-deleted file back to ACTIVE status.
	UndeleteFile(ctx context.Context, transaction *sql.Tx, fileID string) (*filerepository.FileMetadata, error)
	// PurgeFile permanently removes file metadata and should trigger cloud storage deletion.
	PurgeFile(ctx context.Context, transaction *sql.Tx, fileID string) error

	//--- Access logging and audit trail
	// RecordAccess logs a file access event for audit and analytics.
	RecordAccess(ctx context.Context, transaction *sql.Tx, req *filerepository.RecordAccessRequest) (string, error)
	// GetAccessLog retrieves paginated access logs for a file with optional filtering.
	GetAccessLog(ctx context.Context, transaction *sql.Tx, req *filerepository.GetAccessLogRequest) (*filerepository.GetAccessLogResponse, error)

	//--- Analytics and metrics
	// IncrementDownloadCount increments the download counter for a file.
	IncrementDownloadCount(ctx context.Context, transaction *sql.Tx, fileID string) error
	// UpdateLastAccessed updates the last_accessed timestamp for a file.
	UpdateLastAccessed(ctx context.Context, transaction *sql.Tx, fileID string) error
}

// NewFileRepositoryRepository returns a FileRepositoryRepository based on the provided configuration.
func NewFileRepositoryRepository(postgresDB *sql.DB) (FileRepositoryRepository, *sql.DB, error) {
	if postgresDB == nil {
		return nil, nil, errors.New("database is nil: cannot initialize FileRepositoryRepository")
	}
	return NewPostgresFileRepositoryRepository(postgresDB), postgresDB, nil
}
