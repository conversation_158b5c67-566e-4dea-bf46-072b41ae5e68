package filerepository

import (
	"database/sql"
	"log"
	"net/http"

	"github.com/aws/aws-sdk-go-v2/service/s3"

	"filerepository/internal/api/connect"
	filerepositoryRepository "filerepository/internal/data"
	"filerepository/internal/usecase"
	filerepositoryconnect "proto/hero/filerepository/v1/filerepositoryconnect"
)

// Simple logging middleware
func loggingMiddleware(next http.Handler) http.Handler {
	return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		log.Printf("FileRepository: %s %s", r.Method, r.URL.Path)
		next.ServeHTTP(w, r)
	})
}

func RegisterRoutes(
	mux *http.ServeMux,
	filerepositoryDB *sql.DB,
	filerepositoryRepo filerepositoryRepository.FileRepositoryRepository,
	s3Client *s3.Client,
	s3BucketName string) {

	// Initialize FileRepository Use Case with injected dependencies
	filerepositoryUseCase, err := usecase.NewFileRepositoryUseCase(filerepositoryDB, filerepositoryRepo, s3Client, s3BucketName)
	if err != nil {
		log.Fatalf("Failed to initialize FileRepository Use Case: %v", err)
	}

	// Create our Connect-based FileRepository Server
	filerepositoryServer := connect.NewFileRepositoryServer(filerepositoryUseCase)

	// Generate HTTP handler from Connect
	servicePath, serviceHandler := filerepositoryconnect.NewFileRepositoryServiceHandler(filerepositoryServer)

	// Add the handler to the mux with logging middleware
	mux.Handle(servicePath, loggingMiddleware(serviceHandler))
}
