package usecase

import (
	"context"
	"database/sql"
	"encoding/base64"
	"encoding/hex"
	"errors"
	"fmt"
	"log"
	"os"
	"regexp"
	"strconv"
	"strings"
	"time"

	cmncontext "common/context"

	"github.com/google/uuid"
	_ "github.com/lib/pq"
	"github.com/twilio/twilio-go"
	api "github.com/twilio/twilio-go/rest/api/v2010"
	"google.golang.org/protobuf/types/known/timestamppb"

	services "common/clients/services"
	zello "common/clients/zello"
	utils "common/utils"
	"orgs/internal/config"
	orgRepository "orgs/internal/data"
	util "orgs/internal/util"
	assets "proto/hero/assets/v2"
	orgs "proto/hero/orgs/v1"
	permspb "proto/hero/permissions/v1"

	"github.com/aws/aws-sdk-go-v2/aws"
	awsconfig "github.com/aws/aws-sdk-go-v2/config"
	"github.com/aws/aws-sdk-go-v2/service/cognitoidentityprovider"
	"github.com/aws/aws-sdk-go-v2/service/cognitoidentityprovider/types"
	"github.com/aws/aws-sdk-go-v2/service/kms"

	"crypto/rand"

	connect "connectrpc.com/connect"
	"golang.org/x/crypto/bcrypt"
)

const UsaCountryCode = "US"

// Regex validator for a friendly name for a Twilio queue
var friendlyNamePattern = regexp.MustCompile(`^[\w\s\-]+$`)

// Max length for a friendly name for a Twilio queue
const maxFriendlyNameLen = 64

func ValidateFriendlyName(name string) error {
	if len(name) == 0 {
		return errors.New("friendly name cannot be empty")
	}
	if len(name) > maxFriendlyNameLen {
		return errors.New("friendly name exceeds max length (64 chars)")
	}
	if !friendlyNamePattern.MatchString(name) {
		return errors.New("friendly name contains invalid characters (only letters, numbers, spaces, dashes, underscores allowed)")
	}
	return nil
}

// OrgUseCase defines the use-case layer for organization operations.
type OrgUseCase struct {
	database                *sql.DB // Only needed for transactional operations.
	orgRepo                 orgRepository.OrgRepository
	kmsClient               *kms.Client
	kmsKeyArn               string
	cognitoClient           *cognitoidentityprovider.Client
	userPoolId              string
	templates               *config.TemplatesConfig
	permsClient             services.PermissionClient
	commsServerPublicDomain string
}

// NewOrgUseCase creates a new OrgUseCase.
func NewOrgUseCase(
	database *sql.DB,
	orgRepo orgRepository.OrgRepository) *OrgUseCase {
	// For in-memory orgRepository, we need a dummy DB to support transactions.
	if database == nil {
		var openError error
		database, openError = sql.Open("sqlite", ":memory:")
		if openError != nil {
			log.Fatalf("failed to open in-memory sqlite db: %v", openError)
		}
	}

	ctx := context.Background()
	cfg, err := awsconfig.LoadDefaultConfig(ctx, awsconfig.WithRegion("us-west-2"))
	if err != nil {
		log.Fatalf("failed to load AWS config: %v", err)
	}

	kmsKeyArn := os.Getenv("KMS_KEY_ARN")
	if kmsKeyArn == "" {
		kmsKeyArn = "test-key-arn"
	}

	kmsClient := kms.NewFromConfig(cfg)
	cognitoClient := cognitoidentityprovider.NewFromConfig(cfg)

	userPoolId := os.Getenv("COGNITO_USER_POOL_ID")
	if userPoolId == "" {
		userPoolId = "us-west-2_7SyrT2GBd" // Default to the one from the middleware
	}

	// Load organization templates
	var templates *config.TemplatesConfig
	templates, err = config.LoadTemplates("/app/config/org_templates.yaml")
	if err != nil {
		log.Fatalf("failed to load organization templates: %v", err)
	}

	permsClient := services.NewPermissionClient(os.Getenv("PERMS_SERVICE_URL"))

	commsServerPublicDomain := os.Getenv("COMMS_SERVER_PUBLIC_DOMAIN")
	if commsServerPublicDomain == "" {
		commsServerPublicDomain = "communications.basic.api.gethero.com"
	}

	return &OrgUseCase{
		database:                database,
		orgRepo:                 orgRepo,
		kmsClient:               kmsClient,
		kmsKeyArn:               kmsKeyArn,
		cognitoClient:           cognitoClient,
		userPoolId:              userPoolId,
		templates:               templates,
		permsClient:             permsClient,
		commsServerPublicDomain: commsServerPublicDomain,
	}
}

// CreateOrg creates a new organization.
func (orgUseCase *OrgUseCase) CreateOrg(ctx context.Context, org *orgs.Org) error {
	if org.Name == "" {
		return fmt.Errorf("organization name is required")
	}
	if org.ServiceType == orgs.ServiceType_SERVICE_TYPE_UNSPECIFIED {
		org.ServiceType = orgs.ServiceType_SERVICE_TYPE_DEMO
	}

	if org.PrimaryPhoneNumber == "" {
		return fmt.Errorf("primary_phone_number is required for twilio phone number claiming")
	}

	// Validate call forwarding settings
	if org.IsCallForwardingEnabled {
		if org.CallForwardingType == "" {
			return fmt.Errorf("call_forwarding_type is required when call forwarding is enabled")
		}
		switch org.CallForwardingType {
		case "PSTN":
			if org.PrimaryPhoneNumber == "" {
				return fmt.Errorf("primary_phone_number is required for PSTN call forwarding")
			}
			// Standardize and validate the phone number format
			standardizedNumber, err := utils.StandardizeUSPhoneNumber(org.PrimaryPhoneNumber)
			if err != nil {
				return fmt.Errorf("invalid primary_phone_number format: %v", err)
			}
			org.PrimaryPhoneNumber = standardizedNumber
		case "SIP":
			if org.SipUri == "" {
				return fmt.Errorf("sip_uri is required for SIP call forwarding")
			}
			// Basic SIP URI format validation
			if !strings.HasPrefix(org.SipUri, "sip:") {
				return fmt.Errorf("invalid sip_uri format: must start with 'sip:'")
			}
		default:
			return fmt.Errorf("invalid call_forwarding_type: %s", org.CallForwardingType)
		}
	}

	// Get the template for this organization
	if org.TemplateId == "" {
		org.TemplateId = "local" // Default to full template if none specified
	}
	template, err := orgUseCase.templates.GetTemplate(org.TemplateId)
	if err != nil {
		return fmt.Errorf("failed to get template: %v", err)
	}

	// Create the org in the repository
	createdOrg, err := orgUseCase.orgRepo.CreateOrg(ctx, nil, org)
	if err != nil {
		return fmt.Errorf("failed to create org: %v", err)
	}

	// Track resources for potential rollback
	var cognitoGroupCreated bool
	var twilioNumberClaimed bool
	var twilioQueueCreated bool
	var twilioAppCreated bool
	var zelloChannelCreated bool
	var TwilioQueueSid string

	// Function to handle rollback if needed
	rollback := func() {
		// Rollback in reverse order of creation
		if zelloChannelCreated {
			if err := orgUseCase.DeleteZelloOrgChannels(ctx, createdOrg.Id); err != nil {
				log.Printf("Rollback error: failed to delete Zello channels: %v", err)
			}
		}

		if twilioAppCreated && org.TwimlAppSid != "" {
			client := twilio.NewRestClient()
			if err := client.Api.DeleteApplication(org.TwimlAppSid, &api.DeleteApplicationParams{}); err != nil {
				log.Printf("Rollback error: failed to delete Twilio application: %v", err)
			}
		}

		if twilioQueueCreated && TwilioQueueSid != "" {
			client := twilio.NewRestClient()
			params := &api.DeleteQueueParams{}
			if err := client.Api.DeleteQueue(TwilioQueueSid, params); err != nil {
				log.Printf("Rollback error: failed to delete Twilio queue: %v", err)
			}
		}

		if twilioNumberClaimed && org.TwilioNumberSid != "" {
			client := twilio.NewRestClient()
			params := &api.DeleteIncomingPhoneNumberParams{}
			if err := client.Api.DeleteIncomingPhoneNumber(org.TwilioNumberSid, params); err != nil {
				log.Printf("Rollback error: failed to release Twilio number: %v", err)
			}
		}

		if cognitoGroupCreated {
			_, err := orgUseCase.cognitoClient.DeleteGroup(ctx, &cognitoidentityprovider.DeleteGroupInput{
				UserPoolId: aws.String(orgUseCase.userPoolId),
				GroupName:  aws.String("org:" + strconv.Itoa(int(createdOrg.Id))),
			})
			if err != nil {
				log.Printf("Rollback error: failed to delete Cognito group: %v", err)
			}
		}

		// Finally, delete the org from the database (this will cascade delete API users)
		if err := orgUseCase.orgRepo.DeleteOrg(ctx, nil, createdOrg.Id); err != nil {
			log.Printf("Rollback error: failed to delete org: %v", err)
		}
	}

	// Create Cognito group if enabled in template
	if template.Features.Cognito {
		_, err = orgUseCase.cognitoClient.CreateGroup(ctx, &cognitoidentityprovider.CreateGroupInput{
			UserPoolId:  aws.String(orgUseCase.userPoolId),
			GroupName:   aws.String("org:" + strconv.Itoa(int(createdOrg.Id))),
			Description: aws.String(fmt.Sprintf("Group for organization %s", org.Name)),
		})
		if err != nil {
			rollback()
			return fmt.Errorf("failed to create Cognito group: %v", err)
		}
		cognitoGroupCreated = true
	}

	// Set up Twilio if enabled in template
	if template.Features.Twilio {
		phoneNumber, err := orgUseCase.FetchAvailablePhoneNumberCountry(ctx, org.PrimaryPhoneNumber)
		if err != nil {
			rollback()
			return fmt.Errorf("failed to fetch available phone number: %v", err)
		}
		org.TwilioNumber = phoneNumber

		twilioNumberSid, err := orgUseCase.ClaimPhoneNumber(ctx, phoneNumber)
		if err != nil {
			rollback()
			return fmt.Errorf("failed to claim phone number: %v", err)
		}
		twilioNumberClaimed = true
		org.TwilioNumberSid = twilioNumberSid

		apiUsername, apiPassword, err := orgUseCase.CreateOrgAPIUser(ctx, org.Id)
		if err != nil {
			rollback()
			return fmt.Errorf("failed to create API user: %v", err)
		}

		appSid, err := orgUseCase.CreateTwilioApplication(ctx, org.Name, apiUsername.Username, apiPassword)
		if err != nil {
			rollback()
			return fmt.Errorf("failed to create Twilio application: %v", err)
		}
		twilioAppCreated = true
		org.TwimlAppSid = appSid

		// Connect the phone number to the Twilio application
		client := twilio.NewRestClient()
		params := &api.UpdateIncomingPhoneNumberParams{}
		params.SetVoiceApplicationSid(appSid)
		_, err = client.Api.UpdateIncomingPhoneNumber(org.TwilioNumberSid, params)
		if err != nil {
			rollback()
			return fmt.Errorf("failed to connect phone number to Twilio application: %v", err)
		}

		// create a twilio queue
		queueSid, err := orgUseCase.CreateTwilioQueue(ctx, org.Id, org.Name)
		if err != nil {
			rollback()
			return fmt.Errorf("failed to create Twilio queue: %v", err)
		}
		twilioQueueCreated = true
		TwilioQueueSid = queueSid
	}

	// Set up Zello if enabled in template
	if template.Features.Zello {
		err = orgUseCase.CreateZelloChannel(ctx, org.Id, "Everyone")
		if err != nil {
			rollback()
			return fmt.Errorf("failed to create Zello channel: %v", err)
		}
		zelloChannelCreated = true
	}

	// Update the org with any new credentials
	return orgUseCase.orgRepo.UpdateOrg(ctx, nil, org)
}

func generateRandomPassword() (string, error) {
	bytes := make([]byte, 16) // 16 bytes = 128 bits
	_, err := rand.Read(bytes)
	if err != nil {
		return "", err
	}
	return hex.EncodeToString(bytes), nil
}

// CreateOrgAPIUser creates a new organization API user.
func (orgUseCase *OrgUseCase) CreateOrgAPIUser(ctx context.Context, orgID int32) (*orgs.CreateOrgAPIUserResponse, string, error) {

	tokenOrgId := cmncontext.GetOrgId(ctx)
	// allow the meta org to specify the orgId
	var orgIdToUse int32
	if tokenOrgId == -1 {
		orgIdToUse = orgID
	} else {
		orgIdToUse = tokenOrgId
	}

	username := uuid.New().String()
	password, err := generateRandomPassword()
	if err != nil {
		return nil, "", fmt.Errorf("failed to generate password: %v", err)
	}

	input := &kms.EncryptInput{
		KeyId:     aws.String(orgUseCase.kmsKeyArn),
		Plaintext: []byte(password),
	}
	result, err := orgUseCase.kmsClient.Encrypt(ctx, input)
	if err != nil {
		return nil, "", fmt.Errorf("failed to encrypt data: %v", err)
	}

	encryptedPassword := base64.StdEncoding.EncodeToString(result.CiphertextBlob)
	hashedPassword, err := bcrypt.GenerateFromPassword([]byte(password), bcrypt.DefaultCost)
	if err != nil {
		return nil, "", fmt.Errorf("failed to hash password: %v", err)
	}
	hashedPasswordString := string(hashedPassword)

	err = orgUseCase.orgRepo.CreateOrgAPIUser(ctx, nil, orgIdToUse, username, encryptedPassword, hashedPasswordString)
	if err != nil {
		return nil, "", err
	}

	return &orgs.CreateOrgAPIUserResponse{
		Username:          username,
		EncryptedPassword: encryptedPassword,
		HashedPassword:    hashedPasswordString,
	}, password, nil
}

// GetOrg retrieves an organization by its ID.
func (orgUseCase *OrgUseCase) GetOrg(ctx context.Context, orgID int32) (*orgs.Org, error) {
	return orgUseCase.orgRepo.GetOrg(ctx, nil, orgID)
}

// DeleteOrg deletes an organization and its associated Twilio resources.
func (orgUseCase *OrgUseCase) DeleteOrg(ctx context.Context, orgID int32) error {
	// First get the org to access Twilio credentials
	org, err := orgUseCase.orgRepo.GetOrg(ctx, nil, orgID)
	if err != nil {
		return fmt.Errorf("failed to get org: %v", err)
	}

	accountSid := os.Getenv("TWILIO_ACCOUNT_SID")
	if accountSid == "" {
		return fmt.Errorf("TWILIO_ACCOUNT_SID is not set")
	}

	twilioQueueSid, err := orgUseCase.orgRepo.GetTwilioQueueSid(ctx, nil, orgID)
	if err != nil {
		return fmt.Errorf("failed to get Twilio queue sid: %v", err)
	}

	// Delete the org's twilio queue if it exists
	if twilioQueueSid != "" {
		client := twilio.NewRestClient()
		err := client.Api.DeleteQueue(twilioQueueSid, &api.DeleteQueueParams{})
		if err != nil {
			return fmt.Errorf("failed to delete Twilio queue %s: %v", twilioQueueSid, err)
		}
		err = orgUseCase.orgRepo.DeleteTwilioQueueConfiguration(ctx, nil, orgID)
		if err != nil {
			return fmt.Errorf("failed to delete Twilio queue configuration: %v", err)
		}
	}

	// Delete Twilio application if it exists
	if org.TwimlAppSid != "" {
		client := twilio.NewRestClient()
		err := client.Api.DeleteApplication(org.TwimlAppSid, &api.DeleteApplicationParams{})
		if err != nil {
			return fmt.Errorf("failed to delete Twilio application %s: %v", org.TwimlAppSid, err)
		}
	}

	// Release Twilio phone number if it exists
	if org.TwilioNumber != "" {
		client := twilio.NewRestClient()
		params := &api.DeleteIncomingPhoneNumberParams{}
		err := client.Api.DeleteIncomingPhoneNumber(org.TwilioNumberSid, params)
		if err != nil {
			return fmt.Errorf("failed to release Twilio number %s: %v", org.TwilioNumber, err)
		}
	}

	// delete the Zello channels
	err = orgUseCase.DeleteZelloOrgChannels(ctx, orgID)
	if err != nil {
		return fmt.Errorf("failed to delete Zello channels: %v", err)
	}

	// delete all the users in the org
	err = orgUseCase.DeleteOrgAssetsAndCognitoUsers(ctx, orgID)
	if err != nil {
		return fmt.Errorf("failed to delete org users: %v", err)
	}

	// Delete Cognito group
	_, err = orgUseCase.cognitoClient.DeleteGroup(ctx, &cognitoidentityprovider.DeleteGroupInput{
		UserPoolId: aws.String(orgUseCase.userPoolId),
		GroupName:  aws.String("org:" + strconv.Itoa(int(orgID))),
	})
	if err != nil {
		return fmt.Errorf("failed to delete Cognito group: %v", err)
	}

	// Finally delete the org from our database
	return orgUseCase.orgRepo.DeleteOrg(ctx, nil, orgID)
}

// ListOrgs returns a list of all organizations.
func (orgUseCase *OrgUseCase) ListOrgs(ctx context.Context) ([]*orgs.Org, error) {
	return orgUseCase.orgRepo.ListOrgs(ctx, nil)
}

// ValidateOrgCreds validates organization credentials.
func (orgUseCase *OrgUseCase) ValidateOrgCreds(ctx context.Context, username, password string) (bool, *orgs.OrgApiUser, error) {
	if username == "" || password == "" {
		return false, nil, fmt.Errorf("username and password are required")
	}

	orgAPIUser, err := orgUseCase.orgRepo.GetOrgAPIUser(ctx, nil, username)
	if err != nil {
		// Log the specific error but return a generic message
		log.Printf("Error fetching credentials for user %s: %v", username, err)
		return false, nil, fmt.Errorf("authentication failed")
	}

	log.Printf("orgAPIUser: %v", orgAPIUser.HashedPassword)

	err = bcrypt.CompareHashAndPassword([]byte(orgAPIUser.HashedPassword), []byte(password))
	if err != nil {
		if err == bcrypt.ErrMismatchedHashAndPassword {
			// Wrong password - don't expose this detail in the error
			return false, nil, fmt.Errorf("authentication failed")
		}
		// Unexpected error during comparison
		log.Printf("Error comparing password hash for user %s: %v", username, err)
		return false, nil, fmt.Errorf("authentication failed")
	}

	return true, orgAPIUser, nil
}

func (orgUseCase *OrgUseCase) GetOrgAPIUserPrivateById(ctx context.Context, userId string) (*orgs.OrgApiUserPrivate, error) {
	if userId == "" {
		return nil, fmt.Errorf("user ID is required")
	}

	orgAPIUser, err := orgUseCase.orgRepo.GetOrgAPIUserById(ctx, nil, userId)
	if err != nil {
		return nil, fmt.Errorf("error getting org API user: %v", err)
	}

	// decrypt the password
	ciphertextBlob, err := base64.StdEncoding.DecodeString(orgAPIUser.EncryptedPassword)
	if err != nil {
		return nil, fmt.Errorf("failed to decode base64 encrypted password: %v", err)
	}
	input := &kms.DecryptInput{
		CiphertextBlob: ciphertextBlob,
		KeyId:          aws.String(orgUseCase.kmsKeyArn),
	}
	decryptedPassword, err := orgUseCase.kmsClient.Decrypt(ctx, input)
	if err != nil {
		return nil, fmt.Errorf("error decrypting password: %v", err)
	}
	privateOrgAPIUser := orgs.OrgApiUserPrivate{
		Id:          orgAPIUser.Id,
		Username:    orgAPIUser.Username,
		RawPassword: string(decryptedPassword.Plaintext),
	}
	return &privateOrgAPIUser, nil
}

// UpdateOrg updates an organization's fields.
func (orgUseCase *OrgUseCase) UpdateOrg(ctx context.Context, updatedOrg *orgs.Org) (*orgs.Org, error) {
	existingOrg, err := orgUseCase.orgRepo.GetOrg(ctx, nil, updatedOrg.Id)
	if err != nil {
		return nil, err
	}

	// Validate call forwarding settings
	if updatedOrg.IsCallForwardingEnabled {
		if updatedOrg.CallForwardingType == "" {
			return nil, fmt.Errorf("call_forwarding_type is required when call forwarding is enabled")
		}
		switch updatedOrg.CallForwardingType {
		case "PSTN":
			if updatedOrg.PrimaryPhoneNumber == "" {
				return nil, fmt.Errorf("primary_phone_number is required for PSTN call forwarding")
			}
			// Standardize and validate the phone number format
			standardizedNumber, err := utils.StandardizeUSPhoneNumber(updatedOrg.PrimaryPhoneNumber)
			if err != nil {
				return nil, fmt.Errorf("invalid primary_phone_number format: %v", err)
			}
			updatedOrg.PrimaryPhoneNumber = standardizedNumber
		case "SIP":
			if updatedOrg.SipUri == "" {
				return nil, fmt.Errorf("sip_uri is required for SIP call forwarding")
			}
			// Basic SIP URI format validation
			if !strings.HasPrefix(updatedOrg.SipUri, "sip:") {
				return nil, fmt.Errorf("invalid sip_uri format: must start with 'sip:'")
			}
		default:
			return nil, fmt.Errorf("invalid call_forwarding_type: %s", updatedOrg.CallForwardingType)
		}
	}

	// Update only fields that are provided
	if updatedOrg.Name != "" {
		existingOrg.Name = updatedOrg.Name
	}
	if len(updatedOrg.Domains) > 0 {
		existingOrg.Domains = updatedOrg.Domains
	}
	if updatedOrg.TwimlAppSid != "" {
		existingOrg.TwimlAppSid = updatedOrg.TwimlAppSid
	}
	if updatedOrg.TwilioNumber != "" {
		existingOrg.TwilioNumber = updatedOrg.TwilioNumber
	}
	if updatedOrg.ServiceType != orgs.ServiceType_SERVICE_TYPE_UNSPECIFIED {
		existingOrg.ServiceType = updatedOrg.ServiceType
	}
	if updatedOrg.IsCallForwardingEnabled != existingOrg.IsCallForwardingEnabled {
		existingOrg.IsCallForwardingEnabled = updatedOrg.IsCallForwardingEnabled
	}
	if updatedOrg.CallForwardingType != "" {
		existingOrg.CallForwardingType = updatedOrg.CallForwardingType
	}
	if updatedOrg.PrimaryPhoneNumber != "" {
		existingOrg.PrimaryPhoneNumber = updatedOrg.PrimaryPhoneNumber
	}
	if updatedOrg.SipUri != "" {
		existingOrg.SipUri = updatedOrg.SipUri
	}

	// Always update the update time
	existingOrg.UpdatedAt = timestamppb.New(time.Now())

	// Persist the updated org
	if err := orgUseCase.orgRepo.UpdateOrg(ctx, nil, existingOrg); err != nil {
		return nil, err
	}

	return existingOrg, nil
}

// GetZelloChannels returns a list of all Zello channels for an organization.
func (orgUseCase *OrgUseCase) GetZelloChannels(ctx context.Context) ([]*orgs.ZelloChannel, error) {
	return orgUseCase.orgRepo.GetZelloChannels(ctx, nil)
}

// CreateZelloChannel creates a new Zello channel for an organization.
func (orgUseCase *OrgUseCase) CreateZelloChannel(ctx context.Context, orgID int32, channelName string) error {
	zelloClient, err := zello.NewZelloClient()
	if err != nil {
		return fmt.Errorf("failed to create Zello client: %v", err)
	}

	namespacedChannelName := strconv.Itoa(int(orgID)) + "_" + channelName
	_, err = zelloClient.AddChannel(namespacedChannelName, true, false, []string{})
	if err != nil {
		return fmt.Errorf("failed to create Zello channel: %v", err)
	}

	zelloChannel := &orgs.ZelloChannel{
		OrgId:          orgID,
		ZelloChannelId: namespacedChannelName,
		DisplayName:    channelName,
	}

	return orgUseCase.orgRepo.CreateZelloChannel(ctx, nil, orgID, zelloChannel)
}

// DeleteZelloOrgChannels deletes all Zello channels for an organization.
func (orgUseCase *OrgUseCase) DeleteZelloOrgChannels(ctx context.Context, orgID int32) error {
	zelloClient, err := zello.NewZelloClient()
	if err != nil {
		return fmt.Errorf("failed to create Zello client: %v", err)
	}

	orgZelloChannels, err := orgUseCase.orgRepo.GetZelloChannels(ctx, nil)
	if err != nil {
		return fmt.Errorf("failed to get Zello channels: %v", err)
	}
	orgZelloChannelIds := []string{}
	for _, zelloChannel := range orgZelloChannels {
		orgZelloChannelIds = append(orgZelloChannelIds, zelloChannel.ZelloChannelId)
	}
	_, err = zelloClient.DeleteChannel(orgZelloChannelIds)
	if err != nil {
		return fmt.Errorf("failed to delete Zello channels: %v", err)
	}

	return orgUseCase.orgRepo.DeleteZelloChannel(ctx, nil, orgID, orgZelloChannelIds)
}

// FetchAvailablePhoneNumberCountry fetches available phone number information for a specific country.
func (orgUseCase *OrgUseCase) FetchAvailablePhoneNumberCountry(ctx context.Context, phoneNumber string) (string, error) {
	client := twilio.NewRestClient()

	localParams := &api.ListAvailablePhoneNumberLocalParams{
		NearNumber: aws.String(phoneNumber),
	}
	phoneNumbers := []string{}
	localResp, err := client.Api.ListAvailablePhoneNumberLocal(UsaCountryCode, localParams)
	if err != nil {
		log.Printf("Warning: failed to fetch local numbers: %v", err)
		return "", fmt.Errorf("failed to fetch local numbers: %v", err)
	} else {
		log.Printf("Found %d local numbers", len(localResp))
		for _, number := range localResp {
			if number.PhoneNumber != nil {
				log.Printf("Local number: %s", *number.PhoneNumber)
				phoneNumbers = append(phoneNumbers, *number.PhoneNumber)
			}
		}
	}

	bestNumber := util.MostRecognizableNumber(phoneNumbers)
	if bestNumber == "" {
		return "", fmt.Errorf("no suitable phone numbers found")
	}

	log.Printf("Best number: %s with score %d", bestNumber, util.ScoreNumber(bestNumber))
	log.Printf("All numbers: %v", phoneNumbers)

	return bestNumber, nil
}

// ClaimPhoneNumber claims a specific phone number using Twilio.
func (orgUseCase *OrgUseCase) ClaimPhoneNumber(ctx context.Context, phoneNumber string) (string, error) {
	client := twilio.NewRestClient()

	params := &api.CreateIncomingPhoneNumberParams{}
	params.SetPhoneNumber(phoneNumber)

	resp, err := client.Api.CreateIncomingPhoneNumber(params)
	if err != nil {
		return "", fmt.Errorf("failed to claim phone number: %v", err)
	}

	if resp.AccountSid == nil {
		return "", fmt.Errorf("failed to claim phone number: no account SID returned")
	}

	log.Printf("Successfully claimed phone number %s with account SID %s", phoneNumber, *resp.AccountSid)
	return *resp.Sid, nil
}

// CreateTwilioQueue creates a new Twilio queue for an organization
func (orgUseCase *OrgUseCase) CreateTwilioQueue(ctx context.Context, orgID int32, friendlyName string) (string, error) {
	if err := ValidateFriendlyName(friendlyName); err != nil {
		return "", fmt.Errorf("invalid queue friendly name: %w", err)
	}

	client := twilio.NewRestClient()

	// Create a queue with the organization's name
	params := &api.CreateQueueParams{
		FriendlyName: &friendlyName,
	}

	queue, err := client.Api.CreateQueue(params)
	if err != nil {
		return "", fmt.Errorf("failed to create Twilio queue: %w", err)
	}

	// Store the queue SID in the twilio_queue_configurations table
	description := fmt.Sprintf("Queue for organization %d", orgID)
	err = orgUseCase.orgRepo.StoreTwilioQueueConfiguration(ctx, nil, orgID, friendlyName, *queue.Sid, description)
	if err != nil {
		// Try to clean up the created queue if we can't store its SID
		deleteParams := &api.DeleteQueueParams{}
		if deleteErr := client.Api.DeleteQueue(*queue.Sid, deleteParams); deleteErr != nil {
			// Log the delete error but return the original error
			log.Printf("Failed to delete Twilio queue %s after storage error: %v", *queue.Sid, deleteErr)
		}
		return "", err
	}

	return *queue.Sid, nil
}

// CreateTwilioApplication creates a new Twilio application for voice calls.
func (orgUseCase *OrgUseCase) CreateTwilioApplication(ctx context.Context, friendlyName string, username string, password string) (string, error) {
	client := twilio.NewRestClient()

	params := &api.CreateApplicationParams{}
	params.SetVoiceMethod("POST")
	params.SetVoiceUrl("https://" + username + ":" + password + "@" + orgUseCase.commsServerPublicDomain + "/hero.communications.v1.TwilioWebhookService/voice")
	params.SetStatusCallback("https://" + username + ":" + password + "@" + orgUseCase.commsServerPublicDomain + "/hero.communications.v1.TwilioWebhookService/callstatus")
	params.SetFriendlyName("Voice Channel - " + friendlyName)

	resp, err := client.Api.CreateApplication(params)
	if err != nil {
		return "", fmt.Errorf("failed to create Twilio application: %v", err)
	}

	if resp.AccountSid == nil {
		return "", fmt.Errorf("failed to create Twilio application: no account SID returned")
	}

	log.Printf("Successfully created Twilio application %s with account SID %s", friendlyName, *resp.AccountSid)
	return *resp.Sid, nil
}

// DeleteOrgAssetsAndCognitoUsers lists all assets for an organization and deletes any Cognito users associated with those assets.
func (orgUseCase *OrgUseCase) DeleteOrgAssetsAndCognitoUsers(ctx context.Context, orgID int32) error {
	// First get the org to verify it exists
	_, err := orgUseCase.orgRepo.GetOrg(ctx, nil, orgID)
	if err != nil {
		return fmt.Errorf("failed to get org: %v", err)
	}

	// Get assets with Cognito JWT sub from the repository
	assets, err := orgUseCase.orgRepo.GetOrgAssetsWithCognitoJwtSub(ctx, nil, orgID)
	if err != nil {
		return fmt.Errorf("failed to get assets: %v", err)
	}

	// Create a Zello client for deleting Zello users
	zelloClient, err := zello.NewZelloClient()
	if err != nil {
		return fmt.Errorf("failed to create Zello client: %v", err)
	}
	defer func() {
		if err := zelloClient.Logout(); err != nil {
			log.Printf("could not logout Zello client: %v", err)
		}
	}()

	// Delete Cognito users and Zello users for assets
	for _, asset := range assets {
		// Delete the Cognito user if it exists
		if asset.CognitoJwtSub != "" {
			_, err = orgUseCase.cognitoClient.AdminDeleteUser(ctx, &cognitoidentityprovider.AdminDeleteUserInput{
				UserPoolId: aws.String(orgUseCase.userPoolId),
				Username:   aws.String(asset.CognitoJwtSub),
			})
			if err != nil {
				// Log the error but continue with other assets
				log.Printf("failed to delete Cognito user %s for asset %s: %v", asset.CognitoJwtSub, asset.ID, err)
			} else {
				log.Printf("successfully deleted Cognito user %s for asset %s", asset.CognitoJwtSub, asset.ID)
			}
		}

		// Get Zello credentials for the asset
		zelloCreds, err := orgUseCase.orgRepo.GetZelloCreds(ctx, nil, asset.ID)
		if err != nil {
			// Log the error but continue with other assets
			log.Printf("failed to get Zello credentials for asset %s: %v", asset.ID, err)
			continue
		}

		// Delete the Zello user
		if zelloCreds != nil && zelloCreds.Username != "" {
			_, err = zelloClient.DeleteUser([]string{zelloCreds.Username})
			if err != nil {
				// Log the error but continue with other assets
				log.Printf("failed to delete Zello user %s for asset %s: %v", zelloCreds.Username, asset.ID, err)
			} else {
				log.Printf("successfully deleted Zello user %s for asset %s", zelloCreds.Username, asset.ID)
			}

			// Delete the Zello credentials from our database
			err = orgUseCase.orgRepo.DeleteZelloCreds(ctx, nil, asset.ID)
			if err != nil {
				// Log the error but continue with other assets
				log.Printf("failed to delete Zello credentials for asset %s: %v", asset.ID, err)
			}
		}
	}

	return nil
}

// InsertOrgQueue inserts a new organization queue.
func (orgUseCase *OrgUseCase) InsertOrgQueue(ctx context.Context, req *orgs.InsertOrgQueueRequest) (*orgs.OrgQueue, error) {
	// Add any business logic/validation here before calling the repository.
	// For example, validating req.FriendlyName, req.TwilioQueueSid, etc.
	// For now, it's a direct pass-through.
	var orgIDToUse int32
	tokenOrgId := cmncontext.GetOrgId(ctx)
	// only allow the meta org to insert queues for other orgs
	if tokenOrgId == -1 {
		orgIDToUse = int32(req.OrgId)
	} else {
		orgIDToUse = tokenOrgId
	}
	req.OrgId = orgIDToUse
	return orgUseCase.orgRepo.InsertOrgQueue(ctx, nil, req)
}

// AddUserToCognitoGroup adds a user to a Cognito group.
// AddUserToCognitoGroup is a special case, potentially very powerful.
// Its intent is to add a user as a guest to a different org, and attach this information
// directly to the cognito token.

// Normally, a user's role primarily determines their permissions.

// But a role in org X cannot, in general, access org Y and should therefore not be able to access this endpoint.

// So as an additional security measure, we run an additional check to confirm the user already has a role in the org
// that they are trying to access.

// This role assignment would necesssarily have to be done by a member of the host org, thus keeping the host org
// in control of the guest's entry.

// This role will also serve to control access, when they make requests as a guest.
func (orgUseCase *OrgUseCase) TurnOnGuestMode(ctx context.Context, orgId int32) error {
	if orgId == 0 {
		return fmt.Errorf("org id is required")
	}

	// get username from cmncontext
	username := cmncontext.GetUsername(ctx)
	// orgId := cmncontext.GetOrgId(ctx)

	groupName := "org:" + strconv.Itoa(int(orgId)) + ":guest"

	// check if user has role in org
	request := connect.NewRequest(&permspb.GetUserRolesRequest{
		OrgId:  orgId,
		UserId: username,
	})
	resp, err := orgUseCase.permsClient.GetUserRoles(ctx, request)
	if err != nil {
		return fmt.Errorf("failed to get role: %v", err)
	}
	if len(resp.Msg.Roles) == 0 {
		return fmt.Errorf("user does not have a role in org")
	}

	// drop cognito prefix
	username = strings.TrimPrefix(username, "cognito:")
	// Add user to the group
	_, err = orgUseCase.cognitoClient.AdminAddUserToGroup(ctx, &cognitoidentityprovider.AdminAddUserToGroupInput{
		UserPoolId: aws.String(orgUseCase.userPoolId),
		Username:   aws.String(username),
		GroupName:  aws.String(groupName),
	})
	if err != nil {
		return fmt.Errorf("failed to add user to Cognito group: %v", err)
	}

	return nil
}

// this one is simpler. We are less concerned when privileges are being dropped.
func (orgUseCase *OrgUseCase) TurnOffGuestMode(ctx context.Context, orgId int32) error {

	username := cmncontext.GetUsername(ctx)
	// drop cognito prefix
	username = strings.TrimPrefix(username, "cognito:")

	groupName := "org:" + strconv.Itoa(int(orgId)) + ":guest"

	// Remove user from the group
	_, err := orgUseCase.cognitoClient.AdminRemoveUserFromGroup(ctx, &cognitoidentityprovider.AdminRemoveUserFromGroupInput{
		UserPoolId: aws.String(orgUseCase.userPoolId),
		Username:   aws.String(username),
		GroupName:  aws.String(groupName),
	})
	if err != nil {
		return fmt.Errorf("failed to remove user from Cognito group: %v", err)
	}

	return nil
}

// CreateCognitoUser creates a new user in Cognito for an organization.
func (orgUseCase *OrgUseCase) CreateCognitoUser(ctx context.Context, orgID int32, username, email, password string) (string, error) {
	if username == "" || email == "" || password == "" {
		return "", fmt.Errorf("username, email, and password are required")
	}

	// Validate email format
	emailRegex := regexp.MustCompile(`^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$`)
	if !emailRegex.MatchString(email) {
		return "", fmt.Errorf("invalid email format")
	}

	// Validate password strength
	if len(password) < 8 {
		return "", fmt.Errorf("password must be at least 8 characters long")
	}

	// Create the user in Cognito
	createUserOutput, err := orgUseCase.cognitoClient.AdminCreateUser(ctx, &cognitoidentityprovider.AdminCreateUserInput{
		UserPoolId: aws.String(orgUseCase.userPoolId),
		Username:   aws.String(username),
		UserAttributes: []types.AttributeType{
			{
				Name:  aws.String("email"),
				Value: aws.String(email),
			},
			{
				Name:  aws.String("email_verified"),
				Value: aws.String("true"),
			},
		},
		MessageAction: "SUPPRESS",
	})
	if err != nil {
		return "", fmt.Errorf("failed to create Cognito user: %v", err)
	}

	// Get the Cognito sub (user ID)
	var cognitoSub string
	for _, attr := range createUserOutput.User.Attributes {
		if *attr.Name == "sub" {
			cognitoSub = *attr.Value
			break
		}
	}
	if cognitoSub == "" {
		// If setting password fails, try to clean up by deleting the user
		_, deleteErr := orgUseCase.cognitoClient.AdminDeleteUser(ctx, &cognitoidentityprovider.AdminDeleteUserInput{
			UserPoolId: aws.String(orgUseCase.userPoolId),
			Username:   aws.String(username),
		})
		if deleteErr != nil {
			log.Printf("Failed to delete Cognito user after sub not found: %v", deleteErr)
		}
		return "", fmt.Errorf("failed to get Cognito sub for user")
	}

	// Set the user's password
	_, err = orgUseCase.cognitoClient.AdminSetUserPassword(ctx, &cognitoidentityprovider.AdminSetUserPasswordInput{
		UserPoolId: aws.String(orgUseCase.userPoolId),
		Username:   aws.String(username),
		Password:   aws.String(password),
		Permanent:  true,
	})
	if err != nil {
		// If setting password fails, try to clean up by deleting the user
		_, deleteErr := orgUseCase.cognitoClient.AdminDeleteUser(ctx, &cognitoidentityprovider.AdminDeleteUserInput{
			UserPoolId: aws.String(orgUseCase.userPoolId),
			Username:   aws.String(username),
		})
		if deleteErr != nil {
			log.Printf("Failed to delete Cognito user after password set failure: %v", deleteErr)
		}
		return "", fmt.Errorf("failed to set user password: %v", err)
	}

	// Add user to the organization's group
	groupName := "org:" + strconv.Itoa(int(orgID))
	_, err = orgUseCase.cognitoClient.AdminAddUserToGroup(ctx, &cognitoidentityprovider.AdminAddUserToGroupInput{
		UserPoolId: aws.String(orgUseCase.userPoolId),
		Username:   aws.String(username),
		GroupName:  aws.String(groupName),
	})
	if err != nil {
		// If adding to group fails, try to clean up by deleting the user
		_, deleteErr := orgUseCase.cognitoClient.AdminDeleteUser(ctx, &cognitoidentityprovider.AdminDeleteUserInput{
			UserPoolId: aws.String(orgUseCase.userPoolId),
			Username:   aws.String(username),
		})
		if deleteErr != nil {
			log.Printf("Failed to delete Cognito user after group assignment failure: %v", deleteErr)
		}
		return "", fmt.Errorf("failed to add user to organization group: %v", err)
	}

	return cognitoSub, nil
}

// AddToContactBook creates a new contact record in the organization's contact book
func (orgUseCase *OrgUseCase) AddToContactBook(ctx context.Context, orgID int32, name, phone string) (*orgs.ContactRecord, error) {
	if name == "" {
		return nil, fmt.Errorf("contact name is required")
	}
	if phone == "" {
		return nil, fmt.Errorf("contact phone is required")
	}

	// Standardize and validate the phone number format
	standardizedPhone, err := utils.StandardizeUSPhoneNumber(phone)
	if err != nil {
		return nil, fmt.Errorf("invalid phone number format: %v", err)
	}

	// Validate the organization exists
	_, err = orgUseCase.orgRepo.GetOrg(ctx, nil, orgID)
	if err != nil {
		return nil, fmt.Errorf("organization not found: %v", err)
	}

	// Generate unique ID for the contact
	contactID := uuid.New().String()

	// Create the contact
	contact := &orgs.ContactRecord{
		Id:        contactID,
		OrgId:     orgID,
		Name:      name,
		Phone:     standardizedPhone,
		CreatedAt: timestamppb.New(time.Now()),
		UpdatedAt: timestamppb.New(time.Now()),
	}

	err = orgUseCase.orgRepo.AddToContactBook(ctx, nil, contact)
	if err != nil {
		return nil, fmt.Errorf("failed to create contact: %v", err)
	}

	return contact, nil
}

// UpdateContactInContactBook updates an existing contact record in the organization's contact book
func (orgUseCase *OrgUseCase) UpdateContactInContactBook(ctx context.Context, contactID, name, phone string) (*orgs.ContactRecord, error) {
	if contactID == "" {
		return nil, fmt.Errorf("contact ID is required")
	}
	if name == "" {
		return nil, fmt.Errorf("contact name is required")
	}
	if phone == "" {
		return nil, fmt.Errorf("contact phone is required")
	}

	// Standardize and validate the phone number format
	standardizedPhone, err := utils.StandardizeUSPhoneNumber(phone)
	if err != nil {
		return nil, fmt.Errorf("invalid phone number format: %v", err)
	}

	// Get the existing contact
	existingContact, err := orgUseCase.orgRepo.GetContactFromContactBook(ctx, nil, contactID)
	if err != nil {
		return nil, fmt.Errorf("contact not found: %v", err)
	}

	// Update the contact fields
	existingContact.Name = name
	existingContact.Phone = standardizedPhone
	existingContact.UpdatedAt = timestamppb.New(time.Now())

	err = orgUseCase.orgRepo.UpdateContactInContactBook(ctx, nil, existingContact)
	if err != nil {
		return nil, fmt.Errorf("failed to update contact: %v", err)
	}

	return existingContact, nil
}

// DeleteFromContactBook deletes a contact record from the organization's contact book
func (orgUseCase *OrgUseCase) DeleteFromContactBook(ctx context.Context, contactID string) error {
	if contactID == "" {
		return fmt.Errorf("contact ID is required")
	}

	// Verify the contact exists before deleting
	_, err := orgUseCase.orgRepo.GetContactFromContactBook(ctx, nil, contactID)
	if err != nil {
		return fmt.Errorf("contact not found: %v", err)
	}

	return orgUseCase.orgRepo.DeleteFromContactBook(ctx, nil, contactID)
}

// GetContactFromContactBook retrieves a contact record by its ID from the organization's contact book
func (orgUseCase *OrgUseCase) GetContactFromContactBook(ctx context.Context, contactID string) (*orgs.ContactRecord, error) {
	if contactID == "" {
		return nil, fmt.Errorf("contact ID is required")
	}

	return orgUseCase.orgRepo.GetContactFromContactBook(ctx, nil, contactID)
}

// ListContactsInContactBook returns paginated contact records for an organization's contact book
func (orgUseCase *OrgUseCase) ListContactsInContactBook(ctx context.Context, orgID int32, pageToken string, pageSize int32) ([]*orgs.ContactRecord, string, int32, error) {
	// Verify the organization exists
	_, err := orgUseCase.orgRepo.GetOrg(ctx, nil, orgID)
	if err != nil {
		return nil, "", 0, fmt.Errorf("organization not found: %v", err)
	}

	return orgUseCase.orgRepo.ListContactsInContactBook(ctx, nil, orgID, pageToken, pageSize)
}

// roleNameToAssetType maps role names to corresponding asset types
func roleNameToAssetType(roleName string) assets.AssetType {
	switch strings.ToLower(strings.TrimSpace(roleName)) {
	case "admin", "administrator":
		return assets.AssetType_ASSET_TYPE_DISPATCHER
	case "dispatcher":
		return assets.AssetType_ASSET_TYPE_DISPATCHER
	case "responder", "officer", "first responder":
		return assets.AssetType_ASSET_TYPE_RESPONDER
	case "member":
		return assets.AssetType_ASSET_TYPE_MEMBER
	case "supervisor", "manager":
		return assets.AssetType_ASSET_TYPE_SUPERVISOR
	case "camera":
		return assets.AssetType_ASSET_TYPE_CAMERA
	case "bot":
		return assets.AssetType_ASSET_TYPE_BOT
	default:
		// Log unknown role for manual review
		log.Printf("Unknown role '%s', defaulting to RESPONDER", roleName)
		return assets.AssetType_ASSET_TYPE_RESPONDER
	}
}

// CreatePreRegistrationMapping creates a new pre-registration user mapping.
// If assetType is ASSET_TYPE_UNSPECIFIED (0), it will be auto-filled based on roleName.
// Common mappings: admin->dispatcher, responder->responder, member->member, etc.
func (orgUseCase *OrgUseCase) CreatePreRegistrationMapping(ctx context.Context, email, roleName, createdBy string, orgID int32, assetType assets.AssetType) (*orgs.PreRegistrationUserMapping, error) {
	if email == "" {
		return nil, fmt.Errorf("email is required")
	}
	if roleName == "" {
		return nil, fmt.Errorf("role name is required")
	}

	// Auto-fill asset_type based on role name if not specified (ASSET_TYPE_UNSPECIFIED = 0)
	if assetType == assets.AssetType_ASSET_TYPE_UNSPECIFIED {
		assetType = roleNameToAssetType(roleName)
	}

	// Validate that the organization exists
	_, err := orgUseCase.orgRepo.GetOrg(ctx, nil, orgID)
	if err != nil {
		return nil, fmt.Errorf("organization not found: %v", err)
	}

	mapping := &orgs.PreRegistrationUserMapping{
		Email:     email,
		OrgId:     orgID,
		RoleName:  roleName,
		AssetType: assetType,
		CreatedBy: createdBy,
	}

	return orgUseCase.orgRepo.CreatePreRegistrationMapping(ctx, nil, mapping)
}

// CreatePreRegistrationMappings creates multiple pre-registration user mappings atomically
func (orgUseCase *OrgUseCase) CreatePreRegistrationMappings(ctx context.Context, mappings []*orgs.CreatePreRegistrationMappingRequest) ([]*orgs.PreRegistrationUserMapping, []string, error) {
	if len(mappings) == 0 {
		return nil, nil, fmt.Errorf("no mappings provided")
	}

	// Start a transaction for atomic operation
	tx, err := orgUseCase.database.BeginTx(ctx, nil)
	if err != nil {
		return nil, nil, fmt.Errorf("failed to begin transaction: %v", err)
	}
	defer func() {
		if rollbackErr := tx.Rollback(); rollbackErr != nil && rollbackErr != sql.ErrTxDone {
			// Log the rollback error but don't override the main error
			// In a production system, you might want to use a proper logger here
			fmt.Printf("Warning: failed to rollback transaction: %v\n", rollbackErr)
		}
	}()

	var createdMappings []*orgs.PreRegistrationUserMapping
	var errors []string

	for i, req := range mappings {
		if req.Email == "" {
			errors = append(errors, fmt.Sprintf("mapping %d: email is required", i))
			continue
		}
		if req.RoleName == "" {
			errors = append(errors, fmt.Sprintf("mapping %d: role name is required", i))
			continue
		}

		// Auto-fill asset_type based on role name if not specified (ASSET_TYPE_UNSPECIFIED = 0)
		assetType := req.AssetType
		if assetType == assets.AssetType_ASSET_TYPE_UNSPECIFIED {
			assetType = roleNameToAssetType(req.RoleName)
		}

		// Validate that the organization exists
		_, err := orgUseCase.orgRepo.GetOrg(ctx, tx, req.OrgId)
		if err != nil {
			errors = append(errors, fmt.Sprintf("mapping %d: organization not found: %v", i, err))
			continue
		}

		mapping := &orgs.PreRegistrationUserMapping{
			Email:     req.Email,
			OrgId:     req.OrgId,
			RoleName:  req.RoleName,
			AssetType: assetType,
			CreatedBy: req.CreatedBy,
		}

		createdMapping, err := orgUseCase.orgRepo.CreatePreRegistrationMapping(ctx, tx, mapping)
		if err != nil {
			errors = append(errors, fmt.Sprintf("mapping %d: failed to create: %v", i, err))
			continue
		}

		createdMappings = append(createdMappings, createdMapping)
	}

	// If there are any errors, rollback the transaction
	if len(errors) > 0 {
		return nil, errors, fmt.Errorf("failed to create %d mappings", len(errors))
	}

	// Commit the transaction
	if err := tx.Commit(); err != nil {
		return nil, nil, fmt.Errorf("failed to commit transaction: %v", err)
	}

	return createdMappings, nil, nil
}

// GetPreRegistrationMapping retrieves a pre-registration mapping by email and org ID
func (orgUseCase *OrgUseCase) GetPreRegistrationMapping(ctx context.Context, email string, orgID int32) (*orgs.PreRegistrationUserMapping, error) {
	if email == "" {
		return nil, fmt.Errorf("email is required")
	}

	return orgUseCase.orgRepo.GetPreRegistrationMapping(ctx, nil, email, orgID)
}

// ListPreRegistrationMappings returns paginated pre-registration mappings for an organization
func (orgUseCase *OrgUseCase) ListPreRegistrationMappings(ctx context.Context, orgID int32, pageToken string, pageSize int32, includeUsed bool) ([]*orgs.PreRegistrationUserMapping, string, int32, error) {
	// Verify the organization exists
	_, err := orgUseCase.orgRepo.GetOrg(ctx, nil, orgID)
	if err != nil {
		return nil, "", 0, fmt.Errorf("organization not found: %v", err)
	}

	return orgUseCase.orgRepo.ListPreRegistrationMappings(ctx, nil, orgID, pageToken, pageSize, includeUsed)
}

// UpdatePreRegistrationMapping updates an existing pre-registration mapping.
// If assetType is ASSET_TYPE_UNSPECIFIED (0), it will be auto-filled based on roleName.
func (orgUseCase *OrgUseCase) UpdatePreRegistrationMapping(ctx context.Context, mappingID, roleName string, assetType assets.AssetType) (*orgs.PreRegistrationUserMapping, error) {
	if mappingID == "" {
		return nil, fmt.Errorf("mapping ID is required")
	}
	if roleName == "" {
		return nil, fmt.Errorf("role name is required")
	}

	// Auto-fill asset_type based on role name if not specified (ASSET_TYPE_UNSPECIFIED = 0)
	if assetType == assets.AssetType_ASSET_TYPE_UNSPECIFIED {
		assetType = roleNameToAssetType(roleName)
	}

	mapping := &orgs.PreRegistrationUserMapping{
		Id:        mappingID,
		RoleName:  roleName,
		AssetType: assetType,
	}

	return orgUseCase.orgRepo.UpdatePreRegistrationMapping(ctx, nil, mapping)
}

// DeletePreRegistrationMapping deletes a pre-registration mapping
func (orgUseCase *OrgUseCase) DeletePreRegistrationMapping(ctx context.Context, mappingID string) error {
	if mappingID == "" {
		return fmt.Errorf("mapping ID is required")
	}

	return orgUseCase.orgRepo.DeletePreRegistrationMapping(ctx, nil, mappingID)
}

// MarkMappingAsUsed marks a pre-registration mapping as used
func (orgUseCase *OrgUseCase) MarkMappingAsUsed(ctx context.Context, email string, orgID int32) error {
	if email == "" {
		return fmt.Errorf("email is required")
	}

	return orgUseCase.orgRepo.MarkMappingAsUsed(ctx, nil, email, orgID)
}

// GetContactByPhoneNumber retrieves a contact record by phone number from the organization's contact book
func (orgUseCase *OrgUseCase) GetContactByPhoneNumber(ctx context.Context, orgID int32, phone string) (*orgs.ContactRecord, error) {
	if phone == "" {
		return nil, fmt.Errorf("phone number is required")
	}

	// Standardize the phone number for consistent lookup
	standardizedPhone, err := utils.StandardizeUSPhoneNumber(phone)
	if err != nil {
		return nil, fmt.Errorf("invalid phone number format: %v", err)
	}

	// Verify the organization exists
	_, err = orgUseCase.orgRepo.GetOrg(ctx, nil, orgID)
	if err != nil {
		return nil, fmt.Errorf("organization not found: %v", err)
	}

	return orgUseCase.orgRepo.GetContactByPhoneNumber(ctx, nil, orgID, standardizedPhone)
}
