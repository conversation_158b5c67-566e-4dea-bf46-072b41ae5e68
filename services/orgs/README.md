# Org Service

In our system, an **Organization** (Org) represents a key operational entity that anchors communication, management, and integration efforts. Organizations store essential information—from basic details such as name and contact phone number to integration data like Twilio credentials and supported domains. This data is critical for configuring and securing the interactions between different modules in our system.

Organizations are managed via our **Orgs Service** which provides endpoints to create, retrieve, list, and delete organizations, as well as handle organization API users and associated Zello channels for secure communications.

---

## Running Locally

When you run `make run`, the service will, by default, run on `localhost:9087`. It uses the locally running PostgreSQL Docker container for database purposes.  
If you encounter database errors, run `make db` **before** `make run` to ensure that the local database is properly set up and migrated to the correct version.

If you prefer to switch to an in‑memory database, update the `<root>/docker-compose.yml` file's `workflow-service > environment > REPO_TYPE` value to `inmemory`.

---

## Database Schema Overview

Organization data is persisted in dedicated database tables that map directly to the **Org** message and related API user credentials. Although the underlying SQL uses snake_case, our service code converts these fields to camelCase for consistency.

### Organizations Table (Assumed)

The organization details are stored with the following key fields:

- **id:** Primary key (integer) generated by the server.
- **name:** Name of the organization.
- **domains:** List of domains associated with the organization.
- **twiml_app_sid:** Identifier for the TwiML application related for communications.
- **twilio_number:** The Twilio phone number associated with the organization.
- **twilio_number_sid:** The identifier for the Twilio number. This is given initially when claiming a twilio number. Will be used for future cleanup.
- **service_type:** Integer value representing the type of service (demo or production).
- **created_at & updated_at:** Timestamps indicating when the organization was created and last updated.
- **template_id:** ID of the template used to setup the org's features.

#### Call Forwarding Configuration

- **is_call_forwarding_enabled:** Boolean indicating whether call forwarding is enabled.
- **primary_phone_number:** Primary phone number for the organization (used for Twilio phone number claiming setup and call forwarding).
- **call_forwarding_type:** Type of call forwarding ("PSTN" for standard phone or "SIP" for VoIP).
- **sip_uri:** SIP URI for SIP forwarding (format: sip:<EMAIL>). (Currently not fully implemented)

### Org API Users Table

The following schema defines the API user credentials for organizations:

- **id:** Primary key (TEXT) uniquely identifying the API user.
- **orgId:** Foreign key (INTEGER) referencing the organization (ensuring cascade deletion).
- **username:** Username for API access.
- **encryptedPassword:** Securely encrypted password.
- **hashedPassword:** Hashed version of the password.
- **createdAt & updatedAt:** Timestamps (with time zone) for creation and last update.

Row-level security policies enforce that API users can only access records linked to their organization or a special organization with an id of zero.

---

# Data Model Reference

## Enums

### ServiceType

Defines the available service types for an organization:

| Name                     | Value | Description                                       |
| ------------------------ | ----- | ------------------------------------------------- |
| SERVICE_TYPE_UNSPECIFIED | 0     | Default unspecified service type.                 |
| SERVICE_TYPE_DEMO        | 1     | Represents a demo environment organization.       |
| SERVICE_TYPE_PRODUCTION  | 2     | Represents a production environment organization. |

---

## Messages

### Org

Represents an organization entity.

| Field                      | Type                      | Description                                                                                        |
| -------------------------- | ------------------------- | -------------------------------------------------------------------------------------------------- |
| id                         | int32                     | Unique identifier for the organization.                                                            |
| name                       | string                    | Name of the organization.                                                                          |
| domains                    | []string                  | List of domains associated with the organization.                                                  |
| twiml_app_sid              | string                    | Twilio application SID for voice calls.                                                            |
| twilio_number              | string                    | Twilio phone number assigned to the organization.                                                  |
| twilio_number_sid          | string                    | Twilio phone number SID.                                                                           |
| service_type               | ServiceType               | Type of service (DEMO or PRODUCTION).                                                              |
| created_at                 | google.protobuf.Timestamp | Creation timestamp.                                                                                |
| updated_at                 | google.protobuf.Timestamp | Last update timestamp.                                                                             |
| template_id                | string                    | ID of the template used to setup the org's features.                                               |
| is_call_forwarding_enabled | bool                      | Whether call forwarding is enabled for this organization.                                          |
| primary_phone_number       | string                    | Primary phone number for the organization (used for Twilio setup and call forwarding).             |
| call_forwarding_type       | string                    | Type of call forwarding: "PSTN" (standard phone) or "SIP" (VoIP).                                  |
| sip_uri                    | string                    | Optional SIP URI for SIP forwarding (format: sip:<EMAIL>). Currently not fully implemented |

### OrgApiUser

Represents the API user credentials linked to an organization.

| Field             | Type                      | Description                                         |
| ----------------- | ------------------------- | --------------------------------------------------- |
| id                | string                    | Unique identifier for the API user.                 |
| orgId             | int32                     | Identifier of the organization the user belongs to. |
| username          | string                    | Username for API access.                            |
| encryptedPassword | string                    | Securely encrypted password.                        |
| hashedPassword    | string                    | Hashed version of the password.                     |
| createdAt         | google.protobuf.Timestamp | Timestamp when the API user was created.            |
| updatedAt         | google.protobuf.Timestamp | Timestamp when the API user was last updated.       |

### ZelloChannel

Represents a Zello channel associated with an organization for secure communications.

| Field          | Type   | Description                                           |
| -------------- | ------ | ----------------------------------------------------- |
| id             | string | Unique identifier for the Zello channel.              |
| orgId          | int32  | Identifier of the organization linked to the channel. |
| zelloChannelId | string | Zello-specific channel identifier.                    |
| displayName    | string | Human-readable display name for the channel.          |

---

## Overview of Endpoints

The Orgs module exposes the following operations:

1. **[CreateOrg](#1-createorg)**
2. **[DeleteOrg](#2-deleteorg)**
3. **[GetOrg](#3-getorg)**
4. **[ListOrgs](#4-listorgs)**
5. **[ValidateOrgCreds](#5-validateorgcreds)**
6. **[CreateOrgAPIUser](#6-createorgapiuser)**
7. **[GetZelloChannels](#7-getzellochannels)**

---

### 1. CreateOrg

**Method:** `CreateOrg`  
**Route:** `POST /hero.orgs.v1.OrgsService/CreateOrg`

#### Message Fields

**CreateOrgRequest:**

| Field | Type | Description                        |
| ----- | ---- | ---------------------------------- |
| org   | Org  | Organization object to be created. |

**CreateOrgResponse:**

| Field | Type | Description                                                                    |
| ----- | ---- | ------------------------------------------------------------------------------ |
| org   | Org  | The newly created organization with a generated identifier and default values. |

#### Sample Request and Response

**Request (JSON):**

```json
{
  "org": {
    "name": "Example Organization",
    "primary_phone_number": "+1234567890",
    "domains": ["example.com"],
    "service_type": "SERVICE_TYPE_DEMO",
    "template_id": "local"
  }
}
```

**Response (JSON):**

```json
{
  "org": {
    "id": 101,
    "name": "Example Organization",
    "primary_phone_number": "+1234567890",
    "domains": ["example.com"],
    "twiml_app_sid": "APXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXX",
    "twilio_number": "+19876543210",
    "twilio_number_sid": "PNXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXX",
    "service_type": "SERVICE_TYPE_DEMO",
    "created_at": "2025-01-14T11:00:00Z",
    "updated_at": "2025-01-14T11:00:00Z",
    "template_id": "local"
  }
}
```

---

### 2. DeleteOrg

**Method:** `DeleteOrg`  
**Route:** `POST /hero.orgs.v1.OrgsService/DeleteOrg`

#### Message Fields

**DeleteOrgRequest:**

| Field | Type  | Description                                      |
| ----- | ----- | ------------------------------------------------ |
| id    | int32 | Unique identifier of the organization to delete. |

**DeleteOrgResponse:**  
_(Empty message)_

#### Sample Request and Response

**Request (JSON):**

```json
{
  "id": 101
}
```

**Response (JSON):**

```json
{}
```

---

### 3. GetOrg

**Method:** `GetOrg`  
**Route:** `POST /hero.orgs.v1.OrgsService/GetOrg`

#### Message Fields

**GetOrgRequest:**

| Field | Type  | Description                             |
| ----- | ----- | --------------------------------------- |
| id    | int32 | Unique identifier for the organization. |

**GetOrgResponse:**

| Field | Type | Description                                |
| ----- | ---- | ------------------------------------------ |
| org   | Org  | The organization object with full details. |

#### Sample Request and Response

**Request (JSON):**

```json
{
  "id": 101
}
```

**Response (JSON):**

```json
{
  "org": {
    "id": 101,
    "name": "Example Organization",
    "primary_phone_number": "+1234567890",
    "domains": ["example.com"],
    "twiml_app_sid": "APXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXX",
    "twilio_number": "+19876543210",
    "twilio_number_sid": "PNXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXX",
    "service_type": "SERVICE_TYPE_DEMO",
    "created_at": "2025-01-14T11:00:00Z",
    "updated_at": "2025-01-14T11:00:00Z",
    "template_id": "local"
  }
}
```

---

### 4. ListOrgs

**Method:** `ListOrgs`  
**Route:** `POST /hero.orgs.v1.OrgsService/ListOrgs`

#### Message Fields

**ListOrgsRequest:**

_(No fields required)_

**ListOrgsResponse:**

| Field | Type         | Description                   |
| ----- | ------------ | ----------------------------- |
| orgs  | repeated Org | List of organization objects. |

#### Sample Request and Response

**Request (JSON):**

```json
{}
```

**Response (JSON):**

```json
{
  "orgs": [
    {
      "id": 101,
      "name": "Example Organization",
      "primary_phone_number": "+1234567890",
      "domains": ["example.com"],
      "twiml_app_sid": "APXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXX",
      "twilio_number": "+19876543210",
      "twilio_number_sid": "PNXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXX",
      "service_type": "SERVICE_TYPE_DEMO",
      "created_at": "2025-01-14T11:00:00Z",
      "updated_at": "2025-01-14T11:00:00Z",
      "template_id": "local"
    },
    {
      "id": 102,
      "name": "Beta LLC",
      "primary_phone_number": "+10987654321",
      "domains": ["beta.com"],
      "twiml_app_sid": "APYYYYYYYYYYYYYYYYYYYYYYYYYYYYYYYY",
      "twilio_number": "+10123456789",
      "twilio_number_sid": "PNYYYYYYYYYYYYYYYYYYYYYYYYYYYYYYYY",
      "service_type": "SERVICE_TYPE_DEMO",
      "created_at": "2025-01-15T12:00:00Z",
      "updated_at": "2025-01-15T12:00:00Z",
      "template_id": "local"
    }
  ]
}
```

---

### 5. ValidateOrgCreds

**Method:** `ValidateOrgCreds`  
**Route:** `POST /hero.orgs.v1.OrgsService/ValidateOrgCreds`

This endpoint validates the credentials of an organization API user.

#### Message Fields

**ValidateOrgCredsRequest:**

| Field    | Type   | Description                  |
| -------- | ------ | ---------------------------- |
| username | string | Username of the API user.    |
| password | string | Password for authentication. |

**ValidateOrgCredsResponse:**

| Field      | Type       | Description                                          |
| ---------- | ---------- | ---------------------------------------------------- |
| valid      | bool       | Indicates if the provided credentials are valid.     |
| orgApiUser | OrgApiUser | API user details provided the credentials are valid. |

#### Sample Request and Response

**Request (JSON):**

```json
{
  "username": "acmeApi",
  "password": "securepassword"
}
```

**Response (JSON):**

```json
{
  "valid": true,
  "orgApiUser": {
    "id": "user-001",
    "orgId": 101,
    "username": "acmeApi",
    "encryptedPassword": "encrypted_value",
    "hashedPassword": "hashed_value",
    "createdAt": "2025-01-14T11:05:00Z",
    "updatedAt": "2025-01-14T11:05:00Z"
  }
}
```

---

### 6. CreateOrgAPIUser

**Method:** `CreateOrgAPIUser`  
**Route:** `POST /hero.orgs.v1.OrgsService/CreateOrgAPIUser`

This endpoint generates a new API user for the organization, providing credentials for secure API access.

#### Message Fields

**CreateOrgAPIUserRequest:**

| Field | Type  | Description                     |
| ----- | ----- | ------------------------------- |
| orgId | int32 | Identifier of the organization. |

**CreateOrgAPIUserResponse:**

| Field             | Type   | Description                            |
| ----------------- | ------ | -------------------------------------- |
| username          | string | Generated username for the API user.   |
| encryptedPassword | string | Encrypted password for secure storage. |
| hashedPassword    | string | Hashed version of the password.        |

#### Sample Request and Response

**Request (JSON):**

```json
{
  "orgId": 101
}
```

**Response (JSON):**

```json
{
  "username": "acmeApi",
  "encryptedPassword": "encrypted_value",
  "hashedPassword": "hashed_value"
}
```

---

### 7. GetZelloChannels

**Method:** `GetZelloChannels`  
**Route:** `POST /hero.orgs.v1.OrgsService/GetZelloChannels`

Retrieves a list of Zello channels associated with organizations for secure communications.

#### Message Fields

**GetZelloChannelsRequest:**

_(No fields required)_

**GetZelloChannelsResponse:**

| Field         | Type                  | Description                    |
| ------------- | --------------------- | ------------------------------ |
| zelloChannels | repeated ZelloChannel | List of Zello channel objects. |

#### Sample Request and Response

**Request (JSON):**

```json
{}
```

**Response (JSON):**

```json
{
  "zelloChannels": [
    {
      "id": "chan-001",
      "orgId": 101,
      "zelloChannelId": "ZC123456",
      "displayName": "Main Communications"
    },
    {
      "id": "chan-002",
      "orgId": 102,
      "zelloChannelId": "ZC654321",
      "displayName": "Secondary Channel"
    }
  ]
}
```

---

## 8. Pre-Registering Users ("Pre-loading")

**Why?**  
New orgs (aka customers) often need to invite users _before_ those users have created a Cognito account. This is especially true for federated logins (SAML based). The pre-registration mapping workflow lets an admin

1. **pre-load** an e-mail → role/asset-type mapping, and
2. hand the user a _magic link_ or simply wait for the normal sign-up flow.  
   When the user finally signs-up with the same e-mail, the backend resolves the pending mapping and automatically:

- assigns the correct **role** in the Permissions service
- provisions the **asset** (Responder, Dispatcher, Camera, …)
- marks the mapping as _used_ (so it cannot be reused)

This keeps onboarding lightweight while still guaranteeing principle-of-least-privilege.

### At a glance

| RPC                             | Typical Use                                                                | Notes                                                 |
| ------------------------------- | -------------------------------------------------------------------------- | ----------------------------------------------------- |
| `CreatePreRegistrationMapping`  | Invite **one** user.                                                       | Auto-infers `asset_type` from `role_name` if omitted. |
| `CreatePreRegistrationMappings` | Bulk invite _n_ users in **one** transaction – either all succeed or none. | Returns per-row error list for quick UI feedback.     |
| `GetPreRegistrationMapping`     | Check if a mapping exists for an e-mail/org pair.                          |                                                       |
| `ListPreRegistrationMappings`   | Paginate through all mappings for an org. Supports `include_used` flag.    |                                                       |
| `UpdatePreRegistrationMapping`  | Change the role / asset-type _before_ it is consumed.                      |                                                       |
| `DeletePreRegistrationMapping`  | Remove an unused mapping.                                                  |                                                       |
| `MarkMappingAsUsed`             | **Internal** – called by auth flow once a mapping is consumed.             |                                                       |

### Quick example – invite a dispatcher

```json
POST /hero.orgs.v1.OrgsService/CreatePreRegistrationMapping
{
  "email": "<EMAIL>",
  "orgId": 101,
  "roleName": "dispatcher",   // will automatically set asset_type = DISPATCHER
  "createdBy": "<EMAIL>"
}
```

Returns (simplified):

```json
{
  "mapping": {
    "id": "prm_6f9dc4f2",
    "email": "<EMAIL>",
    "roleName": "dispatcher",
    "assetType": "ASSET_TYPE_DISPATCHER",
    "createdBy": "<EMAIL>",
    "orgId": 101,
    "used": false
  }
}
```

### When to use it

- **Bulk roll-outs** – load a CSV, hit `CreatePreRegistrationMappings`, get a transactional guarantee.
- **Tight audit trails** – every mapping records who created it (`created_by`) and when it was consumed (`used_at`).

---
