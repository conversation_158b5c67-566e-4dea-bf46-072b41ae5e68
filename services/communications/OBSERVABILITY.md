# Communications Service Observability

This document describes the Sentry instrumentation implemented in the Communications Service backend for cellular call operations.

## What's Currently Instrumented

The Communications Service has **Sentry Go SDK** instrumentation on:
- **5 HTTP Webhooks** - Twilio voice, waithold, callstatus, agent dial status, and TwiML generation callbacks
- **7 Connect RPC Operations** - All major cellular call management methods
- **Error Tracking** - Automatic exception capture with operation context
- **Performance Monitoring** - Transaction timing for each operation

## Instrumented Operations

### HTTP Server Operations (5)

#### POST /voice
**Transaction Name**: `POST /voice`  
**Operation Type**: `http.server`

**What it does**: Handles Twilio voice webhooks for call routing, queueing, and TwiML generation

**Tags Set**:
```go
span.Op = "http.server"
span.SetTag("http.method", "POST")
span.SetTag("http.route", "/hero.communications.v1.TwilioWebhookService/voice")
span.SetTag("service", "communications-service")
span.SetTag("call_sid", callSID)
span.SetTag("from", from)
span.SetTag("to", to)
span.SetTag("action", action)
span.SetTag("http.status_code", "200") // On success
```

#### POST /waithold
**Transaction Name**: `POST /waithold`  
**Operation Type**: `http.server`

**What it does**: Generates smart wait/hold messaging TwiML for queued calls

**Tags Set**:
```go
span.Op = "http.server"
span.SetTag("http.method", "POST")
span.SetTag("http.route", "/hero.communications.v1.TwilioWebhookService/waithold")
span.SetTag("service", "communications-service")
span.SetTag("action", action) // "enqueue" or "hold"
span.SetTag("http.status_code", "200") // On success
```

#### POST /callstatus
**Transaction Name**: `POST /callstatus`  
**Operation Type**: `http.server`

**What it does**: Processes Twilio call status callbacks for call lifecycle tracking

**Tags Set**:
```go
span.Op = "http.server"
span.SetTag("http.method", "POST")
span.SetTag("http.route", "/hero.communications.v1.TwilioWebhookService/callstatus")
span.SetTag("service", "communications-service")
span.SetTag("call_sid", callSID)
span.SetTag("call_status", callStatus) // "completed", "in-progress", etc.
span.SetTag("direction", direction) // "inbound", "outbound"
span.SetTag("http.status_code", "200") // On success
```

#### POST /agent-dial-status
**Transaction Name**: `POST /agent-dial-status`  
**Operation Type**: `http.server`

**What it does**: Handles Twilio status callbacks for agent leg connections during call transfers

**Tags Set**:
```go
span.Op = "http.server"
span.SetTag("http.method", "POST")
span.SetTag("http.route", "/hero.communications.v1.TwilioWebhookService/agent-dial-status")
span.SetTag("service", "communications-service")
span.SetTag("agent_leg_sid", agentLegSID)
span.SetTag("call_status", callStatus)
span.SetTag("customer_sid", customerSID)
span.SetTag("session_suffix", sessionSuffix)
span.SetTag("call_duration", callDuration)
span.SetTag("http.status_code", "200") // On success
```

#### POST /twiml/connectAgent
**Transaction Name**: `POST /twiml/connectAgent`  
**Operation Type**: `http.server`

**What it does**: Generates TwiML to connect an agent to a specific customer call

**Tags Set**:
```go
span.Op = "http.server"
span.SetTag("http.method", "POST")
span.SetTag("http.route", "/hero.communications.v1.TwilioWebhookService/twiml/connectAgent")
span.SetTag("service", "communications-service")
span.SetTag("agent_id", agentID)
span.SetTag("customer_sid", customerSID)
span.SetTag("session_suffix", sessionSuffix)
span.SetTag("http.status_code", "200") // On success
```

### RPC Server Operations (7)

#### CellularCall/QueueCall
**Transaction Name**: `CellularCall/QueueCall`  
**Operation Type**: `rpc.server`

**What it does**: Places incoming calls into the dispatch queue

**Tags Set**:
```go
span.Op = "rpc.server"
span.SetTag("rpc.service", "CellularCall")
span.SetTag("rpc.method", "QueueCall")
span.SetTag("service", "communications-service")
span.SetTag("caller", req.Caller)
span.SetTag("caller_name", req.CallerName)
span.SetTag("call_sid", callSID)
span.SetTag("rpc.status", "success") // On success
```

#### CellularCall/DequeueCallBySid
**Transaction Name**: `CellularCall/DequeueCallBySid`  
**Operation Type**: `rpc.server`

**What it does**: Connects a specific dispatcher to a queued call by Call SID

**Tags Set**:
```go
span.SetTag("rpc.service", "CellularCall")
span.SetTag("rpc.method", "DequeueCallBySid")
span.SetTag("service", "communications-service")
span.SetTag("call_sid", req.CallSid)
span.SetTag("asset_id", req.AssetId)
span.SetTag("session_suffix", req.SessionSuffix)
span.SetTag("rpc.status", "success") // On success
// Error cases:
span.SetTag("call_not_found", "true") // When call not in waiting state
span.SetTag("redirect_failed", "true") // When Twilio redirect fails
span.SetTag("retry_count", "3") // Retry attempts
```

#### CellularCall/HoldCall
**Transaction Name**: `CellularCall/HoldCall`  
**Operation Type**: `rpc.server`

**What it does**: Places an active call on hold

**Tags Set**:
```go
span.SetTag("rpc.service", "CellularCall")
span.SetTag("rpc.method", "HoldCall")
span.SetTag("service", "communications-service")
span.SetTag("call_sid", req.CallSid)
span.SetTag("asset_id", req.AssetId)
span.SetTag("rpc.status", "success") // On success
```

#### CellularCall/ResumeCall
**Transaction Name**: `CellularCall/ResumeCall`  
**Operation Type**: `rpc.server`

**What it does**: Reconnects a dispatcher to a held call

**Tags Set**:
```go
span.SetTag("rpc.service", "CellularCall")
span.SetTag("rpc.method", "ResumeCall")
span.SetTag("service", "communications-service")
span.SetTag("call_sid", req.CallSid)
span.SetTag("asset_id", req.AssetId)
span.SetTag("session_suffix", req.CurrentSessionSuffix)
span.SetTag("rpc.status", "success") // On success
```

#### CellularCall/EndCall
**Transaction Name**: `CellularCall/EndCall`  
**Operation Type**: `rpc.server`

**What it does**: Terminates an active call

**Tags Set**:
```go
span.SetTag("rpc.service", "CellularCall")
span.SetTag("rpc.method", "EndCall")
span.SetTag("service", "communications-service")
span.SetTag("call_sid", req.CallSid)
span.SetTag("asset_id", req.AssetId)
span.SetTag("rpc.status", "success") // On success
```

#### CellularCall/RevertSelectiveClaim
**Transaction Name**: `CellularCall/RevertSelectiveClaim`  
**Operation Type**: `rpc.server`

**What it does**: Reverts a call from pending assignment back to waiting queue

**Tags Set**:
```go
span.SetTag("rpc.service", "CellularCall")
span.SetTag("rpc.method", "RevertSelectiveClaim")
span.SetTag("service", "communications-service")
span.SetTag("call_sid", req.CallSid)
span.SetTag("reverted", "true") // Whether state was actually changed
span.SetTag("rpc.status", "success") // On success
```

#### CellularCall/GetCellularCallAccessToken
**Transaction Name**: `CellularCall/GetCellularCallAccessToken`  
**Operation Type**: `rpc.server`

**What it does**: Generates Twilio access tokens for client applications

**Tags Set**:
```go
span.SetTag("rpc.service", "CellularCall")
span.SetTag("rpc.method", "GetCellularCallAccessToken")
span.SetTag("service", "communications-service")
span.SetTag("identity", req.Identity)
span.SetTag("session_suffix", req.SessionSuffix)
span.SetTag("expire", fmt.Sprintf("%d", req.Expire))
span.SetTag("rpc.status", "success") // On success
```

## Error Tracking

### Automatic Error Capture
All instrumented operations capture errors with:
- **Stack traces** with file locations
- **Operation context** via span tags
- **Request parameters** for debugging
- **Error categorization** via tags

### Error Examples

#### Validation Errors
```
Error: "call SID is required"
Tags: {rpc.service: "CellularCall", rpc.method: "HoldCall", error: "true"}
```

#### Database/Repository Errors
```
Error: "failed to get org twilio details: connection timeout"
Tags: {rpc.service: "CellularCall", rpc.method: "QueueCall", error: "true"}
```

#### External API Errors
```
Error: "failed to redirect queue member: Twilio authentication failed"
Tags: {
  rpc.service: "CellularCall", 
  rpc.method: "DequeueCallBySid", 
  error: "true",
  redirect_failed: "true",
  retry_count: "3"
}
```

#### Business Logic Errors
```
Error: "call not found or not in waiting state"
Tags: {
  rpc.service: "CellularCall",
  rpc.method: "DequeueCallBySid", 
  call_not_found: "true"
}
```

## Performance Tracking

### Metrics Currently Tracked

#### Response Times
- **Transaction duration** for each instrumented operation
- **Individual operation performance** via Sentry Performance tab

#### Success/Error Rates  
- **HTTP 200 status** tracking for successful webhook responses
- **RPC success status** tracking for completed operations
- **Error capture** with stack traces and operation context

#### Operational Context
- **Call identifiers** (call_sid, asset_id)
- **Request parameters** (caller, session_suffix, action)
- **Operation types** (queue, dequeue, hold, resume, end)
- **Service identification** (communications-service)

## Configuration

### Sentry Initialization (Current Implementation)
```go
sampleRate := 1.0 // Default to 100% for development
if env := os.Getenv("ENVIRONMENT"); env == "production" {
    sampleRate = 0.1 // 10% sampling for production to control costs
}

sentryOptions := sentry.ClientOptions{
    Dsn:              os.Getenv("SENTRY_DSN"),
    Environment:      os.Getenv("ENVIRONMENT"),
    ServerName:       "communications-service",
    EnableTracing:    true,
    TracesSampleRate: sampleRate,
    SendDefaultPII:   false, // Security: No PII in public safety system
    Debug:           env != "production", // Enable debug in non-prod
}
```

### Environment Variables
```bash
# Required
SENTRY_DSN=https://<EMAIL>/project-id
ENVIRONMENT=development|production
```

### Webhook Panic Recovery
```go
defer func() {
    if rec := recover(); rec != nil {
        log.Printf("Webhook panic recovered: %v", rec)
        sentry.CaptureException(fmt.Errorf("webhook panic: %v", rec))
        http.Error(w, "Internal server error", http.StatusInternalServerError)
    }
}()
```

## Useful Sentry Queries

### Performance Queries
```
# Slow call queue operations
transaction:"CellularCall/QueueCall" p95(transaction.duration):>2s

# Webhook response times
transaction:"POST /voice" p95(transaction.duration):>1s

# RPC operation performance
rpc.server rpc.service:CellularCall
```

### Error Queries
```
# All communications service errors  
service:communications-service error:true

# Failed call connections
"failed to redirect queue member" OR redirect_failed:true

# Call not found issues
call_not_found:true

# Validation errors
"is required" service:communications-service

# Database/external errors
"failed to get org twilio details" OR "Twilio authentication"
```

### Operation-Specific Queries
```
# Queue operations only
rpc.method:QueueCall

# Hold/Resume operations
rpc.method:HoldCall OR rpc.method:ResumeCall

# Webhook operations
http.server http.route:*TwilioWebhookService*

# Failed operations
rpc.status:error OR http.status_code:5*
```

## Security & Privacy

### PII Protection
- `SendDefaultPII: false` prevents automatic PII collection
- Call SIDs and Asset IDs are logged (operational identifiers, not personal data)
- Phone numbers appear in tags but can be filtered in Sentry settings
- No personal names or addresses in error logs

## Instrumented Operations Summary

| Operation Type | Count | Transaction Names |
|----------------|-------|-------------------|
| HTTP Webhooks | 5 | `POST /voice`, `POST /waithold`, `POST /callstatus`, `POST /agent-dial-status`, `POST /twiml/connectAgent` |
| RPC Methods | 7 | `CellularCall/QueueCall`, `CellularCall/DequeueCallBySid`, `CellularCall/HoldCall`, `CellularCall/ResumeCall`, `CellularCall/EndCall`, `CellularCall/RevertSelectiveClaim`, `CellularCall/GetCellularCallAccessToken` |
| Error Capture | All | Validation, database, external API, business logic errors |
| Status Tracking | All | HTTP 200, RPC success/failure with context |

## Implementation Location

All Sentry instrumentation is implemented in:
- **File**: `/services/communications/internal/cellularcall/usecase/cellularcall_usecase.go`
- **Initialization**: `/services/communications/cmd/server/main.go`
- **Webhook Recovery**: `/services/communications/cmd/server/main.go` (registerTwilioWebhooks)

## Database Instrumentation 

The Communications Service includes comprehensive **PostgreSQL repository layer instrumentation** for high-impact database operations.

### Instrumented Database Operations (4)

#### GetOrgTwilioDetails (Frequent Query)
**Span Name**: `db.get_org_twilio_details`  
**Operation Type**: `db.query`

**What it does**: Frequently called lookup for organization Twilio configuration

**Tags Set**:
```go
span.Op = "db.query"
span.SetTag("db.operation", "SELECT")
span.SetTag("db.table", "orgs")
span.SetTag("org_id", fmt.Sprintf("%d", orgId))
span.SetTag("db.rows_affected", "1") // On success
```

#### EnqueueCall (INSERT Operation)
**Span Name**: `db.enqueue_call`  
**Operation Type**: `db.query`

**What it does**: Adds new calls to the waiting queue

**Tags Set**:
```go
span.Op = "db.query"
span.SetTag("db.operation", "INSERT")
span.SetTag("db.table", "call_queue")
span.SetTag("call_sid", call.CallSID)
span.SetTag("caller", call.Caller)
span.SetTag("direction", call.Direction)
span.SetTag("db.rows_affected", "1") // On success
```

#### GetQueueStatus (Multi-Query Operation)
**Span Name**: `db.get_queue_status` (parent)  
**Operation Type**: `db.query`

**What it does**: Returns current queue statistics with count and list operations

**Child Spans**:
- `db.count_waiting_calls` - COUNT query for queue size
- `db.select_waiting_calls` - SELECT query for waiting calls list

**Tags Set**:
```go
// Parent span
span.Op = "db.query"
span.SetTag("db.operation", "SELECT")
span.SetTag("db.table", "call_queue")
span.SetTag("queue_size", fmt.Sprintf("%d", queueSize))
span.SetTag("waiting_calls_count", fmt.Sprintf("%d", len(waitingCalls)))

// Child spans have their own operation-specific tags
```

#### DequeueCallBySid (Complex Transaction)
**Span Name**: `db.dequeue_call_by_sid` (parent)  
**Operation Type**: `db.transaction`

**What it does**: Atomically claims a specific call with SELECT FOR UPDATE, UPDATE, and re-SELECT

**Child Spans**:
- `db.select_call_for_update` - SELECT FOR UPDATE with row locking
- `db.update_call_assignment` - UPDATE call state and assignment  
- `db.refetch_updated_call` - Re-SELECT to get final state

**Tags Set**:
```go
// Parent span
span.Op = "db.transaction"
span.SetTag("db.operation", "SELECT_UPDATE")
span.SetTag("db.table", "call_queue")
span.SetTag("call_sid", callSID)
span.SetTag("asset_id", assetID)
span.SetTag("call_found", fmt.Sprintf("%t", found))
span.SetTag("final_state", call.State) // If found
span.SetTag("assigned_asset", call.AssetID) // If found
```

### Database Performance Queries
```
# Find slow database operations
op:db.query p95(transaction.duration):>500ms

# See specific slow query types  
op:db.query db.operation:SELECT p95(transaction.duration):>200ms
op:db.transaction p95(transaction.duration):>1s

# Database error patterns
op:db.query error:true db.table:call_queue
op:db.transaction error:true

# Queue operation performance
span:db.dequeue_call_by_sid p95(transaction.duration):>2s
span:db.get_queue_status p95(transaction.duration):>1s
```

## Distributed Tracing 

The Communications Service also has **automatic distributed tracing** for all cross-service RPC calls.

### Cross-Service Operations Tracked

#### Situations Service Calls
**Operations**: `CreateSituation`, `SearchSituations`, `GetSituation`  
**Span Names**: `SituationService/CreateSituation`, etc.  
**Operation Type**: `rpc.client`

**What it does**: Links calls to incidents/situations for dispatch coordination

#### Organizations Service Calls  
**Operations**: `GetOrgTwilioDetails`, `GetOrgAPIUserPrivateById`  
**Span Names**: `OrgsService/GetOrgTwilioDetails`, etc.  
**Operation Type**: `rpc.client`

**What it does**: Fetches organization configuration and API credentials

#### Assets Service Calls
**Operations**: `GetAsset`  
**Span Names**: `AssetRegistryService/GetAsset`  
**Operation Type**: `rpc.client`

**What it does**: Fetches asset details for identity formatting and validation

### Distributed Trace Tags
```go
span.Op = "rpc.client"
span.SetTag("rpc.service", rpcService)     // "SituationService"
span.SetTag("rpc.method", rpcMethod)       // "CreateSituation"  
span.SetTag("client.service", "communications-service")
span.SetTag("rpc.procedure", spec.Procedure) // Full gRPC procedure path
span.SetTag("rpc.status", "success|error")
```

### Distributed Tracing Queries
```
# Cross-service performance  
op:rpc.client client.service:communications-service p95(transaction.duration):>500ms

# Service dependency errors
op:rpc.client rpc.status:error client.service:communications-service

# Specific service call patterns
rpc.service:SituationService rpc.method:CreateSituation
rpc.service:OrgsService rpc.method:GetOrgTwilioDetails

# End-to-end call flows
transaction:"CellularCall/QueueCall" op:rpc.client
```

## Updated Implementation Summary

| Operation Type | Count | Details |
|----------------|-------|---------|
| **HTTP Webhooks** | 5 | Twilio voice, status, and TwiML generation callbacks |
| **RPC Methods** | 7 | Cellular call management operations |
| **Database Operations** | 4 | High-impact repository operations with child spans |
| **Cross-Service Calls** | 3 Services | Automatic distributed tracing via interceptor |
| **Error Capture** | All | Validation, database, external API, business logic |
| **Performance Monitoring** | All | Transaction timing and child span breakdown |

## Implementation Locations

### Usecase Layer Instrumentation
- **File**: `/services/communications/internal/cellularcall/usecase/cellularcall_usecase.go`
- **Content**: RPC methods, HTTP webhooks, distributed tracing client setup

### Repository Layer Instrumentation  
- **File**: `/services/communications/internal/cellularcall/data/postgres_callqueue_repo.go`
- **Content**: Database query spans, transaction timing, error capture

### Distributed Tracing Infrastructure
- **File**: `/lib/common/middleware/tracing.go`
- **Content**: Connect RPC interceptor for automatic trace propagation
- **Recent Fix**: Context key consistency issue resolved for proper trace continuation

### Service Initialization
- **File**: `/services/communications/cmd/server/main.go`
- **Content**: Sentry configuration, environment-aware sampling, webhook panic recovery

For implementation patterns to use in other services, see `/services/BACKEND_OBSERVABILITY_GUIDE.md`.