# Backend Observability Guide

A guide for implementing Sentry observability across Hero Core Go microservices, based on patterns that have worked.

## Table of Contents

**Quick Start**
- [5-Minute Setup](#minimum-viable-setup-5-minutes) - Get observability working immediately
- [Verify It Works](#verify-it-works) - Confirm everything works

**Core Patterns**
- [Distributed Tracing](#distributed-tracing-setup) - Cross-service traces
- [HTTP Server](#http-server-pattern) - Webhook instrumentation
- [RPC Server](#rpc-server-pattern-with-distributed-tracing) - Service-to-service calls
- [Database Operations](#database-pattern-with-nil-safety) - Safe DB instrumentation

**Reference**
- [Implementation Checklist](#implementation-checklist) - Production readiness
- [Common Issues](#common-issues--solutions) - Troubleshooting guide
- [Success Metrics](#success-metrics) - What to expect in Sentry

---

## What Good Backend Instrumentation Looks Like

**Goal:** Debug API issues faster and understand business impact during service outages.

### Key Success Criteria:

1. **Answer "What failed?"** - Rich error context with operation details (RPC method, HTTP endpoint)
2. **Answer "Which requests affected?"** - Request IDs, user context, entity IDs for correlation
3. **Answer "When did it start?"** - Transaction timing and operation sequences
4. **Answer "Business impact?"** - Track failed business operations (call connections, asset updates, etc.)

### Essential Patterns:

- **Transaction-per-operation** - Each RPC/HTTP request gets its own transaction for independence
- **Business context in errors** - Entity IDs (call_sid, asset_id), operation types, retry counts
- **Operation-specific tags** - `rpc.method`, `http.route`, `external.service` for filtering
- **Environment-aware sampling** - 100% dev visibility, 10% production to control costs
- **Panic recovery** - Webhook panic recovery with Sentry capture prevents service crashes

---

## Minimum Viable Setup (5 minutes)

### 1. Add Dependency & Initialize

```go
// go.mod
go get github.com/getsentry/sentry-go@v0.30.0
go mod tidy

// main.go
import (
    "strings"  // Required for TracesSampler
    "github.com/getsentry/sentry-go"
)

func main() {
    // Initialize Sentry for error tracking and performance monitoring
    sentrySampleRate := 1.0 // Default to 100% for development
    if env := os.Getenv("ENVIRONMENT"); env == "production" {
        sentrySampleRate = 0.1 // 10% sampling for production
    }

    sentryOptions := sentry.ClientOptions{
        Dsn:              os.Getenv("SENTRY_DSN"),
        Environment:      os.Getenv("ENVIRONMENT"),
        ServerName:       "your-service-name", // e.g. "communications-service"
        EnableTracing:    true,
        TracesSampleRate: sentrySampleRate,
        SendDefaultPII:   false, // Security: Don't send PII in public safety system
        // CRITICAL: Filter out noisy DB-only transactions (saves 60-80% quota)
        TracesSampler: sentry.TracesSampler(func(ctx sentry.SamplingContext) float64 {
            name := ctx.Span.Name
            if strings.HasPrefix(name, "db.") {
                // Keep transactions with business context
                if _, ok := ctx.Span.Data["call_sid"]; ok { return 1.0 }
                if _, ok := ctx.Span.Data["asset_id"]; ok { return 1.0 }
                if table, ok := ctx.Span.Data["db.table"]; ok && table == "call_queue" { return 1.0 } // Example table name and operation
                return 0.0 // Drop standalone DB operations
            }
            return sentrySampleRate
        }),
    }
    err := sentry.Init(sentryOptions)
    if err != nil {
        log.Printf("Sentry initialization failed: %v", err)
    }

    // ... rest of your app setup ...

    // Start server
    if err := srv.ListenAndServe(); err != nil {
        sentry.CaptureException(err)
        sentry.Flush(2 * time.Second) // Flush before fatal exit
        log.Fatalf("Failed to serve: %v", err)
    }
    // Ensure events are sent before normal shutdown
    sentry.Flush(2 * time.Second)
}
```

### 2. Environment Variables

```bash
# docker-compose.yml
SENTRY_DSN=https://<EMAIL>/project-id
ENVIRONMENT=development
```

### 3. Import Common Utilities

```go
import "common/observability"
import "common/middleware"
```

### 4. Add One Transaction

```go
// In any handler/method
func (s *server) YourMethod(ctx context.Context, req *YourRequest) (*YourResponse, error) {
    transaction := sentry.StartTransaction(ctx, "YourService/YourMethod")
    defer transaction.Finish()

    transaction.SetTag("service", "your-service")
    transaction.SetTag("entity_id", req.EntityId)

    // Your business logic here

    if err != nil {
        transaction.SetTag("error", "true")
        sentry.CaptureException(err)
        return nil, err
    }

    return response, nil
}
```

### 5. Verify It Works

1. Run your service
2. Call your method
3. Check Sentry Performance tab for `YourService/YourMethod` transaction
4. Check Issues tab for any errors

**✅ Success:** You should see transactions and errors in Sentry dashboard within 30 seconds.

---

## Core Patterns

### Distributed Tracing Setup

**Required for cross-service traces.** Add to your service initialization:

```go
// In your main.go or service setup
func NewYourServiceServer() (string, http.Handler) {
    // 1. Create client tracing interceptor for outgoing calls
    tracingInterceptor := middleware.SentryTracingInterceptor("your-service-name")

    // 2. Initialize clients with distributed tracing
    assetsClient := clients.NewAssetsClient(assetsServiceURL, tracingInterceptor)
    orgClient := clients.NewOrgsClient(orgsServiceURL, tracingInterceptor)

    usecase := NewYourUsecase(assetsClient, orgClient)
    serviceServer := NewYourServiceServer(usecase)

    // 3. Add server-side tracing interceptor
    serverTracingInterceptor := middleware.SentryServerTracingInterceptor()
    path, handler := yourv1connect.NewYourServiceHandler(
        serviceServer,
        connect.WithInterceptors(serverTracingInterceptor),
    )

    return path, handler
}
```

### Instrumentation Method Decision Tree

Choose the right instrumentation method based on your use case:

```
Is this an RPC/HTTP handler (service entry point)?
├── Yes → Use middleware.StartTransactionWithTraceContext()
│         // Extracts trace context from incoming requests
│         transaction := middleware.StartTransactionWithTraceContext(ctx, "ServiceName/MethodName")
│         defer transaction.Finish()
│         ctx = transaction.Context() // Use this context for downstream calls
│
└── No → Is there already a transaction in the context?
    ├── Yes → Is this a high-frequency operation (polling, health checks)?
    │   ├── Yes → Use observability.StartSampledSpan()
    │   │         // Samples at specified rate to reduce overhead
    │   │         span, cleanup := observability.StartSampledSpan(ctx, "db.get_status", "Repository.GetStatus", 0.1)
    │   │         defer cleanup()
    │   │
    │   └── No → Use sentry.StartSpan() directly
    │           // Creates child span within existing transaction
    │           span := sentry.StartSpan(ctx, "business.operation")
    │           defer span.Finish()
    │
    └── No → Is this a standalone operation that needs its own transaction?
        ├── Yes → Use observability.StartSpanWithTransaction()
        │         // Creates transaction if none exists, or child span if one does
        │         span, cleanup := observability.StartSpanWithTransaction(ctx, "db.operation", "Repository.Method")
        │         defer cleanup()
        │
        └── No → Use sentry.StartTransaction() directly
                // Creates a new transaction (rare, usually for background tasks)
                transaction := sentry.StartTransaction(ctx, "BackgroundTask")
                defer transaction.Finish()
```

### Naming Standards

#### Transaction Names

```go
// HTTP Operations
"POST /webhook"
"GET /health"

// RPC Operations
"ServiceName/MethodName"
"AssetService/CreateAsset"
"CommunicationsService/QueueCall"
```

#### Span Names

```go
// Infrastructure
"db.{operation}"           // db.query, db.insert
"rpc.client"              // Outgoing RPC calls
"http.client"             // Outgoing HTTP calls

// Business Logic
"{service}.{operation}"   // communications.create_call
                         // assets.validate_status
```

### HTTP Server Pattern

```go
func (s *server) HandleWebhook(w http.ResponseWriter, r *http.Request) {
    transaction := sentry.StartTransaction(r.Context(), "POST /webhook")
    defer transaction.Finish()

    transaction.Op = "http.server"
    transaction.SetTag("http.method", "POST")
    transaction.SetTag("http.route", "/webhook")
    transaction.SetTag("service", "your-service")

    // Extract key parameters
    callSID := r.FormValue("CallSid")
    transaction.SetTag("call_sid", callSID)

    // Business logic
    result, err := s.processWebhook(transaction.Context(), callSID)
    if err != nil {
        transaction.SetTag("error", "true")
        sentry.CaptureException(err)
        http.Error(w, "Internal error", http.StatusInternalServerError)
        return
    }

    w.WriteHeader(http.StatusOK)
    transaction.SetTag("http.status_code", "200")
}
```

### Webhook Panic Recovery (Critical)

```go
func registerWebhooks(mux *http.ServeMux, usecase YourUsecase) {
    mux.HandleFunc("/webhook", func(w http.ResponseWriter, r *http.Request) {
        defer func() {
            if rec := recover(); rec != nil {
                sentry.CaptureException(fmt.Errorf("webhook panic: %v", rec))
                http.Error(w, "Internal server error", http.StatusInternalServerError)
            }
        }()

        if err := r.ParseForm(); err != nil {
            sentry.CaptureException(err)
            http.Error(w, "Error parsing form data", http.StatusBadRequest)
            return
        }

        usecase.HandleWebhook(w, r)
    })
}
```

### RPC Server Pattern (with Distributed Tracing)

```go
func (s *server) CreateAsset(ctx context.Context, req *CreateAssetRequest) (*CreateAssetResponse, error) {
    // Use utility for distributed tracing support
    transaction := middleware.StartTransactionWithTraceContext(ctx, "AssetService/CreateAsset")
    defer transaction.Finish()
    ctx = transaction.Context()

    transaction.Op = "rpc.server"
    transaction.SetTag("rpc.service", "AssetService")
    transaction.SetTag("rpc.method", "CreateAsset")
    transaction.SetTag("service", "assets-service")
    transaction.SetTag("asset_type", req.AssetType)

    // Business operation span (creates proper trace hierarchy)
    businessSpan := sentry.StartSpan(ctx, "assets.create_asset")
    businessSpan.SetTag("operation", "create_asset")
    businessSpan.SetTag("asset_type", req.AssetType)

    // Business logic - these calls automatically create traced child spans
    asset, err := s.usecase.CreateAsset(businessSpan.Context(), req)
    businessSpan.Finish()

    if err != nil {
        businessSpan.SetTag("error", "true")
        transaction.SetTag("error", "true")
        sentry.CaptureException(err)
        return nil, err
    }

    transaction.SetTag("asset_id", asset.Id)
    transaction.SetTag("rpc.status", "success")
    return &CreateAssetResponse{Asset: asset}, nil
}
```

### Database Pattern (with Nil Safety)

```go
func (r *repository) GetAsset(ctx context.Context, id string) (*Asset, error) {
    // Use utility for smart span creation - creates child span if transaction exists
    span, cleanup := observability.StartSpanWithTransaction(ctx, "db.select_asset", "Repository.GetAsset")
    defer cleanup()

    if span != nil {
        span.Op = "db.query"
        span.SetTag("db.table", "assets")
        span.SetTag("db.operation", "SELECT")
        span.SetTag("asset_id", id)
    }

    var asset Asset
    var nullableField sql.NullString // Safe handling of nullable fields

    err := r.db.QueryRowContext(ctx, "SELECT id, name, nullable_field FROM assets WHERE id = $1", id).
        Scan(&asset.ID, &asset.Name, &nullableField)
    if err != nil {
        if span != nil {
            span.SetTag("error", "true")
            span.SetTag("db.rows_affected", "0")
        }
        sentry.CaptureException(err)
        return nil, err
    }

    // Safe nil handling
    if nullableField.Valid {
        asset.NullableField = nullableField.String
    } else {
        asset.NullableField = "" // Explicit default for NULL
    }

    if span != nil {
        span.SetTag("db.rows_affected", "1")
    }
    return &asset, nil
}
```

### High-Frequency Operations

```go
// For operations called frequently (polling, health checks)
func (r *repository) GetQueueStatus(ctx context.Context) (int, error) {
    // Sample at 10% to reduce observability overhead
    span, cleanup := observability.StartSampledSpan(ctx, "db.get_queue_status", "Repository.GetQueueStatus", 0.1)
    defer cleanup()

    if span != nil {
        span.Op = "db.query"
        span.SetTag("db.table", "call_queue")
        span.SetTag("sampled", "true")
    }

    // Your database logic here
    return queueSize, nil
}
```

### Standard Tags Reference

```go
// Always include
span.SetTag("service", "your-service-name")

// HTTP-specific
span.SetTag("http.method", "POST")
span.SetTag("http.route", "/api/endpoint")
span.SetTag("http.status_code", "200")

// RPC-specific
span.SetTag("rpc.service", "ServiceName")
span.SetTag("rpc.method", "MethodName")
span.SetTag("rpc.status", "success|error")

// Database-specific
span.SetTag("db.table", "table_name")
span.SetTag("db.operation", "SELECT|INSERT|UPDATE")
span.SetTag("db.rows_affected", "1")

// Business context (use your entity IDs)
span.SetTag("asset_id", assetId)
span.SetTag("call_sid", callSid)
span.SetTag("org_id", orgId)

// Errors
span.SetTag("error", "true")
span.SetTag("error.type", "validation|database|external")
```

---

## Production Setup

### Infrastructure Configuration

```typescript
// infra/cloud/servers/servers.ts
const serviceEnvironmentVariables = {
  SENTRY_DSN: "https://<EMAIL>/project-id",
  ENVIRONMENT: "production",
  // ... other vars
};
```

### Expected Trace Structure

With proper implementation, your traces will show:

```
Transaction: "AssetService/CreateAsset"
├── assets.create_asset                    // Business operation span
│   ├── rpc.client: OrgsClient/GetOrg     // Distributed trace to other service
│   └── rpc.client: ValidatorClient/Check
└── db.query: assets.insert               // Database operation span
```

---

## Implementation Checklist

### Basic Setup

- [ ] Add Sentry dependency (`go get github.com/getsentry/sentry-go`)
- [ ] Initialize Sentry in main.go with TracesSampler
- [ ] Set SENTRY_DSN and ENVIRONMENT variables
- [ ] Import observability utilities (`common/observability`, `common/middleware`)
- [ ] **Implement proper shutdown with explicit `sentry.Flush()` before all exits**
- [ ] Add one transaction to verify it works

### Core Patterns

- [ ] HTTP endpoints use transaction naming: `METHOD /path`
- [ ] RPC methods use transaction naming: `Service/Method` with distributed tracing
- [ ] Database operations use span naming: `db.operation_table` with nil safety
- [ ] All operations include `service` tag
- [ ] Business operations use service-specific spans: `service.operation`

### Error Handling

- [ ] Validation errors capture with `sentry.CaptureException()`
- [ ] Database errors capture with business context and nil pointer safety
- [ ] External API errors capture with retry context
- [ ] Set `error: true` tag on all error spans
- [ ] Webhook panic recovery implemented

### Production Ready

- [ ] Environment-aware sampling (10% production)
- [ ] TracesSampler filtering noisy DB operations
- [ ] Production SENTRY_DSN in infrastructure config
- [ ] Monitor quota usage in Sentry dashboard

### Verification

- [ ] Transactions appear in Sentry Performance tab
- [ ] Errors appear in Sentry Issues tab
- [ ] Tags are properly set (service, entity IDs, etc.)
- [ ] Cross-service traces connect properly
- [ ] Sampling reduces production volume significantly

---

## Common Issues & Solutions

### Build Errors

```bash
# Missing go.sum entries (most common)
go mod tidy

# Type conversion errors (tags must be strings)
span.SetTag("count", fmt.Sprintf("%d", count))

# Missing imports for production patterns
import "strings"  // For TracesSampler
```

### Missing Traces

```bash
# Check: Business context tags are set early
transaction.SetTag("call_sid", callSID)
transaction.SetTag("asset_id", assetID)

# Check: Distributed tracing interceptors are configured
```

### Missing Events on Shutdown

```bash
# Problem: Events lost when service exits
# Solution: Explicit flush before ANY exit point

# ❌ Wrong - defer won't run with log.Fatalf
defer sentry.Flush(2 * time.Second)
log.Fatalf("Failed: %v", err)

# ✅ Correct - flush before fatal exit
sentry.CaptureException(err)
sentry.Flush(2 * time.Second)
log.Fatalf("Failed: %v", err)
```

### Performance Issues

```bash
# Check: TracesSampler is configured for production
# Should drop 60-80% of DB-only noise

# Check: High-frequency operations use sampling
# Use observability.StartSampledSpan() for polling
```

### Database Panics

```bash
# Use sql.NullString for nullable fields
var nullableField sql.NullString
if nullableField.Valid {
    result.Field = nullableField.String
} else {
    result.Field = "" // Explicit default
}
```

---

## Success Metrics

### What You Should See in Sentry:

1. **Performance Tab**: Your transaction names (`ServiceName/MethodName`)
2. **Issues Tab**: Captured errors with business context
3. **Tags**: Service name, entity IDs, operation types
4. **Traces**: Connected spans across service calls
5. **Quota Usage**: 60-80% reduction with TracesSampler

### Key Queries to Try:

```
# Find your service operations
service:your-service-name

# Find slow operations
transaction.duration:>2s

# Find error patterns
service:your-service-name error:true

# Find specific business operations
call_sid:* OR asset_id:*
```
