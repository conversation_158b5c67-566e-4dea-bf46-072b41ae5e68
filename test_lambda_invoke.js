#!/usr/bin/env node

/**
 * Test script to invoke the postConfirmation Lambda locally
 * This simulates the exact Cognito events that would trigger the Lambda
 */

const fs = require('fs');
const path = require('path');

// Mock Lambda context
const mockContext = {
    callbackWaitsForEmptyEventLoop: false,
    functionName: 'postConfirmationLambda',
    functionVersion: '$LATEST',
    invokedFunctionArn: 'arn:aws:lambda:us-west-2:123456789012:function:postConfirmationLambda',
    memoryLimitInMB: '128',
    awsRequestId: 'test-request-id',
    logGroupName: '/aws/lambda/postConfirmationLambda',
    logStreamName: '2024/01/15/[$LATEST]test-stream',
    getRemainingTimeInMillis: () => 30000,
    done: (error, result) => {
        if (error) {
            console.error('❌ Lambda execution failed:', error);
        } else {
            console.log('✅ Lambda execution succeeded:', result);
        }
    },
    fail: (error) => {
        console.error('❌ Lambda failed:', error);
    },
    succeed: (result) => {
        console.log('✅ Lambda succeeded:', result);
    }
};

// Test events based on your real Cognito users
const testEvents = [
    {
        name: "andrei_with_preregistration",
        event: {
            version: "1",
            triggerSource: "PostConfirmation_ConfirmSignUp",
            region: "us-west-2",
            userName: "<EMAIL>",
            userPoolId: "us-west-2_7SyrT2GBd",
            callerContext: {
                clientId: "52s9qm0anac1sohgioj2upgp1s"
            },
            request: {
                userAttributes: {
                    email: "<EMAIL>",
                    identities: JSON.stringify([{
                        dateCreated: "1752610858878",
                        userId: "<EMAIL>",
                        providerName: "OktaSandbox",
                        providerType: "SAML",
                        issuer: "http://www.okta.com/exk1wzdmtadxej3up1d8",
                        primary: "true"
                    }]),
                    sub: "6851f340-60a1-70ea-15e0-dcdd6f4c4284",
                    given_name: "Andrei",
                    family_name: "Michael"
                }
            },
            response: {}
        },
        description: "Test <EMAIL> with pre-registration mapping (should get Admin role + Dispatcher asset type)"
    },
    {
        name: "travis_with_preregistration", 
        event: {
            version: "1",
            triggerSource: "PostConfirmation_ConfirmSignUp",
            region: "us-west-2",
            userName: "<EMAIL>",
            userPoolId: "us-west-2_7SyrT2GBd",
            callerContext: {
                clientId: "52s9qm0anac1sohgioj2upgp1s"
            },
            request: {
                userAttributes: {
                    email: "<EMAIL>",
                    identities: JSON.stringify([{
                        dateCreated: "1752610858878",
                        userId: "<EMAIL>", 
                        providerName: "OktaSandbox",
                        providerType: "SAML",
                        issuer: "http://www.okta.com/exk1wzdmtadxej3up1d8",
                        primary: "true"
                    }]),
                    sub: "test-travis-sub-123",
                    given_name: "Travis",
                    family_name: "Test"
                }
            },
            response: {}
        },
        description: "Test <EMAIL> with pre-registration mapping (should get Supervisor role + Supervisor asset type)"
    },
    {
        name: "stoney_with_preregistration",
        event: {
            version: "1", 
            triggerSource: "PostConfirmation_ConfirmSignUp",
            region: "us-west-2",
            userName: "<EMAIL>",
            userPoolId: "us-west-2_7SyrT2GBd",
            callerContext: {
                clientId: "52s9qm0anac1sohgioj2upgp1s"
            },
            request: {
                userAttributes: {
                    email: "<EMAIL>",
                    identities: JSON.stringify([{
                        dateCreated: "1752610858878",
                        userId: "<EMAIL>",
                        providerName: "OktaSandbox", 
                        providerType: "SAML",
                        issuer: "http://www.okta.com/exk1wzdmtadxej3up1d8",
                        primary: "true"
                    }]),
                    sub: "test-stoney-sub-456",
                    given_name: "Stoney",
                    family_name: "Test"
                }
            },
            response: {}
        },
        description: "Test <EMAIL> with pre-registration mapping (should get Dispatcher role + Dispatcher asset type)"
    },
    {
        name: "unknown_user_fallback",
        event: {
            version: "1",
            triggerSource: "PostConfirmation_ConfirmSignUp", 
            region: "us-west-2",
            userName: "<EMAIL>",
            userPoolId: "us-west-2_7SyrT2GBd",
            callerContext: {
                clientId: "52s9qm0anac1sohgioj2upgp1s"
            },
            request: {
                userAttributes: {
                    email: "<EMAIL>",
                    identities: JSON.stringify([{
                        dateCreated: "1752610858878",
                        userId: "<EMAIL>",
                        providerName: "OktaSandbox",
                        providerType: "SAML", 
                        issuer: "http://www.okta.com/exk1wzdmtadxej3up1d8",
                        primary: "true"
                    }]),
                    sub: "test-unknown-sub-789",
                    given_name: "Unknown",
                    family_name: "User"
                }
            },
            response: {}
        },
        description: "Test unknown user without pre-registration (should fallback to default Responder role + Responder asset type)"
    }
];

// Set environment variables for testing
process.env.ORGS_SERVICE_URL = "http://localhost:8080";
process.env.WORKFLOW_SERVICE_URL = "http://localhost:8081";
process.env.PERMS_SERVICE_URL = "http://localhost:8082";
process.env.USER_POOL_ID = "us-west-2_7SyrT2GBd";
process.env.RESPONDER_ROLE_NAME = "Responder";
process.env.OKTA_SANDBOX_ISSUER = "http://www.okta.com/exk1wzdmtadxej3up1d8";
process.env.SANDBOX_ORG_ID = "2";

async function runTests() {
    console.log('🧪 Testing postConfirmation Lambda');
    console.log('===================================\n');

    // Try to load the actual Lambda handler
    let handler;
    try {
        // Assuming the Lambda handler is in index.js
        const lambdaModule = require('./index.js');
        handler = lambdaModule.handler;
        console.log('✅ Lambda handler loaded successfully\n');
    } catch (error) {
        console.log('⚠️  Could not load Lambda handler:', error.message);
        console.log('📝 This script will show the test events that should be used\n');
    }

    for (const testCase of testEvents) {
        console.log(`🔍 Test Case: ${testCase.name}`);
        console.log(`Description: ${testCase.description}`);
        console.log('---');
        
        if (handler) {
            try {
                console.log('🚀 Invoking Lambda...');
                const result = await new Promise((resolve, reject) => {
                    const callback = (error, result) => {
                        if (error) reject(error);
                        else resolve(result);
                    };
                    handler(testCase.event, mockContext, callback);
                });
                console.log('✅ Lambda execution completed:', JSON.stringify(result, null, 2));
            } catch (error) {
                console.error('❌ Lambda execution failed:', error);
            }
        } else {
            console.log('📋 Event JSON:');
            console.log(JSON.stringify(testCase.event, null, 2));
        }
        
        console.log('\n' + '='.repeat(50) + '\n');
    }

    console.log('📋 Manual Testing Instructions:');
    console.log('===============================');
    console.log('1. First, run the API test script to create pre-registration mappings:');
    console.log('   chmod +x test_pre_registration_api.sh');
    console.log('   ./test_pre_registration_api.sh\n');
    console.log('2. Start your services locally:');
    console.log('   - Orgs service on :8080');
    console.log('   - Assets service on :8081');
    console.log('   - Permissions service on :8082\n');
    console.log('3. Run this script to test the Lambda:');
    console.log('   node test_lambda_invoke.js\n');
    console.log('4. Check the database to verify:');
    console.log('   - Pre-registration mappings are marked as used');
    console.log('   - Assets are created with correct types');
    console.log('   - Users are assigned correct roles\n');
    console.log('5. Database queries to verify:');
    console.log('   SELECT * FROM pre_registration_user_mappings WHERE used_at IS NOT NULL;');
    console.log('   SELECT * FROM assets WHERE cognito_jwt_sub LIKE \'oktasandbox_%\';');
}

// Run the tests
runTests().catch(console.error);
