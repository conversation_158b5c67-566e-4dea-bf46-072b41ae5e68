t=2025-07-09T16:29:04-0700 lvl=info msg="no configuration paths supplied"
t=2025-07-09T16:29:04-0700 lvl=info msg="using configuration at default config path" path="/Users/<USER>/Library/Application Support/ngrok/ngrok.yml"
t=2025-07-09T16:29:04-0700 lvl=info msg="open config file" path="/Users/<USER>/Library/Application Support/ngrok/ngrok.yml" err=nil
t=2025-07-09T16:29:04-0700 lvl=info msg="starting web service" obj=web addr=127.0.0.1:4040 allow_hosts=[]
t=2025-07-09T16:29:04-0700 lvl=info msg="client session established" obj=tunnels.session
t=2025-07-09T16:29:04-0700 lvl=info msg="tunnel session started" obj=tunnels.session
t=2025-07-09T16:29:04-0700 lvl=info msg="started tunnel" obj=tunnels name=command_line addr=http://localhost:9084 url=https://5fb62915a1df.ngrok-free.app
t=2025-07-09T16:29:05-0700 lvl=info msg="update available" obj=updater
t=2025-07-09T16:29:05-0700 lvl=info msg="join connections" obj=join id=65ef46698bb3 l=[::1]:9084 r=**************:53883
t=2025-07-09T16:33:37-0700 lvl=info msg="join connections" obj=join id=4572132cf7b1 l=[::1]:9084 r=************:35716
t=2025-07-09T16:33:37-0700 lvl=info msg="join connections" obj=join id=5a876083cde9 l=[::1]:9084 r=*************:52392
t=2025-07-09T16:33:44-0700 lvl=info msg="join connections" obj=join id=329821327df8 l=[::1]:9084 r=*************:61262
t=2025-07-09T16:33:44-0700 lvl=info msg="join connections" obj=join id=402d62329820 l=[::1]:9084 r=*************:23256
t=2025-07-09T16:34:41-0700 lvl=info msg="join connections" obj=join id=96dbe53903b1 l=[::1]:9084 r=*************:47524
t=2025-07-09T16:34:41-0700 lvl=info msg="join connections" obj=join id=0a28c1e31a1f l=[::1]:9084 r=*************:64302
t=2025-07-09T16:34:43-0700 lvl=info msg="join connections" obj=join id=3d65f8e8b712 l=[::1]:9084 r=18.207.195.18:32214
t=2025-07-09T16:34:43-0700 lvl=info msg="join connections" obj=join id=e424053b7ef1 l=[::1]:9084 r=18.209.18.252:62280
t=2025-07-09T16:34:47-0700 lvl=info msg="join connections" obj=join id=49e623f85458 l=[::1]:9084 r=13.217.207.176:39464
t=2025-07-09T16:34:47-0700 lvl=info msg="join connections" obj=join id=f3e1c914b101 l=[::1]:9084 r=13.218.206.69:64838
t=2025-07-09T16:42:24-0700 lvl=eror msg="session closed, starting reconnect loop" obj=tunnels.session obj=csess id=5f728e7de670 err="read EOF from remote peer"
t=2025-07-09T16:42:24-0700 lvl=info msg="client session established" obj=tunnels.session
t=2025-07-09T16:42:24-0700 lvl=info msg="tunnel session started" obj=tunnels.session
t=2025-07-09T16:42:36-0700 lvl=eror msg="heartbeat timeout, terminating session" obj=tunnels.session obj=csess id=eac68ba41b51 clientid=0b47db377ac53e4a10fcb402d717d21a
t=2025-07-09T16:52:04-0700 lvl=eror msg="session closed, starting reconnect loop" obj=tunnels.session obj=csess id=5f728e7de670 err="read tcp **********:60019->52.53.75.151:443: read: connection reset by peer"
t=2025-07-09T16:52:05-0700 lvl=info msg="client session established" obj=tunnels.session
t=2025-07-09T16:52:05-0700 lvl=info msg="tunnel session started" obj=tunnels.session
t=2025-07-09T16:52:19-0700 lvl=eror msg="heartbeat timeout, terminating session" obj=tunnels.session obj=csess id=48004131b4b2 clientid=0b47db377ac53e4a10fcb402d717d21a
t=2025-07-09T17:00:19-0700 lvl=eror msg="heartbeat timeout, terminating session" obj=tunnels.session obj=csess id=def9cf4bda2c clientid=0b47db377ac53e4a10fcb402d717d21a
t=2025-07-09T17:00:19-0700 lvl=eror msg="session closed, starting reconnect loop" obj=tunnels.session obj=csess id=5f728e7de670 err="session closed"
t=2025-07-09T17:00:19-0700 lvl=info msg="client session established" obj=tunnels.session
t=2025-07-09T17:00:19-0700 lvl=info msg="tunnel session started" obj=tunnels.session
t=2025-07-09T17:53:44-0700 lvl=eror msg="heartbeat timeout, terminating session" obj=tunnels.session obj=csess id=22ce53109c6f clientid=0b47db377ac53e4a10fcb402d717d21a
t=2025-07-09T17:53:44-0700 lvl=eror msg="session closed, starting reconnect loop" obj=tunnels.session obj=csess id=5f728e7de670 err="session closed"
t=2025-07-09T17:53:44-0700 lvl=info msg="client session established" obj=tunnels.session
t=2025-07-09T17:53:44-0700 lvl=info msg="tunnel session started" obj=tunnels.session
t=2025-07-09T17:54:26-0700 lvl=eror msg="heartbeat timeout, terminating session" obj=tunnels.session obj=csess id=a6df9a4d545e clientid=0b47db377ac53e4a10fcb402d717d21a
t=2025-07-09T17:54:26-0700 lvl=eror msg="session closed, starting reconnect loop" obj=tunnels.session obj=csess id=5f728e7de670 err="session closed"
t=2025-07-09T17:54:26-0700 lvl=eror msg="failed to reconnect session" obj=tunnels.session err="failed to dial ngrok server with address \"connect.ngrok-agent.com:443\": dial tcp [2600:1f1c:d8:5f01::6e74:5]:443: connect: no route to host"
t=2025-07-09T17:54:26-0700 lvl=eror msg="failed to reconnect session" obj=tunnels.session err="failed to dial ngrok server with address \"connect.ngrok-agent.com:443\": dial tcp [2600:1f1c:d8:5f01::6e74:5]:443: connect: no route to host"
t=2025-07-09T17:54:27-0700 lvl=eror msg="failed to reconnect session" obj=tunnels.session err="failed to dial ngrok server with address \"connect.ngrok-agent.com:443\": dial tcp [2600:1f1c:d8:5f01::6e74:5]:443: connect: no route to host"
t=2025-07-09T18:26:22-0700 lvl=eror msg="failed to reconnect session" obj=tunnels.session err="failed to dial ngrok server with address \"connect.ngrok-agent.com:443\": dial tcp: lookup connect.ngrok-agent.com: i/o timeout"
t=2025-07-09T18:26:33-0700 lvl=eror msg="failed to reconnect session" obj=tunnels.session err="failed to dial ngrok server with address \"connect.ngrok-agent.com:443\": dial tcp: lookup connect.ngrok-agent.com: no such host"
t=2025-07-09T18:26:45-0700 lvl=eror msg="failed to reconnect session" obj=tunnels.session err="failed to dial ngrok server with address \"connect.ngrok-agent.com:443\": dial tcp: lookup connect.ngrok-agent.com: no such host"
t=2025-07-09T18:28:29-0700 lvl=info msg="client session established" obj=tunnels.session
t=2025-07-09T18:28:29-0700 lvl=info msg="tunnel session started" obj=tunnels.session
t=2025-07-09T18:46:39-0700 lvl=eror msg="heartbeat timeout, terminating session" obj=tunnels.session obj=csess id=2d10c98afcf9 clientid=0b47db377ac53e4a10fcb402d717d21a
t=2025-07-09T18:46:39-0700 lvl=eror msg="session closed, starting reconnect loop" obj=tunnels.session obj=csess id=5f728e7de670 err="session closed"
t=2025-07-09T18:46:39-0700 lvl=info msg="client session established" obj=tunnels.session
t=2025-07-09T18:46:39-0700 lvl=info msg="tunnel session started" obj=tunnels.session
t=2025-07-09T19:17:46-0700 lvl=eror msg="session closed, starting reconnect loop" obj=tunnels.session obj=csess id=5f728e7de670 err="read tcp [2601:643:8a80:1cb0:a1db:d9d3:d06d:d746]:49892->[2600:1f1c:d8:5f01::6e74:3]:443: read: connection reset by peer"
t=2025-07-09T19:17:47-0700 lvl=info msg="client session established" obj=tunnels.session
t=2025-07-09T19:17:47-0700 lvl=info msg="tunnel session started" obj=tunnels.session
t=2025-07-09T19:34:16-0700 lvl=eror msg="session closed, starting reconnect loop" obj=tunnels.session obj=csess id=5f728e7de670 err="read tcp [2601:643:8a80:1cb0:a1db:d9d3:d06d:d746]:50013->[2600:1f1c:d8:5f00::6e74:0]:443: read: connection reset by peer"
t=2025-07-09T19:34:16-0700 lvl=info msg="client session established" obj=tunnels.session
t=2025-07-09T19:34:16-0700 lvl=info msg="tunnel session started" obj=tunnels.session
t=2025-07-09T19:34:20-0700 lvl=eror msg="heartbeat timeout, terminating session" obj=tunnels.session obj=csess id=21e1a789e683 clientid=0b47db377ac53e4a10fcb402d717d21a
t=2025-07-09T19:49:54-0700 lvl=eror msg="session closed, starting reconnect loop" obj=tunnels.session obj=csess id=5f728e7de670 err="read tcp [2601:643:8a80:1cb0:a1db:d9d3:d06d:d746]:50128->[2600:1f1c:d8:5f00::6e74:2]:443: read: connection reset by peer"
t=2025-07-09T19:49:54-0700 lvl=info msg="client session established" obj=tunnels.session
t=2025-07-09T19:49:54-0700 lvl=info msg="tunnel session started" obj=tunnels.session
t=2025-07-09T19:51:29-0700 lvl=eror msg="heartbeat timeout, terminating session" obj=tunnels.session obj=csess id=2f0d430ddd0b clientid=0b47db377ac53e4a10fcb402d717d21a
t=2025-07-09T20:07:55-0700 lvl=eror msg="session closed, starting reconnect loop" obj=tunnels.session obj=csess id=5f728e7de670 err="read tcp [2601:643:8a80:1cb0:a1db:d9d3:d06d:d746]:50228->[2600:1f1c:d8:5f01::6e74:1]:443: read: connection reset by peer"
t=2025-07-09T20:07:56-0700 lvl=info msg="client session established" obj=tunnels.session
t=2025-07-09T20:07:56-0700 lvl=info msg="tunnel session started" obj=tunnels.session
t=2025-07-09T20:25:30-0700 lvl=eror msg="heartbeat timeout, terminating session" obj=tunnels.session obj=csess id=e23e008abe1e clientid=0b47db377ac53e4a10fcb402d717d21a
t=2025-07-09T20:41:46-0700 lvl=eror msg="session closed, starting reconnect loop" obj=tunnels.session obj=csess id=5f728e7de670 err="read tcp 10.0.0.157:50375->52.53.75.151:443: read: connection reset by peer"
t=2025-07-09T20:41:46-0700 lvl=info msg="client session established" obj=tunnels.session
t=2025-07-09T20:41:46-0700 lvl=info msg="tunnel session started" obj=tunnels.session
t=2025-07-09T20:41:50-0700 lvl=eror msg="heartbeat timeout, terminating session" obj=tunnels.session obj=csess id=746388bee5f3 clientid=0b47db377ac53e4a10fcb402d717d21a
t=2025-07-09T20:52:27-0700 lvl=eror msg="heartbeat timeout, terminating session" obj=tunnels.session obj=csess id=4d204d0892b8 clientid=0b47db377ac53e4a10fcb402d717d21a
t=2025-07-09T21:09:25-0700 lvl=eror msg="heartbeat timeout, terminating session" obj=tunnels.session obj=csess id=3c3a4a0a3372 clientid=0b47db377ac53e4a10fcb402d717d21a
t=2025-07-09T21:09:25-0700 lvl=eror msg="session closed, starting reconnect loop" obj=tunnels.session obj=csess id=5f728e7de670 err="session closed"
t=2025-07-09T21:09:26-0700 lvl=info msg="client session established" obj=tunnels.session
t=2025-07-09T21:09:26-0700 lvl=info msg="tunnel session started" obj=tunnels.session
t=2025-07-09T21:26:10-0700 lvl=eror msg="session closed, starting reconnect loop" obj=tunnels.session obj=csess id=5f728e7de670 err="read tcp [2601:643:8a80:1cb0:a1db:d9d3:d06d:d746]:50748->[2600:1f1c:d8:5f01::6e74:5]:443: read: connection reset by peer"
t=2025-07-09T21:26:10-0700 lvl=info msg="client session established" obj=tunnels.session
t=2025-07-09T21:26:10-0700 lvl=info msg="tunnel session started" obj=tunnels.session
t=2025-07-09T21:45:32-0700 lvl=eror msg="heartbeat timeout, terminating session" obj=tunnels.session obj=csess id=fd325e2e0bc2 clientid=0b47db377ac53e4a10fcb402d717d21a
t=2025-07-09T21:50:20-0700 lvl=eror msg="heartbeat timeout, terminating session" obj=tunnels.session obj=csess id=0586891e48e1 clientid=0b47db377ac53e4a10fcb402d717d21a
t=2025-07-09T21:50:20-0700 lvl=eror msg="session closed, starting reconnect loop" obj=tunnels.session obj=csess id=5f728e7de670 err="session closed"
t=2025-07-09T21:50:20-0700 lvl=info msg="client session established" obj=tunnels.session
t=2025-07-09T21:50:20-0700 lvl=info msg="tunnel session started" obj=tunnels.session
t=2025-07-09T22:05:54-0700 lvl=eror msg="session closed, starting reconnect loop" obj=tunnels.session obj=csess id=5f728e7de670 err="read tcp [2601:643:8a80:1cb0:a1db:d9d3:d06d:d746]:51099->[2600:1f1c:d8:5f01::6e74:1]:443: read: connection reset by peer"
t=2025-07-09T22:05:54-0700 lvl=info msg="client session established" obj=tunnels.session
t=2025-07-09T22:05:54-0700 lvl=info msg="tunnel session started" obj=tunnels.session
t=2025-07-09T22:38:14-0700 lvl=eror msg="session closed, starting reconnect loop" obj=tunnels.session obj=csess id=5f728e7de670 err="read tcp [2601:643:8a80:1cb0:a1db:d9d3:d06d:d746]:51240->[2600:1f1c:d8:5f01::6e74:3]:443: read: connection reset by peer"
t=2025-07-09T22:38:14-0700 lvl=info msg="client session established" obj=tunnels.session
t=2025-07-09T22:38:14-0700 lvl=info msg="tunnel session started" obj=tunnels.session
t=2025-07-09T22:38:19-0700 lvl=eror msg="heartbeat timeout, terminating session" obj=tunnels.session obj=csess id=3892332400af clientid=0b47db377ac53e4a10fcb402d717d21a
t=2025-07-09T22:38:29-0700 lvl=eror msg="heartbeat timeout, terminating session" obj=tunnels.session obj=csess id=ceb1a09953d6 clientid=0b47db377ac53e4a10fcb402d717d21a
t=2025-07-09T22:55:43-0700 lvl=eror msg="session closed, starting reconnect loop" obj=tunnels.session obj=csess id=5f728e7de670 err="read tcp [2601:643:8a80:1cb0:a1db:d9d3:d06d:d746]:51371->[2600:1f1c:d8:5f00::6e74:0]:443: read: connection reset by peer"
t=2025-07-09T22:55:43-0700 lvl=info msg="client session established" obj=tunnels.session
t=2025-07-09T22:55:43-0700 lvl=info msg="tunnel session started" obj=tunnels.session
t=2025-07-09T22:56:31-0700 lvl=eror msg="heartbeat timeout, terminating session" obj=tunnels.session obj=csess id=99fc17f56640 clientid=0b47db377ac53e4a10fcb402d717d21a
t=2025-07-09T22:56:41-0700 lvl=eror msg="heartbeat timeout, terminating session" obj=tunnels.session obj=csess id=271dea3f4d9b clientid=0b47db377ac53e4a10fcb402d717d21a
t=2025-07-09T22:56:41-0700 lvl=eror msg="session closed, starting reconnect loop" obj=tunnels.session obj=csess id=5f728e7de670 err="session closed"
t=2025-07-09T22:56:41-0700 lvl=info msg="client session established" obj=tunnels.session
t=2025-07-09T22:56:41-0700 lvl=info msg="tunnel session started" obj=tunnels.session
t=2025-07-09T23:11:51-0700 lvl=eror msg="session closed, starting reconnect loop" obj=tunnels.session obj=csess id=5f728e7de670 err="read tcp [2601:643:8a80:1cb0:a1db:d9d3:d06d:d746]:51837->[2600:1f1c:d8:5f01::6e74:5]:443: read: connection reset by peer"
t=2025-07-09T23:11:51-0700 lvl=info msg="client session established" obj=tunnels.session
t=2025-07-09T23:11:51-0700 lvl=info msg="tunnel session started" obj=tunnels.session
t=2025-07-09T23:12:06-0700 lvl=eror msg="heartbeat timeout, terminating session" obj=tunnels.session obj=csess id=911411d57ff2 clientid=0b47db377ac53e4a10fcb402d717d21a
t=2025-07-09T23:29:12-0700 lvl=eror msg="session closed, starting reconnect loop" obj=tunnels.session obj=csess id=5f728e7de670 err="read tcp [2601:643:8a80:1cb0:a1db:d9d3:d06d:d746]:51954->[2600:1f1c:d8:5f00::6e74:2]:443: read: connection reset by peer"
t=2025-07-09T23:29:12-0700 lvl=info msg="client session established" obj=tunnels.session
t=2025-07-09T23:29:12-0700 lvl=info msg="tunnel session started" obj=tunnels.session
t=2025-07-10T00:02:00-0700 lvl=eror msg="session closed, starting reconnect loop" obj=tunnels.session obj=csess id=5f728e7de670 err="read tcp [2601:643:8a80:1cb0:a1db:d9d3:d06d:d746]:52159->[2600:1f1c:d8:5f01::6e74:3]:443: read: connection reset by peer"
t=2025-07-10T00:02:00-0700 lvl=info msg="client session established" obj=tunnels.session
t=2025-07-10T00:02:00-0700 lvl=info msg="tunnel session started" obj=tunnels.session
t=2025-07-10T00:18:31-0700 lvl=eror msg="heartbeat timeout, terminating session" obj=tunnels.session obj=csess id=69fac075568d clientid=0b47db377ac53e4a10fcb402d717d21a
t=2025-07-10T00:26:51-0700 lvl=eror msg="session closed, starting reconnect loop" obj=tunnels.session obj=csess id=5f728e7de670 err="read tcp [2601:643:8a80:1cb0:a1db:d9d3:d06d:d746]:52293->[2600:1f1c:d8:5f01::6e74:1]:443: read: connection reset by peer"
t=2025-07-10T00:26:51-0700 lvl=info msg="client session established" obj=tunnels.session
t=2025-07-10T00:26:51-0700 lvl=info msg="tunnel session started" obj=tunnels.session
t=2025-07-10T00:26:56-0700 lvl=eror msg="heartbeat timeout, terminating session" obj=tunnels.session obj=csess id=d230170d4f10 clientid=0b47db377ac53e4a10fcb402d717d21a
t=2025-07-10T00:44:10-0700 lvl=eror msg="session closed, starting reconnect loop" obj=tunnels.session obj=csess id=5f728e7de670 err="read tcp [2601:643:8a80:1cb0:a1db:d9d3:d06d:d746]:52426->[2600:1f1c:d8:5f00::6e74:2]:443: read: connection reset by peer"
t=2025-07-10T00:44:10-0700 lvl=info msg="client session established" obj=tunnels.session
t=2025-07-10T00:44:10-0700 lvl=info msg="tunnel session started" obj=tunnels.session
t=2025-07-10T00:59:46-0700 lvl=eror msg="heartbeat timeout, terminating session" obj=tunnels.session obj=csess id=1cb3fe48eb91 clientid=0b47db377ac53e4a10fcb402d717d21a
t=2025-07-10T01:16:30-0700 lvl=eror msg="session closed, starting reconnect loop" obj=tunnels.session obj=csess id=5f728e7de670 err="read tcp [2601:643:8a80:1cb0:a1db:d9d3:d06d:d746]:52542->[2600:1f1c:d8:5f00::6e74:4]:443: read: connection reset by peer"
t=2025-07-10T01:16:30-0700 lvl=info msg="client session established" obj=tunnels.session
t=2025-07-10T01:16:30-0700 lvl=info msg="tunnel session started" obj=tunnels.session
t=2025-07-10T01:31:50-0700 lvl=eror msg="heartbeat timeout, terminating session" obj=tunnels.session obj=csess id=1ff3843d43f7 clientid=0b47db377ac53e4a10fcb402d717d21a
t=2025-07-10T01:48:30-0700 lvl=eror msg="session closed, starting reconnect loop" obj=tunnels.session obj=csess id=5f728e7de670 err="read tcp [2601:643:8a80:1cb0:a1db:d9d3:d06d:d746]:52656->[2600:1f1c:d8:5f01::6e74:3]:443: read: connection reset by peer"
t=2025-07-10T01:48:31-0700 lvl=info msg="client session established" obj=tunnels.session
t=2025-07-10T01:48:31-0700 lvl=info msg="tunnel session started" obj=tunnels.session
t=2025-07-10T02:04:36-0700 lvl=eror msg="heartbeat timeout, terminating session" obj=tunnels.session obj=csess id=416821ee9d95 clientid=0b47db377ac53e4a10fcb402d717d21a
t=2025-07-10T02:22:33-0700 lvl=eror msg="session closed, starting reconnect loop" obj=tunnels.session obj=csess id=5f728e7de670 err="read tcp [2601:643:8a80:1cb0:a1db:d9d3:d06d:d746]:52845->[2600:1f1c:d8:5f00::6e74:2]:443: read: connection reset by peer"
t=2025-07-10T02:22:33-0700 lvl=info msg="client session established" obj=tunnels.session
t=2025-07-10T02:22:33-0700 lvl=info msg="tunnel session started" obj=tunnels.session
t=2025-07-10T02:38:56-0700 lvl=eror msg="heartbeat timeout, terminating session" obj=tunnels.session obj=csess id=87544b3f3207 clientid=0b47db377ac53e4a10fcb402d717d21a
t=2025-07-10T02:56:19-0700 lvl=eror msg="session closed, starting reconnect loop" obj=tunnels.session obj=csess id=5f728e7de670 err="read tcp [2601:643:8a80:1cb0:a1db:d9d3:d06d:d746]:52985->[2600:1f1c:d8:5f01::6e74:1]:443: read: connection reset by peer"
t=2025-07-10T02:56:19-0700 lvl=info msg="client session established" obj=tunnels.session
t=2025-07-10T02:56:19-0700 lvl=info msg="tunnel session started" obj=tunnels.session
t=2025-07-10T02:56:27-0700 lvl=eror msg="heartbeat timeout, terminating session" obj=tunnels.session obj=csess id=51170b8d6f23 clientid=0b47db377ac53e4a10fcb402d717d21a
t=2025-07-10T03:14:00-0700 lvl=eror msg="heartbeat timeout, terminating session" obj=tunnels.session obj=csess id=ada1581dba47 clientid=0b47db377ac53e4a10fcb402d717d21a
t=2025-07-10T03:31:44-0700 lvl=eror msg="session closed, starting reconnect loop" obj=tunnels.session obj=csess id=5f728e7de670 err="read tcp [2601:643:8a80:1cb0:a1db:d9d3:d06d:d746]:53139->[2600:1f1c:d8:5f01::6e74:5]:443: read: connection reset by peer"
t=2025-07-10T03:31:44-0700 lvl=info msg="client session established" obj=tunnels.session
t=2025-07-10T03:31:44-0700 lvl=info msg="tunnel session started" obj=tunnels.session
t=2025-07-10T04:05:38-0700 lvl=eror msg="session closed, starting reconnect loop" obj=tunnels.session obj=csess id=5f728e7de670 err="read tcp [2601:643:8a80:1cb0:a1db:d9d3:d06d:d746]:53345->[2600:1f1c:d8:5f00::6e74:2]:443: read: connection reset by peer"
t=2025-07-10T04:05:38-0700 lvl=info msg="client session established" obj=tunnels.session
t=2025-07-10T04:05:38-0700 lvl=info msg="tunnel session started" obj=tunnels.session
t=2025-07-10T04:21:40-0700 lvl=eror msg="heartbeat timeout, terminating session" obj=tunnels.session obj=csess id=996f1dd7548a clientid=0b47db377ac53e4a10fcb402d717d21a
t=2025-07-10T04:38:52-0700 lvl=eror msg="session closed, starting reconnect loop" obj=tunnels.session obj=csess id=5f728e7de670 err="read tcp [2601:643:8a80:1cb0:a1db:d9d3:d06d:d746]:53476->[2600:1f1c:d8:5f00::6e74:4]:443: read: connection reset by peer"
t=2025-07-10T04:38:52-0700 lvl=info msg="client session established" obj=tunnels.session
t=2025-07-10T04:38:52-0700 lvl=info msg="tunnel session started" obj=tunnels.session
t=2025-07-10T04:43:06-0700 lvl=eror msg="heartbeat timeout, terminating session" obj=tunnels.session obj=csess id=149457fd268f clientid=0b47db377ac53e4a10fcb402d717d21a
t=2025-07-10T05:00:36-0700 lvl=eror msg="heartbeat timeout, terminating session" obj=tunnels.session obj=csess id=9cfbc0b5fb10 clientid=0b47db377ac53e4a10fcb402d717d21a
t=2025-07-10T05:32:24-0700 lvl=eror msg="heartbeat timeout, terminating session" obj=tunnels.session obj=csess id=220eef02c364 clientid=0b47db377ac53e4a10fcb402d717d21a
t=2025-07-10T05:32:24-0700 lvl=eror msg="session closed, starting reconnect loop" obj=tunnels.session obj=csess id=5f728e7de670 err="session closed"
t=2025-07-10T05:32:25-0700 lvl=info msg="client session established" obj=tunnels.session
t=2025-07-10T05:32:25-0700 lvl=info msg="tunnel session started" obj=tunnels.session
t=2025-07-10T06:06:09-0700 lvl=eror msg="session closed, starting reconnect loop" obj=tunnels.session obj=csess id=5f728e7de670 err="read tcp [2601:643:8a80:1cb0:a1db:d9d3:d06d:d746]:54004->[2600:1f1c:d8:5f01::6e74:1]:443: read: connection reset by peer"
t=2025-07-10T06:06:09-0700 lvl=info msg="client session established" obj=tunnels.session
t=2025-07-10T06:06:09-0700 lvl=info msg="tunnel session started" obj=tunnels.session
t=2025-07-10T06:37:48-0700 lvl=eror msg="session closed, starting reconnect loop" obj=tunnels.session obj=csess id=5f728e7de670 err="read tcp [2601:643:8a80:1cb0:a1db:d9d3:d06d:d746]:54167->[2600:1f1c:d8:5f00::6e74:4]:443: read: connection reset by peer"
t=2025-07-10T06:37:48-0700 lvl=info msg="client session established" obj=tunnels.session
t=2025-07-10T06:37:48-0700 lvl=info msg="tunnel session started" obj=tunnels.session
t=2025-07-10T06:44:05-0700 lvl=eror msg="heartbeat timeout, terminating session" obj=tunnels.session obj=csess id=d0cad2f5de33 clientid=0b47db377ac53e4a10fcb402d717d21a
t=2025-07-10T06:44:11-0700 lvl=eror msg="session closed, starting reconnect loop" obj=tunnels.session obj=csess id=5f728e7de670 err="read tcp [2601:643:8a80:1cb0:a1db:d9d3:d06d:d746]:54338->[2600:1f1c:d8:5f00::6e74:0]:443: read: connection reset by peer"
t=2025-07-10T06:44:11-0700 lvl=info msg="client session established" obj=tunnels.session
t=2025-07-10T06:44:11-0700 lvl=info msg="tunnel session started" obj=tunnels.session
t=2025-07-10T06:45:11-0700 lvl=eror msg="heartbeat timeout, terminating session" obj=tunnels.session obj=csess id=358b6abcc0cc clientid=0b47db377ac53e4a10fcb402d717d21a
t=2025-07-10T06:45:34-0700 lvl=eror msg="heartbeat timeout, terminating session" obj=tunnels.session obj=csess id=c5a29570cba5 clientid=0b47db377ac53e4a10fcb402d717d21a
t=2025-07-10T06:46:03-0700 lvl=eror msg="heartbeat timeout, terminating session" obj=tunnels.session obj=csess id=554b4fa45e35 clientid=0b47db377ac53e4a10fcb402d717d21a
t=2025-07-10T06:46:03-0700 lvl=eror msg="session closed, starting reconnect loop" obj=tunnels.session obj=csess id=5f728e7de670 err="session closed"
t=2025-07-10T06:46:03-0700 lvl=info msg="client session established" obj=tunnels.session
t=2025-07-10T06:46:03-0700 lvl=info msg="tunnel session started" obj=tunnels.session
t=2025-07-10T07:03:56-0700 lvl=eror msg="session closed, starting reconnect loop" obj=tunnels.session obj=csess id=5f728e7de670 err="read tcp 10.0.0.157:54683->52.53.56.252:443: read: connection reset by peer"
t=2025-07-10T07:03:56-0700 lvl=info msg="client session established" obj=tunnels.session
t=2025-07-10T07:03:56-0700 lvl=info msg="tunnel session started" obj=tunnels.session
t=2025-07-10T07:19:06-0700 lvl=eror msg="session closed, starting reconnect loop" obj=tunnels.session obj=csess id=5f728e7de670 err="read tcp [2601:643:8a80:1cb0:a1db:d9d3:d06d:d746]:54775->[2600:1f1c:d8:5f00::6e74:4]:443: read: connection reset by peer"
t=2025-07-10T07:19:06-0700 lvl=info msg="client session established" obj=tunnels.session
t=2025-07-10T07:19:06-0700 lvl=info msg="tunnel session started" obj=tunnels.session
t=2025-07-10T07:19:11-0700 lvl=eror msg="heartbeat timeout, terminating session" obj=tunnels.session obj=csess id=3ef78b11bed3 clientid=0b47db377ac53e4a10fcb402d717d21a
t=2025-07-10T07:19:21-0700 lvl=eror msg="heartbeat timeout, terminating session" obj=tunnels.session obj=csess id=aef5c363e4a0 clientid=0b47db377ac53e4a10fcb402d717d21a
t=2025-07-10T07:45:08-0700 lvl=eror msg="session closed, starting reconnect loop" obj=tunnels.session obj=csess id=5f728e7de670 err="read tcp [2601:643:8a80:1cb0:a1db:d9d3:d06d:d746]:54901->[2600:1f1c:d8:5f00::6e74:0]:443: read: connection reset by peer"
t=2025-07-10T07:45:08-0700 lvl=info msg="client session established" obj=tunnels.session
t=2025-07-10T07:45:08-0700 lvl=info msg="tunnel session started" obj=tunnels.session
t=2025-07-10T07:45:23-0700 lvl=eror msg="heartbeat timeout, terminating session" obj=tunnels.session obj=csess id=f4b82ab236df clientid=0b47db377ac53e4a10fcb402d717d21a
t=2025-07-10T08:01:53-0700 lvl=eror msg="session closed, starting reconnect loop" obj=tunnels.session obj=csess id=5f728e7de670 err="read tcp [2601:643:8a80:1cb0:a1db:d9d3:d06d:d746]:55367->[2600:1f1c:d8:5f00::6e74:0]:443: read: connection reset by peer"
t=2025-07-10T08:01:53-0700 lvl=info msg="client session established" obj=tunnels.session
t=2025-07-10T08:01:53-0700 lvl=info msg="tunnel session started" obj=tunnels.session
t=2025-07-10T08:34:15-0700 lvl=eror msg="session closed, starting reconnect loop" obj=tunnels.session obj=csess id=5f728e7de670 err="read tcp [2601:643:8a80:1cb0:a1db:d9d3:d06d:d746]:55589->[2600:1f1c:d8:5f00::6e74:4]:443: read: connection reset by peer"
t=2025-07-10T08:34:16-0700 lvl=info msg="client session established" obj=tunnels.session
t=2025-07-10T08:34:16-0700 lvl=info msg="tunnel session started" obj=tunnels.session
t=2025-07-10T08:34:20-0700 lvl=eror msg="heartbeat timeout, terminating session" obj=tunnels.session obj=csess id=2458074ebf15 clientid=0b47db377ac53e4a10fcb402d717d21a
t=2025-07-10T08:34:30-0700 lvl=eror msg="heartbeat timeout, terminating session" obj=tunnels.session obj=csess id=8eb8f598e155 clientid=0b47db377ac53e4a10fcb402d717d21a
t=2025-07-10T08:46:05-0700 lvl=eror msg="heartbeat timeout, terminating session" obj=tunnels.session obj=csess id=633488c13521 clientid=0b47db377ac53e4a10fcb402d717d21a
t=2025-07-10T08:46:05-0700 lvl=eror msg="session closed, starting reconnect loop" obj=tunnels.session obj=csess id=5f728e7de670 err="session closed"
t=2025-07-10T08:46:05-0700 lvl=info msg="client session established" obj=tunnels.session
t=2025-07-10T08:46:05-0700 lvl=info msg="tunnel session started" obj=tunnels.session
t=2025-07-10T08:53:21-0700 lvl=eror msg="session closed, starting reconnect loop" obj=tunnels.session obj=csess id=5f728e7de670 err="read tcp 10.0.0.157:56053->52.53.75.151:443: read: connection reset by peer"
t=2025-07-10T08:53:21-0700 lvl=info msg="client session established" obj=tunnels.session
t=2025-07-10T08:53:21-0700 lvl=info msg="tunnel session started" obj=tunnels.session
t=2025-07-10T09:00:03-0700 lvl=eror msg="session closed, starting reconnect loop" obj=tunnels.session obj=csess id=5f728e7de670 err="read tcp [2601:643:8a80:1cb0:a1db:d9d3:d06d:d746]:56176->[2600:1f1c:d8:5f00::6e74:4]:443: read: connection reset by peer"
t=2025-07-10T09:00:03-0700 lvl=info msg="client session established" obj=tunnels.session
t=2025-07-10T09:00:03-0700 lvl=info msg="tunnel session started" obj=tunnels.session
t=2025-07-10T09:17:16-0700 lvl=eror msg="heartbeat timeout, terminating session" obj=tunnels.session obj=csess id=f218ee15a186 clientid=0b47db377ac53e4a10fcb402d717d21a
t=2025-07-10T09:18:06-0700 lvl=eror msg="session closed, starting reconnect loop" obj=tunnels.session obj=csess id=5f728e7de670 err="read tcp [2601:643:8a80:1cb0:a1db:d9d3:d06d:d746]:56297->[2600:1f1c:d8:5f01::6e74:5]:443: read: connection reset by peer"
t=2025-07-10T09:18:07-0700 lvl=info msg="client session established" obj=tunnels.session
t=2025-07-10T09:18:07-0700 lvl=info msg="tunnel session started" obj=tunnels.session
t=2025-07-10T09:18:11-0700 lvl=eror msg="heartbeat timeout, terminating session" obj=tunnels.session obj=csess id=a8191b57c730 clientid=0b47db377ac53e4a10fcb402d717d21a
t=2025-07-10T09:19:21-0700 lvl=eror msg="heartbeat timeout, terminating session" obj=tunnels.session obj=csess id=ecf220a16371 clientid=0b47db377ac53e4a10fcb402d717d21a
t=2025-07-10T09:36:36-0700 lvl=eror msg="session closed, starting reconnect loop" obj=tunnels.session obj=csess id=5f728e7de670 err="read tcp [2601:643:8a80:1cb0:a1db:d9d3:d06d:d746]:56443->[2600:1f1c:d8:5f00::6e74:2]:443: read: no route to host"
t=2025-07-10T09:47:07-0700 lvl=eror msg="heartbeat timeout, terminating session" obj=tunnels.session obj=csess id=961231d8ec98 clientid=0b47db377ac53e4a10fcb402d717d21a
t=2025-07-10T09:52:17-0700 lvl=eror msg="failed to reconnect session" obj=tunnels.session err="failed to dial ngrok server with address \"connect.ngrok-agent.com:443\": dial tcp: lookup connect.ngrok-agent.com: i/o timeout"
t=2025-07-10T09:52:23-0700 lvl=info msg="client session established" obj=tunnels.session
t=2025-07-10T09:52:23-0700 lvl=info msg="tunnel session started" obj=tunnels.session
t=2025-07-10T09:54:16-0700 lvl=eror msg="heartbeat timeout, terminating session" obj=tunnels.session obj=csess id=b5379f7a18ef clientid=0b47db377ac53e4a10fcb402d717d21a
t=2025-07-10T09:54:16-0700 lvl=eror msg="session closed, starting reconnect loop" obj=tunnels.session obj=csess id=5f728e7de670 err="session closed"
t=2025-07-10T09:54:17-0700 lvl=info msg="client session established" obj=tunnels.session
t=2025-07-10T09:54:17-0700 lvl=info msg="tunnel session started" obj=tunnels.session
t=2025-07-10T10:46:47-0700 lvl=eror msg="heartbeat timeout, terminating session" obj=tunnels.session obj=csess id=b4d7d19b75f2 clientid=0b47db377ac53e4a10fcb402d717d21a
t=2025-07-10T10:46:47-0700 lvl=eror msg="session closed, starting reconnect loop" obj=tunnels.session obj=csess id=5f728e7de670 err="session closed"
t=2025-07-10T10:46:48-0700 lvl=info msg="client session established" obj=tunnels.session
t=2025-07-10T10:46:48-0700 lvl=info msg="tunnel session started" obj=tunnels.session
t=2025-07-10T10:47:55-0700 lvl=eror msg="session closed, starting reconnect loop" obj=tunnels.session obj=csess id=5f728e7de670 err="read EOF from remote peer"
t=2025-07-10T10:47:55-0700 lvl=info msg="client session established" obj=tunnels.session
t=2025-07-10T10:47:55-0700 lvl=info msg="tunnel session started" obj=tunnels.session
t=2025-07-10T10:48:17-0700 lvl=eror msg="heartbeat timeout, terminating session" obj=tunnels.session obj=csess id=7e756b6aa5f9 clientid=0b47db377ac53e4a10fcb402d717d21a
t=2025-07-10T10:52:45-0700 lvl=eror msg="heartbeat timeout, terminating session" obj=tunnels.session obj=csess id=471ba4fb3ec8 clientid=0b47db377ac53e4a10fcb402d717d21a
t=2025-07-10T10:52:45-0700 lvl=eror msg="session closed, starting reconnect loop" obj=tunnels.session obj=csess id=5f728e7de670 err="session closed"
t=2025-07-10T10:52:46-0700 lvl=info msg="client session established" obj=tunnels.session
t=2025-07-10T10:52:46-0700 lvl=info msg="tunnel session started" obj=tunnels.session
t=2025-07-10T11:01:30-0700 lvl=eror msg="heartbeat timeout, terminating session" obj=tunnels.session obj=csess id=7a6d10beb7e9 clientid=0b47db377ac53e4a10fcb402d717d21a
t=2025-07-10T11:01:30-0700 lvl=eror msg="session closed, starting reconnect loop" obj=tunnels.session obj=csess id=5f728e7de670 err="session closed"
t=2025-07-10T11:01:31-0700 lvl=info msg="client session established" obj=tunnels.session
t=2025-07-10T11:01:31-0700 lvl=info msg="tunnel session started" obj=tunnels.session
t=2025-07-10T11:24:09-0700 lvl=eror msg="heartbeat timeout, terminating session" obj=tunnels.session obj=csess id=631efc5b4553 clientid=0b47db377ac53e4a10fcb402d717d21a
t=2025-07-10T11:24:09-0700 lvl=eror msg="session closed, starting reconnect loop" obj=tunnels.session obj=csess id=5f728e7de670 err="session closed"
t=2025-07-10T11:24:09-0700 lvl=info msg="client session established" obj=tunnels.session
t=2025-07-10T11:24:09-0700 lvl=info msg="tunnel session started" obj=tunnels.session
t=2025-07-10T12:07:20-0700 lvl=eror msg="heartbeat timeout, terminating session" obj=tunnels.session obj=csess id=6d72f5caf28c clientid=0b47db377ac53e4a10fcb402d717d21a
t=2025-07-10T12:07:20-0700 lvl=eror msg="session closed, starting reconnect loop" obj=tunnels.session obj=csess id=5f728e7de670 err="session closed"
t=2025-07-10T12:07:21-0700 lvl=info msg="client session established" obj=tunnels.session
t=2025-07-10T12:07:21-0700 lvl=info msg="tunnel session started" obj=tunnels.session
t=2025-07-10T12:14:09-0700 lvl=eror msg="session closed, starting reconnect loop" obj=tunnels.session obj=csess id=5f728e7de670 err="read tcp **********:55945->204.236.189.107:443: read: connection reset by peer"
t=2025-07-10T12:14:09-0700 lvl=info msg="client session established" obj=tunnels.session
t=2025-07-10T12:14:09-0700 lvl=info msg="tunnel session started" obj=tunnels.session
t=2025-07-10T12:20:30-0700 lvl=eror msg="session closed, starting reconnect loop" obj=tunnels.session obj=csess id=5f728e7de670 err="read tcp **********:55999->54.176.167.82:443: read: connection reset by peer"
t=2025-07-10T12:20:30-0700 lvl=info msg="client session established" obj=tunnels.session
t=2025-07-10T12:20:30-0700 lvl=info msg="tunnel session started" obj=tunnels.session
t=2025-07-10T12:20:34-0700 lvl=eror msg="heartbeat timeout, terminating session" obj=tunnels.session obj=csess id=aab73866da94 clientid=0b47db377ac53e4a10fcb402d717d21a
t=2025-07-10T12:20:44-0700 lvl=eror msg="heartbeat timeout, terminating session" obj=tunnels.session obj=csess id=6940b1916ca0 clientid=0b47db377ac53e4a10fcb402d717d21a
t=2025-07-10T12:57:38-0700 lvl=eror msg="session closed, starting reconnect loop" obj=tunnels.session obj=csess id=5f728e7de670 err="read EOF from remote peer"
t=2025-07-10T12:57:40-0700 lvl=info msg="client session established" obj=tunnels.session
t=2025-07-10T12:57:40-0700 lvl=info msg="tunnel session started" obj=tunnels.session
t=2025-07-10T12:57:54-0700 lvl=eror msg="heartbeat timeout, terminating session" obj=tunnels.session obj=csess id=e050e391c416 clientid=0b47db377ac53e4a10fcb402d717d21a
t=2025-07-10T13:31:09-0700 lvl=eror msg="session closed, starting reconnect loop" obj=tunnels.session obj=csess id=5f728e7de670 err="read tcp **********:62786->54.176.167.82:443: read: connection reset by peer"
t=2025-07-10T13:31:10-0700 lvl=info msg="client session established" obj=tunnels.session
t=2025-07-10T13:31:10-0700 lvl=info msg="tunnel session started" obj=tunnels.session
t=2025-07-10T13:31:24-0700 lvl=eror msg="heartbeat timeout, terminating session" obj=tunnels.session obj=csess id=52c56c111d75 clientid=0b47db377ac53e4a10fcb402d717d21a
t=2025-07-10T14:26:51-0700 lvl=eror msg="session closed, starting reconnect loop" obj=tunnels.session obj=csess id=5f728e7de670 err="read tcp **********:51760->54.176.167.82:443: read: connection reset by peer"
t=2025-07-10T14:26:51-0700 lvl=info msg="client session established" obj=tunnels.session
t=2025-07-10T14:26:51-0700 lvl=info msg="tunnel session started" obj=tunnels.session
t=2025-07-10T14:27:06-0700 lvl=eror msg="heartbeat timeout, terminating session" obj=tunnels.session obj=csess id=2a5419fb1667 clientid=0b47db377ac53e4a10fcb402d717d21a
t=2025-07-10T15:33:11-0700 lvl=info msg="update available" obj=updater
t=2025-07-10T18:09:02-0700 lvl=eror msg="heartbeat timeout, terminating session" obj=tunnels.session obj=csess id=311a19b6ee3c clientid=0b47db377ac53e4a10fcb402d717d21a
t=2025-07-10T18:09:02-0700 lvl=eror msg="session closed, starting reconnect loop" obj=tunnels.session obj=csess id=5f728e7de670 err="session closed"
t=2025-07-10T18:09:12-0700 lvl=eror msg="failed to reconnect session" obj=tunnels.session err="failed to dial ngrok server with address \"connect.ngrok-agent.com:443\": dial tcp: lookup connect.ngrok-agent.com: i/o timeout"
t=2025-07-10T18:09:23-0700 lvl=eror msg="failed to reconnect session" obj=tunnels.session err="failed to dial ngrok server with address \"connect.ngrok-agent.com:443\": dial tcp: lookup connect.ngrok-agent.com: i/o timeout"
t=2025-07-10T18:09:24-0700 lvl=info msg="client session established" obj=tunnels.session
t=2025-07-10T18:09:24-0700 lvl=info msg="tunnel session started" obj=tunnels.session
t=2025-07-10T18:10:13-0700 lvl=eror msg="session closed, starting reconnect loop" obj=tunnels.session obj=csess id=5f728e7de670 err="read EOF from remote peer"
t=2025-07-10T18:10:13-0700 lvl=info msg="client session established" obj=tunnels.session
t=2025-07-10T18:10:13-0700 lvl=info msg="tunnel session started" obj=tunnels.session
t=2025-07-10T18:10:54-0700 lvl=eror msg="heartbeat timeout, terminating session" obj=tunnels.session obj=csess id=b3718eb12319 clientid=0b47db377ac53e4a10fcb402d717d21a
t=2025-07-10T18:28:23-0700 lvl=eror msg="session closed, starting reconnect loop" obj=tunnels.session obj=csess id=5f728e7de670 err="read tcp **********:51985->**************:443: read: can't assign requested address"
t=2025-07-10T18:28:27-0700 lvl=eror msg="heartbeat timeout, terminating session" obj=tunnels.session obj=csess id=f18bbef66615 clientid=0b47db377ac53e4a10fcb402d717d21a
t=2025-07-10T18:40:22-0700 lvl=eror msg="failed to reconnect session" obj=tunnels.session err="failed to dial ngrok server with address \"connect.ngrok-agent.com:443\": dial tcp: lookup connect.ngrok-agent.com: i/o timeout"
t=2025-07-10T18:40:23-0700 lvl=info msg="client session established" obj=tunnels.session
t=2025-07-10T18:40:23-0700 lvl=info msg="tunnel session started" obj=tunnels.session
t=2025-07-10T18:57:36-0700 lvl=eror msg="session closed, starting reconnect loop" obj=tunnels.session obj=csess id=5f728e7de670 err="read tcp [2601:643:8a80:1cb0:b9f6:f8c5:eb94:a14a]:54451->[2600:1f1c:d8:5f00::6e74:4]:443: read: connection reset by peer"
t=2025-07-10T18:57:36-0700 lvl=info msg="client session established" obj=tunnels.session
t=2025-07-10T18:57:36-0700 lvl=info msg="tunnel session started" obj=tunnels.session
t=2025-07-10T19:28:43-0700 lvl=eror msg="session closed, starting reconnect loop" obj=tunnels.session obj=csess id=5f728e7de670 err="read tcp [2601:643:8a80:1cb0:b9f6:f8c5:eb94:a14a]:54598->[2600:1f1c:d8:5f00::6e74:4]:443: read: connection reset by peer"
t=2025-07-10T19:28:43-0700 lvl=info msg="client session established" obj=tunnels.session
t=2025-07-10T19:28:43-0700 lvl=info msg="tunnel session started" obj=tunnels.session
t=2025-07-10T19:28:48-0700 lvl=eror msg="heartbeat timeout, terminating session" obj=tunnels.session obj=csess id=e4c1f6bfe163 clientid=0b47db377ac53e4a10fcb402d717d21a
t=2025-07-10T19:44:09-0700 lvl=eror msg="heartbeat timeout, terminating session" obj=tunnels.session obj=csess id=cc1898c499dc clientid=0b47db377ac53e4a10fcb402d717d21a
t=2025-07-10T20:01:01-0700 lvl=eror msg="session closed, starting reconnect loop" obj=tunnels.session obj=csess id=5f728e7de670 err="read tcp [2601:643:8a80:1cb0:b9f6:f8c5:eb94:a14a]:54669->[2600:1f1c:d8:5f00::6e74:2]:443: read: connection reset by peer"
t=2025-07-10T20:01:01-0700 lvl=info msg="client session established" obj=tunnels.session
t=2025-07-10T20:01:01-0700 lvl=info msg="tunnel session started" obj=tunnels.session
t=2025-07-10T20:29:45-0700 lvl=eror msg="session closed, starting reconnect loop" obj=tunnels.session obj=csess id=5f728e7de670 err="read tcp [2601:643:8a80:1cb0:b9f6:f8c5:eb94:a14a]:54802->[2600:1f1c:d8:5f01::6e74:3]:443: read: connection reset by peer"
t=2025-07-10T20:29:45-0700 lvl=info msg="client session established" obj=tunnels.session
t=2025-07-10T20:29:45-0700 lvl=info msg="tunnel session started" obj=tunnels.session
t=2025-07-10T20:46:35-0700 lvl=eror msg="heartbeat timeout, terminating session" obj=tunnels.session obj=csess id=b350e95461bf clientid=0b47db377ac53e4a10fcb402d717d21a
t=2025-07-10T21:02:50-0700 lvl=eror msg="session closed, starting reconnect loop" obj=tunnels.session obj=csess id=5f728e7de670 err="read tcp [2601:643:8a80:1cb0:b9f6:f8c5:eb94:a14a]:54865->[2600:1f1c:d8:5f01::6e74:1]:443: read: connection reset by peer"
t=2025-07-10T21:02:51-0700 lvl=info msg="client session established" obj=tunnels.session
t=2025-07-10T21:02:51-0700 lvl=info msg="tunnel session started" obj=tunnels.session
t=2025-07-10T21:02:55-0700 lvl=eror msg="heartbeat timeout, terminating session" obj=tunnels.session obj=csess id=be5bcbced985 clientid=0b47db377ac53e4a10fcb402d717d21a
t=2025-07-10T21:20:07-0700 lvl=eror msg="session closed, starting reconnect loop" obj=tunnels.session obj=csess id=5f728e7de670 err="read tcp [2601:643:8a80:1cb0:b9f6:f8c5:eb94:a14a]:54918->[2600:1f1c:d8:5f01::6e74:1]:443: read: connection reset by peer"
t=2025-07-10T21:20:07-0700 lvl=info msg="client session established" obj=tunnels.session
t=2025-07-10T21:20:07-0700 lvl=info msg="tunnel session started" obj=tunnels.session
t=2025-07-10T21:20:10-0700 lvl=eror msg="heartbeat timeout, terminating session" obj=tunnels.session obj=csess id=7406bc789993 clientid=0b47db377ac53e4a10fcb402d717d21a
t=2025-07-10T21:20:21-0700 lvl=eror msg="heartbeat timeout, terminating session" obj=tunnels.session obj=csess id=fe1b4cf56915 clientid=0b47db377ac53e4a10fcb402d717d21a
t=2025-07-10T21:46:49-0700 lvl=eror msg="session closed, starting reconnect loop" obj=tunnels.session obj=csess id=5f728e7de670 err="read tcp [2601:643:8a80:1cb0:b9f6:f8c5:eb94:a14a]:55023->[2600:1f1c:d8:5f00::6e74:4]:443: read: connection reset by peer"
t=2025-07-10T21:46:49-0700 lvl=info msg="client session established" obj=tunnels.session
t=2025-07-10T21:46:49-0700 lvl=info msg="tunnel session started" obj=tunnels.session
t=2025-07-10T22:07:13-0700 lvl=eror msg="heartbeat timeout, terminating session" obj=tunnels.session obj=csess id=18a83fbb6d65 clientid=0b47db377ac53e4a10fcb402d717d21a
t=2025-07-10T22:25:10-0700 lvl=eror msg="heartbeat timeout, terminating session" obj=tunnels.session obj=csess id=51fc74a2d565 clientid=0b47db377ac53e4a10fcb402d717d21a
t=2025-07-10T22:25:10-0700 lvl=eror msg="session closed, starting reconnect loop" obj=tunnels.session obj=csess id=5f728e7de670 err="session closed"
t=2025-07-10T22:25:10-0700 lvl=info msg="client session established" obj=tunnels.session
t=2025-07-10T22:25:10-0700 lvl=info msg="tunnel session started" obj=tunnels.session
t=2025-07-10T22:40:20-0700 lvl=eror msg="session closed, starting reconnect loop" obj=tunnels.session obj=csess id=5f728e7de670 err="read tcp [2601:643:8a80:1cb0:b9f6:f8c5:eb94:a14a]:55635->[2600:1f1c:d8:5f01::6e74:1]:443: read: connection reset by peer"
t=2025-07-10T22:40:20-0700 lvl=info msg="client session established" obj=tunnels.session
t=2025-07-10T22:40:20-0700 lvl=info msg="tunnel session started" obj=tunnels.session
t=2025-07-10T22:55:36-0700 lvl=eror msg="heartbeat timeout, terminating session" obj=tunnels.session obj=csess id=be5eceb9ee99 clientid=0b47db377ac53e4a10fcb402d717d21a
t=2025-07-10T22:58:43-0700 lvl=eror msg="session closed, starting reconnect loop" obj=tunnels.session obj=csess id=5f728e7de670 err="read tcp [2601:643:8a80:1cb0:b9f6:f8c5:eb94:a14a]:55689->[2600:1f1c:d8:5f01::6e74:1]:443: read: connection reset by peer"
t=2025-07-10T22:58:43-0700 lvl=info msg="client session established" obj=tunnels.session
t=2025-07-10T22:58:43-0700 lvl=info msg="tunnel session started" obj=tunnels.session
t=2025-07-10T23:14:14-0700 lvl=eror msg="heartbeat timeout, terminating session" obj=tunnels.session obj=csess id=ac30a68b98dd clientid=0b47db377ac53e4a10fcb402d717d21a
t=2025-07-10T23:14:19-0700 lvl=eror msg="session closed, starting reconnect loop" obj=tunnels.session obj=csess id=5f728e7de670 err="read tcp [2601:643:8a80:1cb0:b9f6:f8c5:eb94:a14a]:55773->[2600:1f1c:d8:5f01::6e74:1]:443: read: connection reset by peer"
t=2025-07-10T23:14:19-0700 lvl=info msg="client session established" obj=tunnels.session
t=2025-07-10T23:14:19-0700 lvl=info msg="tunnel session started" obj=tunnels.session
t=2025-07-10T23:14:34-0700 lvl=eror msg="heartbeat timeout, terminating session" obj=tunnels.session obj=csess id=c40c6c67efc1 clientid=0b47db377ac53e4a10fcb402d717d21a
t=2025-07-10T23:30:52-0700 lvl=eror msg="session closed, starting reconnect loop" obj=tunnels.session obj=csess id=5f728e7de670 err="read tcp [2601:643:8a80:1cb0:b9f6:f8c5:eb94:a14a]:55850->[2600:1f1c:d8:5f00::6e74:0]:443: read: connection reset by peer"
t=2025-07-10T23:30:52-0700 lvl=info msg="client session established" obj=tunnels.session
t=2025-07-10T23:30:52-0700 lvl=info msg="tunnel session started" obj=tunnels.session
t=2025-07-11T00:01:36-0700 lvl=eror msg="session closed, starting reconnect loop" obj=tunnels.session obj=csess id=5f728e7de670 err="read tcp [2601:643:8a80:1cb0:b9f6:f8c5:eb94:a14a]:55918->[2600:1f1c:d8:5f01::6e74:3]:443: read: connection reset by peer"
t=2025-07-11T00:01:36-0700 lvl=info msg="client session established" obj=tunnels.session
t=2025-07-11T00:01:36-0700 lvl=info msg="tunnel session started" obj=tunnels.session
t=2025-07-11T00:01:41-0700 lvl=eror msg="heartbeat timeout, terminating session" obj=tunnels.session obj=csess id=8f178094391d clientid=0b47db377ac53e4a10fcb402d717d21a
t=2025-07-11T00:35:28-0700 lvl=eror msg="session closed, starting reconnect loop" obj=tunnels.session obj=csess id=5f728e7de670 err="read tcp [2601:643:8a80:1cb0:b9f6:f8c5:eb94:a14a]:55974->[2600:1f1c:d8:5f01::6e74:3]:443: read: connection reset by peer"
t=2025-07-11T00:35:29-0700 lvl=info msg="client session established" obj=tunnels.session
t=2025-07-11T00:35:29-0700 lvl=info msg="tunnel session started" obj=tunnels.session
t=2025-07-11T00:35:32-0700 lvl=eror msg="heartbeat timeout, terminating session" obj=tunnels.session obj=csess id=a6a5b9b98680 clientid=0b47db377ac53e4a10fcb402d717d21a
t=2025-07-11T01:05:50-0700 lvl=eror msg="session closed, starting reconnect loop" obj=tunnels.session obj=csess id=5f728e7de670 err="read tcp 10.0.0.157:56063->204.236.189.107:443: read: connection reset by peer"
t=2025-07-11T01:05:51-0700 lvl=info msg="client session established" obj=tunnels.session
t=2025-07-11T01:05:51-0700 lvl=info msg="tunnel session started" obj=tunnels.session
t=2025-07-11T01:05:54-0700 lvl=eror msg="heartbeat timeout, terminating session" obj=tunnels.session obj=csess id=0de5bc21d486 clientid=0b47db377ac53e4a10fcb402d717d21a
t=2025-07-11T01:21:21-0700 lvl=eror msg="session closed, starting reconnect loop" obj=tunnels.session obj=csess id=5f728e7de670 err="read tcp 10.0.0.157:56194->52.53.56.252:443: read: connection reset by peer"
t=2025-07-11T01:21:21-0700 lvl=info msg="client session established" obj=tunnels.session
t=2025-07-11T01:21:21-0700 lvl=info msg="tunnel session started" obj=tunnels.session
t=2025-07-11T01:37:52-0700 lvl=eror msg="heartbeat timeout, terminating session" obj=tunnels.session obj=csess id=cae7f715dcb1 clientid=0b47db377ac53e4a10fcb402d717d21a
t=2025-07-11T01:55:46-0700 lvl=eror msg="session closed, starting reconnect loop" obj=tunnels.session obj=csess id=5f728e7de670 err="read tcp [2601:643:8a80:1cb0:b9f6:f8c5:eb94:a14a]:56260->[2600:1f1c:d8:5f01::6e74:1]:443: read: connection reset by peer"
t=2025-07-11T01:55:46-0700 lvl=info msg="client session established" obj=tunnels.session
t=2025-07-11T01:55:46-0700 lvl=info msg="tunnel session started" obj=tunnels.session
t=2025-07-11T02:12:54-0700 lvl=eror msg="heartbeat timeout, terminating session" obj=tunnels.session obj=csess id=2f55412f1c6b clientid=0b47db377ac53e4a10fcb402d717d21a
t=2025-07-11T02:30:33-0700 lvl=eror msg="session closed, starting reconnect loop" obj=tunnels.session obj=csess id=5f728e7de670 err="read tcp [2601:643:8a80:1cb0:b9f6:f8c5:eb94:a14a]:56357->[2600:1f1c:d8:5f00::6e74:2]:443: read: connection reset by peer"
t=2025-07-11T02:30:34-0700 lvl=info msg="client session established" obj=tunnels.session
t=2025-07-11T02:30:34-0700 lvl=info msg="tunnel session started" obj=tunnels.session
t=2025-07-11T02:46:19-0700 lvl=eror msg="heartbeat timeout, terminating session" obj=tunnels.session obj=csess id=fc281a8aea67 clientid=0b47db377ac53e4a10fcb402d717d21a
t=2025-07-11T03:03:10-0700 lvl=eror msg="session closed, starting reconnect loop" obj=tunnels.session obj=csess id=5f728e7de670 err="read tcp [2601:643:8a80:1cb0:b9f6:f8c5:eb94:a14a]:56479->[2600:1f1c:d8:5f01::6e74:1]:443: read: connection reset by peer"
t=2025-07-11T03:03:10-0700 lvl=info msg="client session established" obj=tunnels.session
t=2025-07-11T03:03:10-0700 lvl=info msg="tunnel session started" obj=tunnels.session
t=2025-07-11T03:20:53-0700 lvl=eror msg="heartbeat timeout, terminating session" obj=tunnels.session obj=csess id=1bc8c0efcb63 clientid=0b47db377ac53e4a10fcb402d717d21a
t=2025-07-11T03:38:06-0700 lvl=eror msg="session closed, starting reconnect loop" obj=tunnels.session obj=csess id=5f728e7de670 err="read tcp [2601:643:8a80:1cb0:b9f6:f8c5:eb94:a14a]:56575->[2600:1f1c:d8:5f01::6e74:1]:443: read: connection reset by peer"
t=2025-07-11T03:38:07-0700 lvl=info msg="client session established" obj=tunnels.session
t=2025-07-11T03:38:07-0700 lvl=info msg="tunnel session started" obj=tunnels.session
t=2025-07-11T03:38:11-0700 lvl=eror msg="heartbeat timeout, terminating session" obj=tunnels.session obj=csess id=517643e866db clientid=0b47db377ac53e4a10fcb402d717d21a
t=2025-07-11T04:11:34-0700 lvl=eror msg="session closed, starting reconnect loop" obj=tunnels.session obj=csess id=5f728e7de670 err="read tcp [2601:643:8a80:1cb0:b9f6:f8c5:eb94:a14a]:56658->[2600:1f1c:d8:5f01::6e74:3]:443: read: connection reset by peer"
t=2025-07-11T04:11:34-0700 lvl=info msg="client session established" obj=tunnels.session
t=2025-07-11T04:11:34-0700 lvl=info msg="tunnel session started" obj=tunnels.session
t=2025-07-11T04:11:38-0700 lvl=eror msg="heartbeat timeout, terminating session" obj=tunnels.session obj=csess id=067dc5769c4b clientid=0b47db377ac53e4a10fcb402d717d21a
t=2025-07-11T04:28:42-0700 lvl=eror msg="session closed, starting reconnect loop" obj=tunnels.session obj=csess id=5f728e7de670 err="read tcp [2601:643:8a80:1cb0:b9f6:f8c5:eb94:a14a]:56751->[2600:1f1c:d8:5f00::6e74:2]:443: read: connection reset by peer"
t=2025-07-11T04:28:42-0700 lvl=info msg="client session established" obj=tunnels.session
t=2025-07-11T04:28:42-0700 lvl=info msg="tunnel session started" obj=tunnels.session
t=2025-07-11T04:46:14-0700 lvl=eror msg="heartbeat timeout, terminating session" obj=tunnels.session obj=csess id=c9513af21d12 clientid=0b47db377ac53e4a10fcb402d717d21a
t=2025-07-11T05:02:28-0700 lvl=eror msg="session closed, starting reconnect loop" obj=tunnels.session obj=csess id=5f728e7de670 err="read tcp [2601:643:8a80:1cb0:b9f6:f8c5:eb94:a14a]:56844->[2600:1f1c:d8:5f00::6e74:0]:443: read: connection reset by peer"
t=2025-07-11T05:02:28-0700 lvl=info msg="client session established" obj=tunnels.session
t=2025-07-11T05:02:28-0700 lvl=info msg="tunnel session started" obj=tunnels.session
t=2025-07-11T05:18:16-0700 lvl=eror msg="heartbeat timeout, terminating session" obj=tunnels.session obj=csess id=8d156f3d531b clientid=0b47db377ac53e4a10fcb402d717d21a
t=2025-07-11T05:35:55-0700 lvl=eror msg="session closed, starting reconnect loop" obj=tunnels.session obj=csess id=5f728e7de670 err="read tcp [2601:643:8a80:1cb0:b9f6:f8c5:eb94:a14a]:56991->[2600:1f1c:d8:5f00::6e74:4]:443: read: connection reset by peer"
t=2025-07-11T05:35:55-0700 lvl=info msg="client session established" obj=tunnels.session
t=2025-07-11T05:35:55-0700 lvl=info msg="tunnel session started" obj=tunnels.session
t=2025-07-11T05:51:57-0700 lvl=eror msg="heartbeat timeout, terminating session" obj=tunnels.session obj=csess id=d946f3326b5a clientid=0b47db377ac53e4a10fcb402d717d21a
t=2025-07-11T06:07:19-0700 lvl=eror msg="session closed, starting reconnect loop" obj=tunnels.session obj=csess id=5f728e7de670 err="read tcp [2601:643:8a80:1cb0:b9f6:f8c5:eb94:a14a]:57094->[2600:1f1c:d8:5f00::6e74:4]:443: read: connection reset by peer"
t=2025-07-11T06:07:19-0700 lvl=info msg="client session established" obj=tunnels.session
t=2025-07-11T06:07:19-0700 lvl=info msg="tunnel session started" obj=tunnels.session
t=2025-07-11T06:23:17-0700 lvl=eror msg="heartbeat timeout, terminating session" obj=tunnels.session obj=csess id=cf1d56e0177e clientid=0b47db377ac53e4a10fcb402d717d21a
t=2025-07-11T06:39:17-0700 lvl=eror msg="session closed, starting reconnect loop" obj=tunnels.session obj=csess id=5f728e7de670 err="read tcp [2601:643:8a80:1cb0:b9f6:f8c5:eb94:a14a]:57226->[2600:1f1c:d8:5f00::6e74:2]:443: read: connection reset by peer"
t=2025-07-11T06:39:18-0700 lvl=info msg="client session established" obj=tunnels.session
t=2025-07-11T06:39:18-0700 lvl=info msg="tunnel session started" obj=tunnels.session
t=2025-07-11T06:54:22-0700 lvl=eror msg="heartbeat timeout, terminating session" obj=tunnels.session obj=csess id=8d6aa3a65986 clientid=0b47db377ac53e4a10fcb402d717d21a
t=2025-07-11T06:54:27-0700 lvl=eror msg="session closed, starting reconnect loop" obj=tunnels.session obj=csess id=5f728e7de670 err="read tcp [2601:643:8a80:1cb0:b9f6:f8c5:eb94:a14a]:57320->[2600:1f1c:d8:5f01::6e74:3]:443: read: connection reset by peer"
t=2025-07-11T06:54:28-0700 lvl=info msg="client session established" obj=tunnels.session
t=2025-07-11T06:54:28-0700 lvl=info msg="tunnel session started" obj=tunnels.session
t=2025-07-11T06:54:32-0700 lvl=eror msg="heartbeat timeout, terminating session" obj=tunnels.session obj=csess id=1c3965958d9b clientid=0b47db377ac53e4a10fcb402d717d21a
t=2025-07-11T06:54:42-0700 lvl=eror msg="heartbeat timeout, terminating session" obj=tunnels.session obj=csess id=16fecbab5134 clientid=0b47db377ac53e4a10fcb402d717d21a
t=2025-07-11T07:10:17-0700 lvl=eror msg="session closed, starting reconnect loop" obj=tunnels.session obj=csess id=5f728e7de670 err="read tcp [2601:643:8a80:1cb0:b9f6:f8c5:eb94:a14a]:57401->[2600:1f1c:d8:5f00::6e74:4]:443: read: connection reset by peer"
t=2025-07-11T07:10:17-0700 lvl=info msg="client session established" obj=tunnels.session
t=2025-07-11T07:10:17-0700 lvl=info msg="tunnel session started" obj=tunnels.session
t=2025-07-11T07:18:39-0700 lvl=eror msg="session closed, starting reconnect loop" obj=tunnels.session obj=csess id=5f728e7de670 err="read tcp [2601:643:8a80:1cb0:b9f6:f8c5:eb94:a14a]:57448->[2600:1f1c:d8:5f00::6e74:4]:443: read: connection reset by peer"
t=2025-07-11T07:18:48-0700 lvl=info msg="client session established" obj=tunnels.session
t=2025-07-11T07:18:48-0700 lvl=info msg="tunnel session started" obj=tunnels.session
t=2025-07-11T07:18:52-0700 lvl=eror msg="heartbeat timeout, terminating session" obj=tunnels.session obj=csess id=c79a6c33183d clientid=0b47db377ac53e4a10fcb402d717d21a
t=2025-07-11T07:21:21-0700 lvl=eror msg="heartbeat timeout, terminating session" obj=tunnels.session obj=csess id=8cfbfbf3ffe7 clientid=0b47db377ac53e4a10fcb402d717d21a
t=2025-07-11T07:41:07-0700 lvl=eror msg="heartbeat timeout, terminating session" obj=tunnels.session obj=csess id=b3faaad3943a clientid=0b47db377ac53e4a10fcb402d717d21a
t=2025-07-11T07:41:07-0700 lvl=eror msg="session closed, starting reconnect loop" obj=tunnels.session obj=csess id=5f728e7de670 err="session closed"
t=2025-07-11T07:41:07-0700 lvl=info msg="client session established" obj=tunnels.session
t=2025-07-11T07:41:07-0700 lvl=info msg="tunnel session started" obj=tunnels.session
t=2025-07-11T08:13:07-0700 lvl=eror msg="session closed, starting reconnect loop" obj=tunnels.session obj=csess id=5f728e7de670 err="read tcp [2601:643:8a80:1cb0:b9f6:f8c5:eb94:a14a]:57650->[2600:1f1c:d8:5f00::6e74:2]:443: read: connection reset by peer"
t=2025-07-11T08:13:07-0700 lvl=info msg="client session established" obj=tunnels.session
t=2025-07-11T08:13:07-0700 lvl=info msg="tunnel session started" obj=tunnels.session
t=2025-07-11T08:13:22-0700 lvl=eror msg="heartbeat timeout, terminating session" obj=tunnels.session obj=csess id=331dfeb78ab7 clientid=0b47db377ac53e4a10fcb402d717d21a
t=2025-07-11T08:31:28-0700 lvl=eror msg="session closed, starting reconnect loop" obj=tunnels.session obj=csess id=5f728e7de670 err="read tcp [2601:643:8a80:1cb0:b9f6:f8c5:eb94:a14a]:57787->[2600:1f1c:d8:5f00::6e74:4]:443: read: connection reset by peer"
t=2025-07-11T08:31:28-0700 lvl=info msg="client session established" obj=tunnels.session
t=2025-07-11T08:31:28-0700 lvl=info msg="tunnel session started" obj=tunnels.session
t=2025-07-11T09:00:04-0700 lvl=eror msg="session closed, starting reconnect loop" obj=tunnels.session obj=csess id=5f728e7de670 err="read tcp [2601:643:8a80:1cb0:b9f6:f8c5:eb94:a14a]:57898->[2600:1f1c:d8:5f00::6e74:4]:443: read: connection reset by peer"
t=2025-07-11T09:00:04-0700 lvl=info msg="client session established" obj=tunnels.session
t=2025-07-11T09:00:04-0700 lvl=info msg="tunnel session started" obj=tunnels.session
t=2025-07-11T09:00:08-0700 lvl=eror msg="heartbeat timeout, terminating session" obj=tunnels.session obj=csess id=46a88c43cbec clientid=0b47db377ac53e4a10fcb402d717d21a
t=2025-07-11T09:17:03-0700 lvl=eror msg="session closed, starting reconnect loop" obj=tunnels.session obj=csess id=5f728e7de670 err="read tcp [2601:643:8a80:1cb0:b9f6:f8c5:eb94:a14a]:58007->[2600:1f1c:d8:5f00::6e74:0]:443: read: connection reset by peer"
t=2025-07-11T09:17:03-0700 lvl=info msg="client session established" obj=tunnels.session
t=2025-07-11T09:17:03-0700 lvl=info msg="tunnel session started" obj=tunnels.session
t=2025-07-11T09:18:53-0700 lvl=eror msg="heartbeat timeout, terminating session" obj=tunnels.session obj=csess id=6426ac37d853 clientid=0b47db377ac53e4a10fcb402d717d21a
t=2025-07-11T09:21:47-0700 lvl=eror msg="heartbeat timeout, terminating session" obj=tunnels.session obj=csess id=d0cbb1caabc3 clientid=0b47db377ac53e4a10fcb402d717d21a
t=2025-07-11T09:23:47-0700 lvl=eror msg="heartbeat timeout, terminating session" obj=tunnels.session obj=csess id=64d063e7f882 clientid=0b47db377ac53e4a10fcb402d717d21a
t=2025-07-11T09:23:47-0700 lvl=eror msg="session closed, starting reconnect loop" obj=tunnels.session obj=csess id=5f728e7de670 err="session closed"
t=2025-07-11T09:24:38-0700 lvl=eror msg="failed to reconnect session" obj=tunnels.session err="failed to dial ngrok server with address \"connect.ngrok-agent.com:443\": dial tcp: lookup connect.ngrok-agent.com: i/o timeout"
t=2025-07-11T09:24:40-0700 lvl=eror msg="failed to reconnect session" obj=tunnels.session err="failed to dial ngrok server with address \"connect.ngrok-agent.com:443\": dial tcp: lookup connect.ngrok-agent.com: no such host"
t=2025-07-11T09:24:41-0700 lvl=eror msg="failed to reconnect session" obj=tunnels.session err="failed to dial ngrok server with address \"connect.ngrok-agent.com:443\": dial tcp: lookup connect.ngrok-agent.com: no such host"
t=2025-07-11T09:24:43-0700 lvl=eror msg="failed to reconnect session" obj=tunnels.session err="failed to dial ngrok server with address \"connect.ngrok-agent.com:443\": dial tcp: lookup connect.ngrok-agent.com: no such host"
t=2025-07-11T09:24:47-0700 lvl=eror msg="failed to reconnect session" obj=tunnels.session err="failed to dial ngrok server with address \"connect.ngrok-agent.com:443\": dial tcp: lookup connect.ngrok-agent.com: no such host"
t=2025-07-11T09:24:55-0700 lvl=eror msg="failed to reconnect session" obj=tunnels.session err="failed to dial ngrok server with address \"connect.ngrok-agent.com:443\": dial tcp: lookup connect.ngrok-agent.com: no such host"
t=2025-07-11T09:42:35-0700 lvl=eror msg="failed to reconnect session" obj=tunnels.session err="failed to dial ngrok server with address \"connect.ngrok-agent.com:443\": dial tcp: lookup connect.ngrok-agent.com: no such host"
t=2025-07-11T09:54:41-0700 lvl=info msg="client session established" obj=tunnels.session
t=2025-07-11T09:54:41-0700 lvl=info msg="tunnel session started" obj=tunnels.session
t=2025-07-11T09:58:28-0700 lvl=eror msg="heartbeat timeout, terminating session" obj=tunnels.session obj=csess id=7bad00570e0c clientid=0b47db377ac53e4a10fcb402d717d21a
t=2025-07-11T09:58:28-0700 lvl=eror msg="session closed, starting reconnect loop" obj=tunnels.session obj=csess id=5f728e7de670 err="session closed"
t=2025-07-11T09:58:28-0700 lvl=info msg="client session established" obj=tunnels.session
t=2025-07-11T09:58:28-0700 lvl=info msg="tunnel session started" obj=tunnels.session
t=2025-07-11T10:27:19-0700 lvl=eror msg="heartbeat timeout, terminating session" obj=tunnels.session obj=csess id=ab9813eb8454 clientid=0b47db377ac53e4a10fcb402d717d21a
t=2025-07-11T10:27:19-0700 lvl=eror msg="session closed, starting reconnect loop" obj=tunnels.session obj=csess id=5f728e7de670 err="session closed"
t=2025-07-11T10:27:20-0700 lvl=info msg="client session established" obj=tunnels.session
t=2025-07-11T10:27:20-0700 lvl=info msg="tunnel session started" obj=tunnels.session
