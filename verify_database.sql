-- Database verification queries for Pre-Registration User Mapping tests
-- Run these queries to verify the test results

-- 1. Check all pre-registration mappings created
SELECT 
    email,
    org_id,
    role_name,
    asset_type,
    created_by,
    created_at,
    used_at,
    CASE 
        WHEN used_at IS NULL THEN 'UNUSED'
        ELSE 'USED'
    END as status
FROM pre_registration_user_mappings 
WHERE org_id = 2
ORDER BY created_at DESC;

-- 2. Check specifically for our test users
SELECT 
    email,
    role_name,
    asset_type,
    used_at
FROM pre_registration_user_mappings 
WHERE email IN (
    '<EMAIL>',
    '<EMAIL>', 
    '<EMAIL>'
)
ORDER BY email;

-- 3. Check assets created with cognito_jwt_sub matching our test pattern
SELECT 
    id,
    name,
    cognito_jwt_sub,
    asset_type,
    org_id,
    created_at
FROM assets 
WHERE cognito_jwt_sub LIKE 'oktasandbox_%'
ORDER BY created_at DESC;

-- 4. Check users and their roles (if users table exists)
-- Note: This might need adjustment based on your actual schema
SELECT 
    u.email,
    u.cognito_sub,
    r.name as role_name,
    ur.created_at as role_assigned_at
FROM users u
LEFT JOIN user_roles ur ON u.id = ur.user_id
LEFT JOIN roles r ON ur.role_id = r.id
WHERE u.email LIKE '%@gethero.com'
ORDER BY u.email;

-- 5. Check for any unused mappings (should be empty after Lambda runs)
SELECT 
    email,
    role_name,
    asset_type,
    created_at
FROM pre_registration_user_mappings 
WHERE used_at IS NULL 
AND email LIKE 'oktasandbox_%'
ORDER BY created_at;

-- 6. Verify asset types match expected values
-- Asset type mapping reference:
-- 1 = ASSET_TYPE_UNSPECIFIED
-- 2 = ASSET_TYPE_DISPATCHER  
-- 3 = ASSET_TYPE_RESPONDER
-- 4 = ASSET_TYPE_VEHICLE
-- 5 = ASSET_TYPE_CAMERA
-- 6 = ASSET_TYPE_SUPERVISOR
-- 7 = ASSET_TYPE_ADMIN

SELECT 
    a.name,
    a.cognito_jwt_sub,
    a.asset_type,
    CASE a.asset_type
        WHEN 1 THEN 'UNSPECIFIED'
        WHEN 2 THEN 'DISPATCHER'
        WHEN 3 THEN 'RESPONDER'
        WHEN 4 THEN 'VEHICLE'
        WHEN 5 THEN 'CAMERA'
        WHEN 6 THEN 'SUPERVISOR'
        WHEN 7 THEN 'ADMIN'
        ELSE 'UNKNOWN'
    END as asset_type_name,
    prm.role_name as expected_role
FROM assets a
LEFT JOIN pre_registration_user_mappings prm ON 
    a.cognito_jwt_sub = prm.email AND 
    a.org_id = prm.org_id
WHERE a.cognito_jwt_sub LIKE 'oktasandbox_%'
ORDER BY a.cognito_jwt_sub;

-- 7. Check org assignments (if there's an org_users table)
SELECT 
    ou.user_id,
    ou.org_id,
    u.email,
    ou.created_at
FROM org_users ou
JOIN users u ON ou.user_id = u.id
WHERE u.email LIKE 'oktasandbox_%'
ORDER BY u.email;

-- 8. Summary report
SELECT 
    'Total pre-registration mappings created' as metric,
    COUNT(*) as count
FROM pre_registration_user_mappings 
WHERE org_id = 2

UNION ALL

SELECT 
    'Mappings used by Lambda' as metric,
    COUNT(*) as count
FROM pre_registration_user_mappings 
WHERE org_id = 2 AND used_at IS NOT NULL

UNION ALL

SELECT 
    'Assets created with oktasandbox prefix' as metric,
    COUNT(*) as count
FROM assets 
WHERE cognito_jwt_sub LIKE 'oktasandbox_%'

UNION ALL

SELECT 
    'Test user mappings (andrei, travis, stoney)' as metric,
    COUNT(*) as count
FROM pre_registration_user_mappings 
WHERE email IN (
    '<EMAIL>',
    '<EMAIL>', 
    '<EMAIL>'
);

-- Expected results after successful test:
-- 1. 5 pre-registration mappings should be created (3 individual + 2 bulk)
-- 2. Mappings for test users should have correct role_name and asset_type:
--    - andrei: Admin role, asset_type 2 (DISPATCHER)
--    - travis: Supervisor role, asset_type 6 (SUPERVISOR)  
--    - stoney: Dispatcher role, asset_type 2 (DISPATCHER)
-- 3. After Lambda execution, used_at should be populated for processed mappings
-- 4. Assets should be created with cognito_jwt_sub = '<EMAIL>'
-- 5. Asset types should match the pre-registration mapping asset_type values
