#!/bin/bash

# Test script for Pre-Registration User Mapping API
# Usage: ./test_pre_registration_api.sh

set -e

# Configuration
BASE_URL="http://localhost:8080"
BEARER_TOKEN="eyJraWQiOiJCS20yRjNPcjZVMDI2cDVUc24xSjlpUkJ2cDNJMldydzlQMWQ2dVh1UDJVPSIsImFsZyI6IlJTMjU2In0.***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.LQkcLFmr14QBs6C9nKqxmgGk7b0Omo9446-eJkJiKzkHuk1ALOrZZ62qhBYqKB9-QLKZT0k03dLfa7ZOwAIHO6Q59_O3-7laYCelSWAN7P5X8FDy126GvQm-AqGqg3s98B84Vsfvf3MzctNDXj2QRe3d86Iv3F6S-dPh6BAbI52M2sJv9lw1wVysOvBVLyg5Opx8IktWd18l4jWe4H_1_KgEBF9uKBm8Cun4WiQNrqsBAoHLAHDPQe3P-WsDCS6QQE_bYcjBEBo8AA76x1Vh5qEs_fKqBOhVau0xjobS3sRMQwU7G7dKtgtvF0ZbNqQLyBS_4jMwA04Nr4Tw9sZg5w"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Helper function to make API calls
make_api_call() {
    local method=$1
    local endpoint=$2
    local data=$3
    local description=$4
    
    echo -e "${BLUE}Testing: $description${NC}"
    echo "Endpoint: $method $endpoint"
    
    if [ -n "$data" ]; then
        echo "Request body: $data"
        response=$(curl -s -w "\nHTTP_STATUS:%{http_code}" \
            -X POST \
            -H "Content-Type: application/json" \
            -H "Authorization: Bearer $BEARER_TOKEN" \
            -d "$data" \
            "$BASE_URL$endpoint")
    else
        response=$(curl -s -w "\nHTTP_STATUS:%{http_code}" \
            -X POST \
            -H "Content-Type: application/json" \
            -H "Authorization: Bearer $BEARER_TOKEN" \
            "$BASE_URL$endpoint")
    fi
    
    # Extract HTTP status and body
    http_status=$(echo "$response" | grep "HTTP_STATUS:" | cut -d: -f2)
    body=$(echo "$response" | sed '/HTTP_STATUS:/d')
    
    if [ "$http_status" -eq 200 ]; then
        echo -e "${GREEN}✅ SUCCESS (HTTP $http_status)${NC}"
        echo "Response: $body"
    else
        echo -e "${RED}❌ FAILED (HTTP $http_status)${NC}"
        echo "Response: $body"
    fi
    echo "----------------------------------------"
    echo
}

echo -e "${YELLOW}🚀 Starting Pre-Registration User Mapping API Tests${NC}"
echo

# Test 1: Create individual pre-registration mappings
echo -e "${YELLOW}📝 Test 1: Creating Individual Pre-Registration Mappings${NC}"

# Create <NAME_EMAIL> (dispatcher)
make_api_call "POST" "/hero.orgs.v1.OrgsService/CreatePreRegistrationMapping" '{
    "email": "<EMAIL>",
    "org_id": 2,
    "role_name": "Admin",
    "asset_type": 2,
    "created_by": "test-admin"
}' "Create <NAME_EMAIL> as Admin/Dispatcher"

# Create <NAME_EMAIL> (supervisor)
make_api_call "POST" "/hero.orgs.v1.OrgsService/CreatePreRegistrationMapping" '{
    "email": "<EMAIL>",
    "org_id": 2,
    "role_name": "Supervisor",
    "asset_type": 6,
    "created_by": "test-admin"
}' "Create <NAME_EMAIL> as Supervisor"

# Create <NAME_EMAIL> (dispatcher)
make_api_call "POST" "/hero.orgs.v1.OrgsService/CreatePreRegistrationMapping" '{
    "email": "<EMAIL>",
    "org_id": 2,
    "role_name": "Dispatcher",
    "asset_type": 2,
    "created_by": "test-admin"
}' "Create <NAME_EMAIL> as Dispatcher"

# Test 2: Test bulk creation
echo -e "${YELLOW}📦 Test 2: Testing Bulk Creation${NC}"

make_api_call "POST" "/hero.orgs.v1.OrgsService/CreatePreRegistrationMappings" '{
    "mappings": [
        {
            "email": "<EMAIL>",
            "org_id": 2,
            "role_name": "Responder",
            "asset_type": 3,
            "created_by": "bulk-test"
        },
        {
            "email": "<EMAIL>",
            "org_id": 2,
            "role_name": "Admin",
            "asset_type": 2,
            "created_by": "bulk-test"
        }
    ]
}' "Bulk create multiple mappings"

# Test 3: Get individual mappings
echo -e "${YELLOW}🔍 Test 3: Getting Individual Mappings${NC}"

make_api_call "POST" "/hero.orgs.v1.OrgsService/GetPreRegistrationMapping" '{
    "email": "<EMAIL>",
    "org_id": 2
}' "Get <NAME_EMAIL>"

make_api_call "POST" "/hero.orgs.v1.OrgsService/GetPreRegistrationMapping" '{
    "email": "<EMAIL>",
    "org_id": 2
}' "Get <NAME_EMAIL>"

# Test 4: List all mappings
echo -e "${YELLOW}📋 Test 4: Listing All Mappings${NC}"

make_api_call "POST" "/hero.orgs.v1.OrgsService/ListPreRegistrationMappings" '{
    "org_id": 2,
    "page_size": 10,
    "include_used": false
}' "List unused mappings for org 2"

make_api_call "POST" "/hero.orgs.v1.OrgsService/ListPreRegistrationMappings" '{
    "org_id": 2,
    "page_size": 10,
    "include_used": true
}' "List all mappings (including used) for org 2"

echo -e "${GREEN}🎉 All API tests completed!${NC}"
echo
echo -e "${YELLOW}📝 Next Steps:${NC}"
echo "1. Check the database to verify mappings were created:"
echo "   SELECT * FROM pre_registration_user_mappings WHERE org_id = 2;"
echo
echo "2. Test the postConfirmation Lambda by simulating a user signup"
echo "3. Verify mappings get marked as 'used' after Lambda execution"
