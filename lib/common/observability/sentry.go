package observability

import (
	"context"
	"math/rand"

	"github.com/getsentry/sentry-go"
)

// StartSpanWithTransaction creates spans intelligently based on existing context.
// Creates a child span if transaction exists, otherwise starts a new transaction.
// This pattern ensures proper span hierarchy while avoiding orphaned spans.
func StartSpanWithTransaction(ctx context.Context, operation string, transactionName string) (*sentry.Span, func()) {
	transaction := sentry.TransactionFromContext(ctx)
	var span *sentry.Span
	var cleanup func()

	if transaction != nil {
		// There's already a transaction, create a child span
		span = transaction.StartChild(operation)
		cleanup = func() { span.Finish() }
	} else {
		// No transaction exists, start a new one with proper name
		transaction = sentry.StartTransaction(ctx, transactionName)
		span = transaction
		cleanup = func() {
			span.Finish()
		}
	}
	return span, cleanup
}

// StartSampledSpan creates a span with conditional sampling for high-frequency operations.
// Use this for operations that are called multiple times per second (polling, health checks, etc.)
// to reduce observability overhead while maintaining some visibility.
func StartSampledSpan(ctx context.Context, operation string, transactionName string, sampleRate float64) (*sentry.Span, func()) {
	// Check if tracing is enabled and apply sampling
	shouldSample := sentry.HasHubOnContext(ctx) &&
		sentry.GetHubFromContext(ctx).Client().Options().TracesSampleRate > 0

	if shouldSample && rand.Float64() < sampleRate {
		// Create span with sampling indicator
		span, cleanup := StartSpanWithTransaction(ctx, operation, transactionName)
		if span != nil {
			span.SetTag("sampled", "true")
		}
		return span, cleanup
	}

	// Return no-op span and cleanup
	return nil, func() {}
}
