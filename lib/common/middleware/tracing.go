package middleware

import (
	"context"
	"fmt"
	"strings"

	"connectrpc.com/connect"
	"github.com/getsentry/sentry-go"
)

// sentryTraceKey is the context key for storing sentry-trace headers
// Defined once at package level to ensure consistent context key usage
type sentryTraceKey struct{}

var sentryTraceContextKey = sentryTraceKey{}

// SentryTracingInterceptor creates a Connect RPC interceptor that propagates Sentry traces
// across service boundaries for distributed tracing.
//
// This interceptor:
// - Creates child spans for outgoing RPC calls
// - Propagates trace context via HTTP headers
// - Tags spans with RPC-specific metadata
// - Captures errors automatically
func SentryTracingInterceptor(serviceName string) connect.Interceptor {
	return connect.UnaryInterceptorFunc(
		func(next connect.UnaryFunc) connect.UnaryFunc {
			return func(ctx context.Context, req connect.AnyRequest) (connect.AnyResponse, error) {
				// Extract service and method from the request spec
				spec := req.Spec()
				rpcService := extractServiceName(spec.Procedure)
				rpcMethod := extractMethodName(spec.Procedure)

				// Create a child span for this outgoing RPC call
				span := sentry.StartSpan(ctx, fmt.Sprintf("%s/%s", rpcService, rpcMethod))
				defer span.Finish()

				// Update context to include the new span
				ctx = span.Context()

				// Set span metadata
				span.Op = "rpc.client"
				span.Description = fmt.Sprintf("%s.%s", rpcService, rpcMethod)
				span.SetTag("rpc.service", rpcService)
				span.SetTag("rpc.method", rpcMethod)
				span.SetTag("client.service", serviceName)
				span.SetTag("rpc.procedure", spec.Procedure)

				// Propagate Sentry trace headers for distributed tracing
				propagateSentryHeaders(ctx, req)

				// Make the RPC call
				resp, err := next(ctx, req)

				// Handle response and errors
				if err != nil {
					span.SetTag("error", "true")
					span.SetTag("rpc.status", "error")
					sentry.CaptureException(err)
				} else {
					span.SetTag("rpc.status", "success")
				}

				return resp, err
			}
		},
	)
}

// extractServiceName extracts the service name from a Connect RPC procedure
// Example: "/hero.assets.v2.AssetRegistryService/GetAsset" -> "AssetRegistryService"
func extractServiceName(procedure string) string {
	// Split by '/' and get the service part
	parts := splitProcedure(procedure)
	if len(parts) < 2 {
		return "unknown"
	}

	// Extract service name from the full package path
	servicePath := parts[0] // "hero.assets.v2.AssetRegistryService"
	pathParts := splitByDot(servicePath)
	if len(pathParts) == 0 {
		return "unknown"
	}

	return pathParts[len(pathParts)-1] // "AssetRegistryService"
}

// extractMethodName extracts the method name from a Connect RPC procedure
// Example: "/hero.assets.v2.AssetRegistryService/GetAsset" -> "GetAsset"
func extractMethodName(procedure string) string {
	parts := splitProcedure(procedure)
	if len(parts) < 2 {
		return "unknown"
	}
	return parts[1] // "GetAsset"
}

// splitProcedure splits a procedure by '/' and removes empty parts
func splitProcedure(procedure string) []string {
	var parts []string
	for _, part := range strings.Split(procedure, "/") {
		if part != "" {
			parts = append(parts, part)
		}
	}
	return parts
}

// splitByDot splits a string by '.'
func splitByDot(s string) []string {
	var parts []string
	for _, part := range strings.Split(s, ".") {
		if part != "" {
			parts = append(parts, part)
		}
	}
	return parts
}

// propagateSentryHeaders adds Sentry trace headers to the outgoing request
// This enables distributed tracing across service boundaries
func propagateSentryHeaders(ctx context.Context, req connect.AnyRequest) {
	// Get the current Sentry span from context
	span := sentry.SpanFromContext(ctx)
	if span == nil {
		return
	}

	// Get the trace context for propagation
	traceContext := span.TraceID
	spanContext := span.SpanID

	if traceContext.String() != "00000000000000000000000000000000" {
		// First checks if there is valid a trace context id, if not, it will not add the header
		// This is to avoid adding the header to requests that are not part of a distributed trace

		// Add Sentry trace header for distributed tracing
		// Format: sentry-trace: {trace_id}-{span_id}-{sampled}
		sampled := "1" // Assume sampled since we have an active span
		traceHeader := fmt.Sprintf("%s-%s-%s", traceContext.String(), spanContext.String(), sampled)
		req.Header().Set("sentry-trace", traceHeader)
	}
}

// SentryServerTracingInterceptor creates a Connect RPC server interceptor that continues
// distributed traces from incoming requests.
//
// This interceptor:
// - Checks for incoming sentry-trace headers
// - Continues existing distributed traces
// - Allows existing RPC instrumentation to handle transaction creation
func SentryServerTracingInterceptor() connect.Interceptor {
	return connect.UnaryInterceptorFunc(
		func(next connect.UnaryFunc) connect.UnaryFunc {
			return func(ctx context.Context, req connect.AnyRequest) (connect.AnyResponse, error) {
				// Check for incoming sentry-trace header to continue distributed trace
				if traceHeader := req.Header().Get("sentry-trace"); traceHeader != "" {
					// Parse the sentry-trace header and continue the trace
					ctx = continueFromSentryTrace(ctx, traceHeader)
				}

				// Let existing RPC instrumentation handle transaction creation
				return next(ctx, req)
			}
		},
	)
}

// continueFromSentryTrace parses a sentry-trace header and continues the distributed trace
// This uses Sentry's proper span option for trace continuation
func continueFromSentryTrace(ctx context.Context, traceHeader string) context.Context {
	// Store the sentry-trace header value for use with sentry.ContinueFromHeaders
	// when creating new transactions in the RPC handlers
	return context.WithValue(ctx, sentryTraceContextKey, traceHeader)
}

// GetSentryTraceFromContext retrieves the sentry-trace header from context
// This can be used by transaction creation code to continue distributed traces
func GetSentryTraceFromContext(ctx context.Context) string {
	if trace, ok := ctx.Value(sentryTraceContextKey).(string); ok {
		return trace
	}
	return ""
}

// StartTransactionWithTraceContext creates a transaction that continues from
// an existing distributed trace if available, or starts a new one
func StartTransactionWithTraceContext(ctx context.Context, name string) *sentry.Span {
	// Check if we have a trace header from the server interceptor
	if traceHeader := GetSentryTraceFromContext(ctx); traceHeader != "" {
		// Continue from the distributed trace using proper Sentry API
		return sentry.StartTransaction(ctx, name, sentry.ContinueFromHeaders("sentry-trace", traceHeader))
	}

	// No existing trace, start a new one
	return sentry.StartTransaction(ctx, name)
}
