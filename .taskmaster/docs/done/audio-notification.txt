# Call Audio Notification System PRD

## Overview

This document outlines the implementation of audio notifications for incoming calls in the Hero Core web application. The system will provide context-aware audio alerts to agents based on their current call state, enhancing the user experience by providing auditory feedback synchronized with the existing visual notification system in the CellularCallControlWidgetV2 component.

## Product requirements

### Purpose and goals

The Call Audio Notification System aims to improve agent awareness of incoming calls through audio feedback that adapts to their current call state. The system will provide immediate auditory notification when new calls enter the queue, with volume and urgency adjusted based on whether the agent is currently idle or engaged in an active call.

**Primary Goals:**
- Enhance agent awareness of incoming calls through audio feedback
- Provide context-aware notifications based on agent call state
- Maintain call quality by avoiding audio interference with active conversations
- Integrate seamlessly with existing visual notification system

**Success Metrics:**
- Audio notifications play within 500ms of new call detection
- No audio interference with active Twilio WebRTC calls
- Consistent audio behavior across different browser environments

### Target audience

**Primary Users:**
- Emergency dispatchers using the Hero Command & Control web application
- Public safety agents managing incoming call queues
- Operators handling multiple simultaneous communication channels

**User Context:**
- Agents work in high-stress, time-sensitive environments
- Multiple incoming calls require immediate attention
- Agents may be engaged in active conversations when new calls arrive
- Visual attention may be focused on maps, incident reports, or other interface elements

### Core functionality

**Audio Notification Behavior:**
- **Agent Idle State**: Play standard volume notification sound when new calls arrive
- **Agent Active Call State**: Play subtle volume notification sound for call waiting scenarios
- **Single Notification**: Each incoming call triggers exactly one audio notification
- **Per-Agent Context**: Audio behavior determined by individual agent's call state

**Technical Requirements:**
- Utilize HTML5 Audio Element with static MP3 files for notification playback
- Synchronize with existing CellularCallControlWidgetV2 visual notification logic
- Maintain separation from Twilio WebRTC audio streams
- Support browser autoplay policies through user interaction requirements

**Integration Points:**
- Hooks into existing new call detection logic in CellularCallControlWidgetV2
- Utilizes CallContext currentActiveCall state for agent status determination
- Co-located with visual tab switching and widget expansion behavior

### User experience

**Idle Agent Experience:**
1. Agent monitoring dashboard with no active calls
2. New call enters queue
3. Standard volume audio notification plays once
4. Visual notification system activates (widget expansion, tab switch)
5. Agent can accept call through existing UI

**Active Call Agent Experience:**
1. Agent engaged in active conversation
2. New call enters queue  
3. Subtle volume audio notification plays once (non-disruptive)
4. Visual notification system activates
5. Agent can place current call on hold and accept new call

**Emergency Call Handling:**
- Emergency calls follow same audio logic (idle=loud, active=subtle)
- Emergency visual indicators remain unchanged
- Audio volume determined by agent state, not call priority

### Technical specifications

**Architecture Components:**
- `useCallAudioNotifications.tsx`: Custom React hook for audio playback
- Integration point: CellularCallControlWidgetV2 component
- Audio trigger: Existing new call detection logic (lines 167-181)
- State dependency: CallContext.currentActiveCall

**Audio Implementation:**
- HTML5 Audio Element with static MP3 files
- Pre-recorded notification sounds for different states
- Audio files stored in `/public/sounds/` directory
- Independent audio channel from WebRTC streams
- Browser autoplay policy compliance

**Performance Requirements:**
- Audio notification latency: <500ms from call detection
- No impact on WebRTC call quality
- Minimal memory footprint for audio contexts
- Compatible with Chrome, Firefox, Safari browsers

## User stories and acceptance criteria

### Primary user stories

**US-001: Agent receives audio notification when idle**
- As an idle agent monitoring the dashboard
- I want to receive an audio notification when a new call arrives
- So that I'm immediately aware of incoming calls even when focused elsewhere
- **Acceptance Criteria:**
  - Audio notification plays when agent detects any new incoming call while currentActiveCall is null
  - Audio notification plays at standard volume level (idle state)
  - Trigger is based on agent's individual call state, not global queue changes
  - Visual notification system activates simultaneously
  - Audio completes within 3 seconds

**US-002: Agent receives subtle audio notification during active call**
- As an agent currently on an active call
- I want to receive a subtle audio notification when a new call arrives
- So that I'm aware of waiting calls without disruption to my current conversation
- **Acceptance Criteria:**
  - Audio notification plays when agent detects any new incoming call while currentActiveCall exists
  - Audio notification plays at reduced volume (approximately 30% of standard)
  - Audio duration is shorter than idle notification (under 1 second)
  - Trigger is based on agent's individual call state, not global queue changes
  - Active call audio quality remains unaffected
  - Visual notification system activates simultaneously

**US-003: Agent audio state synchronization**
- As an agent using the web application
- I want audio notifications to reflect my individual call state
- So that my notification experience is appropriate to my current context
- **Acceptance Criteria:**
  - Audio behavior determined by agent's own currentActiveCall state
  - Multiple agents can receive different audio experiences for same incoming call
  - Agent call state changes update audio behavior in real-time
  - No cross-contamination between agent sessions

### Alternative scenarios

**US-004: Agent transitions from idle to active during notification**
- As an agent who accepts a call while audio notification is playing
- I want the notification to stop when I become active
- So that audio doesn't continue unnecessarily
- **Acceptance Criteria:**
  - Audio notification stops when currentActiveCall becomes non-null
  - Subsequent incoming calls use active call audio behavior
  - No audio overlap between notification and call audio

**US-005: Agent uses browser with restricted autoplay policy**
- As an agent using a browser with strict autoplay restrictions
- I want audio notifications to work after I interact with the interface
- So that I receive notifications once I've engaged with the application
- **Acceptance Criteria:**
  - Audio notifications initialize after first user interaction
  - Clear indication if audio notifications are pending browser permission
  - Graceful fallback if audio context creation fails
  - No errors or console warnings from audio initialization

### Edge cases

**US-006: Multiple rapid incoming calls**
- As an agent receiving multiple calls in quick succession
- I want to receive appropriate audio feedback for each call
- So that I'm aware of call volume without audio chaos
- **Acceptance Criteria:**
  - Each new call triggers exactly one audio notification
  - No audio overlap between notifications for different calls
  - Audio behavior consistent regardless of call arrival frequency
  - System handles call queue changes during audio playback

**US-007: Agent browser tab loses focus**
- As an agent with the Hero application in a background tab
- I want to receive audio notifications for incoming calls
- So that I'm alerted even when working in other applications
- **Acceptance Criteria:**
  - Audio notifications play regardless of tab focus state
  - Volume and behavior consistent with foreground operation
  - No additional permissions required for background audio
  - Audio stops appropriately when tab regains focus and call is handled

**US-008: Agent experiences browser audio context suspension**
- As an agent whose browser suspends the audio context
- I want the audio system to recover automatically
- So that notifications continue working without manual intervention
- **Acceptance Criteria:**
  - Audio context automatically resumes when suspended
  - Audio notifications continue after context recovery
  - No manual page refresh required
  - Error logging for audio context issues

### Security and access

**US-009: Agent authentication state affects audio notifications**
- As an authenticated agent using the Hero application
- I want audio notifications to respect my session security
- So that only authorized personnel receive call notifications
- **Acceptance Criteria:**
  - Audio notifications only function with valid authentication
  - Audio system respects asset-based identity from CallContext
  - No audio notifications for unauthenticated sessions
  - Audio functionality terminates with session expiration

## Implementation approach

### Development phases

**Phase 1: Core Audio Hook Implementation**
- Create useCallAudioNotifications.tsx hook
- Implement HTML5 Audio Element with static MP3 files
- Basic idle/active state detection
- Integration with CellularCallControlWidgetV2

**Phase 2: Integration and Testing**
- Synchronization with existing visual notification system
- Cross-browser compatibility testing
- Performance optimization and audio quality validation
- Error handling and fallback mechanisms

**Phase 3: Refinement and Documentation**
- Audio timing and volume fine-tuning
- Code documentation and technical specifications
- User acceptance testing with actual dispatchers
- Production deployment and monitoring

### Risk mitigation

**Technical Risks:**
- Browser autoplay policy restrictions: Mitigated through user interaction requirements
- Audio file loading failures: Handled through proper error handling and fallbacks
- WebRTC audio interference: Avoided through separate audio channels

**User Experience Risks:**
- Audio notification timing: Addressed through existing visual notification synchronization
- Volume appropriateness: Configurable through HTML5 Audio volume property
- Browser compatibility: Tested across target browser environments

### Success criteria

**Technical Success:**
- Audio notifications trigger within 500ms of call detection
- Zero audio interference with active Twilio calls
- Compatible across Chrome, Firefox, and Safari browsers
- No memory leaks or performance degradation

**User Experience Success:**
- Agents report improved awareness of incoming calls
- No complaints about audio disruption during active calls
- Seamless integration with existing workflow patterns
- Reduced missed call notifications in user testing