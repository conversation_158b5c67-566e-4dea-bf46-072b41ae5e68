---
description: 
globs: 
alwaysApply: false
---
# Project Overview: hero-command-and-control-web (CAD Module Analysis)

This document provides a high-level overview of the key components, conventions, and architectural patterns observed in the `hero-command-and-control-web` project, specifically focusing on the analyzed CAD (Computer-Aided Dispatch) related modules.

## Reusable Components & Utilities

This section highlights components and utilities designed for reuse across the application.

* **UI Components:**
    * `PriorityChip` (`TaskBar/PriorityChip.tsx`): Displays incident priority levels with corresponding styles.
    * `SituationItem` (`TaskBar/SituationItem.tsx`): Renders a summary card for a single incident, including status, call state, and basic details.
    * `CellularCallControlWidget` (`CommsComponents/CellularCallControlWidget/CellularCallControlWidget.tsx`): Provides the user interface for managing voice calls (dialing, active call controls, hold status).
    * `InterruptionBanner` (Referenced by `CellularCallInterruptionModal.tsx`): A generic banner component for displaying interruptive notifications (like incoming calls).
* **Custom Hooks:**
    * `useTimer` (`CellularCallInterruptionModal.tsx`): Manages simple interval-based timers.
    * `useCallerDisplayInfo` (`utils/caller-identification`): Formats caller ID data.
    * `useTokenManagement` (`CallContext.tsx`): Handles Twilio token fetching and lifecycle.
    * `useDeviceManagement` (`CallContext.tsx`): Manages the Twilio Voice SDK Device instance and state.
    * Data Fetching Hooks (`apis/services/.../hooks`): Encapsulate API calls using React Query (e.g., `useListOrdersForSituation`, `useCreateSituation`, `useSituation`).
* **Utility Functions:**
    * Label Formatters (`utils/utils`): Functions like `getIncidentLabel`, `getIncidentStatusLabel`.
    * Phone Number Utilities (`utils/caller-identification`): Functions like `standardizeUSPhoneNumber`, `formatPhoneNumberForDisplay`.
    * Enum Converters (`apis/services/workflow/orders/enumConverters`): Map enum values to strings (e.g., `hookOrderStatusToString`).
    * Timestamp Converters (`proto/utils`): e.g., `isoStringToProtoTimestamp`.
    * Token Helpers (`CallContext.tsx`): e.g., `getTokenExpiry`.
    * Logging Helpers (`CallContext.tsx`): `logInfo`, `logError` for standardized context logging.

## Coding Conventions

This section outlines the established coding practices and conventions.

* **Frameworks & Language:**
    * React (Functional Components, Hooks).
    * Next.js (App Router structure).
    * TypeScript (Strictness level TBC, some `@ts-ignore` usage).
* **State Management:**
    * React Context (`CallContext`, `DispatcherContext`) for global/shared state, especially complex features.
    * React Query for server state caching, synchronization, and background updates.
    * Local `useState` for component-level UI state.
* **Styling:**
    * Material UI (MUI) as the base component library, styled via the `sx` prop.
    * CSS Modules (`*.module.css`) for component-scoped styling.
    * Inline Styles (`React.CSSProperties`) used notably in `CellularCallControlWidget`.
* **Folder Structure:**
    * Feature-based organization (`app/cad`).
    * Grouped by type (`components`, `contexts`, `apis`, `utils`).
    * Barrel exports (`index.tsx`) common.
* **Naming:**
    * Components: `PascalCase`.
    * Hooks: `useCamelCase`.
    * Variables/Functions: `camelCase`.
    * Constants: `UPPER_SNAKE_CASE`.
    * CSS Classes (Modules): `camelCase`.
    * Types: `PascalCase`.
* **API Interaction:**
    * Centralized data fetching via custom hooks leveraging React Query.
    * Protobuf types used for API contracts.
* **Asynchronicity:** Primarily `async/await` within React Query hooks and Context functions.
* **Error Handling:** `try...catch` blocks, custom logging helpers, some state-based UI error feedback.

## Styling Guide 
# Hero Core Frontend Styling Guide

## Core Principles

1. **Use the design system tokens for all visual properties**
2. **Maintain consistency with established patterns**
3. **Prefer MUI's styling approach over inline styles**
4. **Create modular, reusable components**
5. **Document style decisions for team reference**

---

## Design Tokens

### Colors

- ✅ **DO:** Use the colors token system
  ```tsx
  color: colors.blue[600]
  backgroundColor: colors.grey[50]
  ```

- ❌ **DON'T:** Use hardcoded hex values
  ```tsx
  color: "#0060FF" // Avoid
  backgroundColor: "#F8F9FA" // Avoid
  ```

### Typography

- ✅ **DO:** Use typography style tokens
  ```tsx
  typography.styles.body2
  fontFamily: typography.fontFamily.roboto
  ```

- ❌ **DON'T:** Directly set font properties without tokens
  ```tsx
  fontSize: "14px" // Avoid
  fontWeight: 600   // Avoid
  ```

### Spacing

- ✅ **DO:** Use spacing tokens for margins, padding, gaps
  ```tsx
  padding: spacing.m
  gap: spacing.s
  ```

- ❌ **DON'T:** Use hardcoded pixel values
  ```tsx
  padding: "16px" // Avoid
  gap: "8px"      // Avoid
  ```

### Border Radius

- ✅ **DO:** Use radius tokens
  ```tsx
  borderRadius: radius.m
  ```

- ❌ **DON'T:** Hardcode radius values
  ```tsx
  borderRadius: "8px" // Avoid
  ```

---

## Styling Approaches (in order of preference)

1. **Material UI's `sx` Prop**

   - ✅ **DO:** Use `sx` prop for MUI components
     ```tsx
     <Box sx={{
       display: "flex",
       padding: spacing.m,
       backgroundColor: colors.grey[50],
       borderRadius: radius.m
     }}>
     ```

2. **Design System Components**

   - ✅ **DO:** Use pre-built design system components
     ```tsx
     <Typography
       style="body2"
       color={colors.grey[700]}
     />
     ```

3. **CSS Modules**

   - ✅ **DO:** Use CSS modules for complex components
     ```css
     /* ComponentName.module.css */
     .container { /* ... */ }
     ```
     ```tsx
     import styles from './ComponentName.module.css';
     <div className={styles.container}>
     ```

---

## Component Structure

### Layout Components

- ✅ **DO:** Use `Box` for layout with flex
  ```tsx
  <Box sx={{
    display: "flex",
    flexDirection: "column",
    gap: spacing.m
  }}>
  ```

### State-Based Styling

- ✅ **DO:** Conditionally apply styles based on state
  ```tsx
  <Box sx={{
    backgroundColor: isActive ? colors.blue[100] : colors.grey[50]
  }}>
  ```

---

## Code Style

### Organization

- ✅ **DO:** Group related styles together
  ```tsx
  // Layout properties
  display: "flex",
  flexDirection: "column",
  alignItems: "center",

  // Spacing & size
  padding: spacing.m,
  width: "100%",

  // Visual properties
  backgroundColor: colors.grey[50],
  borderRadius: radius.m,
  ```

### Naming

- ✅ **DO:** Use descriptive names for style objects
  ```tsx
  const headerStyles = { /* ... */ };
  const contentStyles = { /* ... */ };
  ```

---

## Common UI Patterns

### Status Indicators

- **Available status**
  ```tsx
  backgroundColor: colors.green[100],
  color: colors.green[600],
  ```

- **Error/Alert status**
  ```tsx
  backgroundColor: colors.red[100],
  color: colors.red[600],
  ```

- **Warning/Hold status**
  ```tsx
  backgroundColor: colors.orange[100],
  color: colors.orange[600],
  ```

### Interactive Elements

- ✅ **DO:** Use hover and active states consistently
  ```tsx
  "&:hover": {
    backgroundColor: colors.blue[50]
  },
  "&:active": {
    backgroundColor: colors.blue[100]
  }
  ```

### Responsive Design

- ✅ **DO:** Use Material UI's responsive syntax
  ```tsx
  <Box sx={{
    flexDirection: { xs: "column", md: "row" },
    padding: { xs: spacing.s, md: spacing.m }
  }}>
  ```

### Accessibility

- ✅ **DO:** Ensure sufficient color contrast
  ```tsx
  backgroundColor: colors.grey[100],
  color: colors.grey[900],
  ```
- ✅ **DO:** Not rely solely on color
  ```tsx
  border: `1px solid ${colors.red[500]}`
  ```

---

## Best Practices Checklist

- [ ] All colors use the design token system
- [ ] All spacing uses spacing tokens
- [ ] Typography follows the design type system
- [ ] Component styling uses the preferred approach for that context
- [ ] Responsive considerations are implemented
- [ ] Interactive states (hover, active, disabled) are handled consistently
- [ ] Status indicators follow established patterns
- [ ] Styles are organized logically within components
- [ ] No hardcoded values for colors, spacing, or typography

---

## Anti-Patterns to Avoid

- ❌ Using inline styles for complex components
- ❌ Mixing styling approaches within a single component
- ❌ Hardcoding values that have token equivalents
- ❌ Creating one-off styles that don't align with the design system
- ❌ Inconsistent naming conventions for style objects
- ❌ Deeply nested style hierarchies


## Observed Architecture Patterns

This section describes the high-level architectural approaches identified.
* **Context API as Feature Facade (`CallContext`):** Centralizes complex state, logic, and external service interactions (Twilio SDK, backend APIs) for the voice call feature, providing a simplified interface to consuming components.
* **Server State Management via React Query:** Clear separation and management of server data lifecycle (fetching, caching, invalidation) using React Query, often wrapped in feature-specific custom hooks.
* **Hook-Based Logic Encapsulation:** Reusable logic (timers, external service interaction, complex state updates) is extracted into custom hooks.
* **Polling for Real-time Data:** `refetchInterval` in React Query and basic `setInterval` are used to keep data relatively fresh where WebSockets are not apparent.
* **Component Composition:** Features are built by assembling smaller, focused components and larger, feature-rich widgets.
* **Context Dependency:** UI components related to specific features (like calls) heavily rely on their corresponding Context provider (`CallContext`).
* **Centralized API Layer:** API endpoint definitions and request logic appear structured within an `apis` directory, promoting consistency.
