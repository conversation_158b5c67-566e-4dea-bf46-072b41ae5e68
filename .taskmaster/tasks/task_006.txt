# Task ID: 6
# Title: Add Basic Audio Volume Controls
# Status: pending
# Dependencies: 2
# Priority: medium
# Description: Implement simple volume control for different agent states using HTML5 Audio volume property
# Details:
Implement basic volume control using HTML5 Audio volume property only (1.0 for idle, 0.3 for active state). Add simple audio overlap prevention to stop previous notification before playing new one. Keep implementation minimal and pragmatic - no fade effects or complex timing controls needed for <PERSON>.

```typescript
const playNotification = useCallback((isAgentActive: boolean) => {
  const audio = isAgentActive ? activeAudioRef.current : idleAudioRef.current;
  
  // Simple overlap prevention
  if (audio && !audio.paused) {
    audio.pause();
    audio.currentTime = 0;
  }
  
  // Basic volume control
  if (audio) {
    audio.volume = isAgentActive ? 0.3 : 1.0;
    audio.play().catch(console.error);
  }
}, []);
```

Removed from scope: Fade effects, complex timing controls, Web Audio API GainNode usage, advanced duration management.

# Test Strategy:
Test volume levels for idle vs active states, verify basic overlap prevention works, confirm audio plays at correct volume