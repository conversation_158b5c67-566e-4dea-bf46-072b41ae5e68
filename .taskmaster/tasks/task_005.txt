# Task ID: 5
# Title: Implement Agent State Detection and Context Awareness
# Status: pending
# Dependencies: 3
# Priority: medium
# Description: Create robust agent state detection logic that determines appropriate audio behavior based on currentActiveCall status
# Details:
Implement real-time agent state monitoring using CallContext.currentActiveCall. Create state change detection to handle transitions from idle to active during notifications. Add logic to stop ongoing notifications when agent state changes. Ensure per-agent context isolation for multi-agent environments. Implement debouncing for rapid state changes. Add logging for state transitions and audio decisions.

```typescript
const useAgentState = () => {
  const { currentActiveCall } = useContext(CallContext);
  const [previousState, setPreviousState] = useState(!!currentActiveCall);
  
  const isAgentActive = !!currentActiveCall;
  const stateChanged = isAgentActive !== previousState;
  
  useEffect(() => {
    if (stateChanged) {
      setPreviousState(isAgentActive);
      // Handle state transition logic
    }
  }, [isAgentActive, stateChanged]);
  
  return { isAgentActive, stateChanged };
};
```

# Test Strategy:
Test state detection accuracy, verify state change handling, test multi-agent scenarios, validate notification stopping on state transitions
