# Task ID: 3
# Title: Integrate Audio Hook with CellularCallControlWidgetV2
# Status: pending
# Dependencies: 2
# Priority: high
# Description: Integrate the audio notification hook into the existing CellularCallControlWidgetV2 component at the appropriate trigger points
# Details:
Import and initialize useCallAudioNotifications hook in CellularCallControlWidgetV2 component. Integrate audio trigger at lines 167-181 where new call detection logic exists. Access CallContext.currentActiveCall to determine agent state (idle vs active). Ensure audio notification triggers exactly once per new incoming call. Synchronize with existing visual notification system without disrupting current functionality. Add proper TypeScript types and error boundaries.

```typescript
const { playNotification } = useCallAudioNotifications();
const { currentActiveCall } = useContext(CallContext);

// In existing new call detection logic (lines 167-181)
if (newCallDetected) {
  playNotification(!!currentActiveCall);
  // existing visual notification logic continues
}
```

# Test Strategy:
Test integration with existing component, verify audio triggers on new call detection, confirm no disruption to visual notifications, test with both idle and active agent states
