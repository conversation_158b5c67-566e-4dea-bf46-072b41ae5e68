{"tasks": [{"id": 1, "title": "Create Audio Assets and Directory Structure", "description": "Set up the audio file directory structure and create/source the MP3 notification sound files for different agent states", "details": "Create `/public/sounds/` directory structure. Source or create two MP3 audio files: `notification-idle.mp3` (standard volume, ~2-3 seconds) and `notification-active.mp3` (subtle volume, <1 second). Ensure files are optimized for web delivery (128kbps, mono channel recommended). Files should be professionally recorded or sourced from royalty-free libraries. Test audio quality across different devices and browsers. Consider using Web Audio API compatible formats and ensure files are under 100KB each for fast loading.", "testStrategy": "Verify audio files load correctly in browser, test playback quality across Chrome/Firefox/Safari, validate file sizes are optimized, confirm audio duration meets requirements (idle: 2-3s, active: <1s)", "priority": "high", "dependencies": [], "status": "completed", "subtasks": []}, {"id": 2, "title": "Implement useCallAudioNotifications Custom Hook", "description": "Create the core React hook that manages HTML5 Audio elements and provides audio notification functionality", "details": "Create `useCallAudioNotifications.tsx` hook using React 18+ patterns. Implement HTML5 Audio element management with preloading for both notification files. Include audio context initialization with user interaction detection for autoplay policy compliance. Implement volume control (100% for idle, 30% for active state). Add error handling for audio loading failures and browser compatibility issues. Use useRef for Audio element persistence and useCallback for performance optimization. Include cleanup in useEffect return function.\n\n```typescript\nconst useCallAudioNotifications = () => {\n  const idleAudioRef = useRef<HTMLAudioElement | null>(null);\n  const activeAudioRef = useRef<HTMLAudioElement | null>(null);\n  const [isInitialized, setIsInitialized] = useState(false);\n  \n  const initializeAudio = useCallback(() => {\n    // Initialize audio elements with preload\n  }, []);\n  \n  const playNotification = useCallback((isAgentActive: boolean) => {\n    // Play appropriate notification based on state\n  }, []);\n  \n  return { playNotification, isInitialized };\n};\n```", "testStrategy": "Unit test hook initialization, test audio element creation and cleanup, verify volume settings, test error handling for missing files, validate memory cleanup on unmount", "priority": "high", "dependencies": [1], "status": "completed", "subtasks": []}, {"id": 3, "title": "Integrate Audio Hook with CellularCallControlWidgetV2", "description": "Integrate audio notifications into the existing CellularCallControlWidgetV2 component using the proven call detection pattern", "details": "Import and initialize useCallAudioNotifications hook in CellularCallControlWidgetV2. Extend the existing queue monitoring effect (lines 169-181) to add audio notifications for ALL new incoming calls (0→1, 1→2, etc.). Use existing currentActiveCall state from CallContext to determine per-dispatcher audio volume. Leverage the proven pattern already used for UI expansion triggers.\n\n```typescript\nconst { playNotification } = useCallAudioNotifications();\nconst { currentActiveCall, queueStatus } = useCallContext();\n\n// In existing queue monitoring effect (lines 169-181)\nif (currentInboundCount > previousInboundCount) {\n  try {\n    playNotification(!!currentActiveCall); // Per-dispatcher volume\n  } catch (audioError) {\n    console.warn('Audio notification failed:', audioError);\n  }\n}\n```", "testStrategy": "Test integration with existing widget, verify audio triggers on any queue increase, test per-dispatcher volume logic (idle=loud, busy=subtle), confirm no disruption to existing UI behavior, validate all dispatchers get audio for global queue changes", "priority": "high", "dependencies": [2], "status": "completed", "subtasks": []}, {"id": 4, "title": "Implement Browser Autoplay Policy Compliance (DEFERRED)", "description": "DEFERRED: Basic autoplay handling will be implemented in future iteration after core functionality is stable", "details": "DEFERRED TO FUTURE ITERATION: This task involves complex autoplay policy handling that goes beyond the essential scope. The core audio functionality will be implemented first, and autoplay compliance will be added in a subsequent iteration when needed.\n\nOriginal scope included: User interaction detection, audio context initialization, fallback mechanisms, and browser-specific workarounds. This level of complexity is not needed for the MVP implementation.", "testStrategy": "DEFERRED: Will be tested in future iteration", "priority": "low", "dependencies": [], "status": "deferred", "subtasks": []}, {"id": 5, "title": "Implement Agent State Detection and Context Awareness", "description": "Create robust agent state detection logic that determines appropriate audio behavior based on currentActiveCall status", "details": "Implement real-time agent state monitoring using CallContext.currentActiveCall. Create state change detection to handle transitions from idle to active during notifications. Add logic to stop ongoing notifications when agent state changes. Ensure per-agent context isolation for multi-agent environments. Implement debouncing for rapid state changes. Add logging for state transitions and audio decisions.\n\n```typescript\nconst useAgentState = () => {\n  const { currentActiveCall } = useContext(CallContext);\n  const [previousState, setPreviousState] = useState(!!currentActiveCall);\n  \n  const isAgentActive = !!currentActiveCall;\n  const stateChanged = isAgentActive !== previousState;\n  \n  useEffect(() => {\n    if (stateChanged) {\n      setPreviousState(isAgentActive);\n      // Handle state transition logic\n    }\n  }, [isAgentActive, stateChanged]);\n  \n  return { isAgentActive, stateChanged };\n};\n```", "testStrategy": "Test state detection accuracy, verify state change handling, test multi-agent scenarios, validate notification stopping on state transitions", "priority": "medium", "dependencies": [3], "status": "completed", "subtasks": []}, {"id": 6, "title": "Add Basic Audio Volume Controls", "description": "Implement simple volume control for different agent states using HTML5 Audio volume property", "details": "Implement basic volume control using HTML5 Audio volume property only (1.0 for idle, 0.3 for active state). Add simple audio overlap prevention to stop previous notification before playing new one. Keep implementation minimal and pragmatic - no fade effects or complex timing controls needed for <PERSON>.\n\n```typescript\nconst playNotification = useCallback((isAgentActive: boolean) => {\n  const audio = isAgentActive ? activeAudioRef.current : idleAudioRef.current;\n  \n  // Simple overlap prevention\n  if (audio && !audio.paused) {\n    audio.pause();\n    audio.currentTime = 0;\n  }\n  \n  // Basic volume control\n  if (audio) {\n    audio.volume = isAgentActive ? 0.3 : 1.0;\n    audio.play().catch(console.error);\n  }\n}, []);\n```\n\nRemoved from scope: Fade effects, complex timing controls, Web Audio API GainNode usage, advanced duration management.", "testStrategy": "Test volume levels for idle vs active states, verify basic overlap prevention works, confirm audio plays at correct volume", "priority": "medium", "dependencies": [2], "status": "completed", "subtasks": []}, {"id": 7, "title": "Enhan<PERSON><PERSON><PERSON> (MVP)", "description": "Add pragmatic error handling for production monitoring and debugging", "details": "✅ COMPLETED: Essential error handling implemented.\n\nCURRENT IMPLEMENTATION:\n- ✅ Try-catch blocks around all audio operations\n- ✅ Error logging for audio loading failures\n- ✅ Playback error handling with state tracking\n- ✅ Graceful degradation (audio fails silently)\n- ✅ Error state management for monitoring/debugging\n\nADDED IMPROVEMENTS:\n- Enhanced playback error handling with audioError state updates\n- Better error information capture (error.message)\n- Production-ready error logging\n\nREMOVED FROM SCOPE: Retry logic (can be added if production shows issues), React Error Boundaries, complex fallback mechanisms (over-engineering for MVP)", "testStrategy": "Monitor production logs for audio failures, validate error states are properly set", "priority": "low", "dependencies": [], "status": "completed", "subtasks": []}, {"id": 8, "title": "Performance and Memory Management", "description": "✅ COMPLETED: Essential performance optimizations and robust memory management implemented", "details": "✅ COMPLETED: All essential optimizations implemented.\n\nCURRENT IMPLEMENTATION:\n- ✅ Audio preloading with 'auto' preload attribute\n- ✅ Robust memory cleanup for audio elements on component unmount\n- ✅ Proper audio element lifecycle management\n- ✅ Simple overlap prevention to avoid audio conflicts\n- ✅ Error handling during cleanup operations\n- ✅ Event listener cleanup to prevent memory leaks\n\nADDED IMPROVEMENTS:\n- Enhanced cleanup with try-catch error handling\n- Event listener removal during cleanup\n- Better logging for cleanup operations\n- Proper finally blocks to ensure refs are nullified\n\nREMOVED FROM SCOPE: Audio context pooling, performance monitoring latency requirements, React.memo optimizations (premature optimization for MVP)", "testStrategy": "Monitor production performance, validate memory cleanup works correctly in multi-agent scenarios", "priority": "low", "dependencies": [], "status": "completed", "subtasks": []}, {"id": 9, "title": "Basic Cross-Browser Compatibility Testing (MVP)", "description": "Essential browser testing for production readiness", "details": "SIMPLIFIED FOR MVP: Focus on essential browser compatibility validation only.\n\nMINIMAL TESTING REQUIRED:\n- Quick manual test on Chrome (primary), Firefox, Safari\n- Validate MP3 audio playback works\n- Test basic audio notification functionality\n- Document any browser-specific issues found\n\nREMOVED FROM SCOPE: Comprehensive cross-OS testing, BrowserStack automation, formal compatibility matrices, browser detection utilities (over-engineering for MVP)\n\nNOTE: HTML5 Audio with MP3 has excellent browser support (98%+ according to caniuse.com), so major compatibility issues are unlikely.", "testStrategy": "15-minute manual test across Chrome/Firefox/Safari with actual call scenarios", "priority": "medium", "dependencies": [], "status": "pending", "subtasks": []}, {"id": 10, "title": "Authentication Integration (DEFERRED)", "description": "DEFERRED: Authentication is already handled by existing CallContext and component-level security", "details": "DEFERRED: Authentication and security are already handled by the existing Hero Core authentication system. The CellularCallControlWidgetV2 component already operates within the authenticated context provided by CallContext, which includes asset-based identity and session management.\n\nSince the audio notification system integrates at the component level and uses existing CallContext state (currentActiveCall), it automatically inherits the existing security model. No additional authentication integration is needed for MVP.\n\nOriginal scope included: Custom authentication hooks, session expiration handling, role-based access controls, security logging. These are already handled by the existing system.", "testStrategy": "DEFERRED: Existing authentication system provides security boundaries", "priority": "low", "dependencies": [], "status": "deferred", "subtasks": []}, {"id": 11, "title": "Testing Strategy (DEFERRED - Live E2E Preferred)", "description": "DEFERRED: Comprehensive automated testing suite in favor of live end-to-end testing with actual calls", "details": "DEFERRED: Instead of implementing a comprehensive automated testing suite, testing will be conducted through live end-to-end scenarios with actual incoming calls. This approach is more practical for audio functionality validation and aligns with the pragmatic scope.\n\nTesting approach:\n- Live testing with real Twilio calls in development environment\n- Manual validation of audio behavior during idle/active states\n- Browser compatibility testing through actual usage\n- Performance validation through real-world scenarios\n\nOriginal scope included: Jest unit tests, React Testing Library integration tests, Cypress/Playwright e2e tests, audio mocking, performance tests, accessibility tests. These will be deferred in favor of practical live testing.\n\nBasic error handling and console logging will provide sufficient debugging capabilities for the MVP.", "testStrategy": "Live E2E testing with actual calls, manual browser compatibility testing, real-world performance validation", "priority": "low", "dependencies": [], "status": "deferred", "subtasks": []}, {"id": 12, "title": "Essential Documentation (MVP)", "description": "Create minimal but essential documentation for production deployment and maintenance", "details": "SIMPLIFIED FOR MVP: Focus on essential documentation only.\n\nREQUIRED DOCUMENTATION:\n- Add <PERSON><PERSON> comments to useCallAudioNotifications hook\n- Document audio file requirements (location, format, size)\n- Create basic troubleshooting guide for common issues\n- Document the integration pattern used in CellularCallControlWidgetV2\n- Simple README section explaining the audio notification system\n\n```typescript\n/**\n * Custom hook for managing call audio notifications\n * Provides audio notifications for incoming calls with volume based on agent state\n * @returns {Object} Audio notification controls\n * @example\n * const { playNotification, isInitialized } = useCallAudioNotifications();\n * playNotification(isAgentActive); // true = subtle volume, false = loud volume\n */\nexport const useCallAudioNotifications = () => {\n  // Implementation\n};\n```\n\nREMOVED FROM SCOPE: Comprehensive API docs, formal integration guides, production monitoring setup, feature flags, rollback procedures (can be added post-MVP based on needs)", "testStrategy": "Review documentation for completeness and accuracy, ensure new developers can understand the system", "priority": "medium", "dependencies": [], "status": "pending", "subtasks": []}]}