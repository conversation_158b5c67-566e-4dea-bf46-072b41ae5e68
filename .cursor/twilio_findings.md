# Twilio Call Duration API Research

## Overview
This document outlines a pragmatic approach for tracking call duration and hold time in the Hero Command and Control application. The focus is on a minimal, reliable solution that works across service restarts and page refreshes.

## Current Implementation
Our current implementation in the Hero Command and Control application:
- Uses Twilio's API for call management through `twilioClient` in `/services/communications/internal/cellularcall/client/twilio.go`
- Tracks call state transitions (active, hold, ended) in `/services/communications/internal/cellularcall/usecase/cellularcall_usecase.go`
- Stores call data in memory or PostgreSQL via `/services/communications/internal/cellularcall/data/callqueue_repo.go`
- Handles call status updates through webhooks in `HandleCallStatusRequest`

However, the current implementation does not specifically track the duration of calls, especially time spent on hold.

## Twilio API Options for Call Duration

### 1. Call Resource API (GET /Calls/{CallSid})
The primary method to retrieve call duration information is through the Call Resource API:

```
GET https://api.twilio.com/2010-04-01/Accounts/{AccountSid}/Calls/{CallSid}.json
```

This endpoint returns detailed information about a specific call, including:
- `duration`: The length of the call in seconds (only available for completed calls)
- `start_time`: When the call was initiated
- `end_time`: When the call was completed or disconnected
- `status`: Current status of the call (e.g., "in-progress", "completed", "busy", "failed", "no-answer")

For active calls, the `duration` property will not be available until the call is completed.

### 2. Status Callbacks with CallDuration Parameter
Twilio can send HTTP requests to a specified URL when call events occur:

- Configure a StatusCallback URL in your Twilio account or when initiating calls
- Receive updates when call status changes (e.g., "in-progress", "completed")
- The callback includes a `CallDuration` parameter for completed calls

Example callback parameters:
```
CallSid: CA123456789
CallStatus: completed
CallDuration: 120
```

### 3. Call Event Resource
For detailed call event tracking:

```
GET https://api.twilio.com/2010-04-01/Accounts/{AccountSid}/Calls/{CallSid}/Events.json
```

This provides a timeline of events during a call, including:
- TwiML execution events
- Status changes
- Request/response data

Note: Call Event data is only available 15 minutes after a call ends.

## Tracking Call Duration and Hold Time

Twilio doesn't natively track hold time as a separate metric. Our solution needs to be simple yet persistent:

1. **Minimal timestamp tracking**:
   - Record call start time when a call is connected
   - Record call end time when a call is completed
   - Record timestamp when a call is placed on hold
   - Reset hold tracking when a call is resumed

2. **Database schema updates**:
   - Add three fields to the existing `call_queue` table
   - Calculate durations on demand using simple timestamp arithmetic

## Implementation Approach

### 1. Database Schema Changes

Add these three columns to the existing `call_queue` table:

| Column | Type | Description |
|--------|------|-------------|
| `call_start_time` | timestamp with time zone | When the call was connected |
| `call_end_time` | timestamp with time zone | When the call ended (null for active calls) |
| `last_hold_start` | timestamp with time zone | When the call was last placed on hold (null if not on hold) |

This minimal change to the schema provides all the data needed to track call durations.

### 2. Key Operations

Implement these key operations to track call durations:

#### When a call is connected:
```sql
UPDATE call_queue 
SET call_start_time = CURRENT_TIMESTAMP 
WHERE call_sid = ?;
```

#### When a call is placed on hold:
```sql
UPDATE call_queue 
SET state = 'hold', last_hold_start = CURRENT_TIMESTAMP 
WHERE call_sid = ?;
```

#### When a call is resumed from hold:
```sql
UPDATE call_queue 
SET state = 'active', last_hold_start = NULL 
WHERE call_sid = ?;
```

#### When a call ends:
```sql
UPDATE call_queue 
SET state = 'ended', call_end_time = CURRENT_TIMESTAMP 
WHERE call_sid = ?;
```

### 3. Duration Calculations

Calculate call durations on demand with these simple queries:

#### Total Call Duration (Active Call):
```sql
SELECT 
  EXTRACT(EPOCH FROM (CURRENT_TIMESTAMP - call_start_time)) AS total_seconds
FROM call_queue
WHERE call_sid = ?;
```

#### Total Call Duration (Completed Call):
```sql
SELECT 
  EXTRACT(EPOCH FROM (call_end_time - call_start_time)) AS total_seconds
FROM call_queue
WHERE call_sid = ?;
```

#### Current Hold Duration (if on hold):
```sql
SELECT 
  CASE 
    WHEN last_hold_start IS NOT NULL THEN 
      EXTRACT(EPOCH FROM (CURRENT_TIMESTAMP - last_hold_start))
    ELSE 0
  END AS hold_seconds
FROM call_queue
WHERE call_sid = ?;
```

#### Fallback to Twilio API (rarely needed):
```go
// Only used for historical calls or system recovery
func GetTwilioCallDuration(callSID string) (int, error) {
    // GET https://api.twilio.com/2010-04-01/Accounts/{AccountSid}/Calls/{CallSid}.json
    // Extract the duration field from the response
}
```
```

## System Resilience Benefits

This minimal database-driven approach provides several key benefits:

1. **Persistence**: All duration data survives service restarts and page refreshes
2. **Simplicity**: Straightforward implementation with minimal code changes
3. **Performance**: Efficient queries with no joins or complex calculations
4. **Reliability**: Works even when Twilio API is unavailable

By storing just three timestamps, we get complete call duration tracking with minimal overhead.

## Frontend Integration

To display call durations in the UI:

1. **Backend API**: Create a simple endpoint that returns duration data
   ```go
   // GET /api/calls/{callSid}/duration
   func GetCallDuration(callSID string) (totalDuration, holdDuration int) {
       // Query the database for call_start_time, call_end_time, last_hold_start
       // Calculate durations using the formulas above
       return totalDuration, holdDuration
   }
   ```

2. **UI Component**: Update the display every second for active calls
   ```jsx
   function CallDurationDisplay({ callSid }) {
     const [durations, setDurations] = useState({ total: 0, hold: 0 });
     
     useEffect(() => {
       // Fetch durations immediately and every second
       fetchDurations();
       const interval = setInterval(fetchDurations, 1000);
       return () => clearInterval(interval);
     }, [callSid]);
     
     async function fetchDurations() {
       const response = await fetch(`/api/calls/${callSid}/duration`);
       const data = await response.json();
       setDurations(data);
     }
     
     return (
       <div>
         <div>Call Duration: {formatTime(durations.total)}</div>
         {durations.hold > 0 && (
           <div>On Hold: {formatTime(durations.hold)}</div>
         )}
       </div>
     );
   }
   ```

## Integration Points

Integrate duration tracking at these key points in the call flow:

1. **Call Connection**: When a call is first connected (either inbound or outbound)
   ```go
   // In DequeueCall or when a call is first connected
   call.CallStartTime = time.Now()
   ```

2. **Hold/Resume**: When a call's hold state changes
   ```go
   // In HoldCall
   call.LastHoldStart = time.Now()
   
   // In ResumeCall
   call.LastHoldStart = time.Time{} // Zero value (NULL)
   ```

3. **Call End**: When a call is completed or disconnected
   ```go
   // In EndCall or HandleCallStatusRequest
   call.CallEndTime = time.Now()
   ```

4. **Webhook Backup**: Use Twilio's StatusCallback as a safety net
   ```go
   // In HandleCallStatusRequest
   if callStatus == "completed" || callStatus == "failed" {
       // Update call_end_time if not already set
       if call.CallEndTime.IsZero() {
           call.CallEndTime = time.Now()
       }
   }
   ```

## Implementation Considerations

1. **Storage Impact**: Minimal additional storage needed
   - Three timestamp columns (~24 bytes per call record)
   - No separate tables or complex relationships
   - For 10,000 calls: ~240KB additional storage

2. **Performance**: Extremely efficient
   - Simple timestamp arithmetic for all calculations
   - No joins or complex queries needed
   - Calculations happen on demand, no need for background processing

3. **Migration**: Easy to implement
   - Single ALTER TABLE statement to add the columns
   - No need to backfill historical data
   - Can be deployed without downtime

## Conclusion

This minimalist approach provides an elegant solution for tracking call durations:

1. **Simple**: Just three timestamp columns in the existing table
2. **Persistent**: All data survives service restarts and page refreshes
3. **Efficient**: Minimal storage and computation requirements
4. **Complete**: Tracks total duration and current hold time
5. **Independent**: No reliance on Twilio API for normal operation

The key insight is that we don't need complex data structures or external APIs to track call durations effectively. By storing just three timestamps (call start, call end, and last hold start), we can calculate all the duration information we need on demand.

This approach is easy to implement, requires minimal changes to the existing codebase, and provides all the functionality needed for the Hero Command and Control application.