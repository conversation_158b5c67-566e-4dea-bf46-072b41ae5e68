-- +goose Up
-- +goose StatementBegin
CREATE TABLE pre_registration_user_mappings (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    email VARCHAR(255) NOT NULL UNIQUE,
    org_id INTEGER NOT NULL REFERENCES orgs(id),
    role_name VA<PERSON>HAR(100) NOT NULL,
    created_at TIMESTAMP DEFAULT NOW(),
    used_at TIMESTAMP NULL,
    created_by VA<PERSON>HAR(255) NOT NULL DEFAULT 'system'
);

-- Index for fast lookup by email during login
CREATE INDEX idx_pre_registration_user_mappings_email ON pre_registration_user_mappings(email);

-- Index for admin queries by org
CREATE INDEX idx_pre_registration_user_mappings_org_id ON pre_registration_user_mappings(org_id);

-- Index for unused mappings
CREATE INDEX idx_pre_registration_user_mappings_unused ON pre_registration_user_mappings(email) WHERE used_at IS NULL;
-- +goose StatementEnd

-- +goose Down
-- +goose StatementBegin
DROP TABLE IF EXISTS pre_registration_user_mappings;
-- +goose StatementEnd