-- +goose Up
-- +goose StatementBegin

/*───────────────────────────────────────────────────────────────────────────────
 hero.filerepository.v1 – relational schema
───────────────────────────────────────────────────────────────────────────────
  ⬢  Multi‑tenant RLS enforced by org_id
  ⬢  Supports cloud-agnostic file storage with metadata tracking
  ⬢  File lifecycle management: PENDING → ACTIVE → ARCHIVED → DELETED
  ⬢  Access logging and audit trail for compliance
  ⬢  Full-text search capabilities with GIN indexes
  ⬢  File size limit: 250MB maximum enforced at database level
───────────────────────────────────────────────────────────────────────────────*/

-- Extensions
CREATE EXTENSION IF NOT EXISTS pg_trgm;     -- trigram support for search
CREATE EXTENSION IF NOT EXISTS pgcrypto;    -- gen_random_uuid()

/*==============================================================================
  1. Main table: files (FileMetadata)
==============================================================================*/
CREATE TABLE files (
    -- Core identity
    id TEXT PRIMARY KEY DEFAULT gen_random_uuid()::text,
    org_id INTEGER NOT NULL REFERENCES orgs(id) ON DELETE CASCADE,
    owner_id TEXT NOT NULL,
    
    -- File metadata
    file_name TEXT NOT NULL,
    file_type TEXT,  -- MIME type
    file_size INTEGER DEFAULT 0 CHECK (file_size >= 0 AND file_size <= 262144000), -- 250MB = 250 * 1024 * 1024 = 262144000 bytes
    
    -- Timestamps
    created_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
    last_accessed TIMESTAMPTZ,
    
    -- Lifecycle management
    status INTEGER NOT NULL DEFAULT 1,  -- FileStatus enum: 1=PENDING, 2=ACTIVE, 3=ARCHIVED, 4=DELETED
    
    -- Cloud storage fields (provider-agnostic)
    provider INTEGER DEFAULT 0,         -- StorageProvider enum
    storage_key TEXT,                   -- object key/path in cloud storage
    bucket_name TEXT,                   -- cloud storage bucket/container
    object_version TEXT,                -- version identifier if versioning enabled
    integrity_hash TEXT,                -- ETag, MD5, CRC32C from cloud provider
    storage_class INTEGER DEFAULT 1,    -- StorageClass enum: 1=HOT, 2=WARM, 3=COLD, 4=FROZEN
    encryption_key_id TEXT,             -- KMS key ID or customer key reference
    checksum TEXT,                      -- application-level checksum (SHA-256)
    
    -- Metadata and tags
    storage_tags JSONB,                 -- key/value tags for cloud storage object
    provider_metadata JSONB,            -- provider-specific metadata
    extra_metadata JSONB,               -- application-level custom metadata
    
    -- Analytics and access
    download_count INTEGER DEFAULT 0,
    is_public BOOLEAN DEFAULT FALSE,
    thumbnail_url TEXT
);

COMMENT ON TABLE files IS 'File metadata and cloud storage references for multi-tenant file repository';
COMMENT ON COLUMN files.status IS 'FileStatus enum: 0=UNSPECIFIED, 1=PENDING, 2=ACTIVE, 3=ARCHIVED, 4=DELETED';
COMMENT ON COLUMN files.provider IS 'StorageProvider enum: 0=UNSPECIFIED, 1=AWS_S3, 2=GCP_GCS, 3=AZURE_BLOB, 4=MINIO';
COMMENT ON COLUMN files.storage_class IS 'StorageClass enum: 0=UNSPECIFIED, 1=HOT, 2=WARM, 3=COLD, 4=FROZEN';
COMMENT ON COLUMN files.file_size IS 'File size in bytes, maximum 250MB (262144000 bytes)';

-- Row Level Security
ALTER TABLE files ENABLE ROW LEVEL SECURITY;

CREATE POLICY files_organization_access
ON files
USING (
  org_id = ANY(
    string_to_array(
      coalesce(current_setting('app.allowed_org_ids', true),''),
      ','
    )::integer[]
  )
)
WITH CHECK (
  org_id = ANY(
    string_to_array(
      coalesce(current_setting('app.allowed_org_ids', true),''),
      ','
    )::integer[]
  )
  );

-- Unique constraint for storage_key + bucket_name ensures data integrity
-- Even though storage keys use UUIDs (mathematically unique), database constraints provide:
-- 1. Protection against bugs in UUID generation logic
-- 2. Defense against race conditions or data corruption  
-- 3. Enforcement during data migrations or manual operations
-- 4. Clear error messages when violations occur
-- 5. Database-level guarantee independent of application logic
CREATE UNIQUE INDEX idx_files_storage_key_bucket ON files(storage_key, bucket_name) 
WHERE storage_key IS NOT NULL AND bucket_name IS NOT NULL;

-- Indexes for performance (only what's actually needed)
CREATE INDEX idx_files_owner_id ON files(owner_id);                        -- ListFiles, SearchFiles
CREATE INDEX idx_files_status ON files(status);                            -- ListFiles, SearchFiles, CleanupPendingUploads
CREATE INDEX idx_files_file_type ON files(file_type) WHERE file_type IS NOT NULL;  -- SearchFiles file_types filter
CREATE INDEX idx_files_storage_class ON files(storage_class);              -- SearchFiles storage_classes filter
CREATE INDEX idx_files_public ON files(is_public) WHERE is_public = TRUE;  -- SearchFiles public_only filter

-- JSONB indexes for SearchFiles tag filters
CREATE INDEX gin_files_storage_tags ON files USING gin (storage_tags);
CREATE INDEX gin_files_extra_metadata ON files USING gin (extra_metadata);

-- Search indexes
CREATE INDEX idx_files_file_name_trgm ON files USING gin (file_name gin_trgm_ops);  -- SearchFiles query (fuzzy search)
CREATE INDEX idx_files_id_pattern ON files(id text_pattern_ops);                   -- Partial ID search (LIKE queries)
CREATE INDEX idx_files_file_type_pattern ON files(file_type text_pattern_ops) WHERE file_type IS NOT NULL;  -- Partial MIME type search

-- Timestamp indexes for SearchFiles date range filters and CleanupPendingUploads
CREATE INDEX brin_files_created_at ON files USING brin (created_at);
CREATE INDEX brin_files_updated_at ON files USING brin (updated_at);
CREATE INDEX brin_files_last_accessed ON files USING brin (last_accessed);

-- Critical composite indexes for common query patterns
CREATE INDEX idx_files_owner_status ON files(owner_id, status);           -- ListFiles common pattern
CREATE INDEX idx_files_status_created ON files(status, created_at);       -- CleanupPendingUploads
CREATE INDEX idx_files_download_count ON files(download_count);           -- SearchFiles download_range filter
CREATE INDEX idx_files_file_size ON files(file_size);                     -- SearchFiles size_range filter

-- Text pattern index for partial matching (if needed for SearchFiles query field)
CREATE INDEX idx_files_file_name_pattern ON files(file_name text_pattern_ops);

-- Auto-update timestamps trigger
CREATE OR REPLACE FUNCTION touch_files_update_time()
RETURNS trigger LANGUAGE plpgsql AS $$
BEGIN
  NEW.updated_at := NOW();
  RETURN NEW;
END; $$;

CREATE TRIGGER files_update_time_trg
  BEFORE UPDATE ON files
  FOR EACH ROW EXECUTE FUNCTION touch_files_update_time();

/*==============================================================================
  2. Access logs table (AccessLogEntry)
==============================================================================*/
CREATE TABLE file_access_logs (
    id TEXT PRIMARY KEY DEFAULT gen_random_uuid()::text,
    file_id TEXT NOT NULL REFERENCES files(id) ON DELETE CASCADE,
    user_id TEXT NOT NULL,
    action TEXT NOT NULL,           -- "download", "view", "share", "delete", "update"
    timestamp TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
    ip_address INET,
    user_agent TEXT,
    referrer TEXT,
    file_size INTEGER,              -- file size at time of access
    session_id TEXT,
    metadata JSONB,                 -- additional context
    org_id INTEGER NOT NULL REFERENCES orgs(id) ON DELETE CASCADE
);

COMMENT ON TABLE file_access_logs IS 'Audit trail and analytics for file access events';
COMMENT ON COLUMN file_access_logs.action IS 'Action type: download, view, share, delete, update, etc.';

-- Row Level Security  
ALTER TABLE file_access_logs ENABLE ROW LEVEL SECURITY;

CREATE POLICY file_access_logs_organization_access
ON file_access_logs
USING (
  org_id = ANY(
    string_to_array(
      coalesce(current_setting('app.allowed_org_ids', true),''),
      ','
    )::integer[]
  )
)
WITH CHECK (
  org_id = ANY(
    string_to_array(
      coalesce(current_setting('app.allowed_org_ids', true),''),
      ','
    )::integer[]
  )
);

-- Indexes for access logs (minimal for GetAccessLog queries)
CREATE INDEX idx_file_access_logs_file_id ON file_access_logs(file_id);             -- GetAccessLog primary filter
CREATE INDEX idx_file_access_logs_file_timestamp ON file_access_logs(file_id, timestamp DESC);  -- GetAccessLog with pagination
CREATE INDEX idx_file_access_logs_user_id ON file_access_logs(user_id) WHERE user_id IS NOT NULL;     -- GetAccessLog user filter (sparse)
CREATE INDEX idx_file_access_logs_action ON file_access_logs(action) WHERE action IS NOT NULL;        -- GetAccessLog action filter (sparse)

-- BRIN index for efficient timestamp range queries (GetAccessLog time_range filter)
CREATE INDEX brin_file_access_logs_timestamp ON file_access_logs USING brin (timestamp);

-- +goose StatementEnd

-- +goose Down
-- +goose StatementBegin

-- Drop triggers and functions
DROP TRIGGER IF EXISTS files_update_time_trg ON files;
DROP FUNCTION IF EXISTS touch_files_update_time();

-- Drop policies
DROP POLICY IF EXISTS file_access_logs_organization_access ON file_access_logs;
DROP POLICY IF EXISTS files_organization_access ON files;

-- Disable RLS
ALTER TABLE file_access_logs DISABLE ROW LEVEL SECURITY;
ALTER TABLE files DISABLE ROW LEVEL SECURITY;

-- Drop tables (cascade will handle indexes)
DROP TABLE IF EXISTS file_access_logs;
DROP TABLE IF EXISTS files;

-- +goose StatementEnd
