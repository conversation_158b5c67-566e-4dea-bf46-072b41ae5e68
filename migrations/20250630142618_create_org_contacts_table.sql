-- +goose Up
-- +goose StatementBegin
CREATE TABLE org_contacts_book (
    id TEXT PRIMARY KEY,
    org_id INTEGER NOT NULL REFERENCES orgs(id) ON DELETE CASCADE,
    name TEXT NOT NULL,
    phone TEXT NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Row-level security
ALTER TABLE org_contacts_book ENABLE ROW LEVEL SECURITY;
CREATE POLICY "org_contacts_organization_access" ON org_contacts_book
    USING (org_id::text = current_setting('app.org_id'))
    WITH CHECK (org_id::text = current_setting('app.org_id'));

CREATE POLICY "org_contacts_meta_org_access" ON org_contacts_book
    USING (current_setting('app.org_id') = '-1')
    WITH CHECK (current_setting('app.org_id') = '-1');

-- Add index for org_id for efficient org-scoped queries
CREATE INDEX idx_org_contacts_org_id ON org_contacts_book(org_id);

-- Add index for phone number lookups (for caller ID matching)
CREATE INDEX idx_org_contacts_phone ON org_contacts_book(phone);

-- Add unique constraint to prevent duplicate phone numbers within an org
CREATE UNIQUE INDEX idx_org_contacts_org_phone_unique ON org_contacts_book(org_id, phone);

-- +goose StatementEnd

-- +goose Down
-- +goose StatementBegin
-- Drop policies and disable RLS
DROP POLICY IF EXISTS "org_contacts_meta_org_access" ON org_contacts_book;
DROP POLICY IF EXISTS "org_contacts_organization_access" ON org_contacts_book;
ALTER TABLE org_contacts_book DISABLE ROW LEVEL SECURITY;

-- Drop table
DROP TABLE IF EXISTS org_contacts_book;
-- +goose StatementEnd