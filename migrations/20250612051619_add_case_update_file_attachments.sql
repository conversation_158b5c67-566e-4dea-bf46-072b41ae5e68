-- +goose Up
-- +goose StatementBegin
/*───────────────────────────────────────────────────────────────────────────────
 Case Update File Attachments Table
 ───────────────────────────────────────────────────────────────────────────────
 ⬢  Stores file attachment references for case update entries
 ⬢  Links to filerepository service via file_id 
 ⬢  Follows same RLS and indexing patterns as other case tables
 ⬢  Supports ordering and categorization of attachments
 ───────────────────────────────────────────────────────────────────────────────*/
/*==============================================================================
 CASE UPDATE FILE ATTACHMENTS TABLE
 ==============================================================================*/
CREATE TABLE case_update_file_attachments (
    id TEXT PRIMARY KEY DEFAULT gen_random_uuid()::text,
    case_id TEXT NOT NULL REFERENCES cases(id) ON DELETE CASCADE,
    case_update_id INTEGER NOT NULL REFERENCES case_updates(id) ON DELETE CASCADE,
    file_id TEXT NOT NULL,
    caption TEXT,
    display_name TEXT,
    display_order INTEGER DEFAULT 0,
    file_category TEXT,
    metadata JSONB,
    org_id INTEGER NOT NULL REFERENCES orgs(id) ON DELETE CASCADE,
    created_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP
);
COMMENT ON TABLE case_update_file_attachments IS 'File attachment references for case update entries, linking to filerepository service.';
COMMENT ON COLUMN case_update_file_attachments.file_id IS 'References FileMetadata.id from filerepository service - soft FK.';
COMMENT ON COLUMN case_update_file_attachments.file_category IS 'Categories: evidence_photo, evidence_video, evidence_audio, evidence_document, correspondence, other.';
-- RLS Policy
ALTER TABLE case_update_file_attachments ENABLE ROW LEVEL SECURITY;
CREATE POLICY case_update_file_attachments_organization_access ON case_update_file_attachments USING (
    org_id = ANY(
        string_to_array(
            coalesce(current_setting('app.allowed_org_ids', true), ''),
            ','
        )::integer []
    )
) WITH CHECK (
    org_id = ANY(
        string_to_array(
            coalesce(current_setting('app.allowed_org_ids', true), ''),
            ','
        )::integer []
    )
);
CREATE INDEX idx_case_update_file_attachments_case_id ON case_update_file_attachments(case_id);
CREATE INDEX idx_case_update_file_attachments_case_update_id ON case_update_file_attachments(case_update_id);
-- Add composite index for efficient batch loading
-- This index optimizes queries that load file attachments for multiple case updates
-- using "WHERE case_update_id = ANY($1) ORDER BY case_update_id, display_order, id"
CREATE INDEX idx_case_update_file_attachments_batch_loading ON case_update_file_attachments(case_update_id, display_order, id);
COMMENT ON INDEX idx_case_update_file_attachments_batch_loading IS 'Composite index optimized for batch loading file attachments across multiple case updates. Supports efficient queries with case_update_id = ANY() and maintains sort order by display_order, id.';
-- +goose StatementEnd
-- +goose Down
-- +goose StatementBegin
DROP INDEX IF EXISTS idx_case_update_file_attachments_batch_loading;
DROP TABLE IF EXISTS case_update_file_attachments;
-- +goose StatementEnd