import React, { createContext, useContext, useRef, ReactNode } from "react";
import { MapBottomSheetRef } from "../components/MapBottomSheet";

interface NavigationContextType {
  mapBottomSheetRef: React.RefObject<MapBottomSheetRef>;
  navigateToIncident: (incident: any) => void;
  navigateToUnit: (unit: any) => void;
  navigateToList: () => void;
  closeDrawer: () => void;
  setDrawerCloseCallback: (callback: () => void) => void;
}

const NavigationContext = createContext<NavigationContextType | undefined>(undefined);

interface NavigationProviderProps {
  children: ReactNode;
}

export const NavigationProvider: React.FC<NavigationProviderProps> = ({ children }) => {
  const mapBottomSheetRef = useRef<MapBottomSheetRef>(null);
  const drawerCloseCallbackRef = useRef<(() => void) | null>(null);

  const navigateToIncident = (incident: any) => {
    mapBottomSheetRef.current?.navigateToIncident(incident);
  };

  const navigateToUnit = (unit: any) => {
    mapBottomSheetRef.current?.navigateToUnit(unit);
  };

  const navigateToList = () => {
    mapBottomSheetRef.current?.navigateToList();
  };

  const closeDrawer = () => {
    if (drawerCloseCallbackRef.current) {
      drawerCloseCallbackRef.current();
    }
  };

  const setDrawerCloseCallback = (callback: () => void) => {
    drawerCloseCallbackRef.current = callback;
  };

  const value = {
    mapBottomSheetRef,
    navigateToIncident,
    navigateToUnit,
    navigateToList,
    closeDrawer,
    setDrawerCloseCallback,
  };

  return (
    <NavigationContext.Provider value={value}>
      {children}
    </NavigationContext.Provider>
  );
};

export const useNavigation = (): NavigationContextType => {
  const context = useContext(NavigationContext);
  if (!context) {
    throw new Error("useNavigation must be used within a NavigationProvider");
  }
  return context;
}; 