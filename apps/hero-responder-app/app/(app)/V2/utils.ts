export const getPriorityColor = (priority: number) => {
  switch (priority) {
    case 1:
      return "#FF4444";
    case 2:
      return "#FF8800";
    case 3:
      return "#0066CC";
    case 4:
      return "#666666";
    default:
      return "#666666";
  }
};

export const situationTypes = [
  { value: "SITUATION_TYPE_ALCOHOL_VIOLATION", label: "Alcohol Violation" },
  { value: "SITUATION_TYPE_ANIMAL_INCIDENT", label: "Animal Incident" },
  { value: "SITUATION_TYPE_BURGLARY", label: "Burglary" },
  { value: "SITUATION_TYPE_DRUG_ACTIVITY", label: "Drug Activity" },
  { value: "SITUATION_TYPE_FIRE", label: "Fire" },
  { value: "SITUATION_TYPE_FIGHT", label: "Fight" },
  { value: "SITUATION_TYPE_GAS_LEAK", label: "Gas Leak" },
  { value: "SITUATION_TYPE_HAZARDOUS_MATERIAL", label: "Hazardous Material" },
  { value: "SITUATION_TYPE_INTRUSION", label: "Intrusion" },
  { value: "SITUATION_TYPE_LOCKOUT", label: "Lockout" },
  { value: "SITUATION_TYPE_LOST_PERSON", label: "Lost Person" },
  { value: "SITUATION_TYPE_MEDICAL_EMERGENCY", label: "Medical Emergency" },
  { value: "SITUATION_TYPE_NOISE_COMPLAINT", label: "Noise Complaint" },
  { value: "SITUATION_TYPE_OTHER", label: "Other" },
  { value: "SITUATION_TYPE_PARKING_VIOLATION", label: "Parking Violation" },
  { value: "SITUATION_TYPE_POWER_OUTAGE", label: "Power Outage" },
  { value: "SITUATION_TYPE_PUBLIC_DISTURBANCE", label: "Public Disturbance" },
  { value: "SITUATION_TYPE_SITUATION_TYPE_UNSPECIFIED", label: "Unspecified" },
  { value: "SITUATION_TYPE_SUSPICIOUS_ACTIVITY", label: "Suspicious Activity" },
  { value: "SITUATION_TYPE_SUSPICIOUS_PACKAGE", label: "Suspicious Package" },
  { value: "SITUATION_TYPE_THEFT", label: "Theft" },
  { value: "SITUATION_TYPE_TRESPASSING", label: "Trespassing" },
  { value: "SITUATION_TYPE_TRAFFIC_ACCIDENT", label: "Traffic Accident" },
  { value: "SITUATION_TYPE_VANDALISM", label: "Vandalism" },
  { value: "SITUATION_TYPE_VEHICLE_ACCIDENT", label: "Vehicle Accident" },
  { value: "SITUATION_TYPE_WATER_LEAK", label: "Water Leak" },
  { value: "SITUATION_TYPE_WEAPON_DETECTED", label: "Weapon Detected" },
];

export const getSituationType = (type: string) => {
  return situationTypes.find((t) => t.value === type)?.label;
};
