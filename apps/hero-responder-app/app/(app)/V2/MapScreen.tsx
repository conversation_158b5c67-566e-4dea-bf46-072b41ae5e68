import { Ionicons } from "@expo/vector-icons";
import MapboxGL, { LocationPuck } from "@rnmapbox/maps";
import * as Location from "expo-location";
import * as Updates from "expo-updates";
import { jwtDecode } from "jwt-decode";
import {
  AssetStatus,
  UpdateAssetRequest,
} from "proto/hero/assets/v2/assets_pb";
import {
  AcknowledgeOrderRequest,
  ListActiveAssignedOrdersForAssetRequest,
  Order,
  RejectOrderRequest,
  UpdateOrderRequest,
} from "proto/hero/orders/v2/orders_pb";
import { AddSituationUpdateRequest } from "proto/hero/situations/v2/situations_pb";
import React, { useEffect, useRef, useState } from "react";
import {
  Alert,
  Animated,
  AppState,
  AppStateStatus,
  Dimensions,
  Image,
  Platform,
  StyleSheet,
  Text,
  TouchableOpacity,
  View,
} from "react-native";
import { Drawer } from "react-native-drawer-layout";
import {
  useGetAssetByCognitoSub,
  useUpdateAsset,
  useUpdateAssetLocation,
} from "../../apis/services/workflow/assets/hooks";
import {
  hookOrderStatusToString,
  hookOrderTypeToString,
  stringToOrderStatus,
} from "../../apis/services/workflow/orders/enumConverters";
import {
  useAcknowledgeOrder,
  useListActiveAssignedOrdersForAsset,
  useRejectOrder,
  useUpdateOrder,
} from "../../apis/services/workflow/orders/hooks";
import {
  useAddSituationUpdate,
  useGetSituation,
  useListSituations,
} from "../../apis/services/workflow/situations/hooks";
import { useAuth } from "../../AuthContext";
import { createUpdateEntry } from "../utils";
import { AssetMarkersContainer } from "./components/AssetMarkersContainer";
import { AssignedIncidentCard } from "./components/AssignedIncidentCard";
import { MapBottomSheet } from "./components/MapBottomSheet";
import MapHeader from "./components/MapHeader";
import SideMenu from "./components/SideMenu";
import { SituationMarkersContainer } from "./components/SituationMarkersContainer";
import { UpdateBanner } from "./components/UpdateBanner";
import { useNavigation } from "./contexts/NavigationContext";
import { DEFAULT_LOCATION } from "./costants";
import { LocationData } from "./types";

const { width } = Dimensions.get("window");

// Set Mapbox access token
MapboxGL.setAccessToken(
  "pk.eyJ1IjoiaGVyby1zYWZldHkiLCJhIjoiY202bDM3bGlzMDRybTJrcHJibm5sYTFzMSJ9.-ekjZGG1E_cWYCOnBrdEag"
);

// Helper function to get user-friendly order status labels (matching web interface)
const getOrderStatusLabel = (status: string): string => {
  const statusMap = {
    ORDER_STATUS_UNSPECIFIED: "Unspecified",
    ORDER_STATUS_CREATED: "Dispatched",
    ORDER_STATUS_ACKNOWLEDGED: "En-route",
    ORDER_STATUS_REJECTED: "Rejected",
    ORDER_STATUS_SNOOZED: "Snoozed",
    ORDER_STATUS_IN_PROGRESS: "On Scene",
    ORDER_STATUS_COMPLETED: "Completed",
    ORDER_STATUS_CANCELLED: "Cancelled",
  };
  return statusMap[status as keyof typeof statusMap] || status;
};

// Mapbox Directions API
const MAPBOX_ACCESS_TOKEN =
  "pk.eyJ1IjoiaGVyby1zYWZldHkiLCJhIjoiY202bDM3bGlzMDRybTJrcHJibm5sYTFzMSJ9.-ekjZGG1E_cWYCOnBrdEag";

interface RouteInfo {
  distance: number;
  duration: number;
  geometry: {
    type: string;
    coordinates: [number, number][];
  };
}

interface DecodedToken {
  email: string;
  sub: string;
}

// Function to calculate distance between two coordinates in meters using Haversine formula
const calculateDistance = (
  lat1: number,
  lon1: number,
  lat2: number,
  lon2: number
): number => {
  const R = 6371e3; // Earth's radius in meters
  const φ1 = (lat1 * Math.PI) / 180;
  const φ2 = (lat2 * Math.PI) / 180;
  const Δφ = ((lat2 - lat1) * Math.PI) / 180;
  const Δλ = ((lon2 - lon1) * Math.PI) / 180;

  const a =
    Math.sin(Δφ / 2) * Math.sin(Δφ / 2) +
    Math.cos(φ1) * Math.cos(φ2) * Math.sin(Δλ / 2) * Math.sin(Δλ / 2);
  const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));

  return R * c; // Distance in meters
};

// Function to fetch directions from Mapbox
const fetchDirections = async (
  start: LocationData,
  end: LocationData
): Promise<RouteInfo> => {
  const { latitude: lat1, longitude: lon1 } = start;
  const { latitude: lat2, longitude: lon2 } = end;

  const url = `https://api.mapbox.com/directions/v5/mapbox/walking/${lon1},${lat1};${lon2},${lat2}`;

  console.log(
    "[Mapbox Directions API] Fetching directions from",
    { lat1, lon1 },
    "to",
    { lat2, lon2 }
  );

  try {
    const urlWithParams = `${url}?geometries=geojson&access_token=${MAPBOX_ACCESS_TOKEN}&alternatives=false&overview=full`;

    const fullResponse = await fetch(urlWithParams);
    const data = await fullResponse.json();

    console.log("[Mapbox Directions API] Response received", data);

    if (data && data.routes && data.routes.length > 0) {
      const route = data.routes[0];
      return {
        distance: route.distance,
        duration: route.duration,
        geometry: route.geometry,
      };
    }

    throw new Error("No routes found");
  } catch (error) {
    console.error("[Mapbox Directions API] Error:", error);
    throw error;
  }
};

export default function MapScreen() {
  const { authTokens } = useAuth();
  const { mapBottomSheetRef, setDrawerCloseCallback } = useNavigation();
  let cognitoJwtSub = "";

  if (authTokens?.idToken) {
    try {
      const decoded = jwtDecode<DecodedToken>(authTokens.idToken);
      cognitoJwtSub = decoded.sub;
    } catch (error) {
      console.error("Error decoding idToken:", error);
    }
  }

  // User Information and Mutations
  const updateLocationMutation = useUpdateAssetLocation();
  const updateAssetMutation = useUpdateAsset();
  const acknowledgeOrder = useAcknowledgeOrder();
  const rejectOrder = useRejectOrder();
  const updateOrderMutation = useUpdateOrder();
  const addSituationUpdate = useAddSituationUpdate();
  const { data: getAssetByCongitoSubData } =
    useGetAssetByCognitoSub(cognitoJwtSub);
  const userInfoFromServer = getAssetByCongitoSubData?.asset;

  // Fetch situations data to keep selectedIncident updated
  const { data: situationsData } = useListSituations({} as any);

  // Orders polling with explicit refetch and logging
  const { data: activeOrdersData, refetch: refetchOrders } =
    useListActiveAssignedOrdersForAsset(
      {
        assetId: userInfoFromServer?.id || "",
      } as ListActiveAssignedOrdersForAssetRequest,
      5000
    );

  const activeOrders = activeOrdersData?.orders ?? ([] as Order[]);

  // Set up polling for orders every 5 seconds with logging
  useEffect(() => {
    if (!userInfoFromServer?.id) {
      return;
    }

    console.log(
      `[Orders Poll] Setting up orders polling for asset ${userInfoFromServer.id}`
    );

    const interval = setInterval(() => {
      console.log(
        `[Orders Poll] Refetching orders for asset ${userInfoFromServer.id} at`,
        new Date().toISOString()
      );
      refetchOrders();
    }, 5000);

    return () => {
      console.log(
        `[Orders Poll] Clearing orders polling interval for asset ${userInfoFromServer.id}`
      );
      clearInterval(interval);
    };
  }, [refetchOrders, userInfoFromServer?.id]);

  // Find the first order that is assist member and not rejected/cancelled/completed
  const firstAssistMemberOrder = activeOrders?.find(
    (order) =>
      hookOrderTypeToString(order.type) == "ORDER_TYPE_ASSIST_MEMBER" &&
      hookOrderStatusToString(order.status) !== "ORDER_STATUS_REJECTED" &&
      hookOrderStatusToString(order.status) !== "ORDER_STATUS_CANCELLED" &&
      hookOrderStatusToString(order.status) !== "ORDER_STATUS_COMPLETED"
  );

  // State to track if the current incident was cleared by the user
  const [currentIncidentClearedByUser, setCurrentIncidentClearedByUser] =
    useState(false);

  // Get the situationId from the first non-completed order
  const situationIdFromCurrentOrder = firstAssistMemberOrder?.situationId;
  const respondingToSituation = useGetSituation(
    situationIdFromCurrentOrder || "",
    5000
  ).data;

  const [location, setLocation] = useState<LocationData | null>(null);
  const [hasLocationPermission, setHasLocationPermission] =
    useState<boolean>(false);
  const mapRef = useRef<MapboxGL.MapView>(null);
  const cameraRef = useRef<MapboxGL.Camera>(null);
  const locationUpdateInterval = useRef<NodeJS.Timeout | null>(null);
  const [drawerOpen, setDrawerOpen] = useState(false);
  const [bottomSheetIndex, setBottomSheetIndex] = useState(0);
  const hasCentered = useRef(false);
  const currentLocationRef = useRef<LocationData | null>(null);
  const [selectedUnit, setSelectedUnit] = useState<any | null>(null);
  const [selectedIncident, setSelectedIncident] = useState<any | null>(null);
  const [hasInitiallyFocused, setHasInitiallyFocused] = useState(false);
  const [hasShownIncomingIncident, setHasShownIncomingIncident] =
    useState(false);
  const [currentBottomSheetViewType, setCurrentBottomSheetViewType] =
    useState<string>("list");
  const [optimisticAssistingStatus, setOptimisticAssistingStatus] = useState<
    string | null
  >(null);

  // Add loading states for confirm and reject operations
  const [confirmLoading, setConfirmLoading] = useState(false);
  const [rejectLoading, setRejectLoading] = useState(false);

  // Add state for route
  const [route, setRoute] = useState<RouteInfo | null>(null);
  const routePollingInterval = useRef<NodeJS.Timeout | null>(null);

  // Add ref to track selected incident ID to avoid circular dependencies
  const selectedIncidentIdRef = useRef<string | null>(null);

  // Add ref to track current selected incident to avoid closure issues in polling
  const selectedIncidentRef = useRef<any>(null);

  // Add ref to track app state for route recalculation
  const appState = useRef(AppState.currentState);

  // Add state for floating action button
  const [floatingButtonState, setFloatingButtonState] = useState({
    shouldShow: false,
    onPress: () => {},
    currentView: null as any,
    tabIndex: 0,
  });

  // Animation for floating action button
  const buttonOpacity = useRef(new Animated.Value(0)).current;

  // Update-related state
  const [updateAvailable, setUpdateAvailable] = useState(false);
  const [isUpdating, setIsUpdating] = useState(false);
  const [updateDismissed, setUpdateDismissed] = useState(false);
  const updateBannerSlideAnim = useRef(new Animated.Value(0)).current;

  // Animate button appearance/disappearance
  useEffect(() => {
    if (floatingButtonState.shouldShow) {
      // Fade in
      Animated.timing(buttonOpacity, {
        toValue: 1,
        duration: 300,
        useNativeDriver: true,
      }).start();
    } else {
      // Fade out
      Animated.timing(buttonOpacity, {
        toValue: 0,
        duration: 200,
        useNativeDriver: true,
      }).start();
    }
  }, [floatingButtonState.shouldShow, buttonOpacity]);

  // Check for updates on app startup and periodically
  useEffect(() => {
    // Don't check for updates in development
    if (__DEV__) {
      console.log("[Updates] Skipping update check in development mode");
      return;
    }

    const checkForUpdates = async () => {
      try {
        console.log("[Updates] Checking for updates...");
        const update = await Updates.checkForUpdateAsync();

        if (update.isAvailable) {
          console.log("[Updates] Update available");
          setUpdateAvailable(true);
          setUpdateDismissed(false);
        } else {
          console.log("[Updates] No updates available");
          setUpdateAvailable(false);
        }
      } catch (error) {
        console.error("[Updates] Error checking for updates:", error);
      }
    };

    // Check immediately on mount
    checkForUpdates();

    // Check every 30 minutes
    const interval = setInterval(checkForUpdates, 30 * 60 * 1000);

    return () => clearInterval(interval);
  }, []);

  // Animate update banner
  useEffect(() => {
    if (updateAvailable && !updateDismissed && !isUpdating) {
      Animated.timing(updateBannerSlideAnim, {
        toValue: 1,
        duration: 300,
        useNativeDriver: true,
      }).start();
    } else {
      Animated.timing(updateBannerSlideAnim, {
        toValue: 0,
        duration: 200,
        useNativeDriver: true,
      }).start();
    }
  }, [updateAvailable, updateDismissed, isUpdating, updateBannerSlideAnim]);

  // Handle update button press
  const handleUpdatePress = async () => {
    if (isUpdating) return;

    setIsUpdating(true);

    try {
      console.log("[Updates] Fetching and applying update...");
      await Updates.fetchUpdateAsync();
      console.log("[Updates] Update downloaded, reloading app...");
      await Updates.reloadAsync();
    } catch (error) {
      console.error("[Updates] Error updating app:", error);
      Alert.alert(
        "Update Failed",
        "Failed to update the app. Please try again later.",
        [{ text: "OK" }]
      );
      setIsUpdating(false);
    }
  };

  // Handle dismiss update banner
  const handleDismissUpdate = () => {
    setUpdateDismissed(true);
  };

  // Register drawer close callback with NavigationContext
  React.useEffect(() => {
    setDrawerCloseCallback(() => setDrawerOpen(false));
  }, [setDrawerCloseCallback]);

  // Automatically set asset status to active when app opens only if the asset status is not BUSY
  useEffect(() => {
    if (
      userInfoFromServer?.id &&
      //@ts-expect-error TODO: Fix type issue
      userInfoFromServer?.status !== "ASSET_STATUS_BUSY"
    ) {
      console.log(
        "Setting asset status to AVAILABLE for user:",
        userInfoFromServer.id
      );
      updateAssetMutation.mutate(
        {
          asset: {
            id: userInfoFromServer.id,
            status: AssetStatus.AVAILABLE,
          },
        } as UpdateAssetRequest,
        {
          onSuccess: () => {
            console.log("Asset status successfully set to AVAILABLE");
          },
          onError: (error) => {
            console.error("Failed to set asset status to AVAILABLE:", error);
          },
        }
      );
    }
  }, [userInfoFromServer?.id]);

  // Track current bottom sheet view type
  React.useEffect(() => {
    const interval = setInterval(() => {
      if (mapBottomSheetRef.current) {
        const viewType = mapBottomSheetRef.current.getCurrentViewType();
        setCurrentBottomSheetViewType(viewType);
      }
    }, 100); // Check every 100ms

    return () => clearInterval(interval);
  }, []);

  // Update the ref whenever location changes
  useEffect(() => {
    currentLocationRef.current = location;
  }, [location]);

  // Update selectedIncidentIdRef whenever selectedIncident changes
  useEffect(() => {
    selectedIncidentIdRef.current = selectedIncident?.id || null;
    selectedIncidentRef.current = selectedIncident;
  }, [selectedIncident]);

  useEffect(() => {
    requestLocationPermission();
    return () => {
      if (locationUpdateInterval.current) {
        clearInterval(locationUpdateInterval.current);
      }
    };
  }, []);

  // Update selectedIncident when situations data changes - using ref to avoid circular dependency
  useEffect(() => {
    const selectedIncidentId = selectedIncidentIdRef.current;

    if (!selectedIncidentId || !situationsData?.situations) {
      return;
    }

    // Find the updated incident data
    const updatedIncident = situationsData.situations.find(
      (situation) => situation.id === selectedIncidentId
    );

    if (updatedIncident) {
      // Only update if the incident data has actually changed
      setSelectedIncident((currentSelectedIncident: any) => {
        if (!currentSelectedIncident) return updatedIncident;

        // Check if location data has changed
        const locationChanged =
          updatedIncident.latitude !== currentSelectedIncident.latitude ||
          updatedIncident.longitude !== currentSelectedIncident.longitude;

        if (locationChanged) {
          console.log(
            "[Selected Incident] Location changed from polling data, updating selectedIncident"
          );
          console.log(
            "[Selected Incident] Old location:",
            currentSelectedIncident.latitude,
            currentSelectedIncident.longitude
          );
          console.log(
            "[Selected Incident] New location:",
            updatedIncident.latitude,
            updatedIncident.longitude
          );
        }

        return updatedIncident;
      });
    }
  }, [situationsData]);

  // Check if user is assigned to the selected incident
  const isUserAssignedToSelectedIncident = React.useMemo(() => {
    if (!selectedIncident || !activeOrders) return false;

    return activeOrders.some(
      (order) =>
        hookOrderTypeToString(order.type) === "ORDER_TYPE_ASSIST_MEMBER" &&
        order.situationId === selectedIncident.id &&
        hookOrderStatusToString(order.status) !== "ORDER_STATUS_REJECTED" &&
        hookOrderStatusToString(order.status) !== "ORDER_STATUS_CANCELLED" &&
        hookOrderStatusToString(order.status) !== "ORDER_STATUS_COMPLETED"
    );
  }, [selectedIncident, activeOrders]);

  // Get current incident location from polled situations data
  const currentIncidentLocation = React.useMemo(() => {
    if (!selectedIncident?.id || !situationsData?.situations) return null;

    const currentSituation = situationsData.situations.find(
      (situation) => situation.id === selectedIncident.id
    );

    if (
      !currentSituation ||
      !currentSituation.latitude ||
      !currentSituation.longitude
    ) {
      return null;
    }

    return {
      latitude: currentSituation.latitude,
      longitude: currentSituation.longitude,
    };
  }, [selectedIncident?.id, situationsData?.situations]);

  // Reusable function to fetch route
  const fetchRouteForIncident = async () => {
    const currentLocation = currentLocationRef.current;
    const currentIncidentId = selectedIncidentIdRef.current;

    if (!currentLocation || !currentIncidentId) {
      console.log("[Route] No current location or incident ID for route fetch");
      return;
    }

    // Check if user is assigned to the current incident
    if (!isUserAssignedToSelectedIncident) {
      console.log(
        "[Route] User not assigned to current incident, skipping route fetch"
      );
      return;
    }

    // Get the current incident location from situations data
    const currentSituationsData = situationsData;
    if (!currentSituationsData?.situations) {
      console.log("[Route] No situations data available for route fetch");
      return;
    }

    const currentSituation = currentSituationsData.situations.find(
      (situation) => situation.id === currentIncidentId
    );

    if (
      !currentSituation ||
      !currentSituation.latitude ||
      !currentSituation.longitude
    ) {
      console.log(
        "[Route] No current situation or location data found for route fetch"
      );
      return;
    }

    const incidentLocation: LocationData = {
      latitude: currentSituation.latitude,
      longitude: currentSituation.longitude,
    };

    try {
      console.log(
        "[Route] Fetching directions for incident:",
        currentIncidentId
      );
      console.log("[Route] From:", currentLocation, "To:", incidentLocation);
      const routeData = await fetchDirections(
        currentLocation,
        incidentLocation
      );
      setRoute(routeData);
      console.log("[Route] Route fetched successfully");
    } catch (error) {
      console.error("[Route] Error fetching route:", error);
      setRoute(null);
    }
  };

  // Fetch directions when selected incident changes and user is assigned
  useEffect(() => {
    // Clear route if no incident selected or user not assigned
    if (
      !selectedIncident ||
      !isUserAssignedToSelectedIncident ||
      !currentIncidentLocation
    ) {
      console.log(
        "[Route] Clearing route - no incident selected, user not assigned, or no location data"
      );
      setRoute(null);
      if (routePollingInterval.current) {
        clearInterval(routePollingInterval.current);
        routePollingInterval.current = null;
      }
      return;
    }

    // Fetch route immediately
    fetchRouteForIncident();

    // Set up polling every 30 seconds
    console.log("[Route] Setting up route polling every 30 seconds");
    routePollingInterval.current = setInterval(() => {
      console.log("[Route] Polling for route update");
      fetchRouteForIncident();
    }, 30000);

    // Cleanup on unmount or dependency change
    return () => {
      if (routePollingInterval.current) {
        console.log("[Route] Clearing route polling interval");
        clearInterval(routePollingInterval.current);
        routePollingInterval.current = null;
      }
    };
  }, [
    selectedIncident?.id,
    isUserAssignedToSelectedIncident,
    currentIncidentLocation,
  ]);

  // Refetch route immediately when incident location changes from situations polling
  useEffect(() => {
    if (
      !selectedIncident ||
      !isUserAssignedToSelectedIncident ||
      !location ||
      !currentIncidentLocation ||
      !route
    ) {
      return; // Don't refetch if route not already established
    }

    // Refetch route immediately when incident location changes
    fetchRouteForIncident();
  }, [
    currentIncidentLocation?.latitude,
    currentIncidentLocation?.longitude,
    isUserAssignedToSelectedIncident,
  ]);

  // App state listener to recalculate route when coming from background
  useEffect(() => {
    const handleAppStateChange = (nextAppState: AppStateStatus) => {
      console.log(
        "[AppState] App state changed from",
        appState.current,
        "to",
        nextAppState
      );

      if (
        appState.current.match(/inactive|background/) &&
        nextAppState === "active"
      ) {
        console.log("[AppState] App came to foreground");

        // Check if we should recalculate route
        if (
          selectedIncident &&
          isUserAssignedToSelectedIncident &&
          currentIncidentLocation &&
          location
        ) {
          console.log(
            "[AppState] Recalculating route due to app coming to foreground"
          );
          fetchRouteForIncident();
        }
      }

      appState.current = nextAppState;
    };

    const subscription = AppState.addEventListener(
      "change",
      handleAppStateChange
    );

    return () => {
      subscription?.remove();
    };
  }, [
    selectedIncident,
    isUserAssignedToSelectedIncident,
    currentIncidentLocation,
    location,
  ]);

  // Handle incoming incidents
  useEffect(() => {
    if (firstAssistMemberOrder && respondingToSituation) {
      const orderStatus = hookOrderStatusToString(
        firstAssistMemberOrder.status
      );

      if (orderStatus === "ORDER_STATUS_CREATED" && !hasShownIncomingIncident) {
        // New order that needs acknowledgment - show incoming incident view
        console.log(
          "New incoming incident detected (CREATED status), navigating to incoming incident view"
        );
        mapBottomSheetRef.current?.navigateToIncomingIncident(
          respondingToSituation,
          firstAssistMemberOrder
        );
        setHasShownIncomingIncident(true);
      } else if (orderStatus !== "ORDER_STATUS_CREATED") {
        // Order already acknowledged or in progress - navigate directly to incident
        console.log(
          `Order status is ${orderStatus}, navigating directly to incident detail`
        );
        mapBottomSheetRef.current?.navigateToIncident(respondingToSituation);
        setHasShownIncomingIncident(false); // Reset flag since we're not showing incoming incident
      }
    }

    // Reset flag when order is no longer present
    if (!firstAssistMemberOrder) {
      setHasShownIncomingIncident(false);
    }
  }, [
    firstAssistMemberOrder,
    respondingToSituation?.id,
    hasShownIncomingIncident,
  ]);

  // Handle confirm incident
  const handleConfirmIncident = async () => {
    if (
      !firstAssistMemberOrder ||
      !respondingToSituation ||
      !userInfoFromServer ||
      confirmLoading
    ) {
      return;
    }

    setConfirmLoading(true);

    try {
      const orderId = firstAssistMemberOrder.id;
      if (orderId) {
        console.log("[Order Debug] Acknowledging order:", orderId);

        await acknowledgeOrder.mutateAsync({
          id: orderId,
        } as AcknowledgeOrderRequest);

        await addSituationUpdate.mutateAsync({
          id: respondingToSituation.id || "",
          update: createUpdateEntry(
            getOrderStatusLabel("ORDER_STATUS_ACKNOWLEDGED"),
            "status change",
            userInfoFromServer.id || "",
            userInfoFromServer.name || ""
          ),
        } as AddSituationUpdateRequest);

        // Force refetch orders to get updated status immediately
        console.log(
          "[Order Debug] Forcing orders refetch after acknowledgment"
        );
        await refetchOrders();

        // Navigate to incident detail view
        mapBottomSheetRef.current?.navigateToIncident(
          respondingToSituation,
          true
        );
      }
    } catch (error) {
      console.error("Error confirming incident:", error);
      Alert.alert("Error", "Failed to confirm incident. Please try again.");
    } finally {
      setConfirmLoading(false);
    }
  };

  // Handle reject incident
  const handleRejectIncident = async () => {
    if (
      !firstAssistMemberOrder ||
      !respondingToSituation ||
      !userInfoFromServer ||
      rejectLoading
    ) {
      return;
    }

    setRejectLoading(true);

    try {
      const orderId = firstAssistMemberOrder.id;
      if (orderId) {
        console.log("[Order Debug] Rejecting order:", orderId);

        await rejectOrder.mutateAsync({
          id: orderId,
        } as RejectOrderRequest);

        await addSituationUpdate.mutateAsync({
          id: respondingToSituation.id || "",
          update: createUpdateEntry(
            getOrderStatusLabel("ORDER_STATUS_REJECTED"),
            "status change",
            userInfoFromServer.id || "",
            userInfoFromServer.name || ""
          ),
        } as AddSituationUpdateRequest);

        // Force refetch orders to get updated status immediately
        console.log("[Order Debug] Forcing orders refetch after rejection");
        await refetchOrders();

        // Navigate back to list
        mapBottomSheetRef.current?.navigateToList(true);
        setHasShownIncomingIncident(false);
      }
    } catch (error) {
      console.error("Error rejecting incident:", error);
      Alert.alert("Error", "Failed to reject incident. Please try again.");
    } finally {
      setRejectLoading(false);
    }
  };

  // Periodic Location "Ping" every 10 seconds
  useEffect(() => {
    if (!userInfoFromServer || !userInfoFromServer.id) {
      return;
    }

    console.log(
      "Setting up location ping interval for user:",
      userInfoFromServer.id
    );

    const interval = setInterval(() => {
      const currentLocation = currentLocationRef.current;
      if (currentLocation) {
        console.log(
          "Pinging location update - User:",
          userInfoFromServer.id,
          "Location:",
          currentLocation
        );
        updateLocationMutation.mutate(
          {
            assetId: userInfoFromServer.id,
            latitude: currentLocation.latitude,
            longitude: currentLocation.longitude,
          },
          {
            onSuccess: () => {
              console.log("Location ping successful");
            },
            onError: (error) => {
              console.error("Failed to update location:", error);
            },
          }
        );
      } else {
        console.log("No location available for ping");
      }
    }, 10000);

    return () => {
      console.log("Clearing location ping interval");
      clearInterval(interval);
    };
  }, [userInfoFromServer?.id]);

  // Center Camera on User Location (just the first time the location is available)
  useEffect(() => {
    if (location && cameraRef.current && !hasCentered.current) {
      cameraRef.current.setCamera({
        centerCoordinate: [location.longitude, location.latitude],
        zoomLevel: 14,
        animationDuration: 1000,
      });

      hasCentered.current = true;
    }
  }, [location]);

  const requestLocationPermission = async () => {
    try {
      const { status: foregroundStatus } =
        await Location.requestForegroundPermissionsAsync();

      if (foregroundStatus !== "granted") {
        Alert.alert(
          "Location Permission Required",
          "This app needs location access to show your position on the map.",
          [{ text: "OK" }]
        );
        return;
      }

      // Request background location permission for better tracking
      if (Platform.OS === "ios") {
        const { status: backgroundStatus } =
          await Location.requestBackgroundPermissionsAsync();
        console.log("Background location permission:", backgroundStatus);
      }

      setHasLocationPermission(true);
      startLocationTracking();
    } catch (error) {
      console.error("Error requesting location permission:", error);
      Alert.alert("Error", "Failed to request location permission");
    }
  };

  const startLocationTracking = async () => {
    try {
      // First, try to get last known position for immediate display
      try {
        const lastKnownLocation = await Location.getLastKnownPositionAsync({});
        if (lastKnownLocation) {
          const quickLocationData: LocationData = {
            latitude: lastKnownLocation.coords.latitude,
            longitude: lastKnownLocation.coords.longitude,
          };
          setLocation(quickLocationData);
          console.log(
            "Set initial location from last known position:",
            quickLocationData
          );
        }
      } catch (error) {
        console.log("No last known position available:", error);
      }

      // Get initial location with balanced accuracy for speed
      const initialLocation = await Location.getCurrentPositionAsync({
        accuracy: Location.Accuracy.Balanced,
      });

      const locationData: LocationData = {
        latitude: initialLocation.coords.latitude,
        longitude: initialLocation.coords.longitude,
      };

      setLocation(locationData);
      console.log("Set initial accurate location:", locationData);

      // Set up interval to update location every 5 seconds with high accuracy
      locationUpdateInterval.current = setInterval(async () => {
        try {
          const currentLocation = await Location.getCurrentPositionAsync({
            accuracy: Location.Accuracy.High,
          });

          const updatedLocationData: LocationData = {
            latitude: currentLocation.coords.latitude,
            longitude: currentLocation.coords.longitude,
          };

          setLocation(updatedLocationData);
          console.log("updatedLocationData", updatedLocationData);
        } catch (error) {
          console.error("Error updating location:", error);
        }
      }, 5000);
    } catch (error) {
      console.error("Error getting initial location:", error);
      Alert.alert("Error", "Failed to get your location");
    }
  };

  const handleSituationMarkerPress = (situation: any) => {
    console.log("Situation marker pressed:", situation.id);
    // Navigate to incident detail view
    mapBottomSheetRef.current?.navigateToIncident(situation);
  };

  const handleAssetMarkerPress = (asset: any) => {
    console.log("Asset marker pressed:", asset.id);
    // Navigate to unit detail view
    mapBottomSheetRef.current?.navigateToUnit(asset);
  };

  const handleSituationPress = (situation: any) => {
    console.log("Situation pressed from bottom sheet:", situation.id);
  };

  const handleAssetPress = (asset: any) => {
    console.log("Asset pressed from bottom sheet:", asset.id);
  };

  const handleSelectedUnitChange = (unit: any | null) => {
    const isNewUnit = !selectedUnit || selectedUnit?.id !== unit?.id;
    setSelectedUnit(unit);

    // Only center map on selected unit if it's a new unit selection and has location data
    if (
      unit &&
      unit.latitude &&
      unit.longitude &&
      cameraRef.current &&
      isNewUnit
    ) {
      console.log(
        "Centering map on newly selected unit:",
        unit.id,
        unit.latitude,
        unit.longitude
      );

      // Calculate offset based on bottom sheet position
      let offsetY = -0.004;
      if (bottomSheetIndex === 1) {
        offsetY = -0.004;
      } else if (bottomSheetIndex === 2) {
        offsetY = -0.008;
      }

      cameraRef.current.setCamera({
        centerCoordinate: [unit.longitude, unit.latitude + offsetY],
        zoomLevel: 14,
        animationDuration: 1000,
      });
    }

    // Reset focus flag when unit is deselected
    if (!unit) {
      setHasInitiallyFocused(false);
    }
  };

  const handleSelectedIncidentChange = (incident: any | null) => {
    const isNewIncident =
      !selectedIncident || selectedIncident?.id !== incident?.id;
    setSelectedIncident(incident);

    // Only center map on selected incident if it's a new incident selection and has location data
    if (
      incident &&
      incident.latitude &&
      incident.longitude &&
      cameraRef.current &&
      isNewIncident
    ) {
      console.log(
        "Centering map on newly selected incident:",
        incident.id,
        incident.latitude,
        incident.longitude
      );

      // Check if user is assigned to this incident
      const userAssignedToIncident = activeOrders?.some(
        (order) =>
          hookOrderTypeToString(order.type) === "ORDER_TYPE_ASSIST_MEMBER" &&
          order.situationId === incident.id &&
          hookOrderStatusToString(order.status) !== "ORDER_STATUS_REJECTED" &&
          hookOrderStatusToString(order.status) !== "ORDER_STATUS_CANCELLED" &&
          hookOrderStatusToString(order.status) !== "ORDER_STATUS_COMPLETED"
      );

      if (userAssignedToIncident && location) {
        const screenHeight = Dimensions.get("window").height;
        const bottomPadding = screenHeight / 2 + 50;

        const distance = calculateDistance(
          location.latitude,
          location.longitude,
          incident.latitude,
          incident.longitude
        );

        // If incident is very close (less than 200 meters), use a minimum zoom level
        // Otherwise use fitBounds for better framing of both points
        if (distance < 200) {
          const centerLat = (location.latitude + incident.latitude) / 2;
          const centerLon = (location.longitude + incident.longitude) / 2;
          let offsetY = -0.001;
          if (bottomSheetIndex === 1) {
            offsetY = -0.001;
          } else if (bottomSheetIndex === 2) {
            offsetY = -0.002;
          }

          cameraRef.current.setCamera({
            centerCoordinate: [centerLon, centerLat + offsetY],
            zoomLevel: 16,
            animationDuration: 1000,
          });
        } else {
          console.log("[Camera] Using fitBounds for distant incident");
          cameraRef.current.fitBounds(
            [location.longitude, location.latitude],
            [incident.longitude, incident.latitude],
            [180, 50, bottomPadding, 50],
            1000
          );
        }
      } else {
        let offsetY = -0.004;
        if (bottomSheetIndex === 1) {
          offsetY = -0.004;
        } else if (bottomSheetIndex === 2) {
          offsetY = -0.008;
        }

        cameraRef.current.setCamera({
          centerCoordinate: [incident.longitude, incident.latitude + offsetY],
          zoomLevel: 14,
          animationDuration: 1000,
        });
      }
    }

    if (!incident) {
      setHasInitiallyFocused(false);
    }
  };

  const handleCenterLocation = (currentBottomSheetIndex: number) => {
    console.log(
      "Center location pressed, bottom sheet index:",
      currentBottomSheetIndex
    );
    if (location && cameraRef.current) {
      console.log("Center location pressed", location);

      // Calculate offset based on bottom sheet position
      let offsetY = -0.004;
      if (currentBottomSheetIndex === 1) {
        offsetY = -0.004;
      } else if (currentBottomSheetIndex === 2) {
        offsetY = -0.008;
      }

      cameraRef.current.setCamera({
        centerCoordinate: [location.longitude, location.latitude + offsetY],
        zoomLevel: 14,
        pitch: 0,
        animationDuration: 1000,
      });
    }
  };

  // Clear optimistic status when real status is confirmed
  useEffect(() => {
    if (
      optimisticAssistingStatus === "ASSISTING" &&
      firstAssistMemberOrder?.typeSpecificStatus === "ASSISTING"
    ) {
      console.log(
        "[Optimistic] Real ASSISTING status confirmed, clearing optimistic status"
      );
      setOptimisticAssistingStatus(null);
    }
  }, [optimisticAssistingStatus, firstAssistMemberOrder?.typeSpecificStatus]);

  // Handle arrived button press
  const handleArrivedPress = async () => {
    if (
      !firstAssistMemberOrder ||
      !respondingToSituation ||
      !userInfoFromServer
    ) {
      return;
    }

    try {
      const orderId = firstAssistMemberOrder.id;
      if (orderId) {
        console.log("[Order Debug] Updating order to ASSISTING:", orderId);

        // Optimistic update: immediately set the assisting status
        setOptimisticAssistingStatus("ASSISTING");

        // If we're currently viewing this incident, expand the bottom sheet and show Clear Scene card
        if (selectedIncident?.id === respondingToSituation.id) {
          mapBottomSheetRef.current?.forceExpandToFull();
        }

        await updateOrderMutation.mutateAsync({
          order: {
            id: orderId,
            status: stringToOrderStatus("ORDER_STATUS_IN_PROGRESS"),
            typeSpecificStatus: "ASSISTING",
          } as Order,
        } as UpdateOrderRequest);

        await addSituationUpdate.mutateAsync({
          id: respondingToSituation.id || "",
          update: createUpdateEntry(
            getOrderStatusLabel("ORDER_STATUS_IN_PROGRESS"),
            "status change",
            userInfoFromServer.id || "",
            userInfoFromServer.name || ""
          ),
        } as AddSituationUpdateRequest);

        console.log("[Order Debug] Successfully updated order to ASSISTING");

        // Force refetch orders to get updated status immediately
        console.log("[Order Debug] Forcing orders refetch after arriving");
        await refetchOrders();
      }
    } catch (error) {
      console.error("Error updating order to ASSISTING:", error);
      // Revert optimistic update on error
      setOptimisticAssistingStatus(null);
      Alert.alert("Error", "Failed to update status. Please try again.");
    }
  };

  // Handle add update from floating action button
  const handleAddUpdate = async (message: string) => {
    if (!userInfoFromServer) {
      throw new Error("User info not available");
    }

    // Get the current incident from the floating button state
    const currentIncident = floatingButtonState.currentView?.incident;
    if (!currentIncident) {
      throw new Error("No current incident");
    }

    await addSituationUpdate.mutateAsync({
      id: currentIncident.id || "",
      update: createUpdateEntry(
        message,
        "quick note",
        userInfoFromServer.id || "",
        userInfoFromServer.name || ""
      ),
    } as AddSituationUpdateRequest);
  };

  // Handle when an incident is cleared by the user
  const handleIncidentCleared = () => {
    setCurrentIncidentClearedByUser(true);
  };

  // Handle open assigned incident from card
  const handleOpenAssignedIncident = () => {
    if (respondingToSituation) {
      console.log(
        "Opening assigned incident from card:",
        respondingToSituation.id
      );
      mapBottomSheetRef.current?.navigateToIncident(respondingToSituation);
    }
  };

  // Determine if assigned incident card should be visible
  const shouldShowAssignedIncidentCard = React.useMemo(() => {
    // Show card if:
    // 1. User has an assigned incident (respondingToSituation exists)
    // 2. User is NOT currently viewing that incident (selectedIncident?.id !== respondingToSituation?.id)
    // 3. The order is not in CREATED status (not showing incoming incident view)
    const orderStatus = firstAssistMemberOrder
      ? hookOrderStatusToString(firstAssistMemberOrder.status)
      : null;

    return Boolean(
      respondingToSituation &&
        selectedIncident?.id !== respondingToSituation?.id &&
        orderStatus !== "ORDER_STATUS_CREATED"
    );
  }, [respondingToSituation?.id, selectedIncident?.id, firstAssistMemberOrder]);

  // Reset cleared state when incident changes
  useEffect(() => {
    setCurrentIncidentClearedByUser(false);
  }, [selectedIncident?.id]);

  if (!hasLocationPermission) {
    return <View style={styles.container} />;
  }

  return (
    <Drawer
      open={drawerOpen}
      onOpen={() => setDrawerOpen(true)}
      onClose={() => setDrawerOpen(false)}
      renderDrawerContent={() => <SideMenu />}
      drawerPosition="left"
      drawerStyle={{ width: width * 0.7, backgroundColor: "#fff" }}
    >
      <View style={styles.container}>
        <MapboxGL.MapView
          ref={mapRef}
          style={styles.map}
          styleURL="mapbox://styles/hero-safety/cm9n1pd7j004d01ss66zrbjwm"
          compassEnabled={false}
          scaleBarEnabled={false}
          logoEnabled={false}
          attributionEnabled={false}
        >
          <MapboxGL.Images>
            <MapboxGL.Image name="directionPuck">
              <Image
                source={require("../assets/direction.png")}
                style={{ width: 40, height: 40 }}
              />
            </MapboxGL.Image>
          </MapboxGL.Images>

          <MapboxGL.Camera
            ref={cameraRef}
            animationMode="flyTo"
            animationDuration={1000}
            centerCoordinate={
              location
                ? undefined
                : [DEFAULT_LOCATION.longitude, DEFAULT_LOCATION.latitude]
            }
            zoomLevel={location ? undefined : 12}
          />
          <SituationMarkersContainer
            onSituationMarkerPress={handleSituationMarkerPress}
            selectedIncidentId={selectedIncident?.id || null}
            activeOrders={activeOrders}
            incomingIncidentSituationId={
              // Only pass incoming incident ID if we actually have one AND we're showing the incoming incident
              firstAssistMemberOrder &&
              respondingToSituation &&
              hookOrderStatusToString(firstAssistMemberOrder.status) ===
                "ORDER_STATUS_CREATED"
                ? respondingToSituation.id
                : null
            }
          />
          <AssetMarkersContainer
            onAssetMarkerPress={handleAssetMarkerPress}
            selectedUnitId={selectedUnit?.id || null}
          />

          {/* Route Line - Only show if user is assigned to selected incident */}
          {route && selectedIncident && isUserAssignedToSelectedIncident && (
            <MapboxGL.ShapeSource
              id="incidentRouteSource"
              shape={{
                type: "Feature",
                geometry: route.geometry as any,
                properties: {},
              }}
            >
              <MapboxGL.LineLayer
                id="incidentRouteLine"
                style={{
                  lineWidth: 5,
                  lineColor: "#005FDD",
                  lineCap: "round",
                  lineJoin: "round",
                }}
                layerIndex={20}
              />
            </MapboxGL.ShapeSource>
          )}

          {location && (
            <LocationPuck
              visible={true}
              puckBearingEnabled={true}
              puckBearing={"heading"}
              topImage={
                route && selectedIncident && isUserAssignedToSelectedIncident
                  ? "directionPuck"
                  : ""
              }
            />
          )}
        </MapboxGL.MapView>

        <MapHeader
          onMenuPress={() => setDrawerOpen(true)}
          onCenterLocationPress={handleCenterLocation}
          bottomSheetIndex={bottomSheetIndex}
          assignedIncident={
            firstAssistMemberOrder && respondingToSituation
              ? respondingToSituation
              : undefined
          }
          userLocation={location}
          selectedIncidentId={selectedIncident?.id || null}
          onArrivedPress={handleArrivedPress}
          firstAssistMemberOrder={firstAssistMemberOrder}
          currentBottomSheetViewType={currentBottomSheetViewType}
        />

        <UpdateBanner
          isVisible={updateAvailable && !updateDismissed}
          isUpdating={isUpdating}
          onUpdatePress={handleUpdatePress}
          onDismiss={handleDismissUpdate}
          slideAnim={updateBannerSlideAnim}
        />

        <MapBottomSheet
          ref={mapBottomSheetRef}
          onSituationPress={handleSituationPress}
          onAssetPress={handleAssetPress}
          onBottomSheetChange={setBottomSheetIndex}
          onSelectedUnitChange={handleSelectedUnitChange}
          onSelectedIncidentChange={handleSelectedIncidentChange}
          onConfirmIncident={handleConfirmIncident}
          onRejectIncident={handleRejectIncident}
          confirmLoading={confirmLoading}
          rejectLoading={rejectLoading}
          activeOrders={activeOrders}
          firstAssistMemberOrder={firstAssistMemberOrder}
          userInfoFromServer={userInfoFromServer}
          optimisticAssistingStatus={optimisticAssistingStatus}
          onAddUpdateButtonStateChange={setFloatingButtonState}
          onAddUpdate={handleAddUpdate}
          onIncidentCleared={handleIncidentCleared}
          currentIncidentClearedByUser={currentIncidentClearedByUser}
        />

        {/* Assigned Incident Card */}
        <AssignedIncidentCard
          incident={respondingToSituation}
          isVisible={shouldShowAssignedIncidentCard}
          bottomSheetIndex={bottomSheetIndex}
          currentBottomSheetViewType={currentBottomSheetViewType}
          onOpenPress={handleOpenAssignedIncident}
        />

        {/* Floating Action Button for Adding Updates - Positioned relative to entire screen */}
        <Animated.View
          style={[
            styles.floatingActionButton,
            {
              opacity: buttonOpacity,
              transform: [
                {
                  scale: buttonOpacity.interpolate({
                    inputRange: [0, 1],
                    outputRange: [0.8, 1],
                  }),
                },
              ],
            },
          ]}
          pointerEvents={floatingButtonState.shouldShow ? "auto" : "none"}
        >
          <TouchableOpacity
            style={styles.floatingActionButtonTouchable}
            onPress={floatingButtonState.onPress}
            activeOpacity={0.8}
          >
            <Ionicons
              name="add"
              size={20}
              color="white"
              style={styles.buttonIcon}
            />
            <Text style={styles.buttonText}>Update</Text>
          </TouchableOpacity>
        </Animated.View>
      </View>
    </Drawer>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: "#000",
  },
  map: {
    flex: 1,
  },
  floatingActionButton: {
    position: "absolute",
    bottom: 20,
    right: 20,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.25,
    shadowRadius: 4,
    elevation: 5,
    zIndex: 1000,
  },
  floatingActionButtonTouchable: {
    flexDirection: "row",
    alignItems: "center",
    backgroundColor: "#005FDD",
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderRadius: 28,
    minHeight: 56,
  },
  buttonIcon: {
    marginRight: 8,
  },
  buttonText: {
    color: "white",
    fontSize: 16,
    fontWeight: "600",
  },
});
