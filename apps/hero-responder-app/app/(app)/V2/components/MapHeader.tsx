import React, { useEffect, useRef, useState } from "react";
import {
  View,
  StyleSheet,
  TouchableOpacity,
  Text,
  Animated,
} from "react-native";
import { MaterialCommunityIcons } from "@expo/vector-icons";
import { useSafeAreaInsets } from "react-native-safe-area-context";
import { getSituationType } from "../utils";

interface MapHeaderProps {
  onMenuPress: () => void;
  onCenterLocationPress: (bottomSheetIndex: number) => void;
  bottomSheetIndex: number;
  assignedIncident?: any;
  userLocation?: { latitude: number; longitude: number } | null;
  selectedIncidentId?: string | null;
  onArrivedPress?: () => void;
  firstAssistMemberOrder?: any;
  currentBottomSheetViewType?: string;
}

// Calculate distance between two coordinates in miles
const calculateDistance = (
  lat1: number,
  lon1: number,
  lat2: number,
  lon2: number
): number => {
  const R = 3959; // Earth's radius in miles
  const dLat = (lat2 - lat1) * (Math.PI / 180);
  const dLon = (lon2 - lon1) * (Math.PI / 180);
  const a =
    Math.sin(dLat / 2) * Math.sin(dLat / 2) +
    Math.cos(lat1 * (Math.PI / 180)) *
      Math.cos(lat2 * (Math.PI / 180)) *
      Math.sin(dLon / 2) *
      Math.sin(dLon / 2);
  const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));
  return R * c;
};

export default function MapHeader({
  onMenuPress,
  onCenterLocationPress,
  bottomSheetIndex,
  assignedIncident,
  userLocation,
  selectedIncidentId,
  onArrivedPress,
  firstAssistMemberOrder,
  currentBottomSheetViewType,
}: MapHeaderProps) {
  const insets = useSafeAreaInsets();
  const buttonsTranslateY = useRef(new Animated.Value(0)).current;
  const headerTranslateY = useRef(new Animated.Value(-200)).current;
  const headerOpacity = useRef(new Animated.Value(0)).current;

  // Local state for optimistic UI updates
  const [hasArrivedBeenPressed, setHasArrivedBeenPressed] = useState(false);

  // Check if user is already assisting based on order status
  const isAlreadyAssisting =
    firstAssistMemberOrder?.typeSpecificStatus === "ASSISTING";

  // Check if user is currently viewing the incident detail (not incoming incident)
  const isViewingIncidentDetail =
    currentBottomSheetViewType === "incident-detail";

  // Only show incident header if user is assigned AND currently viewing that incident detail
  // AND hasn't pressed arrived AND is not already assisting
  const shouldShowIncidentHeader = Boolean(
    assignedIncident &&
      selectedIncidentId === assignedIncident.id &&
      isViewingIncidentDetail &&
      !hasArrivedBeenPressed &&
      !isAlreadyAssisting
  );

  // Reset the arrived state when incident changes or is cleared
  useEffect(() => {
    if (!assignedIncident || selectedIncidentId !== assignedIncident.id) {
      setHasArrivedBeenPressed(false);
    }
  }, [assignedIncident?.id, selectedIncidentId]);

  useEffect(() => {
    if (shouldShowIncidentHeader) {
      // Animate buttons up and out
      Animated.timing(buttonsTranslateY, {
        toValue: -200,
        duration: 300,
        useNativeDriver: true,
      }).start(() => {
        // After buttons are gone, animate header down
        Animated.parallel([
          Animated.timing(headerTranslateY, {
            toValue: 0,
            duration: 400,
            useNativeDriver: true,
          }),
          Animated.timing(headerOpacity, {
            toValue: 1,
            duration: 400,
            useNativeDriver: true,
          }),
        ]).start();
      });
    } else {
      // Animate header up and out
      Animated.parallel([
        Animated.timing(headerTranslateY, {
          toValue: -200,
          duration: 300,
          useNativeDriver: true,
        }),
        Animated.timing(headerOpacity, {
          toValue: 0,
          duration: 300,
          useNativeDriver: true,
        }),
      ]).start(() => {
        // After header is gone, animate buttons down
        Animated.timing(buttonsTranslateY, {
          toValue: 0,
          duration: 400,
          useNativeDriver: true,
        }).start();
      });
    }
  }, [shouldShowIncidentHeader]);

  // Calculate distance if we have both locations
  const distance = React.useMemo(() => {
    if (
      assignedIncident?.latitude &&
      assignedIncident?.longitude &&
      userLocation?.latitude &&
      userLocation?.longitude
    ) {
      const dist = calculateDistance(
        userLocation.latitude,
        userLocation.longitude,
        assignedIncident.latitude,
        assignedIncident.longitude
      );
      return dist;
    }
    return null;
  }, [assignedIncident, userLocation]);

  // Handle arrived button press with optimistic update
  const handleArrivedPress = () => {
    setHasArrivedBeenPressed(true);
    onArrivedPress?.();
  };

  return (
    <View style={[styles.container, { paddingTop: insets.top }]}>
      {/* Normal Header Buttons */}
      <Animated.View
        style={[
          styles.buttonsContainer,
          {
            top: insets.top,
            transform: [{ translateY: buttonsTranslateY }],
          },
        ]}
      >
        <TouchableOpacity
          style={styles.button}
          onPress={onMenuPress}
          activeOpacity={0.7}
        >
          <MaterialCommunityIcons name="menu" size={24} color="#07090A" />
        </TouchableOpacity>

        <TouchableOpacity
          style={styles.button}
          onPress={() => onCenterLocationPress(bottomSheetIndex)}
          activeOpacity={0.7}
        >
          <MaterialCommunityIcons
            name="crosshairs-gps"
            size={24}
            color="#07090A"
          />
        </TouchableOpacity>
      </Animated.View>

      {/* Incident Header */}
      <Animated.View
        style={[
          styles.incidentHeader,
          {
            top: insets.top,
            transform: [{ translateY: headerTranslateY }],
            opacity: headerOpacity,
          },
        ]}
      >
        <View style={styles.incidentContent}>
          <View style={styles.incidentInfo}>
            <Text style={styles.incidentLocation} numberOfLines={1}>
              {assignedIncident?.address || "Location not available"}
            </Text>
            <Text style={styles.incidentType} numberOfLines={1}>
              {getSituationType(assignedIncident?.type) || "Unknown"}
            </Text>
          </View>
          {distance !== null && (
            <View style={styles.distanceContainer}>
              <Text style={styles.distanceText}>{distance.toFixed(1)} mi</Text>
            </View>
          )}
        </View>

        {/* Arrived Button */}
        <TouchableOpacity
          style={styles.arrivedButton}
          onPress={handleArrivedPress}
          activeOpacity={0.8}
        >
          <Text style={styles.arrivedButtonText}>Arrived</Text>
        </TouchableOpacity>
      </Animated.View>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    position: "absolute",
    top: 0,
    left: 0,
    right: 0,
    zIndex: 100,
    pointerEvents: "box-none",
  },
  buttonsContainer: {
    position: "absolute",
    top: 0,
    left: 0,
    right: 0,
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "flex-start",
    paddingHorizontal: 24,
    paddingVertical: 12,
    pointerEvents: "box-none",
  },
  button: {
    width: 48,
    height: 48,
    borderRadius: 24,
    backgroundColor: "#FFFFFF",
    justifyContent: "center",
    alignItems: "center",
    shadowColor: "#000",
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  incidentHeader: {
    position: "absolute",
    top: 0,
    left: 0,
    right: 0,
    backgroundColor: "#162456",
    borderRadius: 16,
    marginHorizontal: 16,
    shadowColor: "#000",
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  incidentContent: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
    paddingHorizontal: 20,
    paddingVertical: 16,
  },
  incidentInfo: {
    flex: 1,
    marginRight: 16,
  },
  incidentLocation: {
    fontSize: 20,
    fontWeight: "700",
    color: "#FFFFFF",
    marginBottom: 4,
  },
  incidentType: {
    fontSize: 14,
    color: "#FFFFFF",
    opacity: 0.8,
  },
  distanceContainer: {
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 8,
  },
  distanceText: {
    fontSize: 16,
    fontWeight: "600",
    color: "#FFFFFF",
  },
  arrivedButton: {
    backgroundColor: "#FFFFFF",
    marginHorizontal: 20,
    marginBottom: 16,
    paddingVertical: 14,
    borderRadius: 8,
    alignItems: "center",
  },
  arrivedButtonText: {
    fontSize: 16,
    fontWeight: "600",
    color: "#162456",
  },
});
