import React, { useEffect, useRef } from "react";
import { View, StyleSheet, TouchableOpacity, Text, Animated } from "react-native";
import { useSafeAreaInsets } from "react-native-safe-area-context";
import { getSituationType } from "../utils";

interface AssignedIncidentCardProps {
  incident?: any;
  isVisible: boolean;
  bottomSheetIndex: number;
  currentBottomSheetViewType: string;
  onOpenPress: () => void;
}

export const AssignedIncidentCard: React.FC<AssignedIncidentCardProps> = ({
  incident,
  isVisible,
  bottomSheetIndex,
  currentBottomSheetViewType,
  onOpenPress,
}) => {
  const insets = useSafeAreaInsets();
  const translateY = useRef(new Animated.Value(100)).current;
  const opacity = useRef(new Animated.Value(0)).current;

  const getOffsetForViewType = (viewType: string, sheetIndex: number) => {
    if (sheetIndex === 0) {
      switch (viewType) {
        case 'list':
          return -60;
        case 'incident-detail':
          return -100; 
        case 'unit-detail':
          return -100;
        case 'incoming-incident':
          return -100;
        default:
          return -100;
      }
    }
    return 0; // Bottom sheet is open - normal position
  };

  useEffect(() => {
    if (isVisible) {
      // Calculate target position based on bottom sheet index and view type
      const targetY = getOffsetForViewType(currentBottomSheetViewType, bottomSheetIndex);
      
      // Animate card to target position
      Animated.parallel([
        Animated.timing(translateY, {
          toValue: targetY,
          duration: 300,
          useNativeDriver: true,
        }),
        Animated.timing(opacity, {
          toValue: 1,
          duration: 300,
          useNativeDriver: true,
        }),
      ]).start();
    } else {
      // Animate card down and out
      Animated.parallel([
        Animated.timing(translateY, {
          toValue: 100,
          duration: 200,
          useNativeDriver: true,
        }),
        Animated.timing(opacity, {
          toValue: 0,
          duration: 200,
          useNativeDriver: true,
        }),
      ]).start();
    }
  }, [isVisible, bottomSheetIndex, currentBottomSheetViewType, translateY, opacity]);

  if (!incident) return null;

  return (
    <Animated.View
      style={[
        styles.container,
        {
          bottom: insets.bottom + 20,
          transform: [{ translateY }],
          opacity,
        },
      ]}
      pointerEvents={isVisible ? "auto" : "none"}
    >
      <View style={styles.cardContent}>
        <View style={styles.incidentInfo}>
          <Text style={styles.incidentText} numberOfLines={1}>
            {incident.id?.replace(/[^0-9]/g, "").slice(0,3)} • {getSituationType(incident.type) || "Unknown"}
          </Text>
        </View>
        
        <TouchableOpacity 
          style={styles.openButton}
          onPress={onOpenPress}
          activeOpacity={0.8}
        >
          <Text style={styles.openButtonText}>Open</Text>
        </TouchableOpacity>
      </View>
    </Animated.View>
  );
};

const styles = StyleSheet.create({
  container: {
    position: "absolute",
    left: 16,
    right: 16,
    zIndex: 150,
  },
  cardContent: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
    backgroundColor: "#162456",
    borderRadius: 12,
    paddingHorizontal: 20,
    paddingVertical: 16,
    shadowColor: "#000",
    shadowOffset: {
      width: 0,
      height: 4,
    },
    shadowOpacity: 0.15,
    shadowRadius: 8,
    elevation: 6,
  },
  incidentInfo: {
    flex: 1,
    marginRight: 16,
  },
  incidentText: {
    fontSize: 16,
    fontWeight: "600",
    color: "#FFFFFF",
  },
  openButton: {
    backgroundColor: "#FFFFFF",
    paddingVertical: 10,
    paddingHorizontal: 20,
    borderRadius: 8,
  },
  openButtonText: {
    fontSize: 14,
    fontWeight: "600",
    color: "#162456",
  },
}); 