import React from "react";
import { View, Text, StyleSheet, TouchableOpacity } from "react-native";
import { Asset } from "proto/hero/assets/v2/assets_pb";

interface AssetMarkerProps {
  asset: Asset;
  onPress: () => void;
  isSelected?: boolean;
}

export const AssetMarker: React.FC<AssetMarkerProps> = ({ 
  asset, 
  onPress, 
  isSelected = false 
}) => {
  // Get initials from asset name
  const getInitials = (name: string) => {
    return name
      .split(" ")
      .map((word) => word[0])
      .join("")
      .toUpperCase()
      .slice(0, 2);
  };

  // Determine marker color based on status
  const getMarkerColor = () => {
    //@ts-ignore
    if (asset.status === "ASSET_STATUS_AVAILABLE") {
      return "#009F89";
    }
    //@ts-ignore
    if (asset.status === "ASSET_STATUS_BUSY") {
      return "#AD0363";
    }
    return "#009F89";
  };

  const markerColor = getMarkerColor();

  return (
    <TouchableOpacity onPress={onPress} style={styles.container}>
      <View style={[
        styles.marker, 
        { 
          borderColor: markerColor,
          backgroundColor: isSelected ? markerColor : "#FFF"
        }
      ]}>
        <Text style={[
          styles.initials, 
          { 
            color: isSelected ? "#FFF" : markerColor
          }
        ]}>
          {getInitials(asset.name)}
        </Text>
      </View>
      <Text style={[
        styles.assetId, 
        { 
          color: isSelected ? "#FFF" : markerColor,
          backgroundColor: isSelected ? markerColor : "rgba(255, 255, 255, 0.9)"
        }
      ]}>
        {asset.id?.replace(/[^0-9]/g, "").slice(0, 3)}
      </Text>
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  container: {
    alignItems: "center",
    justifyContent: "center",
  },
  marker: {
    width: 36,
    height: 36,
    borderRadius: 20,
    backgroundColor: "#FFF",
    alignItems: "center",
    justifyContent: "center",
    borderWidth: 2,
    borderColor: "#FFFFFF",
    shadowColor: "#000",
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
    elevation: 5,
  },
  initials: {
    color: "#FFFFFF",
    fontSize: 14,
    fontWeight: "bold",
  },
  assetId: {
    marginTop: 5,
    fontSize: 10,
    fontWeight: "bold",
    color: "#333",
    backgroundColor: "rgba(255, 255, 255, 0.9)",
    paddingHorizontal: 6,
    paddingVertical: 2,
    borderRadius: 4,
    textAlign: "center",
    minWidth: 30,
  },
});
