import React from "react";
import { View, Text, TouchableOpacity, StyleSheet } from "react-native";
import { FlagIcon } from "../../icons";

interface SituationMarkerProps {
  situation: any;
  onPress: () => void;
  isSelected?: boolean;
  hasActiveOrder?: boolean;
}

export const SituationMarker: React.FC<SituationMarkerProps> = ({ 
  situation, 
  onPress, 
  isSelected = false,
  hasActiveOrder = false
}) => {
  // Determine colors based on whether there's an active order
  const getColors = () => {
    if (hasActiveOrder) {
      // Blue theme for situations with active orders
      return {
        backgroundColor: isSelected ? '#0060FF' : '#F5F9FF',
        borderColor: '#0060FF',
        flagColor: isSelected ? '#F5F9FF' : '#0060FF',
        textColor: isSelected ? '#FFF' : '#333',
        textBackgroundColor: isSelected ? '#0060FF' : 'rgba(245, 249, 255, 0.9)',
      };
    } else {
      // Orange theme for situations without active orders (original)
      return {
        backgroundColor: isSelected ? '#E17100' : '#FFEDD4',
        borderColor: '#E17100',
        flagColor: isSelected ? '#FFEDD4' : '#E17100',
        textColor: isSelected ? '#FFF' : '#333',
        textBackgroundColor: isSelected ? '#E17100' : 'rgba(255, 255, 255, 0.9)',
      };
    }
  };

  const colors = getColors();

  return (
    <TouchableOpacity onPress={onPress} style={styles.markerContainer}>
      <View style={styles.diamondWrapper}>
        <View style={[
          styles.diamondBox,
          {
            backgroundColor: colors.backgroundColor,
            borderColor: colors.borderColor,
          }
        ]}>
          <View style={styles.flagCenterer}>
            <FlagIcon color={colors.flagColor} />
          </View>
        </View>
      </View>
      <Text style={[
        styles.situationId,
        {
          color: colors.textColor,
          backgroundColor: colors.textBackgroundColor,
        }
      ]}>
        {situation.id?.replace(/[^0-9]/g, "").slice(0, 3) || "000"}
      </Text>
    </TouchableOpacity>
  );
};

const DIAMOND_SIZE = 32;

const styles = StyleSheet.create({
  markerContainer: {
    alignItems: 'center',
    justifyContent: 'center',
  },
  diamondWrapper: {
    width: DIAMOND_SIZE,
    height: DIAMOND_SIZE,
    alignItems: 'center',
    justifyContent: 'center',
  },
  diamondBox: {
    width: DIAMOND_SIZE,
    height: DIAMOND_SIZE,
    borderWidth: 2,
    borderRadius: 4,
    transform: [{ rotate: '45deg' }],
    overflow: 'hidden',
    alignItems: 'center',
    justifyContent: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.18,
    shadowRadius: 2.0,
    elevation: 3,
  },
  flagCenterer: {
    transform: [{ rotate: '-45deg' }],
    alignItems: 'center',
    justifyContent: 'center',
    flex: 1,
    width: DIAMOND_SIZE,
    height: DIAMOND_SIZE,
  },
  situationId: {
    marginTop: 10,
    fontSize: 10,
    fontWeight: 'bold',
    color: '#333',
    backgroundColor: 'rgba(255, 255, 255, 0.9)',
    paddingHorizontal: 6,
    paddingVertical: 2,
    borderRadius: 4,
    textAlign: 'center',
    minWidth: 30,
  },
});
