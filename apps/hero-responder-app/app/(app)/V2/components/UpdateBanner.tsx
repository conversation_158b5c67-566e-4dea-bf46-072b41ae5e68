import React from "react";
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  Animated,
  ActivityIndicator,
} from "react-native";
import { Ionicons } from "@expo/vector-icons";

interface UpdateBannerProps {
  isVisible: boolean;
  isUpdating: boolean;
  onUpdatePress: () => void;
  onDismiss: () => void;
  slideAnim: Animated.Value;
}

export const UpdateBanner: React.FC<UpdateBannerProps> = ({
  isVisible,
  isUpdating,
  onUpdatePress,
  onDismiss,
  slideAnim,
}) => {
  if (!isVisible && !isUpdating) return null;

  return (
    <Animated.View
      style={[
        styles.container,
        {
          transform: [
            {
              translateY: slideAnim.interpolate({
                inputRange: [0, 1],
                outputRange: [-100, 0],
              }),
            },
          ],
        },
      ]}
    >
      <View style={styles.content}>
        <View style={styles.leftContent}>
          <Ionicons name="download-outline" size={20} color="#0066CC" />
          <Text style={styles.title}>Update Available</Text>
        </View>
        
        <View style={styles.rightContent}>
          {isUpdating ? (
            <View style={styles.updatingContainer}>
              <ActivityIndicator size="small" color="#0066CC" />
              <Text style={styles.updatingText}>Updating...</Text>
            </View>
          ) : (
            <>
              <TouchableOpacity
                style={styles.dismissButton}
                onPress={onDismiss}
                activeOpacity={0.7}
              >
                <Ionicons name="close" size={18} color="#666" />
              </TouchableOpacity>
              
              <TouchableOpacity
                style={styles.updateButton}
                onPress={onUpdatePress}
                activeOpacity={0.8}
              >
                <Text style={styles.updateButtonText}>Update</Text>
              </TouchableOpacity>
            </>
          )}
        </View>
      </View>
    </Animated.View>
  );
};

const styles = StyleSheet.create({
  container: {
    position: "absolute",
    top: 0,
    left: 0,
    right: 0,
    backgroundColor: "#F0F8FF",
    borderBottomWidth: 1,
    borderBottomColor: "#E1E5E9",
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
    zIndex: 1000,
  },
  content: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
    paddingHorizontal: 16,
    paddingVertical: 12,
    paddingTop: 50, // Account for status bar
  },
  leftContent: {
    flexDirection: "row",
    alignItems: "center",
    flex: 1,
  },
  title: {
    fontSize: 16,
    fontWeight: "600",
    color: "#1A1A1A",
    marginLeft: 8,
  },
  rightContent: {
    flexDirection: "row",
    alignItems: "center",
  },
  dismissButton: {
    padding: 8,
    marginRight: 8,
  },
  updateButton: {
    backgroundColor: "#0066CC",
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 20,
  },
  updateButtonText: {
    color: "white",
    fontSize: 14,
    fontWeight: "600",
  },
  updatingContainer: {
    flexDirection: "row",
    alignItems: "center",
    paddingHorizontal: 16,
    paddingVertical: 8,
  },
  updatingText: {
    color: "#0066CC",
    fontSize: 14,
    fontWeight: "500",
    marginLeft: 8,
  },
}); 