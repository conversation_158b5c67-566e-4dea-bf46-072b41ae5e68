import React, { useMemo, useState, useCallback } from "react";
import {
  View,
  StyleSheet,
  Text,
  Dimensions,
} from "react-native";
import { BottomSheetScrollView } from "@gorhom/bottom-sheet";
import { TabView, TabBar } from "react-native-tab-view";
import { IncidentCard } from "./IncidentCard";
import { UnitCard } from "./UnitCard";

const screenWidth = Dimensions.get("window").width;

interface BottomSheetListViewProps {
  activeSituations: any[];
  activeAssets: any[];
  allAssets: any[];
  currentUserCognitoSub?: string;
  activeOrders?: any[];
  onSituationPress?: (situation: any) => void;
  onAssetPress?: (asset: any) => void;
}

export const BottomSheetListView: React.FC<BottomSheetListViewProps> = ({
  activeSituations,
  activeAssets,
  allAssets,
  currentUserCognitoSub,
  activeOrders = [],
  onSituationPress,
  onAssetPress,
}) => {
  const [tabIndex, setTabIndex] = useState(0);

  // Tab routes
  const routes = useMemo(
    () => [
      { key: "incidents", title: "Active Incidents" },
      { key: "units", title: "Active Units" },
    ],
    []
  );

  // Render incidents tab content
  const renderIncidentsTab = useCallback(() => (
    <BottomSheetScrollView
      style={styles.listContainer}
      contentContainerStyle={styles.scrollContent}
      nestedScrollEnabled={true}
      showsVerticalScrollIndicator={false}
    >
      {activeSituations.length > 0 ? (
        activeSituations.map(situation => (
          <IncidentCard
            key={situation.id}
            situation={situation}
            assets={allAssets}
            currentUserCognitoSub={currentUserCognitoSub}
            activeOrders={activeOrders}
            onPress={() => onSituationPress?.(situation)}
          />
        ))
      ) : (
        <Text style={styles.emptyText}>No active incidents</Text>
      )}
    </BottomSheetScrollView>
  ), [activeSituations, allAssets, currentUserCognitoSub, activeOrders, onSituationPress]);

  // Render units tab content
  const renderUnitsTab = useCallback(() => (
    <BottomSheetScrollView
      style={styles.listContainer}
      contentContainerStyle={styles.scrollContent}
      nestedScrollEnabled={true}
      showsVerticalScrollIndicator={false}
    >
      {activeAssets.length > 0 ? (
        activeAssets.map(asset => (
          <UnitCard
            key={asset.id}
            asset={asset}
            activeSituations={activeSituations}
            onPress={() => onAssetPress?.(asset)}
          />
        ))
      ) : (
        <Text style={styles.emptyText}>No active units</Text>
      )}
    </BottomSheetScrollView>
  ), [activeAssets, activeSituations, onAssetPress]);

  // Render scene for TabView
  const renderScene = useCallback(({ route }: { route: { key: string } }) => {
    switch (route.key) {
      case "incidents":
        return renderIncidentsTab();
      case "units":
        return renderUnitsTab();
      default:
        return null;
    }
  }, [renderIncidentsTab, renderUnitsTab]);

  return (
    <View style={styles.container}>
      <TabView
        navigationState={{ index: tabIndex, routes }}
        renderScene={renderScene}
        onIndexChange={setTabIndex}
        initialLayout={{ width: screenWidth }}
        renderTabBar={(props) => (
          <TabBar
            {...props}
            indicatorStyle={styles.tabIndicator}
            style={styles.tabBarStyle}
            activeColor="#4A5565"
            inactiveColor="#98A1AE"
            tabStyle={styles.tabStyle}
          />
        )}
      />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  tabBarStyle: {
    backgroundColor: "white",
    elevation: 0,
    shadowOpacity: 0,
    borderBottomWidth: 1,
    borderBottomColor: "#E5E7EB",
  },
  tabStyle: {
    paddingVertical: 4,
  },
  tabIndicator: {
    backgroundColor: "#0060FF",
    height: 2,
  },
  listContainer: {
    flex: 1,
  },
  scrollContent: {
    paddingHorizontal: 16,
    paddingTop: 16,
    paddingBottom: 80,
  },
  emptyText: {
    textAlign: "center",
    color: "#666666",
    fontSize: 16,
    marginTop: 40,
  },
}); 