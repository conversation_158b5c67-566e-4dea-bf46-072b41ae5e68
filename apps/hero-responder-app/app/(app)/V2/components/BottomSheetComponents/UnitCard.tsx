import React, { useMemo, useEffect } from "react";
import {
  View,
  StyleSheet,
  Text,
  TouchableOpacity,
} from "react-native";
import { useListOrdersForAsset } from "../../../../apis/services/workflow/orders/hooks";
import { ListOrdersForAssetRequest } from "proto/hero/orders/v2/orders_pb";
import { getSituationType } from "../../utils";
import { CaretIcon, FlagIcon } from "../../icons";

interface UnitCardProps {
  asset: any;
  activeSituations: any[];
  onPress: () => void;
}

export const UnitCard: React.FC<UnitCardProps> = ({ 
  asset, 
  activeSituations, 
  onPress 
}) => {
  const truncatedId = asset.id.replace(/[^0-9]/g, "").slice(0, 3);
  
  // Get asset name with proper formatting
  const assetName = asset.name
    ? asset.name.includes(" ")
      ? asset.name.split(" ")[0].trim()
      : asset.name.includes("+")
      ? asset.name.split("+")[0].trim()
      : asset.name
    : "";

  // Fetch active orders for this asset
  const { data: ordersData, refetch: refetchOrders } = useListOrdersForAsset(
    { assetId: asset.id, pageSize: 50, pageToken: "" } as ListOrdersForAssetRequest
  );

  // Set up polling for orders
  useEffect(() => {
    const interval = setInterval(() => {
      console.log(`[Order Poll] Refetching orders for asset ${asset.id} at`, new Date().toISOString());
      refetchOrders();
    }, 10000); // Poll every 10 seconds

    return () => clearInterval(interval);
  }, [refetchOrders, asset.id]);

  // Find active ASSIST_MEMBER order
  const activeOrder = useMemo(() => {
    if (!ordersData?.orders) return null;
    
    return ordersData.orders.find(order => 
      // @ts-ignore
      order.type === "ORDER_TYPE_ASSIST_MEMBER" &&
      // @ts-ignore
      order.status !== "ORDER_STATUS_COMPLETED" &&
      // @ts-ignore
      order.status !== "ORDER_STATUS_CANCELLED"
    );
  }, [ordersData]);

  // Find the assigned incident if there's an active order
  const assignedIncident = useMemo(() => {
    if (!activeOrder?.situationId) return null;
    
    return activeSituations.find(situation => 
      situation.id === activeOrder.situationId
    );
  }, [activeOrder, activeSituations]);

  // Get status display text - prioritize order status over asset status
  const getStatusText = () => {
    if (activeOrder) {
      // @ts-ignore
      switch (activeOrder.status) {
        // @ts-ignore
        case "ORDER_STATUS_CREATED":
          return "Dispatched";
        // @ts-ignore
        case "ORDER_STATUS_ACKNOWLEDGED":
          return "En Route";
        // @ts-ignore
        case "ORDER_STATUS_IN_PROGRESS":
          return "On Scene";
        default:
          // @ts-ignore
          return activeOrder.status?.replace("ORDER_STATUS_", "").toLowerCase() || "Unknown";
      }
    }
    
    // Fallback to asset status
    switch (asset.status) {
      case "ASSET_STATUS_AVAILABLE":
        return "Available";
      case "ASSET_STATUS_BUSY":
        return "Busy";
      default:
        return asset.status?.replace("ASSET_STATUS_", "").toLowerCase() || "Unknown";
    }
  };

  // Get status color - styled like priority badge
  const getStatusColorForUnit = () => {
    if (activeOrder) {
      // Any unavailable state (has an active order)
      return { backgroundColor: "#FFE4E6", color: "#EC003F", borderRadius: 4 };
    }
    // Available
    if (asset.status === "ASSET_STATUS_AVAILABLE") {
      return { backgroundColor: "#CEF1EC", color: "#009F89", borderRadius: 4 };
    }
    // Any other unavailable state
    return { backgroundColor: "#FFE4E6", color: "#EC003F", borderRadius: 4 };
  };

  // Calculate time ago for last update
  const getTimeAgo = () => {
    // Use order update time if available, otherwise asset update time
    const updateTime = activeOrder?.updateTime || asset.updatedAt;
    if (!updateTime) return "";
    
    const now = Date.now();
    const updated = new Date(updateTime).getTime();
    const minutesAgo = Math.max(0, Math.floor((now - updated) / 60000));
    return `${minutesAgo}m ago`;
  };

  return (
    <TouchableOpacity
      style={styles.unitCard}
      onPress={onPress}
    >
      {/* Top row: Unit ID and name */}
      <View style={styles.unitTitleRow}>
        <Text style={styles.unitTitle}>
          <Text style={styles.unitId}>{truncatedId}</Text>
          {assetName && <Text style={styles.unitName}> {assetName}</Text>}
        </Text>
        <CaretIcon />
      </View>

      {/* Middle: Incident assignment (if any) */}
      {assignedIncident && (
        <View style={styles.assignmentContainer}>
          <View style={styles.assignmentRow}>
            <FlagIcon color="#697282" />
            <Text style={styles.assignmentText} numberOfLines={1} ellipsizeMode="tail">
              <Text style={styles.assignmentIncidentId}>
                {assignedIncident.id?.replace(/[^0-9]/g, "").slice(0, 3) || "000"}
              </Text>{" "}
              {getSituationType(assignedIncident.type) || "Unknown"} • {assignedIncident.address || "Location not available"}
            </Text>
          </View>
        </View>
      )}

      {/* Bottom row: Status and time */}
      <View style={styles.unitBottomRow}>
        <View style={[styles.unitStatusBadge, getStatusColorForUnit()]}> 
          <Text style={[styles.unitStatusText, { color: getStatusColorForUnit().color }]}> 
            {getStatusText()} 
          </Text> 
        </View>
        <Text style={styles.unitTime}>
          {getTimeAgo()}
        </Text>
      </View>
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  unitCard: {
    backgroundColor: "white",
    borderRadius: 12,
    padding: 16,
    marginVertical: 6,
    borderWidth: 1,
    borderColor: "#E5E5E5",
    shadowColor: "#000",
    shadowOffset: {
      width: 0,
      height: 1,
    },
    shadowOpacity: 0.05,
    shadowRadius: 2,
    elevation: 1,
  },
  unitTitleRow: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    marginBottom: 8,
  },
  unitTitle: {
    fontSize: 16,
    fontWeight: "400",
    color: "#333333",
    flex: 1,
  },
  unitId: {
    fontWeight: "700",
    color: "#333333",
  },
  unitName: {
    fontWeight: "400",
    color: "#666666",
  },
  assignmentContainer: {
    marginBottom: 12,
  },
  assignmentRow: {
    flexDirection: "row",
    alignItems: "center",
    gap: 8,
    flex: 1,
  },
  assignmentText: {
    fontSize: 14,
    fontWeight: "400",
    color: "#333333",
    flex: 1,
  },
  assignmentIncidentId: {
    fontWeight: "700",
    color: "#333333",
  },
  unitBottomRow: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
  },
  unitStatusBadge: {
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 4,
    minWidth: 28,
    alignItems: "center",
    justifyContent: "center",
  },
  unitStatusText: {
    fontSize: 12,
    fontWeight: "600",
    // color is set dynamically
  },
  unitTime: {
    fontSize: 12,
    color: "#4B5563",
    fontWeight: "400",
  },
}); 