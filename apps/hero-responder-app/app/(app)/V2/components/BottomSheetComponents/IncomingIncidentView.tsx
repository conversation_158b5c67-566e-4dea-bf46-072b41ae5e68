import { BottomSheetScrollView } from "@gorhom/bottom-sheet";
import { jwtDecode } from "jwt-decode";
import { ListOrdersForSituationRequest } from "proto/hero/orders/v2/orders_pb";
import React, { useCallback, useEffect, useMemo, useRef } from "react";
import {
  Alert,
  Animated,
  Linking,
  StyleSheet,
  Text,
  TouchableOpacity,
  Vibration,
  View,
} from "react-native";
import Svg, { Path } from "react-native-svg";
import { useListOrdersForSituation } from "../../../../apis/services/workflow/orders/hooks";
import { useListSituations } from "../../../../apis/services/workflow/situations/hooks";
import { useAuth } from "../../../../AuthContext";
import { getSituationType } from "../../utils";

// Helper function to safely parse JSON
const safeJsonParse = (jsonString: string, fallback: any = {}) => {
  try {
    return JSON.parse(jsonString);
  } catch (error) {
    console.warn("Failed to parse JSON:", error, "String:", jsonString);
    return fallback;
  }
};

interface IncomingIncidentViewProps {
  incident: any;
  allAssets: any[];
  onConfirm?: () => void;
  onReject?: () => void;
  onBack: () => void;
}

interface DecodedToken {
  email: string;
  sub: string;
}

const useIncidentVibration = () => {
  const vibrationInterval = useRef<NodeJS.Timeout | null>(null);
  const isVibrating = useRef<boolean>(false);

  const startVibration = useCallback((incidentId: string) => {
    console.log(
      "[Incoming Incident Vibration] Starting vibration pattern for incident:",
      incidentId
    );

    if (isVibrating.current) {
      console.log(
        "[Incoming Incident Vibration] Vibration already active, stopping previous"
      );
      stopVibration();
    }

    isVibrating.current = true;

    const triggerVibration = async () => {
      if (!isVibrating.current) return;

      try {
        console.log(
          "[Incoming Incident Vibration] Triggering vibration at:",
          new Date().toISOString()
        );

        const vibrationPattern = [
          0, 200, 200, 200, 200, 200, 200, 200, 200, 200,
        ];
        Vibration.vibrate(vibrationPattern);
      } catch (error) {
        console.error(
          "[Incoming Incident Vibration] Error triggering vibration:",
          error
        );
      }
    };

    // Trigger initial vibration immediately
    triggerVibration();

    // Set up interval to repeat the pattern every 3 seconds
    vibrationInterval.current = setInterval(() => {
      console.log("[Incoming Incident Vibration] Interval vibration trigger");
      triggerVibration();
    }, 3000);
  }, []);

  const stopVibration = useCallback(() => {
    console.log("[Incoming Incident Vibration] Stopping vibration");
    isVibrating.current = false;

    // Cancel React Native vibration
    Vibration.cancel();

    // Clear interval
    if (vibrationInterval.current) {
      clearInterval(vibrationInterval.current);
      vibrationInterval.current = null;
    }
  }, []);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      stopVibration();
    };
  }, [stopVibration]);

  return { startVibration, stopVibration };
};

// Custom Shield Person Icon Component
const ShieldPersonIcon: React.FC<{ size?: number; color?: string }> = ({
  size = 16,
  color = "#697282",
}) => (
  <Svg width={size} height={size} viewBox="0 0 12 15" fill="none">
    <Path
      d="M6 8.25C6.725 8.25 7.34375 7.99375 7.85625 7.48125C8.36875 6.96875 8.625 6.35 8.625 5.625C8.625 4.9 8.36875 4.28125 7.85625 3.76875C7.34375 3.25625 6.725 3 6 3C5.275 3 4.65625 3.25625 4.14375 3.76875C3.63125 4.28125 3.375 4.9 3.375 5.625C3.375 6.35 3.63125 6.96875 4.14375 7.48125C4.65625 7.99375 5.275 8.25 6 8.25ZM6 15C4.175 14.5375 2.71875 13.5375 1.63125 12C0.54375 10.4625 0 8.7375 0 6.825V2.25L6 0L12 2.25V6.825C12 8.7375 11.4563 10.4625 10.3687 12C9.28125 13.5375 7.825 14.5375 6 15ZM6 13.425C6.7375 13.1875 7.39062 12.8156 7.95938 12.3094C8.52812 11.8031 9.025 11.2312 9.45 10.5938C8.9125 10.3188 8.35313 10.1094 7.77188 9.96563C7.19063 9.82188 6.6 9.75 6 9.75C5.4 9.75 4.80938 9.82188 4.22813 9.96563C3.64688 10.1094 3.0875 10.3188 2.55 10.5938C2.975 11.2312 3.47188 11.8031 4.04063 12.3094C4.60938 12.8156 5.2625 13.1875 6 13.425Z"
      fill={color}
    />
  </Svg>
);

// Custom Call Icon Component
const CallIcon: React.FC<{ size?: number; color?: string }> = ({
  size = 24,
  color = "#005FDD",
}) => (
  <Svg width={size} height={size} viewBox="0 0 24 25" fill="none">
    <Path
      d="M19.2222 15.7695L16.6822 15.4795C16.0722 15.4095 15.4722 15.6195 15.0422 16.0495L13.2022 17.8895C10.3722 16.4495 8.05223 14.1395 6.61223 11.2995L8.46223 9.44953C8.89223 9.01953 9.10223 8.41953 9.03223 7.80953L8.74223 5.28953C8.62223 4.27953 7.77223 3.51953 6.75223 3.51953H5.02223C3.89223 3.51953 2.95223 4.45953 3.02223 5.58953C3.55223 14.1295 10.3822 20.9495 18.9122 21.4795C20.0422 21.5495 20.9822 20.6095 20.9822 19.4795V17.7495C20.9922 16.7395 20.2322 15.8895 19.2222 15.7695Z"
      fill={color}
    />
  </Svg>
);

// Loading Spinner Component
const LoadingSpinner: React.FC<{ color: string }> = ({ color }) => {
  const spinValue = useRef(new Animated.Value(0)).current;

  useEffect(() => {
    const spin = () => {
      spinValue.setValue(0);
      Animated.timing(spinValue, {
        toValue: 1,
        duration: 1000,
        useNativeDriver: true,
      }).start(() => spin());
    };

    spin();
  }, [spinValue]);

  const spin = spinValue.interpolate({
    inputRange: [0, 1],
    outputRange: ["0deg", "360deg"],
  });

  return (
    <Animated.View
      style={[
        styles.loadingSpinner,
        { borderColor: color, transform: [{ rotate: spin }] },
      ]}
    />
  );
};

export const IncomingIncidentView: React.FC<IncomingIncidentViewProps> = ({
  incident,
  allAssets,
  onConfirm,
  onReject,
  onBack,
}) => {
  const { authTokens } = useAuth();
  const currentUserCognitoSub = authTokens?.idToken
    ? (() => {
        try {
          return jwtDecode<DecodedToken>(authTokens.idToken).sub;
        } catch (error) {
          console.error("Error decoding idToken:", error);
          return "";
        }
      })()
    : "";

  const truncatedId = incident.id?.replace(/[^0-9]/g, "").slice(0, 3) || "000";

  // Get vibration manager instance
  const { startVibration, stopVibration } = useIncidentVibration();

  // Fetch situations data with polling to get updated incident information
  const { data: situationsData, refetch: refetchSituations } =
    useListSituations({} as any);

  // Get the current incident from the situations data (this will have the latest updates)
  const currentIncident = useMemo(() => {
    if (!situationsData?.situations) return incident;

    const updatedIncident = situationsData.situations.find(
      (s) => s.id === incident.id
    );
    return updatedIncident || incident;
  }, [situationsData, incident]);

  // Fetch orders for this incident with polling
  const { data: ordersData, refetch: refetchOrders } =
    useListOrdersForSituation({
      situationId: incident.id,
      pageSize: 50,
      pageToken: "",
    } as ListOrdersForSituationRequest);

  // Set up polling for both situations and orders every 10 seconds
  useEffect(() => {
    const interval = setInterval(() => {
      console.log(
        `[Incoming Incident] Refetching data for incident ${incident.id} at`,
        new Date().toISOString()
      );
      refetchSituations();
      refetchOrders();
    }, 10000);

    return () => clearInterval(interval);
  }, [refetchSituations, refetchOrders, incident.id]);

  // Start vibration when component mounts, stop when unmounted
  useEffect(() => {
    startVibration(incident.id);
  }, [incident.id, startVibration]);

  // Calculate assigned responders with current user first
  const assignedResponders = useMemo(() => {
    if (!ordersData?.orders) return [];

    const responderAssets = ordersData.orders

      .filter(
        (order) =>
          // @ts-ignore
          order.type === "ORDER_TYPE_ASSIST_MEMBER" &&
          // @ts-ignore
          order.status !== "ORDER_STATUS_CANCELLED" &&
          // @ts-ignore
          order.status !== "ORDER_STATUS_REJECTED"
      )
      .map((order) => allAssets.find((asset) => asset.id === order.assetId))
      .filter(Boolean);

    // Find current user and other responders
    const currentUserAsset = responderAssets.find(
      (asset) => asset!.cognitoJwtSub === currentUserCognitoSub
    );
    const otherResponders = responderAssets.filter(
      (asset) => asset!.cognitoJwtSub !== currentUserCognitoSub
    );

    // Create sorted list with current user first
    const sortedResponders = [];
    if (currentUserAsset) {
      sortedResponders.push({
        id: currentUserAsset.id.replace(/[^0-9]/g, "").slice(0, 3),
        isCurrentUser: true,
      });
    }

    otherResponders.forEach((asset) => {
      sortedResponders.push({
        id: asset!.id.replace(/[^0-9]/g, "").slice(0, 3),
        isCurrentUser: false,
      });
    });

    return sortedResponders;
  }, [ordersData, allAssets, currentUserCognitoSub]);

  // Format responder display text with current user in blue
  const getResponderDisplayText = ():
    | { text: string; isCurrentUser: boolean }[]
    | string => {
    if (assignedResponders.length === 0) return "No responders assigned";

    const responderElements = assignedResponders.map((responder, index) => {
      const isLast = index === assignedResponders.length - 1;
      const separator = isLast ? "" : ", ";

      return {
        text: responder.id + separator,
        isCurrentUser: responder.isCurrentUser,
      };
    });

    return responderElements;
  };

  const responderDisplayElements = getResponderDisplayText();

  // Get status display text
  const getStatusText = () => {
    const status =
      currentIncident.status?.replace("SITUATION_STATUS_", "").toLowerCase() ||
      "unknown";
    return status.charAt(0).toUpperCase() + status.slice(1);
  };

  const getPriorityColor = (priority: number) => {
    switch (priority) {
      case 1:
        return { backgroundColor: "#FFE4E6", color: "#EC003F" };
      case 2:
        return { backgroundColor: "#FFEDD4", color: "#E17100" };
      case 3:
        return { backgroundColor: "#E5EFFF", color: "#0060FF" };
      case 4:
        return { backgroundColor: "#E5EFFF", color: "#0060FF" };
      default:
        return { backgroundColor: "#F3F4F6", color: "#4A5565" };
    }
  };

  // Handle call press with confirmation
  const handleCallPress = (phoneNumber: string) => {
    Linking.openURL(`tel:${phoneNumber}`).catch((err) => {
      console.error("Failed to make call:", err);
      Alert.alert("Error", "Unable to make phone call");
    });
  };

  return (
    <View style={styles.container}>
      {/* Header with incident title and status badges - no close button or title */}
      <View style={styles.header}>
        <Text style={styles.incidentTitle}>
          <Text style={styles.incidentId}>{truncatedId}</Text>
          <Text style={styles.incidentType}>
            {" "}
            {getSituationType(currentIncident.type) || "Unknown"}
          </Text>
        </Text>
        <View style={styles.statusRow}>
          <View
            style={[
              styles.priorityBadge,
              getPriorityColor(currentIncident.priority),
            ]}
          >
            <Text
              style={[
                styles.priorityText,
                getPriorityColor(currentIncident.priority),
              ]}
            >
              P{currentIncident.priority || "-"}
            </Text>
          </View>
          <View style={styles.statusBadge}>
            <Text style={styles.statusText}>{getStatusText()}</Text>
          </View>
          <View style={styles.responderBadge}>
            <ShieldPersonIcon
              size={12}
              color={
                assignedResponders.length > 0 &&
                assignedResponders[0].isCurrentUser
                  ? "#0060FF"
                  : "#4B5563"
              }
            />
            <View style={styles.responderTextContainer}>
              {Array.isArray(responderDisplayElements) ? (
                responderDisplayElements.map(
                  (
                    element: { text: string; isCurrentUser: boolean },
                    index: number
                  ) => (
                    <Text
                      key={index}
                      style={[
                        styles.responderText,
                        element.isCurrentUser && styles.currentUserText,
                      ]}
                    >
                      {element.text}
                    </Text>
                  )
                )
              ) : (
                <Text style={styles.responderText}>
                  {responderDisplayElements}
                </Text>
              )}
            </View>
          </View>
        </View>
      </View>

      {/* Content */}
      <BottomSheetScrollView
        style={styles.content}
        contentContainerStyle={styles.scrollContent}
        nestedScrollEnabled={true}
        showsVerticalScrollIndicator={false}
      >
        {/* Incident Description */}
        {currentIncident.description && (
          <>
            <Text style={styles.sectionTitle}>Incident Description</Text>
            <View style={styles.section}>
              <View style={styles.descriptionCard}>
                <Text style={styles.descriptionText}>
                  {currentIncident.description}
                </Text>
              </View>
            </View>
          </>
        )}

        {/* Caller Information */}
        <View style={styles.section}>
          {currentIncident.callerPhone ||
          currentIncident.contactNo ||
          (currentIncident.additionalInfoJson &&
            safeJsonParse(currentIncident.additionalInfoJson).callerNo) ? (
            <TouchableOpacity
              style={styles.callerCard}
              onPress={() =>
                handleCallPress(
                  currentIncident.callerPhone ||
                    currentIncident.contactNo ||
                    safeJsonParse(currentIncident.additionalInfoJson).callerNo
                )
              }
            >
              <View style={styles.callerInfo}>
                <Text style={styles.callerName}>
                  {currentIncident.callerName ||
                    currentIncident.reporterName ||
                    "Unknown Caller"}
                </Text>
                <Text style={styles.callerPhone}>
                  {currentIncident.callerPhone ||
                    currentIncident.contactNo ||
                    safeJsonParse(currentIncident.additionalInfoJson).callerNo}
                </Text>
              </View>
              <CallIcon size={20} color="#005FDD" />
            </TouchableOpacity>
          ) : (
            <View style={styles.callerCard}>
              <Text style={styles.callerName}>
                {currentIncident.callerName ||
                  currentIncident.reporterName ||
                  "Unknown Caller"}
              </Text>
            </View>
          )}
        </View>

        {/* Location */}
        <Text style={styles.sectionTitle}>Location</Text>
        <View style={styles.section}>
          <View style={styles.locationCard}>
            <Text style={styles.locationLabel}>Address</Text>
            <Text style={styles.locationValue}>
              {currentIncident.address || "Address not available"}
            </Text>
          </View>
        </View>

        {/* Incident Details */}
        <View style={styles.section}>
          <View style={styles.detailsGroupCard}>
            <View style={styles.detailRow}>
              <Text style={styles.detailLabel}>Incident Type</Text>
              <Text style={styles.detailValue}>
                {getSituationType(currentIncident.type) || "Unknown"}
              </Text>
            </View>
            <View style={styles.detailSeparator} />
            <View style={styles.detailRow}>
              <Text style={styles.detailLabel}>Incident #</Text>
              <Text style={styles.detailValue}>{truncatedId}</Text>
            </View>
          </View>
        </View>
      </BottomSheetScrollView>
    </View>
  );
};

// Footer component for the buttons
export const IncomingIncidentFooter = ({
  onConfirm,
  onReject,
  confirmLoading = false,
  rejectLoading = false,
}: {
  onConfirm: () => void;
  onReject: () => void;
  confirmLoading?: boolean;
  rejectLoading?: boolean;
}) => {
  const { startVibration, stopVibration } = useIncidentVibration();

  // Wrapped handlers to stop vibration when action is taken
  const handleConfirm = useCallback(() => {
    if (confirmLoading) return; // Prevent multiple clicks
    console.log(
      "[Incoming Incident Vibration] Confirm pressed - stopping vibration"
    );
    stopVibration();
    onConfirm();
  }, [stopVibration, onConfirm, confirmLoading]);

  const handleReject = useCallback(() => {
    if (rejectLoading) return; // Prevent multiple clicks
    console.log(
      "[Incoming Incident Vibration] Reject button pressed - showing confirmation"
    );

    Alert.alert(
      "Reject Incident",
      "Are you sure you want to reject this incident? This action cannot be undone.",
      [
        {
          text: "Cancel",
          style: "cancel",
          onPress: () => {
            console.log(
              "[Incoming Incident Vibration] Reject cancelled - continuing vibration"
            );
          },
        },
        {
          text: "Reject",
          style: "destructive",
          onPress: () => {
            console.log(
              "[Incoming Incident Vibration] Reject confirmed - stopping vibration"
            );
            stopVibration();
            onReject();
          },
        },
      ]
    );
  }, [stopVibration, onReject, rejectLoading]);

  return (
    <View style={styles.actionButtons}>
      <TouchableOpacity
        style={[styles.rejectButton, rejectLoading && styles.buttonDisabled]}
        onPress={handleReject}
        disabled={rejectLoading || confirmLoading}
      >
        {rejectLoading ? (
          <View style={styles.loadingContainer}>
            <LoadingSpinner color="#4A5565" />
            <Text style={[styles.rejectButtonText, styles.loadingText]}>
              Rejecting...
            </Text>
          </View>
        ) : (
          <Text style={styles.rejectButtonText}>Reject</Text>
        )}
      </TouchableOpacity>
      <TouchableOpacity
        style={[styles.confirmButton, confirmLoading && styles.buttonDisabled]}
        onPress={handleConfirm}
        disabled={confirmLoading || rejectLoading}
      >
        {confirmLoading ? (
          <View style={styles.loadingContainer}>
            <LoadingSpinner color="#FFFFFF" />
            <Text style={[styles.confirmButtonText, styles.loadingText]}>
              Confirming...
            </Text>
          </View>
        ) : (
          <Text style={styles.confirmButtonText}>Confirm</Text>
        )}
      </TouchableOpacity>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: "white",
  },
  header: {
    padding: 20,
    paddingBottom: 16,
    paddingTop: 16,
  },
  incidentTitle: {
    fontSize: 22,
    fontWeight: "400",
    color: "#000",
    marginBottom: 12,
  },
  incidentId: {
    fontWeight: "700",
  },
  incidentType: {
    fontWeight: "400",
  },
  statusRow: {
    flexDirection: "row",
    alignItems: "center",
    gap: 12,
  },
  priorityBadge: {
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 4,
    minWidth: 28,
    alignItems: "center",
  },
  priorityText: {
    fontSize: 12,
    fontWeight: "600",
  },
  statusBadge: {
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 4,
    minWidth: 28,
    alignItems: "center",
    backgroundColor: "#F3F4F6",
  },
  statusText: {
    fontSize: 14,
    color: "#4A5565",
    fontWeight: "500",
  },
  responderBadge: {
    flexDirection: "row",
    alignItems: "center",
    gap: 4,
    paddingHorizontal: 8,
    paddingVertical: 4,
  },
  responderTextContainer: {
    flexDirection: "row",
    flexWrap: "wrap",
  },
  responderText: {
    fontSize: 12,
    fontWeight: "600",
    color: "#4B5563",
  },
  currentUserText: {
    color: "#0060FF",
    fontWeight: "600",
  },
  content: {
    flex: 1,
    paddingTop: 20,
  },
  scrollContent: {
    paddingBottom: 20,
  },
  section: {
    paddingHorizontal: 20,
    marginBottom: 24,
  },
  sectionTitle: {
    marginLeft: 36,
    fontSize: 14,
    fontWeight: "500",
    color: "#697282",
    marginBottom: 8,
  },
  descriptionCard: {
    backgroundColor: "#F8F9FA",
    borderRadius: 12,
    padding: 16,
    borderWidth: 1,
    borderColor: "#E5E5E5",
  },
  descriptionText: {
    fontSize: 16,
    color: "#000",
    lineHeight: 24,
    fontWeight: "500",
  },
  callerCard: {
    backgroundColor: "#F8F9FA",
    borderRadius: 12,
    padding: 16,
    borderWidth: 1,
    borderColor: "#E5E5E5",
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
  },
  callerInfo: {
    flex: 1,
  },
  callerName: {
    fontSize: 16,
    fontWeight: "500",
    color: "#000",
    marginBottom: 4,
  },
  callerPhone: {
    fontSize: 14,
    color: "#666",
  },
  locationCard: {
    backgroundColor: "#F8F9FA",
    borderRadius: 12,
    padding: 16,
    borderWidth: 1,
    borderColor: "#E5E5E5",
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
  },
  locationLabel: {
    fontSize: 14,
    fontWeight: "400",
    color: "#4A5565",
  },
  locationValue: {
    fontSize: 14,
    fontWeight: "500",
    color: "#000",
    textAlign: "right",
    flex: 1,
    marginLeft: 16,
  },
  detailsGroupCard: {
    backgroundColor: "#F8F9FA",
    borderRadius: 12,
    borderWidth: 1,
    borderColor: "#E5E5E5",
    overflow: "hidden",
  },
  detailRow: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    padding: 16,
  },
  detailSeparator: {
    height: 1,
    backgroundColor: "#E5E5E5",
    marginHorizontal: 16,
  },
  detailLabel: {
    fontSize: 14,
    fontWeight: "400",
    color: "#4A5565",
  },
  detailValue: {
    fontSize: 14,
    fontWeight: "500",
    color: "#000",
    textAlign: "right",
    flex: 1,
    marginLeft: 16,
  },
  actionButtons: {
    flexDirection: "row",
    gap: 12,
    paddingHorizontal: 20,
    paddingVertical: 20,
    paddingBottom: 40,
    backgroundColor: "white",
  },
  rejectButton: {
    flex: 1, // 1/3 of available space
    backgroundColor: "#F3F4F6",
    paddingVertical: 16,
    borderRadius: 12,
    alignItems: "center",
    justifyContent: "center",
  },
  rejectButtonText: {
    fontSize: 16,
    fontWeight: "600",
    color: "#4A5565",
  },
  confirmButton: {
    flex: 2, // 2/3 of available space
    backgroundColor: "#005FDD",
    paddingVertical: 16,
    borderRadius: 12,
    alignItems: "center",
    justifyContent: "center",
  },
  confirmButtonText: {
    fontSize: 16,
    fontWeight: "600",
    color: "#FFFFFF",
  },
  buttonDisabled: {
    opacity: 0.6,
  },
  loadingContainer: {
    flexDirection: "row",
    alignItems: "center",
    gap: 8,
  },
  loadingText: {
    opacity: 0.8,
  },
  loadingSpinner: {
    width: 16,
    height: 16,
    borderRadius: 8,
    borderWidth: 2,
    borderTopColor: "transparent",
  },
});
