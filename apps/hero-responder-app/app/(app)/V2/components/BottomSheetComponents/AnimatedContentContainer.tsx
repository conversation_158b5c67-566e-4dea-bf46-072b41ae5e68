import React, { useEffect, useRef, useState } from 'react';
import { Animated, Dimensions } from 'react-native';

const { width: screenWidth } = Dimensions.get('window');

interface AnimatedContentContainerProps {
  children: React.ReactNode;
  viewKey: string; // Unique key for each view to trigger animations
  animationDirection?: 'forward' | 'back';
}

export const AnimatedContentContainer: React.FC<AnimatedContentContainerProps> = ({
  children,
  viewKey,
  animationDirection = 'forward',
}) => {
  const opacityAnim = useRef(new Animated.Value(1)).current;
  const prevViewKey = useRef<string>(viewKey);
  
  // Keep track of the currently displayed content
  const [displayedContent, setDisplayedContent] = useState(children);
  const [isAnimating, setIsAnimating] = useState(false);

  useEffect(() => {
    // Only animate if the view key actually changed and we're not already animating
    if (prevViewKey.current !== viewKey && !isAnimating) {
      setIsAnimating(true);
      
      // Start animation sequence - just fade out and fade in
      Animated.sequence([
        Animated.timing(opacityAnim, {
          toValue: 0,
          duration: 50,
          useNativeDriver: true,
        }),
        Animated.timing(opacityAnim, {
          toValue: 0,
          duration: 25,
          useNativeDriver: true,
        }),
      ]).start(() => {
        Animated.timing(opacityAnim, {
          toValue: 1,
          duration: 75,
          useNativeDriver: true,
        }).start(() => {
          setIsAnimating(false);
        });
      });

      setTimeout(() => {
        setDisplayedContent(children);
      }, 50);

      prevViewKey.current = viewKey;
    } else if (prevViewKey.current === viewKey && !isAnimating) {
      setDisplayedContent(children);
    }
  }, [viewKey, animationDirection, opacityAnim, children, isAnimating]);

  // Initialize displayed content on first render
  useEffect(() => {
    if (prevViewKey.current === viewKey) {
      setDisplayedContent(children);
    }
  }, []);

  return (
    <Animated.View
      style={{
        flex: 1,
        opacity: opacityAnim,
      }}
    >
      {displayedContent}
    </Animated.View>
  );
}; 