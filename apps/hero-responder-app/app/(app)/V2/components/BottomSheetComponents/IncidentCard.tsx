import React, { useMemo } from "react";
import {
  View,
  StyleSheet,
  Text,
  TouchableOpacity,
} from "react-native";
import { useListOrdersForSituation } from "../../../../apis/services/workflow/orders/hooks";
import { ListOrdersForSituationRequest } from "proto/hero/orders/v2/orders_pb";
import { getSituationType } from "../../utils";
import { CaretIcon } from "../../icons";
import Svg, { Path } from "react-native-svg";

interface IncidentCardProps {
  situation: any;
  assets: any[];
  currentUserCognitoSub?: string;
  activeOrders?: any[];
  onPress: () => void;
}

export const IncidentCard: React.FC<IncidentCardProps> = ({ 
  situation, 
  assets, 
  currentUserCognitoSub,
  activeOrders = [],
  onPress 
}) => {
  // Still fetch orders for this specific incident to show responder counts
  const { data: ordersData } = useListOrdersForSituation(
    { situationId: situation.id } as ListOrdersForSituationRequest
  );

  // Calculate time ago
  const now = Date.now();
  const start = typeof situation.updateTime === "string"
    ? new Date(situation.updateTime).getTime()
    : typeof situation.createdAt === "string"
    ? new Date(situation.createdAt).getTime()
    : Date.now();
  const minutesAgo = Math.max(0, Math.floor((now - start) / 60000));

  // Check if current user has an active order for this incident using the passed activeOrders
  const hasCurrentUserOrder = useMemo(() => {
    if (!activeOrders || activeOrders.length === 0 || !currentUserCognitoSub) return false;
    
    // Find current user's asset
    const currentUserAsset = assets.find(asset => asset.cognitoJwtSub === currentUserCognitoSub);
    if (!currentUserAsset) return false;
    
    // Check if current user has an active assist member order for this specific incident
    const hasActiveOrder = activeOrders.some(order => 
      // @ts-ignore
      order.type === "ORDER_TYPE_ASSIST_MEMBER" &&
      order.assetId === currentUserAsset.id &&
      order.situationId === situation.id &&
      // @ts-ignore
      order.status !== "ORDER_STATUS_COMPLETED" &&
      // @ts-ignore
      order.status !== "ORDER_STATUS_CANCELLED" &&
      // @ts-ignore
      order.status !== "ORDER_STATUS_REJECTED"
    );

    return hasActiveOrder;
  }, [activeOrders, currentUserCognitoSub, assets, situation.id]);

  // Calculate assigned responders (matching web logic)
  const responderNames = useMemo(() => {
    if (!ordersData?.orders) return [];
    
    return Array.from(
      new Set(
        ordersData.orders
          // @ts-ignore
          .filter((order) => order.type === "ORDER_TYPE_ASSIST_MEMBER")
          .map((order) => assets.find((asset) => asset.id === order.assetId))
          // @ts-ignore
          .filter((asset) => asset && asset.type === "ASSET_TYPE_RESPONDER")
          .map((asset) => asset!.id.replace(/[^0-9]/g, "").slice(0, 3))
      )
    );
  }, [ordersData, assets]);

  // Format responder display text
  const getResponderDisplayText = () => {
    if (responderNames.length === 0) return null;
    if (responderNames.length === 1) return responderNames[0];
    if (responderNames.length === 2) return `${responderNames[0]}, ${responderNames[1]}`;
    
    // More than 2: show first 2 + count
    const additionalCount = responderNames.length - 2;
    return `${responderNames[0]}, ${responderNames[1]} +${additionalCount}`;
  };

  const responderDisplayText = getResponderDisplayText();

  const getPriorityColor = (priority: number) => {
    switch (priority) {
      case 1: 
        return { backgroundColor: "#FFE4E6", color: "#EC003F" };
      case 2:
        return { backgroundColor: "#FFEDD4", color: "#E17100" };
      case 3:
        return { backgroundColor: "#E5EFFF", color: "#0060FF" };
      case 4:
        return { backgroundColor: "#E5EFFF", color: "#0060FF" };
      default:
        return { backgroundColor: "#F3F4F6", color: "#4A5565" };
    }
  };

  return (
    <TouchableOpacity
      style={styles.listItem}
      onPress={onPress}
    >
      <View style={styles.situationContent}>
        {/* Top row: Incident title, current tag, and caret */}
        <View style={styles.situationTitleRow}>
          <Text style={styles.situationTitle}>
            <Text style={styles.situationId}>
              {situation.id?.replace(/[^0-9]/g, "").slice(0, 3) || "000"}
            </Text>{" "}
            {getSituationType(situation.type) || "Unknown"}
          </Text>
          <View style={styles.titleRowRight}>
            {hasCurrentUserOrder && (
              <View style={styles.currentTag}>
                <Text style={styles.currentTagText}>CURRENT</Text>
              </View>
            )}
            <CaretIcon />
          </View>
        </View>
        
        {/* Address */}
        <Text style={styles.situationAddress}>
          {situation.address || "Address not available"}
        </Text>
        
        {/* Bottom row: Priority & Status on left, Responder info on right */}
        <View style={styles.situationBottomRow}>
          <View style={styles.situationLeftInfo}>
            <View style={[styles.priorityBadge, getPriorityColor(situation.priority)]}>
              <Text style={[styles.priorityText, getPriorityColor(situation.priority)]}>
                P{situation.priority || "-"}
              </Text>
            </View>
            <View style={styles.statusBadge}>
              <Text style={styles.statusText}>
                {situation.status?.replace("SITUATION_STATUS_", "").toLowerCase() || "unknown"}
              </Text>
            </View>
            <Text style={styles.situationTime}>
              {minutesAgo}m ago
            </Text>
          </View>
          
          {/* Responder info on the right */}
          {responderDisplayText && (
            <View style={styles.responderInfo}>
              <View style={styles.responderIcon}>
                <Svg width="10" height="12" viewBox="0 0 12 15" fill="none">
                  <Path
                    d="M6 8.25C6.725 8.25 7.34375 7.99375 7.85625 7.48125C8.36875 6.96875 8.625 6.35 8.625 5.625C8.625 4.9 8.36875 4.28125 7.85625 3.76875C7.34375 3.25625 6.725 3 6 3C5.275 3 4.65625 3.25625 4.14375 3.76875C3.63125 4.28125 3.375 4.9 3.375 5.625C3.375 6.35 3.63125 6.96875 4.14375 7.48125C4.65625 7.99375 5.275 8.25 6 8.25ZM6 15C4.175 14.5375 2.71875 13.5375 1.63125 12C0.54375 10.4625 0 8.7375 0 6.825V2.25L6 0L12 2.25V6.825C12 8.7375 11.4563 10.4625 10.3687 12C9.28125 13.5375 7.825 14.5375 6 15ZM6 13.425C6.7375 13.1875 7.39062 12.8156 7.95938 12.3094C8.52812 11.8031 9.025 11.2312 9.45 10.5938C8.9125 10.3188 8.35313 10.1094 7.77188 9.96563C7.19063 9.82188 6.6 9.75 6 9.75C5.4 9.75 4.80938 9.82188 4.22813 9.96563C3.64688 10.1094 3.0875 10.3188 2.55 10.5938C2.975 11.2312 3.47188 11.8031 4.04063 12.3094C4.60938 12.8156 5.2625 13.1875 6 13.425Z"
                    fill="#4B5563"
                  />
                </Svg>
              </View>
              <Text style={styles.responderText}>
                {responderDisplayText}
              </Text>
            </View>
          )}
        </View>
      </View>
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  listItem: {
    backgroundColor: "white",
    borderRadius: 12,
    padding: 16,
    marginVertical: 6,
    borderWidth: 1,
    borderColor: "#E5E5E5",
    shadowColor: "#000",
    shadowOffset: {
      width: 0,
      height: 1,
    },
    shadowOpacity: 0.05,
    shadowRadius: 2,
    elevation: 1,
  },
  situationContent: {
    flex: 1,
  },
  situationTitleRow: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    marginBottom: 8,
  },
  situationTitle: {
    fontSize: 16,
    fontWeight: "400",
    color: "#333333",
    flex: 1,
  },
  situationId: {
    fontWeight: "700",
    color: "#333333",
  },
  titleRowRight: {
    flexDirection: "row",
    alignItems: "center",
    gap: 8,
  },
  currentTag: {
    backgroundColor: "#E5EFFF",
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 4,
  },
  currentTagText: {
    fontSize: 10,
    fontWeight: "600",
    color: "#0060FF",
    letterSpacing: 0.5,
  },
  situationAddress: {
    fontSize: 14,
    color: "#666666",
    marginBottom: 12,
  },
  situationBottomRow: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
  },
  situationLeftInfo: {
    flexDirection: "row",
    alignItems: "center",
    gap: 8,
  },
  situationStatus: {
    fontSize: 12,
    fontWeight: "600",
    color: "#4B5563",
    textTransform: "capitalize",
  },
  situationTime: {
    fontSize: 12,
    color: "#4B5563",
    fontWeight: "400",
  },
  priorityBadge: {
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 4,
    minWidth: 28,
    alignItems: "center",
  },
  priorityText: {
    fontSize: 12,
    fontWeight: "600",
    color: "white",
  },
  statusBadge: {
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 4,
    minWidth: 28,
    alignItems: "center",
    backgroundColor: "#F3F4F6",
  },
  statusText: {
    fontSize: 12,
    color: "#4A5565",
    fontWeight: "500",
    textTransform: "capitalize",
  },
  responderInfo: {
    flexDirection: "row",
    alignItems: "center",
    gap: 8,
  },
  responderIcon: {
    width: 12,
    height: 15,
  },
  responderText: {
    fontSize: 12,
    fontWeight: "600",
    color: "#4B5563",
  },
}); 