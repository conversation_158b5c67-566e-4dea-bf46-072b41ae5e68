import React, { useMemo, useEffect, useRef } from "react";
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  ScrollView,
  SafeAreaView,
  Dimensions,
  Animated,
} from "react-native";
import { useRouter } from "expo-router";
import Svg, { Path } from "react-native-svg";
import { useListSituationsForAsset } from "../../../apis/services/workflow/situations/hooks";
import { useGetAssetByCognitoSub } from "../../../apis/services/workflow/assets/hooks";
import { ListSituationsForAssetRequest, ListSituationsForAssetRequestSchema } from "proto/hero/situations/v2/situations_pb";
import { create } from "@bufbuild/protobuf";
import { IncidentCard } from "./BottomSheetComponents/IncidentCard";
import { useAuth } from "../../../AuthContext";
import { useNavigation } from "../contexts/NavigationContext";
import { jwtDecode } from "jwt-decode";

const { width, height } = Dimensions.get("window");

interface DecodedToken {
  email: string;
  sub: string;
}

function CloseIcon() {
  return (
    <Svg width={24} height={24} viewBox="0 0 24 24" fill="none">
      <Path
        d="M18 6L6 18M6 6L18 18"
        stroke="#111827"
        strokeWidth={2}
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </Svg>
  );
}

// Skeleton loading component that matches IncidentCard layout
function SkeletonIncidentCard() {
  const shimmerValue = useRef(new Animated.Value(0)).current;

  useEffect(() => {
    const createShimmerAnimation = () => {
      return Animated.sequence([
        Animated.timing(shimmerValue, {
          toValue: 1,
          duration: 1000,
          useNativeDriver: true,
        }),
        Animated.timing(shimmerValue, {
          toValue: 0,
          duration: 1000,
          useNativeDriver: true,
        }),
      ]);
    };

    const animation = Animated.loop(createShimmerAnimation());
    animation.start();

    return () => animation.stop();
  }, [shimmerValue]);

  const shimmerOpacity = shimmerValue.interpolate({
    inputRange: [0, 1],
    outputRange: [0.3, 0.7],
  });

  return (
    <View style={styles.skeletonCard}>
      {/* Title row skeleton */}
      <View style={styles.skeletonTitleRow}>
        <Animated.View 
          style={[
            styles.skeletonTitle, 
            { opacity: shimmerOpacity }
          ]} 
        />
        <Animated.View 
          style={[
            styles.skeletonCaret, 
            { opacity: shimmerOpacity }
          ]} 
        />
      </View>
      
      {/* Address skeleton */}
      <Animated.View 
        style={[
          styles.skeletonAddress, 
          { opacity: shimmerOpacity }
        ]} 
      />
      
      {/* Bottom row skeleton */}
      <View style={styles.skeletonBottomRow}>
        <View style={styles.skeletonLeftInfo}>
          <Animated.View 
            style={[
              styles.skeletonPriority, 
              { opacity: shimmerOpacity }
            ]} 
          />
          <Animated.View 
            style={[
              styles.skeletonStatus, 
              { opacity: shimmerOpacity }
            ]} 
          />
          <Animated.View 
            style={[
              styles.skeletonTime, 
              { opacity: shimmerOpacity }
            ]} 
          />
        </View>
        
        <View style={styles.skeletonRightInfo}>
          <Animated.View 
            style={[
              styles.skeletonResponder, 
              { opacity: shimmerOpacity }
            ]} 
          />
        </View>
      </View>
    </View>
  );
}

// Loading skeleton with multiple cards
function LoadingSkeleton() {
  return (
    <View style={styles.incidentsList}>
      {[1, 2, 3, 4, 5].map((index) => (
        <SkeletonIncidentCard key={index} />
      ))}
    </View>
  );
}

export default function MyRecentIncidentsScreen() {
  const router = useRouter();
  const { authTokens } = useAuth();
  const { navigateToIncident, closeDrawer } = useNavigation();

  // Get current user's cognito sub
  let cognitoJwtSub = "";
  if (authTokens?.idToken) {
    try {
      const decoded = jwtDecode<DecodedToken>(authTokens.idToken);
      cognitoJwtSub = decoded.sub;
    } catch (error) {
      console.error("Error decoding idToken:", error);
    }
  }

  // Get user's asset information
  const { data: getAssetByCongitoSubData } = useGetAssetByCognitoSub(cognitoJwtSub);
  const userAsset = getAssetByCongitoSubData?.asset;

  // Calculate 24 hours ago
  const now = new Date();
  const yesterday = new Date(now.getTime() - 24 * 60 * 60 * 1000);

  // Search parameters for asset-specific incidents
  const searchParams: ListSituationsForAssetRequest = useMemo(() => {
    if (!userAsset?.id) {
      return {} as ListSituationsForAssetRequest;
    }

    return create(ListSituationsForAssetRequestSchema, {
      assetId: userAsset.id,
      createdAfter: yesterday.toISOString(),
      createdBefore: now.toISOString(),
      pageSize: 50,
    });
  }, [userAsset?.id]);

  const { data: searchResults, isLoading, error } = useListSituationsForAsset(searchParams);

  const handleIncidentPress = (situation: any) => {
    console.log("Incident pressed:", situation.id);
    // Navigate to incident details, close the modal, and close the drawer
    navigateToIncident(situation);
    closeDrawer();
    router.back();
  };

  const handleClose = () => {
    router.back();
  };

  return (
    <SafeAreaView style={styles.container}>
      <View style={styles.header}>
        <View style={styles.headerContent}>
          <View style={styles.titleContainer}>
            <Text style={styles.title}>My Recent Incidents</Text>
            <Text style={styles.subtitle}>Last 24 Hours</Text>
          </View>
          <TouchableOpacity
            style={styles.closeButton}
            onPress={handleClose}
            activeOpacity={0.7}
          >
            <CloseIcon />
          </TouchableOpacity>
        </View>
      </View>

      <ScrollView 
        style={styles.scrollContainer}
        contentContainerStyle={styles.scrollContent}
        showsVerticalScrollIndicator={false}
      >
        {!userAsset ? (
          <LoadingSkeleton />
        ) : isLoading ? (
          <LoadingSkeleton />
        ) : error ? (
          <View style={styles.errorContainer}>
            <Text style={styles.errorText}>Failed to load incidents</Text>
            <Text style={styles.errorDetails}>{error?.message}</Text>
          </View>
        ) : !searchResults?.situations?.length ? (
          <View style={styles.emptyContainer}>
            <Text style={styles.emptyTitle}>No Recent Incidents</Text>
            <Text style={styles.emptyText}>
              You haven't been involved in any incidents in the last 24 hours.
            </Text>
          </View>
        ) : (
          <View style={styles.incidentsList}>
            {searchResults.situations.map((situation) => (
              <IncidentCard
                key={situation.id}
                situation={situation}
                assets={[]}
                onPress={() => handleIncidentPress(situation)}
              />
            ))}
          </View>
        )}
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: "#F9FAFB",
  },
  header: {
    backgroundColor: "#F9FAFB",
    paddingHorizontal: 24,
    paddingTop: 36,
    paddingBottom: 24,
  },
  headerContent: {
    flexDirection: "row",
    alignItems: "flex-start",
    justifyContent: "space-between",
  },
  titleContainer: {
    flex: 1,
  },
  title: {
    fontSize: 28,
    fontWeight: "700",
    color: "#111827",
    marginBottom: 4,
  },
  subtitle: {
    fontSize: 16,
    color: "#6B7280",
    fontWeight: "400",
  },
  closeButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: "#F3F4F6",
    justifyContent: "center",
    alignItems: "center",
    marginTop: -8,
    marginRight: -8,
  },
  scrollContainer: {
    flex: 1,
    paddingBottom: 80,
  },
  scrollContent: {
    paddingHorizontal: 24,
    paddingBottom: 40,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
    paddingVertical: 60,
  },
  loadingText: {
    fontSize: 16,
    color: "#6B7280",
  },
  errorContainer: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
    paddingVertical: 60,
  },
  errorText: {
    fontSize: 16,
    color: "#DC2626",
    marginBottom: 8,
  },
  errorDetails: {
    fontSize: 14,
    color: "#6B7280",
    textAlign: "center",
  },
  emptyContainer: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
    paddingVertical: 60,
  },
  emptyTitle: {
    fontSize: 20,
    fontWeight: "600",
    color: "#111827",
    marginBottom: 8,
  },
  emptyText: {
    fontSize: 16,
    color: "#6B7280",
    textAlign: "center",
    lineHeight: 24,
  },
  incidentsList: {
    gap: 0,
  },
  // Skeleton styles
  skeletonCard: {
    backgroundColor: "white",
    borderRadius: 12,
    padding: 16,
    marginVertical: 6,
    borderWidth: 1,
    borderColor: "#E5E5E5",
    shadowColor: "#000",
    shadowOffset: {
      width: 0,
      height: 1,
    },
    shadowOpacity: 0.05,
    shadowRadius: 2,
    elevation: 1,
  },
  skeletonTitleRow: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    marginBottom: 8,
  },
  skeletonTitle: {
    height: 16,
    backgroundColor: "#E5E7EB",
    borderRadius: 4,
    flex: 1,
    marginRight: 12,
  },
  skeletonCaret: {
    width: 16,
    height: 16,
    backgroundColor: "#E5E7EB",
    borderRadius: 4,
  },
  skeletonAddress: {
    height: 14,
    backgroundColor: "#E5E7EB",
    borderRadius: 4,
    width: "80%",
    marginBottom: 12,
  },
  skeletonBottomRow: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
  },
  skeletonLeftInfo: {
    flexDirection: "row",
    alignItems: "center",
    gap: 8,
  },
  skeletonPriority: {
    width: 28,
    height: 20,
    backgroundColor: "#E5E7EB",
    borderRadius: 4,
  },
  skeletonStatus: {
    width: 60,
    height: 12,
    backgroundColor: "#E5E7EB",
    borderRadius: 4,
  },
  skeletonTime: {
    width: 40,
    height: 12,
    backgroundColor: "#E5E7EB",
    borderRadius: 4,
  },
  skeletonRightInfo: {
    flexDirection: "row",
    alignItems: "center",
  },
  skeletonResponder: {
    width: 50,
    height: 12,
    backgroundColor: "#E5E7EB",
    borderRadius: 4,
  },
}); 