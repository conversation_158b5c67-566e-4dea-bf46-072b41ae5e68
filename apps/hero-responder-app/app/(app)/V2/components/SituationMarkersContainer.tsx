import React, { useEffect } from "react";
import MapboxGL from "@rnmapbox/maps";
import { useListSituations } from "../../../apis/services/workflow/situations/hooks";
import { SituationMarker } from "./Markers/SituationMarker";
import { Order } from "proto/hero/orders/v2/orders_pb";
import { hookOrderTypeToString } from "../../../apis/services/workflow/orders/enumConverters";

interface SituationMarkersContainerProps {
  onSituationMarkerPress: (situation: any) => void;
  selectedIncidentId?: string | null;
  activeOrders: Order[];
  incomingIncidentSituationId?: string | null;
}

export const SituationMarkersContainer: React.FC<
  SituationMarkersContainerProps
> = ({
  onSituationMarkerPress,
  selectedIncidentId,
  activeOrders,
  incomingIncidentSituationId,
}) => {
  // Situation polling setup
  const { data: situationsData, refetch: refetchSituations } =
    useListSituations({} as any);

  // Set up manual polling every 5 seconds for situations
  useEffect(() => {
    const interval = setInterval(() => {
      console.log("refetching situations");
      refetchSituations();
    }, 5000);

    return () => clearInterval(interval);
  }, [refetchSituations]);

  // Filter out situations that are resolved, completed, or escalated
  const activeSituations = (situationsData?.situations || []).filter(
    (situation) =>
      situation.latitude &&
      situation.longitude &&
      //@ts-ignore
      situation.status !==
        //@ts-ignore
        "SITUATION_STATUS_RESOLVED"
  );

  // Helper function to check if a situation has an active assist member order
  const hasActiveAssistMemberOrder = (situationId: string) => {
    return activeOrders.some(
      (order) =>
        order.situationId === situationId &&
        hookOrderTypeToString(order.type) === "ORDER_TYPE_ASSIST_MEMBER"
    );
  };

  return (
    <>
      {/* Situation Markers */}
      {activeSituations
        .filter((situation) => situation.latitude && situation.longitude)
        .map((situation) => {
          const isSelected =
            selectedIncidentId === situation.id ||
            incomingIncidentSituationId === situation.id;
          const hasActiveOrder = hasActiveAssistMemberOrder(situation.id);

          return (
            <MapboxGL.MarkerView
              key={situation.id}
              id={`situation-${situation.id}`}
              coordinate={[situation.longitude, situation.latitude]}
            >
              <SituationMarker
                situation={situation}
                onPress={() => onSituationMarkerPress(situation)}
                isSelected={isSelected}
                hasActiveOrder={hasActiveOrder}
              />
            </MapboxGL.MarkerView>
          );
        })}
    </>
  );
};
