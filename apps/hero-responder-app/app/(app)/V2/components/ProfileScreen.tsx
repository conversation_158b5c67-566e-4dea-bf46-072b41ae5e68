import { useRouter } from "expo-router";
import { jwtDecode } from "jwt-decode";
import React from "react";
import {
  SafeAreaView,
  StatusBar,
  StyleSheet,
  Text,
  TouchableOpacity,
  View,
} from "react-native";
import { useGetAssetByCognitoSub } from "../../../apis/services/workflow/assets/hooks";
import { useAuth } from "../../../AuthContext";
import { DecodedToken } from "../types";

export default function ProfileScreen() {
  const router = useRouter();
  const { authTokens } = useAuth();

  // Get current user's cognitoJwtSub from auth token
  const currentUserCognitoSub = authTokens?.idToken
    ? (() => {
        try {
          return jwtDecode<DecodedToken>(authTokens.idToken).sub;
        } catch (error) {
          console.error("Error decoding idToken:", error);
          return "";
        }
      })()
    : "";

  // Fetch user asset data
  const { data: assetData } = useGetAssetByCognitoSub(currentUserCognitoSub);
  const userAsset = assetData?.asset;

  const handleClose = () => {
    router.back();
  };

  // Extract unit (first 3 digits of ID)
  const getUnit = (id: string | undefined) => {
    if (!id) return "";
    return id.replace(/[^0-9]/g, "").slice(0, 3);
  };

  return (
    <SafeAreaView style={styles.container}>
      <StatusBar barStyle="dark-content" backgroundColor="white" />
      <View style={styles.content}>
        {/* Header */}
        <View style={styles.header}>
          <Text style={styles.title}>Profile</Text>
          <TouchableOpacity onPress={handleClose} style={styles.closeButton}>
            <Text style={styles.closeButtonText}>✕</Text>
          </TouchableOpacity>
        </View>

        {/* Form Fields */}
        <View style={styles.form}>
          <View style={styles.fieldContainer}>
            <Text style={styles.label}>Name</Text>
            <View style={styles.inputContainer}>
              <Text style={styles.inputText}>{userAsset?.name || ""}</Text>
            </View>
          </View>

          <View style={styles.fieldContainer}>
            <Text style={styles.label}>Unit</Text>
            <View style={styles.inputContainer}>
              <Text style={styles.inputText}>{getUnit(userAsset?.id)}</Text>
            </View>
          </View>

          <View style={styles.fieldContainer}>
            <Text style={styles.label}>Phone Number</Text>
            <View style={styles.inputContainer}>
              <Text style={styles.inputText}>{userAsset?.contactNo || ""}</Text>
            </View>
          </View>

          <View style={styles.fieldContainer}>
            <Text style={styles.label}>Email</Text>
            <View style={styles.inputContainer}>
              <Text style={styles.inputText}>
                {userAsset?.contactEmail || ""}
              </Text>
            </View>
          </View>
        </View>
      </View>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: "white",
  },
  content: {
    flex: 1,
    paddingHorizontal: 24,
    paddingTop: 30,
  },
  header: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    marginBottom: 32,
  },
  title: {
    fontSize: 28,
    fontWeight: "600",
    color: "#000",
  },
  closeButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: "#F3F4F6",
    justifyContent: "center",
    alignItems: "center",
    marginTop: -8,
    marginRight: -8,
  },
  closeButtonText: {
    fontSize: 24,
    color: "#000",
    fontWeight: "300",
  },
  form: {
    flex: 1,
  },
  fieldContainer: {
    marginBottom: 24,
  },
  label: {
    fontSize: 14,
    color: "#697282",
    marginBottom: 8,
    marginLeft: 10,
    fontWeight: "400",
  },
  inputContainer: {
    backgroundColor: "#F9FAFB",
    borderRadius: 12,
    paddingHorizontal: 16,
    paddingVertical: 16,
    borderWidth: 1,
    borderColor: "#E5E7EB",
  },
  inputText: {
    fontSize: 16,
    color: "#000",
    fontWeight: "500",
  },
});
