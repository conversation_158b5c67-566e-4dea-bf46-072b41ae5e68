import React, { useMemo, useRef, useState, useCallback } from "react";
import {
  View,
  StyleSheet,
  Text,
} from "react-native";
import BottomSheet, {
  BottomSheetView,
  BottomSheetFooter,
} from "@gorhom/bottom-sheet";
import { useListSituations } from "../../../apis/services/workflow/situations/hooks";
import { useListAssets } from "../../../apis/services/workflow/assets/hooks";
import { useAuth } from "../../../AuthContext";
import { jwtDecode } from "jwt-decode";
import { DecodedToken } from "../types";
import { BottomSheetListView, AnimatedContentContainer, UnitDetailView, IncidentDetailView, IncomingIncidentView, IncomingIncidentFooter } from "./BottomSheetComponents";
import { IncidentDetailViewRef } from "./BottomSheetComponents/IncidentDetailView";

// Navigation state types
type BottomSheetView = 
  | { type: 'list' }
  | { type: 'incident-detail'; incident: any }
  | { type: 'unit-detail'; unit: any }
  | { type: 'assisting'; situation: any }
  | { type: 'incoming-incident'; incident: any; order: any };

interface MapBottomSheetProps {
  onSituationPress?: (situation: any) => void;
  onAssetPress?: (asset: any) => void;
  onBottomSheetChange?: (index: number) => void;
  onSelectedUnitChange?: (unit: any | null) => void;
  onSelectedIncidentChange?: (incident: any | null) => void;
  onConfirmIncident?: () => void;
  onRejectIncident?: () => void;
  confirmLoading?: boolean;
  rejectLoading?: boolean;
  activeOrders?: any[];
  firstAssistMemberOrder?: any;
  userInfoFromServer?: any;
  optimisticAssistingStatus?: string | null;
  onAddUpdateButtonStateChange?: (data: {
    shouldShow: boolean;
    onPress: () => void;
    currentView: any;
    tabIndex: number;
  }) => void;
  onAddUpdate?: (message: string) => Promise<void>;
  currentIncidentClearedByUser?: boolean;
  onIncidentCleared?: () => void;
}

// Ref interface for external navigation
export interface MapBottomSheetRef {
  navigateToUnit: (unit: any, allowFromIncoming?: boolean) => void;
  navigateToIncident: (incident: any, allowFromIncoming?: boolean) => void;
  navigateToIncomingIncident: (incident: any, order: any) => void;
  navigateToList: (allowFromIncoming?: boolean) => void;
  getSelectedUnit: () => any | null;
  getSelectedIncident: () => any | null;
  getCurrentViewType: () => string;
  forceExpandToFull: () => void;
  getIncidentDetailRef: () => IncidentDetailViewRef | null;
}

export const MapBottomSheet = React.forwardRef<MapBottomSheetRef, MapBottomSheetProps>(({
  onSituationPress,
  onAssetPress,
  onBottomSheetChange,
  onSelectedUnitChange,
  onSelectedIncidentChange,
  onConfirmIncident,
  onRejectIncident,
  confirmLoading,
  rejectLoading,
  activeOrders = [],
  firstAssistMemberOrder,
  userInfoFromServer,
  optimisticAssistingStatus,
  onAddUpdateButtonStateChange,
  onAddUpdate,
  currentIncidentClearedByUser,
  onIncidentCleared,
}, ref) => {
  const bottomSheetRef = useRef<BottomSheet>(null);
  const incidentDetailRef = useRef<IncidentDetailViewRef>(null);
  const [currentIndex, setCurrentIndex] = useState(0);
  const [currentView, setCurrentView] = useState<BottomSheetView>({ type: 'list' });
  const [animationDirection, setAnimationDirection] = useState<'forward' | 'back'>('forward');
  const [currentTabIndex, setCurrentTabIndex] = useState(0);
  
  // Check if user is already assisting based on order status (including optimistic status)
  const isAlreadyAssisting = 
    firstAssistMemberOrder?.typeSpecificStatus === "ASSISTING" ||
    optimisticAssistingStatus === "ASSISTING";

  // Check if user is assisting the specific incident being viewed OR has cleared it
  const isAssistingCurrentIncident = useMemo(() => {
    if (currentView.type !== 'incident-detail') {
      return false;
    }
    
    // If user is currently assisting this incident
    if (isAlreadyAssisting && firstAssistMemberOrder?.situationId === currentView.incident?.id) {
      return true;
    }
    
    // If user has cleared this specific incident, maintain the same snap points
    if (currentIncidentClearedByUser) {
      return true;
    }
    
    return false;
  }, [isAlreadyAssisting, firstAssistMemberOrder, currentView, currentIncidentClearedByUser]);

  // Dynamic snap points based on current view and assisting status
  const snapPoints = useMemo(() => {
    if (currentView.type === 'incoming-incident') {
      return ["20%", "50%", "85%"];
    }
    if (currentView.type === 'incident-detail' && isAssistingCurrentIncident) {
      return ["50%", "85%"];
    }
    if (currentView.type === 'unit-detail' || currentView.type === 'incident-detail') {
      return ["15%", "50%", "85%"];
    }
    return ["10%", "50%", "85%"];
  }, [currentView.type, isAssistingCurrentIncident]);

  // Get data
  const { data: situationsData } = useListSituations({} as any);
  const { data: assetsData } = useListAssets({} as any);
  const { authTokens } = useAuth();
  const currentUserCognitoSub = authTokens?.idToken
    ? jwtDecode<DecodedToken>(authTokens.idToken).sub
    : "";

  // Filter active situations
  const activeSituations = useMemo(() => {
    return (situationsData?.situations || []).filter(
      (situation) =>
        // @ts-ignore
        situation.status !== "SITUATION_STATUS_RESOLVED" &&
        // @ts-ignore
        situation.status !== "SITUATION_STATUS_COMPLETED" &&
        // @ts-ignore
        situation.status !== "SITUATION_STATUS_ESCALATED"
    );
  }, [situationsData]);

  // Filter active assets (excluding current user for units list)
  const activeAssets = useMemo(() => {
    return (assetsData?.assets || []).filter(
      (asset) =>
        // @ts-ignore
        asset.type === "ASSET_TYPE_RESPONDER" &&
        // @ts-ignore
        (asset.status === "ASSET_STATUS_AVAILABLE" ||
          // @ts-ignore
          asset.status === "ASSET_STATUS_BUSY") &&
        asset.cognitoJwtSub !== currentUserCognitoSub
    );
  }, [assetsData, currentUserCognitoSub]);

  // All assets including current user (for incident responder calculations)
  const allAssets = useMemo(() => {
    return (assetsData?.assets || []).filter(
      (asset) =>
        // @ts-ignore
        asset.type === "ASSET_TYPE_RESPONDER" &&
        // @ts-ignore
        (asset.status === "ASSET_STATUS_AVAILABLE" ||
          // @ts-ignore
          asset.status === "ASSET_STATUS_BUSY")
    );
  }, [assetsData]);

  // Get currently selected unit and incident
  const selectedUnit = useMemo(() => {
    return currentView.type === 'unit-detail' ? currentView.unit : null;
  }, [currentView]);

  const selectedIncident = useMemo(() => {
    return currentView.type === 'incident-detail' || currentView.type === 'incoming-incident' 
      ? currentView.incident 
      : null;
  }, [currentView]);

  // Notify parent when selected unit or incident changes
  React.useEffect(() => {
    onSelectedUnitChange?.(selectedUnit);
  }, [selectedUnit, onSelectedUnitChange]);

  React.useEffect(() => {
    onSelectedIncidentChange?.(selectedIncident);
  }, [selectedIncident, onSelectedIncidentChange]);

  // Handle bottom sheet position changes
  const handleSheetChanges = useCallback((index: number) => {
    setCurrentIndex(index);
    onBottomSheetChange?.(index);
  }, [onBottomSheetChange]);

  // Check if user has an active order for the current incident
  const hasCurrentUserOrderForCurrentIncident = useMemo(() => {
    if (currentView.type !== 'incident-detail' || !firstAssistMemberOrder || !userInfoFromServer) {
      return false;
    }
    
    const isForThisIncident = firstAssistMemberOrder.situationId === currentView.incident?.id;
    const isActiveOrder = 
      firstAssistMemberOrder.status !== "ORDER_STATUS_COMPLETED" &&
      firstAssistMemberOrder.status !== "ORDER_STATUS_CANCELLED" &&
      firstAssistMemberOrder.status !== "ORDER_STATUS_REJECTED";
    
    return isForThisIncident && isActiveOrder;
  }, [currentView, firstAssistMemberOrder, userInfoFromServer]);

  // Notify parent about floating action button state
  React.useEffect(() => {
    if (onAddUpdateButtonStateChange) {
      const shouldShow = 
        currentView.type === 'incident-detail' &&
        hasCurrentUserOrderForCurrentIncident &&
        currentTabIndex === 1 && // Updates tab
        currentIndex !== 0; // Not collapsed

      onAddUpdateButtonStateChange({
        shouldShow,
        onPress: () => incidentDetailRef.current?.openAddUpdateModal(),
        currentView,
        tabIndex: currentTabIndex,
      });
    }
  }, [
    currentView,
    hasCurrentUserOrderForCurrentIncident,
    currentTabIndex,
    currentIndex,
    onAddUpdateButtonStateChange,
  ]);

  // Navigation functions with animation direction tracking
  const navigateToIncident = useCallback((incident: any, allowFromIncoming = false) => {
    // Block navigation if currently showing incoming incident view, unless explicitly allowed
    if (currentView.type === 'incoming-incident' && !allowFromIncoming) {
      console.log('[Navigation] Blocked navigation to incident - incoming incident view active');
      return;
    }

    setAnimationDirection('forward');
    setCurrentView({ type: 'incident-detail', incident });
        
    // Use setTimeout to ensure snap points have been updated before navigating
    setTimeout(() => {
      if (currentIndex === 0) {
        bottomSheetRef.current?.snapToIndex(1);
      }
    }, 0);
    
    onSituationPress?.(incident);
  }, [onSituationPress, isAlreadyAssisting, firstAssistMemberOrder, currentView.type]);

  const navigateToUnit = useCallback((unit: any, allowFromIncoming = false) => {
    // Block navigation if currently showing incoming incident view, unless explicitly allowed
    if (currentView.type === 'incoming-incident' && !allowFromIncoming) {
      console.log('[Navigation] Blocked navigation to unit - incoming incident view active');
      return;
    }

    setAnimationDirection('forward');
    setCurrentView({ type: 'unit-detail', unit });
    bottomSheetRef.current?.snapToIndex(1);
    onAssetPress?.(unit);
  }, [onAssetPress, currentView.type]);

  const navigateToIncomingIncident = useCallback((incident: any, order: any) => {
    setAnimationDirection('forward');
    setCurrentView({ type: 'incoming-incident', incident, order });
    bottomSheetRef.current?.snapToIndex(1);
  }, []);

  const navigateBack = useCallback((allowFromIncoming = false) => {
    // Block navigation if currently showing incoming incident view, unless explicitly allowed
    if (currentView.type === 'incoming-incident' && !allowFromIncoming) {
      console.log('[Navigation] Blocked back navigation - incoming incident view active');
      return;
    }

    setAnimationDirection('back');
    setCurrentView({ type: 'list' });
    bottomSheetRef.current?.snapToIndex(1);
  }, [currentView.type]);

  const navigateToList = useCallback((allowFromIncoming = false) => {
    // Block navigation if currently showing incoming incident view, unless explicitly allowed
    if (currentView.type === 'incoming-incident' && !allowFromIncoming) {
      console.log('[Navigation] Blocked navigation to list - incoming incident view active');
      return;
    }

    setAnimationDirection('back');
    setCurrentView({ type: 'list' });
    bottomSheetRef.current?.snapToIndex(1);
  }, [currentView.type]);

  // Get currently selected unit and incident functions
  const getSelectedUnit = useCallback(() => {
    return selectedUnit;
  }, [selectedUnit]);

  const getSelectedIncident = useCallback(() => {
    return selectedIncident;
  }, [selectedIncident]);

  const forceExpandToFull = useCallback(() => {
    const currentSnapPoints = snapPoints;
    const targetIndex = currentSnapPoints.length === 2 ? 1 : currentSnapPoints.length - 1;
    
    console.log(`[ForceExpand] Snap points: ${JSON.stringify(currentSnapPoints)}, targeting index: ${targetIndex}`);
    bottomSheetRef.current?.snapToIndex(targetIndex);
  }, [snapPoints]);

  // Expose methods via ref for external navigation
  React.useImperativeHandle(ref, () => ({
    navigateToUnit,
    navigateToIncident,
    navigateToIncomingIncident,
    navigateToList,
    getSelectedUnit,
    getSelectedIncident,
    getCurrentViewType: () => currentView.type,
    forceExpandToFull,
    getIncidentDetailRef: () => incidentDetailRef.current,
  }), [navigateToUnit, navigateToIncident, navigateToIncomingIncident, navigateToList, getSelectedUnit, getSelectedIncident, currentView.type, forceExpandToFull]);

  // Generate a unique key for the current view to trigger animations
  const getViewKey = () => {
    switch (currentView.type) {
      case 'list':
        return 'list';
      case 'incident-detail':
        return `incident-${currentView.incident?.id || 'unknown'}`;
      case 'unit-detail':
        return `unit-${currentView.unit?.id || 'unknown'}`;
      case 'assisting':
        return `assisting-${currentView.situation?.id || 'unknown'}`;
      case 'incoming-incident':
        return `incoming-incident-${currentView.incident?.id || 'unknown'}`;
      default:
        return 'default';
    }
  };

  // Summary view for collapsed state
  const renderSummaryView = () => {
    // Default summary for list view
    return (
      <View style={styles.summaryContainer}>
        <Text style={styles.summaryText}>
          {activeSituations.length} Active Incidents • {activeAssets.length}{" "}
          Active Units
        </Text>
      </View>
    );
  };

  // Render footer for incoming incident view
  const renderFooter = useCallback((props: any) => {
    if (currentView.type === 'incoming-incident') {
      return (
        <BottomSheetFooter {...props} bottomInset={0}>
          <IncomingIncidentFooter 
            onConfirm={onConfirmIncident || (() => {})}
            onReject={onRejectIncident || (() => {})}
            confirmLoading={confirmLoading}
            rejectLoading={rejectLoading}
          />
        </BottomSheetFooter>
      );
    }
    return null;
  }, [currentView.type, onConfirmIncident, onRejectIncident, confirmLoading, rejectLoading]);

  // Main content renderer based on current view
  const renderContent = () => {
    // Show summary only for list view when collapsed
    if (currentIndex === 0 && currentView.type === 'list') {
      return renderSummaryView();
    }

    return (
      <AnimatedContentContainer 
        viewKey={getViewKey()} 
        animationDirection={animationDirection}
      >
        {(() => {
          switch (currentView.type) {
            case 'list':
              return (
                <BottomSheetListView
                  activeSituations={activeSituations}
                  activeAssets={activeAssets}
                  allAssets={allAssets}
                  currentUserCognitoSub={currentUserCognitoSub}
                  activeOrders={activeOrders}
                  onSituationPress={navigateToIncident}
                  onAssetPress={navigateToUnit}
                />
              );
            
            case 'incident-detail':
              return (
                <IncidentDetailView 
                  ref={incidentDetailRef}
                  incident={currentView.incident}
                  allAssets={allAssets}
                  onBack={navigateBack}
                  firstAssistMemberOrder={firstAssistMemberOrder}
                  userInfoFromServer={userInfoFromServer}
                  mapBottomSheetRef={{ current: { forceExpandToFull } }}
                  optimisticAssistingStatus={optimisticAssistingStatus}
                  bottomSheetIndex={currentIndex}
                  onTabChange={setCurrentTabIndex}
                  onAddUpdate={onAddUpdate}
                  onIncidentCleared={onIncidentCleared}
                />
              );
            
            case 'unit-detail':
              return (
                <UnitDetailView 
                  unit={currentView.unit}
                  activeSituations={activeSituations}
                  onBack={navigateBack}
                />
              );

            case 'incoming-incident':
              return (
                <IncomingIncidentView 
                  incident={currentView.incident}
                  allAssets={allAssets}
                  onBack={navigateBack}
                />
              );
            
            case 'assisting':
              // TODO: Implement or import BottomSheetAssisting component
              return (
                <View style={styles.placeholderContainer}>
                  <Text style={styles.placeholderText}>
                    Assisting View
                  </Text>
                  <Text style={styles.placeholderSubtext}>
                    Situation ID: {currentView.situation?.id || 'Unknown'}
                  </Text>
                  <Text style={styles.backButton} onPress={() => navigateBack()}>
                    ← Back to List
                  </Text>
                </View>
              );
            
            default:
              return (
                <BottomSheetListView
                  activeSituations={activeSituations}
                  activeAssets={activeAssets}
                  allAssets={allAssets}
                  currentUserCognitoSub={currentUserCognitoSub}
                  activeOrders={activeOrders}
                  onSituationPress={navigateToIncident}
                  onAssetPress={navigateToUnit}
                />
              );
          }
        })()}
      </AnimatedContentContainer>
    );
  };

  return (
    <BottomSheet
      ref={bottomSheetRef}
      index={0}
      snapPoints={snapPoints}
      onChange={handleSheetChanges}
      handleIndicatorStyle={styles.handleIndicator}
      backgroundStyle={styles.bottomSheetBackground}
      style={styles.bottomSheet}
      enableDynamicSizing={false}
      footerComponent={renderFooter}
    >
      <BottomSheetView style={styles.bottomSheetContent}>
        {renderContent()}
      </BottomSheetView>
    </BottomSheet>
  );
});

const styles = StyleSheet.create({
  bottomSheet: {
    zIndex: 1000,
  },
  bottomSheetContent: {
    flex: 1,
  },
  summaryContainer: {
    paddingHorizontal: 20,
    paddingVertical: 15,
    alignItems: "center",
    backgroundColor: 'white',
  },
  summaryText: {
    fontSize: 16,
    fontWeight: "600",
    color: "#333333",
  },
  handleIndicator: {
    backgroundColor: "#CCCCCC",
    width: 40,
    height: 4,
  },
  bottomSheetBackground: {
    backgroundColor: "white",
    shadowColor: "#000",
    shadowOffset: {
      width: 0,
      height: -2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 5,
  },
  placeholderContainer: {
    flex: 1,
    alignItems: "center",
    justifyContent: "center",
    padding: 20,
  },
  placeholderText: {
    fontSize: 24,
    fontWeight: "600",
    color: "#333333",
    marginBottom: 10,
  },
  placeholderSubtext: {
    fontSize: 16,
    color: "#666666",
    marginBottom: 20,
  },
  backButton: {
    fontSize: 16,
    color: "#005FDD",
    fontWeight: "500",
  },
});


