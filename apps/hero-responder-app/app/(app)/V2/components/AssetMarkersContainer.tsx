import React, { useEffect } from "react";
import MapboxGL from "@rnmapbox/maps";
import { useListAssets } from "../../../apis/services/workflow/assets/hooks";
import { useAuth } from "../../../AuthContext";
import { jwtDecode } from "jwt-decode";
import { AssetMarker } from "./Markers/AssetMarker";
import { DecodedToken } from "../types";

interface AssetMarkersContainerProps {
  onAssetMarkerPress: (asset: any) => void;
  selectedUnitId?: string | null;
}

export const AssetMarkersContainer: React.FC<AssetMarkersContainerProps> = ({
  onAssetMarkerPress,
  selectedUnitId,
}) => {
  // Asset polling setup
  const { data: assetsData, refetch: refetchAssets } = useListAssets({} as any);

  // Get current user's cognitoJwtSub from auth token
  const { authTokens } = useAuth();
  const currentUserCognitoSub = authTokens?.idToken ? jwtDecode<DecodedToken>(authTokens.idToken).sub : "";

  // Set up manual polling every 5 seconds for assets
  useEffect(() => {
    const interval = setInterval(() => {
      console.log("refetching assets");
      refetchAssets();
    }, 5000);

    return () => clearInterval(interval);
  }, [refetchAssets]);

  // Filter active responder assets
  const activeResponderAssets = (assetsData?.assets || []).filter(
    (asset) =>
      asset.latitude &&
      asset.longitude &&
      //@ts-ignore
      asset.type === "ASSET_TYPE_RESPONDER" &&
      //@ts-ignore
      (asset.status === "ASSET_STATUS_AVAILABLE" ||
        //@ts-ignore
        asset.status === "ASSET_STATUS_BUSY") &&
      // Filter out current user's asset
      asset.cognitoJwtSub !== currentUserCognitoSub
  );

  return (
    <>
      {/* Asset Markers */}
      {activeResponderAssets
        .filter((asset) => asset.latitude && asset.longitude)
        .map((asset) => (
          <MapboxGL.MarkerView
            key={asset.id}
            id={`asset-${asset.id}`}
            coordinate={[asset.longitude, asset.latitude]}
          >
            <AssetMarker
              asset={asset}
              onPress={() => onAssetMarkerPress(asset)}
              isSelected={selectedUnitId === asset.id}
            />
          </MapboxGL.MarkerView>
        ))}
    </>
  );
}; 