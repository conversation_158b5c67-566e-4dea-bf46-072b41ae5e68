import React from "react";
import Svg, { Path } from "react-native-svg";

// Flag Icon Component
export const FlagIcon = ({ color = "#E17100" }: { color?: string }): React.JSX.Element => (
  <Svg width="20" height="20" viewBox="0 0 20 20" fill="none">
    <Path
      d="M11.5833 4.58464L11.3833 3.58464C11.3083 3.2013 10.9667 2.91797 10.5667 2.91797H4.58333C4.125 2.91797 3.75 3.29297 3.75 3.7513V16.2513C3.75 16.7096 4.125 17.0846 4.58333 17.0846C5.04167 17.0846 5.41667 16.7096 5.41667 16.2513V11.2513H10.0833L10.2833 12.2513C10.3583 12.643 10.7 12.918 11.1 12.918H15.4167C15.875 12.918 16.25 12.543 16.25 12.0846V5.41797C16.25 4.95964 15.875 4.58464 15.4167 4.58464H11.5833Z"
      fill={color}
    />
  </Svg>
);

// Caret Icon Component
export const CaretIcon = (): React.JSX.Element => (
  <Svg width="24" height="25" viewBox="0 0 24 25" fill="none">
    <Path
      d="M8.99953 16.3766L12.8795 12.4966L8.99953 8.61656C8.60953 8.22656 8.60953 7.59656 8.99953 7.20656C9.38953 6.81656 10.0195 6.81656 10.4095 7.20656L14.9995 11.7966C15.3895 12.1866 15.3895 12.8166 14.9995 13.2066L10.4095 17.7966C10.0195 18.1866 9.38953 18.1866 8.99953 17.7966C8.61953 17.4066 8.60953 16.7666 8.99953 16.3766Z"
      fill="#697282"
    />
  </Svg>
);
