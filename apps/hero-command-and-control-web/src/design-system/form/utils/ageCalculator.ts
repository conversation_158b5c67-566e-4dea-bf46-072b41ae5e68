/**
 * Calculate age from birth date
 * @param birthDate - The birth date
 * @returns Age as a string (e.g., "25 years", "2 months", "15 days") or empty string if invalid
 */
export const calculateAge = (birthDate: Date | null): string => {
  if (!birthDate || isNaN(birthDate.getTime())) {
    return "";
  }

  const today = new Date();
  const birth = new Date(birthDate);

  // If birth date is in the future, return empty
  if (birth > today) {
    return "";
  }

  let years = today.getFullYear() - birth.getFullYear();
  let months = today.getMonth() - birth.getMonth();
  let days = today.getDate() - birth.getDate();

  // Adjust for negative days
  if (days < 0) {
    months--;
    const lastMonth = new Date(today.getFullYear(), today.getMonth(), 0);
    days += lastMonth.getDate();
  }

  // Adjust for negative months
  if (months < 0) {
    years--;
    months += 12;
  }

  // Format the result
  if (years > 0) {
    if (years === 1) {
      return "1 year";
    }
    return `${years} years`;
  } else if (months > 0) {
    if (months === 1) {
      return "1 month";
    }
    return `${months} months`;
  } else if (days >= 0) {
    if (days === 1) {
      return "1 day";
    }
    return `${days} days`;
  }

  return "";
};
