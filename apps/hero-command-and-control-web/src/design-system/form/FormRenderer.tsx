import * as MuiIcons from "@mui/icons-material";
import CloseIcon from "@mui/icons-material/Close";
import EditIcon from "@mui/icons-material/Edit";
import {
  Box,
  Dialog,
  DialogActions,
  DialogContent,
  DialogContentText,
  DialogTitle,
  IconButton,
  Paper,
  Tab,
  Tabs,
} from "@mui/material";
import React, {
  forwardRef,
  useEffect,
  useImperativeHandle,
  useRef,
  useState,
} from "react";
import { Button } from "../components/Button";
import { DatePicker } from "../components/DatePicker";
import { SelectGroup, SelectOption } from "../components/SelectGroup";
import {
  DropdownOption,
  InputMask,
  InputType,
  TextInput,
} from "../components/TextInput";
import { Typography } from "../components/Typography";
import { colors } from "../tokens";
import { FieldSet } from "./components/FieldSet";
import {
  FieldCondition,
  FormConfig,
  FormField,
  FormRenderMode,
  FormSection,
} from "./types";
import { calculateAge } from "./utils/ageCalculator";

const DynamicIcon = ({ iconName }: { iconName: string }) => {
  // @ts-expect-error TODO: Fix type issue - MuiIcons has dynamic properties
  const Icon = MuiIcons[iconName];
  return Icon ? <Icon /> : null;
};

export interface FormValues {
  [sectionId: string]: {
    [fieldId: string]: any;
  };
}

// Internal fields interface for the extra tab
interface InternalFields {
  internalTags?: DropdownOption[];
  internalNotes?: string;
}

export interface FormRendererRef {
  getValues: () => FormValues;
  getFlatValues: () => Record<string, any>;
  submitForm: () => void;
  validate: () => boolean;
  reset: () => void;
}

/**
 * Descriptor for an extra tab that can be injected when the renderer
 * is in "consume" mode.
 */
export interface ExtraTab {
  /** Display label for the tab. */
  name: string;
  /** React content rendered inside the tab panel. */
  content: React.ReactNode;
  /**
   * Desired position for the tab:
   *  - `"start"` – before the autogenerated tabs
   *  - `"end"`   – after the autogenerated tabs (default)
   *  - `number`  – zero‑based index among all tabs
   */
  position?: "start" | "end" | number;
}

/** Internal representation used while composing the final tabs list. */
interface TabDescriptor {
  key: string;
  label: string;
  icon?: React.ReactNode;
  /** `true` if the tab came from `additionalTabs`, `false` if from `config.sections`. */
  isCustom: boolean;
  section?: FormSection;
  content?: React.ReactNode;
}

interface TabPanelProps {
  children?: React.ReactNode;
  index: number;
  value: number;
}

function TabPanel(props: TabPanelProps) {
  const { children, value, index, ...other } = props;

  return (
    <div
      role="tabpanel"
      hidden={value !== index}
      style={{ height: "100%" }}
      {...other}
    >
      {value === index && <Box sx={{ p: 1, height: "100%" }}>{children}</Box>}
    </div>
  );
}

/**
 * Props for {@link FormRenderer}.
 */
interface FormRendererProps {
  /** Form configuration defining sections/fields. Create through the FormBuilder. */
  config: FormConfig;
  /** Callback invoked with structured values on submit. */
  onSubmit?: (values: FormValues) => void;
  /** Initial values for the form (structured). */
  initialValues?: FormValues;
  /** Optional CSS class name applied to the `<form>` element. */
  className?: string;
  /** Whether to render a submit button inside the component. */
  showSubmitButton?: boolean;
  /**
   * Controls edit mode behavior.
   * If true: Form is always in read-only mode with no edit option.
   * If false: Form starts in read-only mode but can be toggled to edit mode (this last part only true for consume mode).
   */
  readOnly?: boolean;
  /** Fires whenever a field value changes. */
  onValueChange?: (values: FormValues) => void;
  /**
   * Extra tabs to render in **consume** mode.
   * They're merged with the section‑generated tabs following the `position`
   * rules defined in {@link ExtraTab}.
   */
  additionalTabs?: ExtraTab[];
  /** Render mode – `"ingest"` (default) or `"consume"`. */
  renderMode?: FormRenderMode;
  /**
   * Fixed height for the card in "consume" mode.
   * Defaults to 510 px. Accepts a number (pixels) or any valid CSS height string.
   */
  containerHeight?: number | string;
  /**
   * Whether to show the internal tags & notes tab in consume mode.
   * This tab will be added at the end of all tabs.
   */
  showInternalTab?: boolean;
  /**
   * External data sources for conditional logic.
   * Array of objects containing field data that can be referenced in conditions.
   */
  externalData?: Record<string, any>[];
  /**
   * Custom title to override the config title. Useful for contextual titles.
   */
  customTitle?: string;
}

/**
 * **FormRenderer**
 *
 * Renders a configurable form in two modes:
 *  - **ingest**: traditional vertical layout.
 *  - **consume**: compact card with tabbed sections.
 *
 * The component is fully controlled/imperative through a `ref`
 * exposing helpers like `getValues`, `submitForm`, and `reset`.
 *
 * **External Conditions:**
 * You can reference external data in conditional logic by passing `externalData` prop.
 *
 * Example usage:
 * ```jsx
 * const externalData = [
 *   { userId: "123", userRole: "admin", hasPermission: true },
 *   { sessionData: { isLoggedIn: true, theme: "dark" } }
 * ];
 *
 * // In your form field conditions, you can now reference:
 * // - "userId" with value "123"
 * // - "userRole" with value "admin"
 * // - "hasPermission" with operator "exists"
 * // - "sessionData.isLoggedIn" (if you want to support nested paths)
 *
 * <FormRenderer
 *   config={formConfig}
 *   externalData={externalData}
 *   onSubmit={handleSubmit}
 * />
 * ```
 */
export const FormRenderer = forwardRef<FormRendererRef, FormRendererProps>(
  (
    {
      config,
      onSubmit,
      initialValues = {},
      className = "",
      showSubmitButton = false,
      readOnly = false,
      onValueChange,
      additionalTabs = [],
      renderMode = "ingest",
      containerHeight = 510,
      showInternalTab = false,
      externalData = [],
      customTitle,
    },
    ref
  ) => {
    // Safety check for empty config
    const safeConfig: FormConfig = {
      id: config.id || "default-form-id",
      title: customTitle || config.title,
      sections: config.sections || [],
    };

    // For tabs in consume mode
    const [activeTabIndex, setActiveTabIndex] = useState(0);

    // State for edit mode - default is not editing (in readonly state at start)
    const [isEditing, setIsEditing] = useState(false);
    const [hasChanges, setHasChanges] = useState(false);
    const [showDiscardDialog, setShowDiscardDialog] = useState(false);
    const [pendingTabChange, setPendingTabChange] = useState<number | null>(
      null
    );
    const [fieldErrors, setFieldErrors] = useState<
      Record<string, Record<string, string>>
    >({});

    // Initialize the structured form values
    const [formValues, setFormValues] = useState<FormValues>(() => {
      // Create structured object from schema
      const initialStructured: FormValues = {};
      safeConfig.sections.forEach((section) => {
        initialStructured[section.id] = {};

        section.fields?.forEach((field) => {
          initialStructured[section.id][field.id] = field.defaultValue || "";
        });
      });

      // Use initialValues if provided, preserving any age fields
      if (initialValues && Object.keys(initialValues).length > 0) {
        // Merge initialValues into the structured object
        Object.keys(initialValues).forEach((sectionId) => {
          if (
            initialValues[sectionId] &&
            typeof initialValues[sectionId] === "object"
          ) {
            if (!initialStructured[sectionId]) {
              initialStructured[sectionId] = {};
            }
            Object.keys(initialValues[sectionId]).forEach((fieldId) => {
              initialStructured[sectionId][fieldId] =
                initialValues[sectionId][fieldId];
            });
          }
        });
      }

      return initialStructured;
    });

    // Keep a copy of the original values to detect changes - use JSON to create a deep copy
    const [originalValues, setOriginalValues] = useState<FormValues>(() => {
      return JSON.parse(JSON.stringify(formValues));
    });

    // State for internal fields
    const [internalFields, setInternalFields] = useState<InternalFields>(() => {
      // Safely extract internal values from initialValues, handling null/undefined cases
      const initialValuesCasted = initialValues as any;
      return {
        internalTags: initialValuesCasted?.internalTags || [],
        internalNotes: initialValuesCasted?.internalNotes || "",
      };
    });

    // Add a variable to track original internal fields
    const [originalInternalFields, setOriginalInternalFields] =
      useState<InternalFields>(internalFields);

    // Validation functions
    const validateRequiredFields = (): boolean => {
      const errors: Record<string, Record<string, string>> = {};
      let hasErrors = false;

      safeConfig.sections.forEach((section) => {
        section.fields?.forEach((field) => {
          // Only validate required fields that are actually visible
          if (field.required && shouldShowField(field)) {
            const fieldValue = formValues[section.id]?.[field.id];

            let isEmpty = false;

            if (field.type === "fieldSet") {
              // For field sets, check if there are any instances with values
              isEmpty =
                !fieldValue ||
                !Array.isArray(fieldValue) ||
                fieldValue.length === 0 ||
                fieldValue.every(
                  (instance) =>
                    !instance ||
                    Object.keys(instance).length === 0 ||
                    Object.values(instance).every(
                      (val) =>
                        !val || (typeof val === "string" && val.trim() === "")
                    )
                );
            } else {
              // For regular fields
              isEmpty =
                !fieldValue ||
                (Array.isArray(fieldValue) && fieldValue.length === 0) ||
                (typeof fieldValue === "string" && fieldValue.trim() === "");
            }

            if (isEmpty) {
              if (!errors[section.id]) {
                errors[section.id] = {};
              }
              errors[section.id][field.id] = `${
                field.title || "This field"
              } is required`;
              hasErrors = true;
            }
          }
        });
      });

      setFieldErrors(errors);
      return !hasErrors;
    };

    const clearFieldError = (sectionId: string, fieldId: string) => {
      setFieldErrors((prev) => {
        const newErrors = { ...prev };
        if (newErrors[sectionId]) {
          const sectionErrors = { ...newErrors[sectionId] };
          delete sectionErrors[fieldId];

          if (Object.keys(sectionErrors).length === 0) {
            delete newErrors[sectionId];
          } else {
            newErrors[sectionId] = sectionErrors;
          }
        }
        return newErrors;
      });
    };

    // Helper function to get nested values using dot notation (e.g., "section.field")
    const getNestedValue = (obj: any, path: string): any => {
      if (!obj || typeof obj !== "object") {
        return undefined;
      }

      const keys = path.split(".");
      let current = obj;

      for (const key of keys) {
        if (
          current === null ||
          current === undefined ||
          typeof current !== "object"
        ) {
          return undefined;
        }
        current = current[key];
      }

      return current;
    };

    // Helper function to recursively search for a field name at any level
    const findFieldRecursively = (obj: any, fieldName: string): any => {
      if (!obj || typeof obj !== "object") {
        return undefined;
      }

      // Check if the field exists at the current level
      if (fieldName in obj) {
        return obj[fieldName];
      }

      // Recursively search through nested objects
      for (const key in obj) {
        if (
          Object.prototype.hasOwnProperty.call(obj, key) &&
          typeof obj[key] === "object" &&
          obj[key] !== null
        ) {
          const result = findFieldRecursively(obj[key], fieldName);
          if (result !== undefined) {
            return result;
          }
        }
      }

      return undefined;
    };

    // Function to evaluate conditional display logic with support for nested conditions
    const shouldShowField = (
      field: FormField,
      visitedFields: Set<string> = new Set()
    ): boolean => {
      if (!field.showIf || !field.showIf.enabled) {
        return true; // Always show if conditional display is not enabled
      }

      // Prevent infinite loops from circular dependencies
      if (visitedFields.has(field.id)) {
        return true; // Default to showing field if circular dependency detected
      }

      const { logic, conditions } = field.showIf;

      if (conditions.length === 0) {
        return true; // Show if no conditions are defined
      }

      const newVisitedFields = new Set(visitedFields);
      newVisitedFields.add(field.id);

      const evaluateCondition = (condition: FieldCondition): boolean => {
        let targetFieldValue: any = undefined;
        let targetField: FormField | undefined = undefined;
        let targetSectionId: string | undefined = undefined;

        if (condition.isExternal) {
          // Handle external field reference
          // Search through external data for the field
          for (const dataSource of externalData || []) {
            if (dataSource && typeof dataSource === "object") {
              // First try direct lookup for backwards compatibility
              if (condition.fieldId in dataSource) {
                targetFieldValue = dataSource[condition.fieldId];
                break;
              }

              // Then try nested lookup with dot notation
              const nestedValue = getNestedValue(dataSource, condition.fieldId);
              if (nestedValue !== undefined) {
                targetFieldValue = nestedValue;
                break;
              }

              // Finally try recursive search for the field name at any level
              const recursiveValue = findFieldRecursively(
                dataSource,
                condition.fieldId
              );
              if (recursiveValue !== undefined) {
                targetFieldValue = recursiveValue;
                break;
              }
            }
          }

          // For external fields, we don't have a FormField object, so we can't check visibility
          // External fields are always considered "visible" since they come from external sources
        } else {
          // Handle internal field reference (existing logic)
          for (const section of safeConfig.sections) {
            if (section.fields) {
              const foundField = section.fields.find(
                (f) => f.id === condition.fieldId
              );
              if (foundField) {
                targetField = foundField;
                targetSectionId = section.id;
                targetFieldValue = formValues[section.id]?.[condition.fieldId];
                break;
              }
            }
          }

          // If the target field is not found, condition fails
          if (!targetField || !targetSectionId) {
            return false;
          }

          // Check if the target field is itself visible (recursive check)
          const isTargetFieldVisible = shouldShowField(
            targetField,
            newVisitedFields
          );

          // If the target field is not visible, treat it as if it has no value
          if (!isTargetFieldVisible) {
            // For "exists" operator, hidden field means condition is false
            // For "equals" operator, hidden field means condition fails (unless comparing to empty)
            // For "notEquals" operator, hidden field means condition succeeds (unless comparing to empty)
            switch (condition.operator) {
              case "exists": {
                return false;
              }
              case "equals": {
                const conditionValueStr = String(condition.value || "").trim();
                return conditionValueStr === ""; // Only true if we're checking for empty value
              }
              case "notEquals": {
                const conditionValueStrNE = String(
                  condition.value || ""
                ).trim();
                return conditionValueStrNE !== ""; // True unless we're checking for empty value
              }
              default:
                return false;
            }
          }
        }

        // Evaluate the condition based on the operator (works for both internal and external fields)
        switch (condition.operator) {
          case "exists":
            // Check if the field has any value (not empty/null/undefined)
            if (targetFieldValue === null || targetFieldValue === undefined) {
              return false;
            }
            if (Array.isArray(targetFieldValue)) {
              return targetFieldValue.length > 0;
            }
            if (typeof targetFieldValue === "string") {
              return targetFieldValue.trim() !== "";
            }
            if (typeof targetFieldValue === "object") {
              return Object.keys(targetFieldValue).length > 0;
            }
            return Boolean(targetFieldValue);
          case "equals": {
            // Convert values to strings for comparison
            const fieldValueStr = String(targetFieldValue || "").trim();
            const conditionValueStr = String(condition.value || "").trim();
            return fieldValueStr === conditionValueStr;
          }
          case "notEquals": {
            // Convert values to strings for comparison
            const fieldValueStrNE = String(targetFieldValue || "").trim();
            const conditionValueStrNE = String(condition.value || "").trim();
            return fieldValueStrNE !== conditionValueStrNE;
          }
          default:
            return true;
        }
      };

      const results = conditions.map(evaluateCondition);

      if (logic === "and") {
        return results.every(Boolean); // All conditions must be true
      } else {
        return results.some(Boolean); // Any condition can be true
      }
    };

    // Create combined tabs information outside of the render flow
    const combinedTabsInfo = React.useMemo(() => {
      if (renderMode !== "consume") return { tabs: [] };

      const baseTabs: TabDescriptor[] = (
        Array.isArray(safeConfig.sections) ? safeConfig.sections : []
      ).map((section, idx) => ({
        key: section.id || `section-${idx}`,
        label: section.title || `Section ${idx + 1}`,
        icon: section.icon ? (
          <DynamicIcon iconName={section.icon} />
        ) : undefined,
        isCustom: false,
        section,
      }));

      const combinedTabs: TabDescriptor[] = [...baseTabs];

      additionalTabs.forEach((tab, idx) => {
        const descriptor: TabDescriptor = {
          key: `custom-${idx}`,
          label: tab.name,
          icon: undefined,
          isCustom: true,
          content: tab.content,
        };

        let insertIndex: number;
        if (tab.position === "start") insertIndex = 0;
        else if (typeof tab.position === "number") {
          insertIndex = Math.min(
            Math.max(tab.position, 0),
            combinedTabs.length
          );
        } else {
          // "end" or undefined
          insertIndex = combinedTabs.length;
        }
        combinedTabs.splice(insertIndex, 0, descriptor);
      });

      // Add internal tab if requested and in consume mode
      if (showInternalTab) {
        combinedTabs.push({
          key: "internal-tab",
          label: "Internal tags & notes",
          icon: undefined,
          isCustom: true,
          content: null, // Will be rendered separately
        });
      }

      return { tabs: combinedTabs };
    }, [safeConfig.sections, additionalTabs, renderMode, showInternalTab]);

    // Initialize default values for later resets
    const defaultValues = React.useMemo(() => {
      const defaultStructured: FormValues = {};
      safeConfig.sections.forEach((section) => {
        defaultStructured[section.id] = {};

        section.fields?.forEach((field) => {
          defaultStructured[section.id][field.id] = field.defaultValue || "";
        });
      });
      return defaultStructured;
    }, [safeConfig]);

    // Update form values when initialValues prop changes
    useEffect(() => {
      if (
        initialValues &&
        typeof initialValues === "object" &&
        Object.keys(initialValues).length > 0
      ) {
        // Create structured object from schema
        const newFormValues: FormValues = {};
        safeConfig.sections.forEach((section) => {
          newFormValues[section.id] = {};
          section.fields?.forEach((field) => {
            newFormValues[section.id][field.id] = field.defaultValue || "";
          });
        });

        // Merge initialValues preserving age fields
        Object.keys(initialValues).forEach((sectionId) => {
          if (
            initialValues[sectionId] &&
            typeof initialValues[sectionId] === "object"
          ) {
            if (!newFormValues[sectionId]) {
              newFormValues[sectionId] = {};
            }
            Object.keys(initialValues[sectionId]).forEach((fieldId) => {
              newFormValues[sectionId][fieldId] =
                initialValues[sectionId][fieldId];
            });
          }
        });

        setFormValues(newFormValues);
        // Create a true deep copy
        setOriginalValues(JSON.parse(JSON.stringify(newFormValues)));
      }
    }, [initialValues, safeConfig.sections]);

    // Ensure the active tab index is always valid - moved outside conditional render
    useEffect(() => {
      if (
        renderMode === "consume" &&
        activeTabIndex >= combinedTabsInfo.tabs.length &&
        combinedTabsInfo.tabs.length > 0
      ) {
        setActiveTabIndex(0);
      }
    }, [activeTabIndex, combinedTabsInfo.tabs.length, renderMode]);

    // Enhanced checkForChanges to handle nested values and print detailed logs
    const checkForChanges = () => {
      console.log("Checking for changes...");
      console.log("Original values:", originalValues);
      console.log("Current values:", formValues);

      // Deep compare formValues with originalValues
      const formKeys = Object.keys(formValues);
      const originalKeys = Object.keys(originalValues);

      if (formKeys.length !== originalKeys.length) {
        console.log("Different number of sections");
        return true;
      }

      for (const sectionId of formKeys) {
        const section = formValues[sectionId];
        const originalSection = originalValues[sectionId];

        if (!originalSection) {
          console.log(`Section ${sectionId} not in original values`);
          return true;
        }

        const fieldKeys = Object.keys(section);
        const originalFieldKeys = Object.keys(originalSection);

        if (fieldKeys.length !== originalFieldKeys.length) {
          console.log(`Different number of fields in section ${sectionId}`);
          return true;
        }

        for (const fieldId of fieldKeys) {
          const value = section[fieldId];
          const originalValue = originalSection[fieldId];

          // Handle array values (like multiselect)
          if (Array.isArray(value) && Array.isArray(originalValue)) {
            if (value.length !== originalValue.length) {
              console.log(`Array length changed for ${sectionId}.${fieldId}`);
              return true;
            }

            // Compare each item in the array
            for (let i = 0; i < value.length; i++) {
              if (
                JSON.stringify(value[i]) !== JSON.stringify(originalValue[i])
              ) {
                console.log(
                  `Array item changed for ${sectionId}.${fieldId}[${i}]`
                );
                return true;
              }
            }
          } else if (JSON.stringify(value) !== JSON.stringify(originalValue)) {
            console.log(
              `Value changed for ${sectionId}.${fieldId}:`,
              originalValue,
              "->",
              value
            );
            return true;
          }
        }
      }

      // Also check internal fields if available
      if (showInternalTab) {
        // Check internal tags
        const originalTags = originalInternalFields.internalTags || [];
        const currentTags = internalFields.internalTags || [];

        if (originalTags.length !== currentTags.length) {
          console.log("Internal tags length changed");
          return true;
        }

        for (let i = 0; i < originalTags.length; i++) {
          if (
            JSON.stringify(originalTags[i]) !== JSON.stringify(currentTags[i])
          ) {
            console.log("Internal tag changed");
            return true;
          }
        }

        // Check internal notes
        const originalNotes = originalInternalFields.internalNotes || "";
        const currentNotes = internalFields.internalNotes || "";

        if (originalNotes !== currentNotes) {
          console.log("Internal notes changed");
          return true;
        }
      }

      console.log("No changes detected");
      return false;
    };

    // Helper function to filter form values to only include visible fields
    const getVisibleFieldValues = (): FormValues => {
      const visibleValues: FormValues = {};

      safeConfig.sections.forEach((section) => {
        if (section.fields) {
          section.fields.forEach((field) => {
            if (shouldShowField(field)) {
              // Initialize section if not exists
              if (!visibleValues[section.id]) {
                visibleValues[section.id] = {};
              }

              // Include the field's value if it exists
              const fieldValue = formValues[section.id]?.[field.id];
              if (fieldValue !== undefined) {
                visibleValues[section.id][field.id] = fieldValue;
              }

              // For age-related date fields, also include the manually entered age if it exists
              const ageRelated = (field as any).ageRelated;
              if (
                ageRelated &&
                (field.type === InputType.Date || field.type === "date")
              ) {
                const ageFieldId = `${field.id}_age`;
                const ageValue = formValues[section.id]?.[ageFieldId];
                if (ageValue !== undefined && ageValue !== "") {
                  visibleValues[section.id][ageFieldId] = ageValue;
                }
              }
            }
          });
        }
      });

      return visibleValues;
    };

    // Expose methods via ref
    useImperativeHandle(
      ref,
      () => ({
        getValues: () => {
          // Get only visible field values
          const visibleValues = getVisibleFieldValues();

          // Return combined values from visible form fields and internal fields
          if (showInternalTab) {
            return {
              ...visibleValues,
              internalTags: internalFields.internalTags,
              internalNotes: internalFields.internalNotes,
            } as any;
          }
          return visibleValues;
        },
        getFlatValues: () => {
          // Convert visible structured values to flat format for backward compatibility
          const flatValues: Record<string, any> = {};
          const visibleValues = getVisibleFieldValues();

          if (visibleValues) {
            Object.keys(visibleValues).forEach((sectionId) => {
              const section = visibleValues[sectionId];
              if (section) {
                Object.keys(section).forEach((fieldId) => {
                  flatValues[fieldId] = section[fieldId];
                });
              }
            });
          }

          // Add internal fields to flat values if needed
          if (showInternalTab) {
            if (internalFields.internalTags) {
              flatValues.internalTags = internalFields.internalTags;
            }
            if (internalFields.internalNotes) {
              flatValues.internalNotes = internalFields.internalNotes;
            }
          }

          return flatValues;
        },
        validate: () => {
          // Validate required fields with scrolling
          return validateRequiredFields();
        },
        submitForm: () => {
          // Validate required fields before submitting with scrolling
          const isValid = validateRequiredFields();
          if (!isValid) {
            // Validation failed, don't submit
            return;
          }

          if (onSubmit) {
            // Get only visible field values for submission
            const visibleValues = getVisibleFieldValues();
            const combinedValues = showInternalTab
              ? ({
                  ...visibleValues,
                  internalTags: internalFields.internalTags,
                  internalNotes: internalFields.internalNotes,
                } as any)
              : visibleValues;
            onSubmit(combinedValues);
          }
        },
        reset: () => {
          setFormValues(defaultValues);
          setInternalFields({ internalTags: [], internalNotes: "" });
          setOriginalValues(defaultValues);
          setIsEditing(false);
          setHasChanges(false);
        },
      }),
      [
        formValues,
        internalFields,
        onSubmit,
        defaultValues,
        showInternalTab,
        safeConfig.sections,
        validateRequiredFields,
      ]
    );

    useEffect(() => {
      if (onValueChange) {
        // Get only visible field values for the callback
        const visibleValues = getVisibleFieldValues();
        const combinedValues = showInternalTab
          ? ({
              ...visibleValues,
              internalTags: internalFields.internalTags,
              internalNotes: internalFields.internalNotes,
            } as any)
          : visibleValues;
        onValueChange(combinedValues);
      }

      // Check for changes when in edit mode
      if (isEditing) {
        const hasChangesNow = checkForChanges();
        if (hasChangesNow !== hasChanges) {
          console.log(
            "Changes detected, updating hasChanges to:",
            hasChangesNow
          );
          setHasChanges(hasChangesNow);
        }
      }
    }, [
      formValues,
      internalFields,
      onValueChange,
      showInternalTab,
      isEditing,
      safeConfig.sections,
    ]);

    const handleFieldChange = (
      sectionId: string,
      fieldId: string,
      value: any
    ) => {
      setFormValues((prev) => {
        const newValues = { ...prev };

        // Ensure section exists
        if (!newValues[sectionId]) {
          newValues[sectionId] = {};
        }

        // Update field value directly
        newValues[sectionId][fieldId] = value;

        return newValues;
      });

      // Clear any existing error for this field when user types
      if (fieldErrors[sectionId]?.[fieldId]) {
        clearFieldError(sectionId, fieldId);
      }
    };

    const handleSubmit = (e: React.FormEvent) => {
      e.preventDefault();

      // Validate required fields before submitting with scrolling
      const isValid = validateRequiredFields();
      if (!isValid) {
        // Validation failed, don't submit
        return;
      }

      if (onSubmit) {
        // Submit only visible field values
        const visibleValues = getVisibleFieldValues();
        onSubmit(visibleValues);
      }

      // If in edit mode, exit edit mode after saving
      if (isEditing) {
        setIsEditing(false);
        setHasChanges(false);
        setOriginalValues({ ...formValues });
      }
    };

    // Fix the handleTabChange function to properly show the confirmation dialog
    const handleTabChange = (event: React.SyntheticEvent, newValue: number) => {
      // Force a check for changes
      const changesExist = isEditing && checkForChanges();

      // If in edit mode with changes, show the discard dialog
      if (changesExist) {
        setShowDiscardDialog(true);
        setPendingTabChange(newValue);
        return;
      }

      // If in edit mode without changes, exit edit mode
      if (isEditing) {
        console.log("No changes, exiting edit mode");
        setIsEditing(false);
      }

      console.log("Changing to tab", newValue);
      setActiveTabIndex(newValue);
    };

    // Handle confirming tab change when discarding changes
    const handleConfirmTabChange = () => {
      if (pendingTabChange !== null) {
        setActiveTabIndex(pendingTabChange);
        setIsEditing(false);
        setHasChanges(false);
        // Reset form values to original - create a fresh deep copy to ensure no references remain
        setFormValues(JSON.parse(JSON.stringify(originalValues)));
        // Reset internal fields to original values as well
        setInternalFields(JSON.parse(JSON.stringify(originalInternalFields)));
        setShowDiscardDialog(false);
        setPendingTabChange(null);
      }
    };

    // Handle save changes and then change tab
    const handleSaveAndChangeTab = () => {
      if (pendingTabChange !== null) {
        // Validate required fields before saving
        const isValid = validateRequiredFields();
        if (!isValid) {
          // Validation failed, don't save or change tab
          return;
        }

        // First save the form with only visible field values
        if (onSubmit) {
          const visibleValues = getVisibleFieldValues();
          const combinedValues = showInternalTab
            ? ({
                ...visibleValues,
                internalTags: internalFields.internalTags,
                internalNotes: internalFields.internalNotes,
              } as any)
            : visibleValues;
          onSubmit(combinedValues);
        }

        // Then exit edit mode and change tab
        setActiveTabIndex(pendingTabChange);
        setIsEditing(false);
        setHasChanges(false);
        // Update original values to match current values
        setOriginalValues(JSON.parse(JSON.stringify(formValues)));
        setOriginalInternalFields(JSON.parse(JSON.stringify(internalFields)));
        setShowDiscardDialog(false);
        setPendingTabChange(null);
      }
    };

    // Handle canceling tab change
    const handleCancelTabChange = () => {
      setShowDiscardDialog(false);
      setPendingTabChange(null);
    };

    // Handle toggle edit mode
    const handleToggleEditMode = () => {
      if (isEditing) {
        // Validate required fields before saving
        const isValid = validateRequiredFields();
        if (!isValid) {
          // Validation failed, don't save
          return;
        }

        // If saving, call onSubmit with only visible field values
        if (onSubmit) {
          const visibleValues = getVisibleFieldValues();
          const combinedValues = showInternalTab
            ? ({
                ...visibleValues,
                internalTags: internalFields.internalTags,
                internalNotes: internalFields.internalNotes,
              } as any)
            : visibleValues;
          onSubmit(combinedValues);
        }
        // Exit edit mode
        setIsEditing(false);
        setHasChanges(false);
        // Use JSON methods to create a true deep copy
        setOriginalValues(JSON.parse(JSON.stringify(formValues)));
        setOriginalInternalFields(JSON.parse(JSON.stringify(internalFields)));
      } else {
        // Enter edit mode
        // Use JSON methods to create a true deep copy
        setOriginalValues(JSON.parse(JSON.stringify(formValues)));
        setOriginalInternalFields(JSON.parse(JSON.stringify(internalFields)));
        setIsEditing(true);
        setHasChanges(false);
        // Clear any existing errors when entering edit mode
        setFieldErrors({});
      }
    };

    // --- Scroll‑reset helpers -------------------------------------------------
    // Container that will hold the tab contents in *consume* mode
    const contentContainerRef = useRef<HTMLDivElement>(null);

    // Whenever we switch tabs, reset the scroll position so that
    // tabs whose content does *not* overflow don't inherit the
    // previous scroll offset.
    useEffect(() => {
      if (contentContainerRef.current) {
        contentContainerRef.current.scrollTop = 0;
      }
    }, [activeTabIndex]);

    // Update renderField function to handle readOnly based on rendering mode
    const renderField = (
      sectionId: string,
      field: FormField,
      totalFieldsInRow: number
    ) => {
      // Check conditional display logic first
      if (!shouldShowField(field)) {
        return null; // Don't render the field if conditions are not met
      }

      const gapPx = 16;
      const subtraction =
        totalFieldsInRow > 1
          ? ((totalFieldsInRow - 1) * gapPx) / totalFieldsInRow
          : 0;

      // Get field value or default
      const fieldValue =
        formValues[sectionId]?.[field.id] !== undefined
          ? formValues[sectionId][field.id]
          : field.defaultValue || "";

      const style: React.CSSProperties = {
        minWidth: field.minWidth || "auto",
        marginBottom: "16px",
        flex: field.width ? "none" : 1,
        width: field.width
          ? `calc(${field.width} - ${subtraction}px)`
          : undefined,
      };

      // Calculate if this field should be read-only
      const fieldReadOnly =
        field.readOnly ||
        (renderMode === "consume" ? readOnly || !isEditing : readOnly);

      // Map the mask string value to InputMask enum value
      const getMaskValue = (maskString?: string) => {
        if (!maskString || maskString === "none") return InputMask.None;

        switch (maskString) {
          case "phone":
            return InputMask.Phone;
          case "date":
            return InputMask.Date;
          case "height":
            return InputMask.Height;
          default:
            return InputMask.None;
        }
      };

      // Handle select groups (checkbox/radio)
      if (field.type === "selectGroup") {
        const selectGroupError = fieldErrors[sectionId]?.[field.id];

        return (
          <div key={field.id} style={{ ...style, marginBottom: "32px" }}>
            <SelectGroup
              label={field.required ? `${field.title} *` : field.title}
              options={(field.options || []) as SelectOption[]}
              value={fieldValue}
              onChange={(value) => {
                handleFieldChange(sectionId, field.id, value);
                // Clear select group error when user makes a selection
                if (selectGroupError) {
                  clearFieldError(sectionId, field.id);
                }
              }}
              multiSelect={field.isMultiSelect}
              direction={field.direction}
              disabled={field.disabled || fieldReadOnly}
              color={field.color || "blue"}
              size={field.size || "medium"}
            />
            {selectGroupError && (
              <div
                style={{
                  marginTop: "8px",
                  marginLeft: "12px",
                }}
              >
                <Typography style="body4" color={colors.rose[600]}>
                  {selectGroupError}
                </Typography>
              </div>
            )}
          </div>
        );
      }

      // Handle field sets
      if (field.type === "fieldSet") {
        const fieldSetError = fieldErrors[sectionId]?.[field.id];

        return (
          <div key={field.id} style={style}>
            <FieldSet
              field={field as any} // Pass as any to handle custom properties
              sectionId={sectionId}
              values={formValues}
              onChange={(sectionId, fieldId, value) => {
                // Update values within the correct section
                setFormValues((prev) => {
                  const newFormValues = { ...prev };
                  if (!newFormValues[sectionId]) {
                    newFormValues[sectionId] = {};
                  }
                  newFormValues[sectionId][fieldId] = value;
                  return newFormValues;
                });

                // Clear field set error when user makes changes
                if (fieldSetError) {
                  clearFieldError(sectionId, field.id);
                }
              }}
              readOnly={fieldReadOnly}
              errorMessage={fieldSetError}
            />
          </div>
        );
      }

      // Handle date picker fields
      if (field.type === InputType.Date || field.type === "date") {
        const dateValue = fieldValue ? new Date(fieldValue) : null;
        const ageRelated = (field as any).ageRelated;
        const calculatedAge =
          ageRelated && dateValue ? calculateAge(dateValue) : "";

        if (ageRelated) {
          // Create a unique age field ID based on the date field ID
          const ageFieldId = `${field.id}_age`;
          const ageValue = formValues[sectionId]?.[ageFieldId] || "";

          // Determine if age field should be read-only (when date is set)
          const ageReadOnly = !!dateValue || fieldReadOnly;

          // Extract just the number from calculated age safely
          const extractNumericAge = (ageString: string): string => {
            if (!ageString) return "";
            const match = ageString.match(/^\d+/);
            return match ? match[0] : "";
          };

          const numericAge =
            dateValue && calculatedAge ? extractNumericAge(calculatedAge) : "";

          // Use numeric age when date is set, otherwise use manually entered age
          const displayAge = dateValue ? numericAge : ageValue;

          // Show date picker and age field side by side
          return (
            <div key={field.id} style={style}>
              <div
                style={{ display: "flex", gap: "16px", alignItems: "flex-end" }}
              >
                <div style={{ flex: 2 }}>
                  <DatePicker
                    title={field.required ? `${field.title} *` : field.title}
                    placeholder={field.placeholder}
                    value={dateValue}
                    onChange={(date) => {
                      handleFieldChange(
                        sectionId,
                        field.id,
                        date ? date.toISOString() : null
                      );
                      // If date is cleared, keep the manually entered age
                      // If date is set, the calculated age will be used automatically
                    }}
                    disabled={field.disabled}
                    readOnly={fieldReadOnly}
                    errorMessage={fieldErrors[sectionId]?.[field.id]}
                  />
                </div>
                <div style={{ flex: 1 }}>
                  <TextInput
                    title="Age"
                    value={displayAge}
                    onChange={(e: React.ChangeEvent<HTMLInputElement>) => {
                      // Only allow changes when no date is set
                      if (!dateValue && !fieldReadOnly) {
                        // Only allow numeric values for manual age entry
                        const value = e.target.value;
                        if (value === "" || /^\d+$/.test(value)) {
                          handleFieldChange(sectionId, ageFieldId, value);
                        }
                      }
                    }}
                    readOnly={ageReadOnly}
                    placeholder="---"
                    type={InputType.Text}
                  />
                </div>
              </div>
            </div>
          );
        } else {
          // Show only date picker
          return (
            <div key={field.id} style={style}>
              <DatePicker
                title={field.required ? `${field.title} *` : field.title}
                placeholder={field.placeholder}
                value={dateValue}
                onChange={(date) => {
                  handleFieldChange(
                    sectionId,
                    field.id,
                    date ? date.toISOString() : null
                  );
                }}
                disabled={field.disabled}
                readOnly={fieldReadOnly}
                errorMessage={fieldErrors[sectionId]?.[field.id]}
              />
            </div>
          );
        }
      }

      return (
        <div key={field.id} style={style}>
          <TextInput
            type={field.type}
            title={field.required ? `${field.title} *` : field.title}
            placeholder={field.placeholder}
            value={fieldValue}
            onChange={(e: React.ChangeEvent<HTMLInputElement>) =>
              handleFieldChange(sectionId, field.id, e.target.value)
            }
            options={field.options}
            onSelectOption={
              field.type === InputType.Dropdown && !field.isMultiSelect
                ? (option) =>
                    handleFieldChange(sectionId, field.id, option.value)
                : undefined
            }
            disabled={field.disabled}
            readOnly={fieldReadOnly}
            mask={getMaskValue(field.mask)}
            numOfLines={field.numOfLines}
            adornment={field.adornment}
            isMultiSelect={field.isMultiSelect}
            selectedItems={
              field.isMultiSelect
                ? Array.isArray(fieldValue)
                  ? fieldValue
                  : []
                : undefined
            }
            onChangeSelectedItems={
              field.isMultiSelect
                ? (items) => handleFieldChange(sectionId, field.id, items)
                : undefined
            }
            errorMessage={fieldErrors[sectionId]?.[field.id]}
          />
        </div>
      );
    };

    const renderRow = (
      sectionId: string,
      fields: FormField[],
      rowIndex: number
    ) => {
      const rowFields = fields.filter((field) => field.row === rowIndex);

      if (rowFields.length === 0) return null;

      return (
        <div
          key={`row-${rowIndex}`}
          style={{ display: "flex", flexWrap: "wrap", gap: "16px" }}
        >
          {rowFields.map((field) =>
            renderField(sectionId, field, rowFields.length)
          )}
        </div>
      );
    };

    const renderSection = (section: FormSection) => {
      // Safety check for missing fields
      if (!section || !section.fields || !Array.isArray(section.fields)) {
        return null;
      }

      // Find fields with explicit row assignments
      const fieldsWithRows = section.fields.filter(
        (field) => field.row !== undefined
      );
      const maxExplicitRow =
        fieldsWithRows.length > 0
          ? Math.max(...fieldsWithRows.map((f) => f.row || 0))
          : -1;

      // Get fields without row assignments
      const fieldsWithoutRows = section.fields.filter(
        (field) => field.row === undefined
      );

      // Create rows for the fields without explicit row assignments
      let currentRow = maxExplicitRow + 1;
      const autoRowedFields: FormField[] = [];

      fieldsWithoutRows.forEach((field) => {
        autoRowedFields.push({
          ...field,
          row: currentRow++,
        });
      });

      // Combine all fields
      const allFields = [...fieldsWithRows, ...autoRowedFields];

      // Get all unique row numbers
      const rows = Array.from(
        new Set(allFields.map((field) => field.row || 0))
      ).sort((a, b) => a - b);

      return (
        <div
          key={section.id || `section-${Math.random()}`}
          className="form-section"
          style={{ marginBottom: "32px" }}
        >
          <div
            className="section-header"
            style={{
              display: "flex",
              alignItems: "center",
              marginBottom: "16px",
            }}
          >
            {section.icon && (
              <div className="section-icon" style={{ marginRight: "8px" }}>
                <DynamicIcon iconName={section.icon} />
              </div>
            )}
            <Typography style="caps1" color={colors.grey[500]}>
              {section.title || "Untitled Section"}
            </Typography>
          </div>

          {rows.map((rowIndex) => renderRow(section.id, allFields, rowIndex))}
        </div>
      );
    };

    const renderTabContent = (section: FormSection, index: number) => {
      // Safety check for missing fields
      if (!section || !section.fields || !Array.isArray(section.fields)) {
        return null;
      }

      // Use the same field organization logic as renderSection
      const fieldsWithRows = section.fields.filter(
        (field) => field.row !== undefined
      );
      const maxExplicitRow =
        fieldsWithRows.length > 0
          ? Math.max(...fieldsWithRows.map((f) => f.row || 0))
          : -1;

      const fieldsWithoutRows = section.fields.filter(
        (field) => field.row === undefined
      );

      let currentRow = maxExplicitRow + 1;
      const autoRowedFields: FormField[] = [];

      fieldsWithoutRows.forEach((field) => {
        autoRowedFields.push({
          ...field,
          row: currentRow++,
        });
      });

      const allFields = [...fieldsWithRows, ...autoRowedFields];

      const rows = Array.from(
        new Set(allFields.map((field) => field.row || 0))
      ).sort((a, b) => a - b);

      return (
        <TabPanel key={section.id} value={activeTabIndex} index={index}>
          <Box sx={{ p: 2 }}>
            {rows.map((rowIndex) => renderRow(section.id, allFields, rowIndex))}
          </Box>
        </TabPanel>
      );
    };

    // Handle internal tab fields change
    const handleInternalTagsChange = (tags: DropdownOption[]) => {
      setInternalFields((prev) => ({
        ...prev,
        internalTags: tags,
      }));
    };

    const handleInternalNotesChange = (
      e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>
    ) => {
      setInternalFields((prev) => ({
        ...prev,
        internalNotes: e.target.value,
      }));
    };

    // Render internal tab content
    const renderInternalTabContent = () => {
      // In consume mode use the toggle logic, otherwise use readOnly prop directly
      const internalReadOnly =
        renderMode === "consume" ? readOnly || !isEditing : readOnly;

      return (
        <Box sx={{ p: 2 }}>
          <TextInput
            title="Internal Tags"
            type={InputType.Dropdown}
            isMultiSelect={true}
            placeholder="Add internal tags..."
            selectedItems={internalFields.internalTags || []}
            onChangeSelectedItems={handleInternalTagsChange}
            options={[
              { value: "urgent", label: "Urgent", color: "rose" },
              { value: "follow-up", label: "Follow-up", color: "amber" },
              { value: "completed", label: "Completed", color: "vine" },
              { value: "in-progress", label: "In Progress", color: "blue" },
              { value: "needs-review", label: "Needs Review", color: "purple" },
            ]}
            readOnly={internalReadOnly}
          />
          <div style={{ marginTop: "16px" }}>
            <TextInput
              title="Internal Notes"
              type={InputType.Multiline}
              value={internalFields.internalNotes || ""}
              onChange={handleInternalNotesChange}
              placeholder="Add internal notes here..."
              readOnly={internalReadOnly}
              numOfLines={8}
            />
          </div>
        </Box>
      );
    };

    // Render in Consume mode - Tabbed Card Interface
    if (renderMode === "consume") {
      // Check if current tab is a custom tab (excluding internal tab)
      const currentTab = combinedTabsInfo.tabs[activeTabIndex];
      const isCustomTab =
        currentTab?.isCustom && currentTab?.key !== "internal-tab";

      return (
        <div className={`form-renderer ${className}`}>
          <Paper
            elevation={0}
            sx={{
              border: "1px solid",
              borderColor: colors.grey[200],
              bgcolor: "white",
              overflow: "hidden",
              display: "flex",
              flexDirection: "column",
              borderRadius: "12px",
              flex: 1,
              minWidth: 0,
              height:
                typeof containerHeight === "number"
                  ? `${containerHeight}px`
                  : containerHeight,
            }}
          >
            <Tabs
              value={activeTabIndex}
              onChange={handleTabChange}
              variant="scrollable"
              scrollButtons="auto"
              sx={{
                minHeight: 50,
                px: 2,
                position: "relative",
                "&::after": {
                  content: '""',
                  position: "absolute",
                  bottom: 0,
                  top: "calc(100% - 1px)",
                  left: 0,
                  right: 0,
                  height: 1,
                  backgroundColor: "#D7DCE4",
                  marginLeft: "-16px",
                  marginRight: "-16px",
                },
                "& .MuiTabs-indicator": {
                  backgroundColor: colors.blue[600],
                  height: 2,
                  bottom: 0,
                  zIndex: 1,
                },
                "& .MuiTab-root": {
                  textTransform: "none",
                  fontSize: "14px",
                  padding: "0 16px",
                  minHeight: 50,
                  color: "#A1A1A8",
                  "&.Mui-selected": {
                    color: "inherit",
                  },
                  "&:not(:last-of-type)": {
                    marginRight: "8px",
                  },
                },
              }}
            >
              {combinedTabsInfo.tabs.map((tab) => (
                <Tab
                  key={tab.key}
                  icon={tab.icon ? <>{tab.icon}</> : undefined}
                  iconPosition={tab.icon ? "start" : undefined}
                  label={tab.label}
                />
              ))}
            </Tabs>

            {/* Content */}
            <Box
              ref={contentContainerRef}
              sx={{
                flexGrow: 1,
                overflow: "auto",
                px: 2,
                scrollbarWidth: "thin",
                scrollbarColor: `${colors.grey[300]} ${colors.grey[100]}`,
              }}
            >
              {(() => {
                const activeTab = combinedTabsInfo.tabs[activeTabIndex];

                // Internal tab
                if (activeTab && activeTab.key === "internal-tab") {
                  return renderInternalTabContent();
                }

                // Custom tabs render the React node provided by the caller.
                if (activeTab && activeTab.isCustom) {
                  return <Box sx={{ p: 2 }}>{activeTab.content}</Box>;
                }

                // Section‑backed tabs reuse the existing helper.
                return activeTab && activeTab.section
                  ? renderTabContent(
                      activeTab.section as FormSection,
                      activeTabIndex
                    )
                  : null;
              })()}
            </Box>

            {/* Edit/Save button - only show if readOnly prop is false and not on custom tabs */}
            {!readOnly && !isCustomTab && (
              <Box
                sx={{
                  p: 2,
                  borderTop: "1px solid",
                  borderColor: colors.grey[200],
                  display: "flex",
                  justifyContent: "flex-end",
                  position: "sticky",
                  bottom: 0,
                  bgcolor: "white",
                  zIndex: 1,
                }}
              >
                {isEditing ? (
                  <Button
                    label="Save"
                    style="filled"
                    color="blue"
                    size="small"
                    onClick={(e) => {
                      e.preventDefault(); // Prevent form submission
                      console.log(
                        "Save button clicked, current isEditing:",
                        isEditing
                      );
                      handleToggleEditMode();
                    }}
                  />
                ) : (
                  <Button
                    label=""
                    color="grey"
                    size="small"
                    prominence={false}
                    leftIcon={<EditIcon sx={{ fontSize: "20px" }} />}
                    onClick={(e) => {
                      e.preventDefault(); // Prevent form submission
                      console.log(
                        "Edit button clicked, current isEditing:",
                        isEditing
                      );
                      handleToggleEditMode();
                    }}
                  />
                )}
              </Box>
            )}

            {/* Regular submit button - for non-editable mode */}
            {onSubmit && showSubmitButton && readOnly && (
              <form onSubmit={handleSubmit}>
                <Box sx={{ p: 2 }}>
                  <button
                    type="submit"
                    style={{
                      padding: "12px 24px",
                      backgroundColor: colors.blue[600],
                      color: "white",
                      border: "none",
                      borderRadius: "8px",
                      cursor: "pointer",
                      fontWeight: 500,
                    }}
                  >
                    Submit
                  </button>
                </Box>
              </form>
            )}
          </Paper>

          {/* Discard changes dialog */}
          <Dialog
            open={showDiscardDialog}
            onClose={handleCancelTabChange}
            aria-labelledby="discard-dialog-title"
            aria-describedby="discard-dialog-description"
            PaperProps={{
              sx: {
                borderRadius: "12px",
                padding: "8px",
                maxWidth: "400px",
                boxShadow: "0px 4px 20px rgba(0, 0, 0, 0.1)",
              },
            }}
          >
            <DialogTitle
              id="discard-dialog-title"
              sx={{
                fontWeight: 600,
                fontSize: "20px",
                color: colors.grey[900],
                pt: 2,
                pb: 1,
              }}
            >
              Unsaved Changes
              <IconButton
                aria-label="close"
                onClick={handleCancelTabChange}
                sx={{
                  position: "absolute",
                  right: 8,
                  top: 8,
                  color: colors.grey[500],
                }}
              >
                <CloseIcon />
              </IconButton>
            </DialogTitle>
            <DialogContent>
              <DialogContentText
                id="discard-dialog-description"
                sx={{
                  color: colors.grey[700],
                  fontSize: "14px",
                  mb: 2,
                }}
              >
                You have unsaved changes. Are you sure you want to leave?
              </DialogContentText>
            </DialogContent>
            <DialogActions sx={{ p: 2, pt: 1 }}>
              <Button
                label="Discard Changes"
                style="ghost"
                color="grey"
                size="medium"
                onClick={handleConfirmTabChange}
              />
              <Button
                label="Save Changes"
                style="filled"
                color="blue"
                size="medium"
                onClick={handleSaveAndChangeTab}
              />
            </DialogActions>
          </Dialog>
        </div>
      );
    }

    // Default Ingest Mode (original scrollable form)
    return (
      <form onSubmit={handleSubmit} className={`form-renderer ${className}`}>
        {safeConfig.title && (
          <div className="form-header" style={{ marginBottom: "24px" }}>
            <Typography style="h1" color={colors.grey[900]}>
              {safeConfig.title}
            </Typography>
          </div>
        )}

        {Array.isArray(safeConfig.sections) &&
          safeConfig.sections.map(renderSection)}

        {onSubmit && showSubmitButton && (
          <div className="form-actions" style={{ marginTop: "24px" }}>
            <button
              type="submit"
              style={{
                padding: "12px 24px",
                backgroundColor: colors.blue[600],
                color: "white",
                border: "none",
                borderRadius: "8px",
                cursor: "pointer",
                fontWeight: 500,
              }}
            >
              Submit
            </button>
          </div>
        )}
      </form>
    );
  }
);

// Add display name to fix the linting error
FormRenderer.displayName = "FormRenderer";
