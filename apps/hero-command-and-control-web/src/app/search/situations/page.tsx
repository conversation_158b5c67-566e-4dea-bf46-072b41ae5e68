'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { useSearchSituations } from '../../apis/services/workflow/situations/hooks';
import {
  SituationStatus,
  SituationType,
  TriggerSource,
  SearchOrderBy,
  Situation
} from 'proto/hero/situations/v2/situations_pb';

// Available search fields based on backend implementation
const AVAILABLE_SEARCH_FIELDS = [
  { id: 'id', label: 'ID' },
  { id: 'title', label: 'Title' },
  { id: 'description', label: 'Description' },
  { id: 'contact_email', label: 'Contact Email' },
  { id: 'reporter_name', label: 'Reporter Name' },
  { id: 'address', label: 'Address' },
  { id: 'contact_no', label: 'Contact Number' },
  { id: 'tags', label: 'Tags' }
];

// Set of valid field IDs for whitelist validation
const VALID_FIELD_IDS = new Set(AVAILABLE_SEARCH_FIELDS.map(field => field.id));

const SituationSearch = () => {
  const router = useRouter();
  const [searchParams, setSearchParams] = useState<any>({
    query: '',
    pageSize: 20,
    orderBy: SearchOrderBy.CREATE_TIME,
    ascending: false
  });
  const [selectedStatuses, setSelectedStatuses] = useState<SituationStatus[]>([]);
  const [selectedTypes, setSelectedTypes] = useState<SituationType[]>([]);
  const [selectedTriggerSources, setSelectedTriggerSources] = useState<TriggerSource[]>([]);
  const [selectedPriorities, setSelectedPriorities] = useState<number[]>([]);
  const [selectedTags, setSelectedTags] = useState<string[]>([]);
  const [selectedSearchFields, setSelectedSearchFields] = useState<string[]>([]);
  const [fieldQueries, setFieldQueries] = useState<{field: string, query: string}[]>([]);
  const [currentFieldQuery, setCurrentFieldQuery] = useState<{field: string, query: string}>({
    field: 'title',
    query: ''
  });
  const [geoBoundingBox, setGeoBoundingBox] = useState<{
    minLatitude?: number;
    maxLatitude?: number;
    minLongitude?: number;
    maxLongitude?: number;
  }>({});
  const [dateFilters, setDateFilters] = useState<{
    createTime?: { from: string; to: string };
    updateTime?: { from: string; to: string };
    incidentTime?: { from: string; to: string };
    resolvedTime?: { from: string; to: string };
    dueTime?: { from: string; to: string };
  }>({});

  // Get search results using the hook
  const { data, isLoading, isError, error } = useSearchSituations(searchParams, {
    enabled: true,
    refetchOnWindowFocus: false
  });

  // Update search parameters when filters change
  useEffect(() => {
    const newParams: any = {
      ...searchParams,
      status: selectedStatuses,
      type: selectedTypes,
      triggerSource: selectedTriggerSources,
      priority: selectedPriorities,
      tags: selectedTags
    };

    // Add search fields if any are selected, otherwise remove the property
    if (selectedSearchFields.length > 0) {
      // Validate each search field against whitelist
      newParams.searchFields = selectedSearchFields.filter(field => VALID_FIELD_IDS.has(field));
    } else {
      delete newParams.searchFields;
    }

    // Add field-specific queries if any are defined
    if (fieldQueries.length > 0) {
      // Filter out any queries with invalid fields
      const validFieldQueries = fieldQueries.filter(fq => VALID_FIELD_IDS.has(fq.field));
      
      if (validFieldQueries.length > 0) {
        newParams.fieldQueries = validFieldQueries.map(fq => ({
          field: fq.field,
          query: fq.query
        }));
      } else {
        delete newParams.fieldQueries;
      }
    } else {
      delete newParams.fieldQueries;
    }

    // Add geographic bounding box if values are set
    if (geoBoundingBox.minLatitude !== undefined) {
      newParams.minLatitude = geoBoundingBox.minLatitude;
    }
    if (geoBoundingBox.maxLatitude !== undefined) {
      newParams.maxLatitude = geoBoundingBox.maxLatitude;
    }
    if (geoBoundingBox.minLongitude !== undefined) {
      newParams.minLongitude = geoBoundingBox.minLongitude;
    }
    if (geoBoundingBox.maxLongitude !== undefined) {
      newParams.maxLongitude = geoBoundingBox.maxLongitude;
    }

    // Add date range filters if they exist
    if (dateFilters.createTime && dateFilters.createTime.from && dateFilters.createTime.to) {
      newParams.createTime = {
        from: dateFilters.createTime.from,
        to: dateFilters.createTime.to
      };
    }

    if (dateFilters.updateTime && dateFilters.updateTime.from && dateFilters.updateTime.to) {
      newParams.updateTime = {
        from: dateFilters.updateTime.from,
        to: dateFilters.updateTime.to
      };
    }

    if (dateFilters.incidentTime && dateFilters.incidentTime.from && dateFilters.incidentTime.to) {
      newParams.incidentTime = {
        from: dateFilters.incidentTime.from,
        to: dateFilters.incidentTime.to
      };
    }

    if (dateFilters.resolvedTime && dateFilters.resolvedTime.from && dateFilters.resolvedTime.to) {
      newParams.resolvedTime = {
        from: dateFilters.resolvedTime.from,
        to: dateFilters.resolvedTime.to
      };
    }

    if (dateFilters.dueTime && dateFilters.dueTime.from && dateFilters.dueTime.to) {
      newParams.dueTime = {
        from: dateFilters.dueTime.from,
        to: dateFilters.dueTime.to
      };
    }

    setSearchParams(newParams);
  }, [
    selectedStatuses,
    selectedTypes,
    selectedTriggerSources,
    selectedPriorities,
    selectedTags,
    selectedSearchFields,
    fieldQueries,
    dateFilters,
    geoBoundingBox
  ]);

  // Handle search query change
  const handleSearchQueryChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setSearchParams({
      ...searchParams,
      query: e.target.value
    });
  };

  // Handle field query changes
  const handleFieldQueryChange = (field: string, value: string) => {
    // Validate field against whitelist
    if (!VALID_FIELD_IDS.has(field)) {
      console.error(`Invalid field: ${field}`);
      return;
    }
    
    setCurrentFieldQuery({
      field,
      query: value
    });
  };

  // Add current field query to the list
  const addFieldQuery = () => {
    if (currentFieldQuery.query.trim() === '') return;
    
    // Validate field against whitelist before adding
    if (!VALID_FIELD_IDS.has(currentFieldQuery.field)) {
      console.error(`Cannot add query with invalid field: ${currentFieldQuery.field}`);
      return;
    }
    
    // Check if there's already a query for this field
    const existingIndex = fieldQueries.findIndex(fq => fq.field === currentFieldQuery.field);
    
    if (existingIndex >= 0) {
      // Update existing query
      const updatedQueries = [...fieldQueries];
      updatedQueries[existingIndex] = {...currentFieldQuery};
      setFieldQueries(updatedQueries);
    } else {
      // Add new query
      setFieldQueries([...fieldQueries, {...currentFieldQuery}]);
    }
    
    // Clear current query input
    setCurrentFieldQuery({...currentFieldQuery, query: ''});
  };

  // Remove a field query
  const removeFieldQuery = (index: number) => {
    const updatedQueries = [...fieldQueries];
    updatedQueries.splice(index, 1);
    setFieldQueries(updatedQueries);
  };

  // Handle search field selection
  const handleSearchFieldChange = (field: string) => {
    // Validate field against whitelist
    if (!VALID_FIELD_IDS.has(field)) {
      console.error(`Invalid search field: ${field}`);
      return;
    }
    
    setSelectedSearchFields(prev => {
      if (prev.includes(field)) {
        return prev.filter(f => f !== field);
      } else {
        return [...prev, field];
      }
    });
  };

  // Handle situation click to navigate to details
  const handleSituationClick = (situation: Situation) => {
    router.push(`/incidents?incidentId=${situation.id}`);
  };

  // Handle checkbox change for statuses
  const handleStatusChange = (status: SituationStatus) => {
    setSelectedStatuses(prev => {
      if (prev.includes(status)) {
        return prev.filter(s => s !== status);
      } else {
        return [...prev, status];
      }
    });
  };

  // Handle checkbox change for types
  const handleTypeChange = (type: SituationType) => {
    setSelectedTypes(prev => {
      if (prev.includes(type)) {
        return prev.filter(t => t !== type);
      } else {
        return [...prev, type];
      }
    });
  };

  // Handle checkbox change for trigger sources
  const handleTriggerSourceChange = (source: TriggerSource) => {
    setSelectedTriggerSources(prev => {
      if (prev.includes(source)) {
        return prev.filter(s => s !== source);
      } else {
        return [...prev, source];
      }
    });
  };

  // Handle checkbox change for priorities
  const handlePriorityChange = (priority: number) => {
    setSelectedPriorities(prev => {
      if (prev.includes(priority)) {
        return prev.filter(p => p !== priority);
      } else {
        return [...prev, priority];
      }
    });
  };

  // Handle adding tags
  const handleTagAdd = (e: React.KeyboardEvent<HTMLInputElement>) => {
    if (e.key === 'Enter' && e.currentTarget.value) {
      const newTag = e.currentTarget.value.trim();
      if (newTag && !selectedTags.includes(newTag)) {
        setSelectedTags(prev => [...prev, newTag]);
        e.currentTarget.value = '';
      }
    }
  };

  // Handle removing tags
  const handleTagRemove = (tag: string) => {
    setSelectedTags(prev => prev.filter(t => t !== tag));
  };

  // Handle date filter changes
  const handleDateFilterChange = (
    filterType: 'createTime' | 'updateTime' | 'incidentTime' | 'resolvedTime' | 'dueTime',
    rangeType: 'from' | 'to',
    value: string
  ) => {
    setDateFilters(prev => {
      const newFilters = { ...prev };
      if (!newFilters[filterType]) {
        newFilters[filterType] = { from: '', to: '' };
      }
      newFilters[filterType]![rangeType] = value;
      return newFilters;
    });
  };

  // Clear a specific date filter
  const clearDateFilter = (filterType: 'createTime' | 'updateTime' | 'incidentTime' | 'resolvedTime' | 'dueTime') => {
    setDateFilters(prev => {
      const newFilters = { ...prev };
      delete newFilters[filterType];
      return newFilters;
    });
  };

  // Handle geographic bounding box changes
  const handleGeoBoundingBoxChange = (
    field: 'minLatitude' | 'maxLatitude' | 'minLongitude' | 'maxLongitude',
    value: string
  ) => {
    const numValue = value === '' ? undefined : parseFloat(value);
    setGeoBoundingBox(prev => ({
      ...prev,
      [field]: numValue
    }));
  };

  // Clear geographic bounding box
  const clearGeoBoundingBox = () => {
    setGeoBoundingBox({});
  };

  // Handle order by change
  const handleOrderByChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    const value = parseInt(e.target.value);
    setSearchParams({
      ...searchParams,
      orderBy: value
    });
  };

  // Handle sort direction change
  const handleSortDirectionChange = () => {
    setSearchParams({
      ...searchParams,
      ascending: !searchParams.ascending
    });
  };

  // Clear all filters
  const clearAllFilters = () => {
    setSearchParams({
      query: '',
      pageSize: 20,
      orderBy: SearchOrderBy.CREATE_TIME,
      ascending: false
    });
    setSelectedStatuses([]);
    setSelectedTypes([]);
    setSelectedTriggerSources([]);
    setSelectedPriorities([]);
    setSelectedTags([]);
    setSelectedSearchFields([]);
    setFieldQueries([]);
    setCurrentFieldQuery({field: 'title', query: ''});
    setDateFilters({});
    setGeoBoundingBox({});
  };

  // Format date for display
  const formatDate = (dateString: string) => {
    if (!dateString) return 'N/A';
    const date = new Date(dateString);
    return date.toLocaleString();
  };

  // Get status display name
  const getStatusDisplay = (status: any) => {
    // If status is a string (like "SITUATION_STATUS_CREATED"), extract the readable part
    if (typeof status === 'string') {
      // Extract the part after the last underscore
      const parts = status.split('_');
      if (parts.length > 0) {
        // Get the last part and capitalize first letter
        const lastPart = parts[parts.length - 1];
        return lastPart.charAt(0).toUpperCase() + lastPart.slice(1).toLowerCase();
      }
      return status; // Return as-is if can't parse
    }
    
    // Fallback to enum map if it's a number
    const statusMap: Record<number, string> = {
      [SituationStatus.CREATED]: 'Created',
      [SituationStatus.TRIAGING]: 'Triaging',
      [SituationStatus.DISPATCHING]: 'Dispatching',
      [SituationStatus.ADDRESSING]: 'Addressing',
      [SituationStatus.RESOLVED]: 'Resolved',
      [SituationStatus.COMPLETED]: 'Completed',
      [SituationStatus.ESCALATED]: 'Escalated',
    };
    return statusMap[status] || 'Unknown';
  };

  // Get type display name
  const getTypeDisplay = (type: any) => {
    // If type is a string (like "SITUATION_TYPE_VANDALISM"), extract the readable part
    if (typeof type === 'string') {
      // Extract the part after SITUATION_TYPE_
      if (type.startsWith('SITUATION_TYPE_')) {
        const typeName = type?.replace('SITUATION_TYPE_', '');
        // Replace underscores with spaces and capitalize first letter of each word
        return typeName
          ?.split('_')
          ?.map(word => word.charAt(0).toUpperCase() + word.slice(1).toLowerCase())
          ?.join(' ');
      }
      return type; // Return as-is if can't parse
    }
    
    // Fallback to enum map if it's a number
    const typeMap: Record<number, string> = {
      [SituationType.VANDALISM]: 'Vandalism',
      [SituationType.INTRUSION]: 'Intrusion',
      [SituationType.MEDICAL_EMERGENCY]: 'Medical Emergency',
      [SituationType.FIGHT]: 'Fight',
      [SituationType.THEFT]: 'Theft',
      [SituationType.SUSPICIOUS_ACTIVITY]: 'Suspicious Activity',
      [SituationType.FIRE]: 'Fire',
      [SituationType.NOISE_COMPLAINT]: 'Noise Complaint',
      [SituationType.OTHER]: 'Other',
      [SituationType.WEAPON_DETECTED]: 'Weapon Detected',
      [SituationType.DRUG_ACTIVITY]: 'Drug Activity',
      [SituationType.ALCOHOL_VIOLATION]: 'Alcohol Violation',
      [SituationType.HAZARDOUS_MATERIAL]: 'Hazardous Material',
      [SituationType.LOST_PERSON]: 'Lost Person',
      [SituationType.TRESPASSING]: 'Trespassing',
      [SituationType.VEHICLE_ACCIDENT]: 'Vehicle Accident',
      [SituationType.POWER_OUTAGE]: 'Power Outage',
      [SituationType.WATER_LEAK]: 'Water Leak',
      [SituationType.GAS_LEAK]: 'Gas Leak',
      [SituationType.ANIMAL_INCIDENT]: 'Animal Incident',
      [SituationType.PARKING_VIOLATION]: 'Parking Violation',
      [SituationType.LOCKOUT]: 'Lockout',
      [SituationType.BURGLARY]: 'Burglary',
    };
    return typeMap[type] || 'Unknown';
  };

  // Helper function to get field label
  const getFieldLabel = (fieldId: string) => {
    const field = AVAILABLE_SEARCH_FIELDS.find(f => f.id === fieldId);
    return field ? field.label : fieldId;
  };

  return (
    <div className="flex h-screen">
      {/* Sidebar with filters */}
      <div className="w-1/4 p-4 border-r border-gray-200 overflow-y-auto">
        <div className="flex justify-between items-center mb-4">
          <h2 className="text-xl font-bold">Filters</h2>
          <button 
            onClick={clearAllFilters}
            className="px-2 py-1 bg-gray-200 hover:bg-gray-300 rounded text-sm"
          >
            Clear All Filters
          </button>
        </div>
        
        {/* Search query */}
        <div className="mb-4">
          <label className="block text-sm font-medium mb-1">Search</label>
          <input
            type="text"
            className="w-full p-2 border border-gray-300 rounded"
            placeholder="Search situations..."
            value={searchParams.query}
            onChange={handleSearchQueryChange}
          />
        </div>
        
        {/* Field-specific queries */}
        <div className="mb-4">
          <h3 className="font-medium mb-2">Field-Specific Search</h3>
          <div className="text-xs text-gray-500 mb-1">
            Search for specific terms in particular fields
          </div>
          <div className="flex gap-2 mb-2">
            <select
              className="flex-1 p-2 border border-gray-300 rounded text-sm"
              value={currentFieldQuery.field}
              onChange={(e) => handleFieldQueryChange(e.target.value, currentFieldQuery.query)}
            >
              {AVAILABLE_SEARCH_FIELDS.map(field => (
                <option key={field.id} value={field.id}>{field.label}</option>
              ))}
            </select>
            <input
              type="text"
              className="flex-1 p-2 border border-gray-300 rounded text-sm"
              placeholder="Search term..."
              value={currentFieldQuery.query}
              onChange={(e) => handleFieldQueryChange(currentFieldQuery.field, e.target.value)}
              onKeyDown={(e) => e.key === 'Enter' && addFieldQuery()}
            />
            <button
              className="p-2 bg-blue-500 text-white rounded hover:bg-blue-600"
              onClick={addFieldQuery}
              disabled={!currentFieldQuery.query.trim()}
            >
              Add
            </button>
          </div>
          
          {/* Display active field queries */}
          {fieldQueries.length > 0 && (
            <div className="mt-2">
              <div className="text-xs font-medium mb-1">Active Field Queries:</div>
              <div className="space-y-1">
                {fieldQueries.map((fq, index) => (
                  <div key={index} className="flex items-center justify-between bg-blue-50 p-2 rounded text-sm">
                    <span>
                      <strong>{getFieldLabel(fq.field)}:</strong> {fq.query}
                    </span>
                    <button
                      className="text-red-500 hover:text-red-700"
                      onClick={() => removeFieldQuery(index)}
                      aria-label="Remove field query"
                    >
                      ×
                    </button>
                  </div>
                ))}
              </div>
            </div>
          )}
        </div>
        
        {/* Search Fields */}
        <div className="mb-4">
          <h3 className="font-medium mb-2">Search Fields</h3>
          <div className="text-xs text-gray-500 mb-1">
            Select specific fields to search in (leave empty to search all fields)
          </div>
          <div className="space-y-1 max-h-40 overflow-y-auto">
            {AVAILABLE_SEARCH_FIELDS.map(field => (
              <div key={field.id} className="flex items-center">
                <input
                  type="checkbox"
                  id={`field-${field.id}`}
                  checked={selectedSearchFields.includes(field.id)}
                  onChange={() => handleSearchFieldChange(field.id)}
                  className="mr-2"
                />
                <label htmlFor={`field-${field.id}`}>{field.label}</label>
              </div>
            ))}
          </div>
        </div>
        
        {/* Status filter */}
        <div className="mb-4">
          <h3 className="font-medium mb-2">Status</h3>
          <div className="space-y-1">
            {[
              { value: SituationStatus.CREATED, label: 'Created' },
              { value: SituationStatus.TRIAGING, label: 'Triaging' },
              { value: SituationStatus.DISPATCHING, label: 'Dispatching' },
              { value: SituationStatus.ADDRESSING, label: 'Addressing' },
              { value: SituationStatus.RESOLVED, label: 'Resolved' },
              { value: SituationStatus.COMPLETED, label: 'Completed' },
              { value: SituationStatus.ESCALATED, label: 'Escalated' },
            ].map(status => (
              <div key={status.value} className="flex items-center">
                <input
                  type="checkbox"
                  id={`status-${status.value}`}
                  checked={selectedStatuses.includes(status.value)}
                  onChange={() => handleStatusChange(status.value)}
                  className="mr-2"
                />
                <label htmlFor={`status-${status.value}`}>{status.label}</label>
              </div>
            ))}
          </div>
        </div>
        
        {/* Type filter */}
        <div className="mb-4">
          <h3 className="font-medium mb-2">Type</h3>
          <div className="space-y-1 max-h-40 overflow-y-auto">
            {[
              { value: SituationType.VANDALISM, label: 'Vandalism' },
              { value: SituationType.INTRUSION, label: 'Intrusion' },
              { value: SituationType.MEDICAL_EMERGENCY, label: 'Medical Emergency' },
              { value: SituationType.FIGHT, label: 'Fight' },
              { value: SituationType.THEFT, label: 'Theft' },
              { value: SituationType.SUSPICIOUS_ACTIVITY, label: 'Suspicious Activity' },
              { value: SituationType.FIRE, label: 'Fire' },
              { value: SituationType.NOISE_COMPLAINT, label: 'Noise Complaint' },
              { value: SituationType.OTHER, label: 'Other' },
              { value: SituationType.WEAPON_DETECTED, label: 'Weapon Detected' },
              { value: SituationType.DRUG_ACTIVITY, label: 'Drug Activity' },
              { value: SituationType.ALCOHOL_VIOLATION, label: 'Alcohol Violation' },
              { value: SituationType.HAZARDOUS_MATERIAL, label: 'Hazardous Material' },
              { value: SituationType.LOST_PERSON, label: 'Lost Person' },
              { value: SituationType.TRESPASSING, label: 'Trespassing' },
              { value: SituationType.VEHICLE_ACCIDENT, label: 'Vehicle Accident' },
              { value: SituationType.POWER_OUTAGE, label: 'Power Outage' },
              { value: SituationType.WATER_LEAK, label: 'Water Leak' },
              { value: SituationType.GAS_LEAK, label: 'Gas Leak' },
              { value: SituationType.ANIMAL_INCIDENT, label: 'Animal Incident' },
              { value: SituationType.PARKING_VIOLATION, label: 'Parking Violation' },
              { value: SituationType.LOCKOUT, label: 'Lockout' },
              { value: SituationType.BURGLARY, label: 'Burglary' },
            ].map(type => (
              <div key={type.value} className="flex items-center">
                <input
                  type="checkbox"
                  id={`type-${type.value}`}
                  checked={selectedTypes.includes(type.value)}
                  onChange={() => handleTypeChange(type.value)}
                  className="mr-2"
                />
                <label htmlFor={`type-${type.value}`}>{type.label}</label>
              </div>
            ))}
          </div>
        </div>
        
        {/* Trigger Source filter */}
        <div className="mb-4">
          <h3 className="font-medium mb-2">Trigger Source</h3>
          <div className="space-y-1 max-h-40 overflow-y-auto">
            {[
              { value: TriggerSource.PHONE_CALL, label: 'Phone Call' },
              { value: TriggerSource.SMS, label: 'SMS' },
              { value: TriggerSource.CAMERA_EVENT, label: 'Camera Event' },
              { value: TriggerSource.SOS, label: 'SOS' },
              { value: TriggerSource.RADIO, label: 'Radio' },
              { value: TriggerSource.EMAIL, label: 'Email' },
              { value: TriggerSource.MOBILE_APP, label: 'Mobile App' },
              { value: TriggerSource.WEB_PORTAL, label: 'Web Portal' },
              { value: TriggerSource.SOCIAL_MEDIA, label: 'Social Media' },
              { value: TriggerSource.IOT_DEVICE, label: 'IoT Device' },
              { value: TriggerSource.MANUAL_ENTRY, label: 'Manual Entry' },
              { value: TriggerSource.AUTOMATED_ALERT, label: 'Automated Alert' },
            ].map(source => (
              <div key={source.value} className="flex items-center">
                <input
                  type="checkbox"
                  id={`source-${source.value}`}
                  checked={selectedTriggerSources.includes(source.value)}
                  onChange={() => handleTriggerSourceChange(source.value)}
                  className="mr-2"
                />
                <label htmlFor={`source-${source.value}`}>{source.label}</label>
              </div>
            ))}
          </div>
        </div>
        
        {/* Priority filter */}
        <div className="mb-4">
          <h3 className="font-medium mb-2">Priority</h3>
          <div className="space-y-1">
            {[1, 2, 3].map(priority => (
              <div key={priority} className="flex items-center">
                <input
                  type="checkbox"
                  id={`priority-${priority}`}
                  checked={selectedPriorities.includes(priority)}
                  onChange={() => handlePriorityChange(priority)}
                  className="mr-2"
                />
                <label htmlFor={`priority-${priority}`}>Priority {priority}</label>
              </div>
            ))}
          </div>
        </div>
        
        {/* Tags filter */}
        <div className="mb-4">
          <h3 className="font-medium mb-2">Tags</h3>
          <input
            type="text"
            className="w-full p-2 border border-gray-300 rounded mb-2"
            placeholder="Add tag and press Enter"
            onKeyDown={handleTagAdd}
          />
          <div className="flex flex-wrap gap-2">
            {selectedTags.map(tag => (
              <div key={tag} className="bg-blue-100 text-blue-800 px-2 py-1 rounded flex items-center">
                {tag}
                <button
                  className="ml-1 text-blue-600 hover:text-blue-800"
                  onClick={() => handleTagRemove(tag)}
                >
                  ×
                </button>
              </div>
            ))}
          </div>
        </div>
        
        {/* Geographic Bounding Box */}
        <div className="mb-4">
          <div className="flex justify-between items-center mb-2">
            <h3 className="font-medium">Geographic Bounding Box</h3>
            {Object.keys(geoBoundingBox).length > 0 && (
              <button 
                className="text-xs text-red-600 hover:text-red-800"
                onClick={clearGeoBoundingBox}
              >
                Clear
              </button>
            )}
          </div>
          <div className="grid grid-cols-2 gap-2 p-2 border border-gray-200 rounded">
            <div className="col-span-2 text-xs text-gray-500 mb-1">
              Define a geographic area to filter situations
            </div>
            <div>
              <label className="block text-xs font-medium mb-1">Min Latitude</label>
              <input
                type="number"
                step="0.0001"
                placeholder="-90 to 90"
                className="w-full p-1 border border-gray-300 rounded text-sm"
                value={geoBoundingBox.minLatitude !== undefined ? geoBoundingBox.minLatitude : ''}
                onChange={(e) => handleGeoBoundingBoxChange('minLatitude', e.target.value)}
                min="-90"
                max="90"
              />
            </div>
            <div>
              <label className="block text-xs font-medium mb-1">Max Latitude</label>
              <input
                type="number"
                step="0.0001"
                placeholder="-90 to 90"
                className="w-full p-1 border border-gray-300 rounded text-sm"
                value={geoBoundingBox.maxLatitude !== undefined ? geoBoundingBox.maxLatitude : ''}
                onChange={(e) => handleGeoBoundingBoxChange('maxLatitude', e.target.value)}
                min="-90"
                max="90"
              />
            </div>
            <div>
              <label className="block text-xs font-medium mb-1">Min Longitude</label>
              <input
                type="number"
                step="0.0001"
                placeholder="-180 to 180"
                className="w-full p-1 border border-gray-300 rounded text-sm"
                value={geoBoundingBox.minLongitude !== undefined ? geoBoundingBox.minLongitude : ''}
                onChange={(e) => handleGeoBoundingBoxChange('minLongitude', e.target.value)}
                min="-180"
                max="180"
              />
            </div>
            <div>
              <label className="block text-xs font-medium mb-1">Max Longitude</label>
              <input
                type="number"
                step="0.0001"
                placeholder="-180 to 180"
                className="w-full p-1 border border-gray-300 rounded text-sm"
                value={geoBoundingBox.maxLongitude !== undefined ? geoBoundingBox.maxLongitude : ''}
                onChange={(e) => handleGeoBoundingBoxChange('maxLongitude', e.target.value)}
                min="-180"
                max="180"
              />
            </div>
          </div>
        </div>
        
        {/* Date range filters */}
        <div className="mb-4">
          <h3 className="font-medium mb-2">Date Filters</h3>
          
          {/* Create Time */}
          <div className="mb-3 p-2 border border-gray-200 rounded">
            <div className="flex justify-between items-center mb-1">
              <label className="block text-sm font-medium">Create Time Range</label>
              {dateFilters.createTime && (
                <button 
                  className="text-xs text-red-600 hover:text-red-800"
                  onClick={() => clearDateFilter('createTime')}
                >
                  Clear
                </button>
              )}
            </div>
            <div className="flex gap-2">
              <input
                type="datetime-local"
                className="flex-1 p-1 border border-gray-300 rounded text-sm"
                onChange={(e) => handleDateFilterChange('createTime', 'from', e.target.value)}
                value={dateFilters.createTime?.from || ''}
              />
              <span className="text-xs self-center">to</span>
              <input
                type="datetime-local"
                className="flex-1 p-1 border border-gray-300 rounded text-sm"
                onChange={(e) => handleDateFilterChange('createTime', 'to', e.target.value)}
                value={dateFilters.createTime?.to || ''}
              />
            </div>
          </div>
          
          {/* Update Time */}
          <div className="mb-3 p-2 border border-gray-200 rounded">
            <div className="flex justify-between items-center mb-1">
              <label className="block text-sm font-medium">Update Time Range</label>
              {dateFilters.updateTime && (
                <button 
                  className="text-xs text-red-600 hover:text-red-800"
                  onClick={() => clearDateFilter('updateTime')}
                >
                  Clear
                </button>
              )}
            </div>
            <div className="flex gap-2">
              <input
                type="datetime-local"
                className="flex-1 p-1 border border-gray-300 rounded text-sm"
                onChange={(e) => handleDateFilterChange('updateTime', 'from', e.target.value)}
                value={dateFilters.updateTime?.from || ''}
              />
              <span className="text-xs self-center">to</span>
              <input
                type="datetime-local"
                className="flex-1 p-1 border border-gray-300 rounded text-sm"
                onChange={(e) => handleDateFilterChange('updateTime', 'to', e.target.value)}
                value={dateFilters.updateTime?.to || ''}
              />
            </div>
          </div>
          
          {/* Incident Time */}
          <div className="mb-3 p-2 border border-gray-200 rounded">
            <div className="flex justify-between items-center mb-1">
              <label className="block text-sm font-medium">Incident Time Range</label>
              {dateFilters.incidentTime && (
                <button 
                  className="text-xs text-red-600 hover:text-red-800"
                  onClick={() => clearDateFilter('incidentTime')}
                >
                  Clear
                </button>
              )}
            </div>
            <div className="flex gap-2">
              <input
                type="datetime-local"
                className="flex-1 p-1 border border-gray-300 rounded text-sm"
                onChange={(e) => handleDateFilterChange('incidentTime', 'from', e.target.value)}
                value={dateFilters.incidentTime?.from || ''}
              />
              <span className="text-xs self-center">to</span>
              <input
                type="datetime-local"
                className="flex-1 p-1 border border-gray-300 rounded text-sm"
                onChange={(e) => handleDateFilterChange('incidentTime', 'to', e.target.value)}
                value={dateFilters.incidentTime?.to || ''}
              />
            </div>
          </div>
          
          {/* Due Time */}
          <div className="mb-3 p-2 border border-gray-200 rounded">
            <div className="flex justify-between items-center mb-1">
              <label className="block text-sm font-medium">Due Time Range</label>
              {dateFilters.dueTime && (
                <button 
                  className="text-xs text-red-600 hover:text-red-800"
                  onClick={() => clearDateFilter('dueTime')}
                >
                  Clear
                </button>
              )}
            </div>
            <div className="flex gap-2">
              <input
                type="datetime-local"
                className="flex-1 p-1 border border-gray-300 rounded text-sm"
                onChange={(e) => handleDateFilterChange('dueTime', 'from', e.target.value)}
                value={dateFilters.dueTime?.from || ''}
              />
              <span className="text-xs self-center">to</span>
              <input
                type="datetime-local"
                className="flex-1 p-1 border border-gray-300 rounded text-sm"
                onChange={(e) => handleDateFilterChange('dueTime', 'to', e.target.value)}
                value={dateFilters.dueTime?.to || ''}
              />
            </div>
          </div>
          
          {/* Resolved Time */}
          <div className="mb-3 p-2 border border-gray-200 rounded">
            <div className="flex justify-between items-center mb-1">
              <label className="block text-sm font-medium">Resolved Time Range</label>
              {dateFilters.resolvedTime && (
                <button 
                  className="text-xs text-red-600 hover:text-red-800"
                  onClick={() => clearDateFilter('resolvedTime')}
                >
                  Clear
                </button>
              )}
            </div>
            <div className="flex gap-2">
              <input
                type="datetime-local"
                className="flex-1 p-1 border border-gray-300 rounded text-sm"
                onChange={(e) => handleDateFilterChange('resolvedTime', 'from', e.target.value)}
                value={dateFilters.resolvedTime?.from || ''}
              />
              <span className="text-xs self-center">to</span>
              <input
                type="datetime-local"
                className="flex-1 p-1 border border-gray-300 rounded text-sm"
                onChange={(e) => handleDateFilterChange('resolvedTime', 'to', e.target.value)}
                value={dateFilters.resolvedTime?.to || ''}
              />
            </div>
          </div>
        </div>
        
        {/* Sorting options */}
        <div className="mb-4">
          <h3 className="font-medium mb-2">Sort By</h3>
          <div className="flex items-center gap-2">
            <select
              className="p-2 border border-gray-300 rounded"
              value={searchParams.orderBy}
              onChange={handleOrderByChange}
            >
              <option value={SearchOrderBy.RELEVANCE}>Relevance</option>
              <option value={SearchOrderBy.CREATE_TIME}>Creation Time</option>
              <option value={SearchOrderBy.UPDATE_TIME}>Update Time</option>
              <option value={SearchOrderBy.PRIORITY}>Priority</option>
            </select>
            <button
              className="p-2 border border-gray-300 rounded"
              onClick={handleSortDirectionChange}
            >
              {searchParams.ascending ? '↑ Ascending' : '↓ Descending'}
            </button>
          </div>
        </div>
      </div>
      
      {/* Main content */}
      <div className="flex-1 p-4 overflow-y-auto">
        <h1 className="text-2xl font-bold mb-4">Situation Search</h1>
        
        {/* Loading and error states */}
        {isLoading && <div className="text-center py-4">Loading...</div>}
        {isError && (
          <div className="bg-red-100 text-red-800 p-4 rounded mb-4">
            Error: {error?.message || 'An error occurred while fetching situations'}
          </div>
        )}
        
        {/* Results count */}
        {data && (
          <div className="mb-4">
            <p>Found {data.situations?.length || 0} results{data.nextPageToken ? ' (more available)' : ''}</p>
          </div>
        )}
        
        {/* Results table */}
        <div className="overflow-x-auto">
          <table className="min-w-full bg-white border border-gray-200">
            <thead className="bg-gray-100">
              <tr>
                <th className="py-2 px-4 border-b text-left">ID</th>
                <th className="py-2 px-4 border-b text-left">Title</th>
                <th className="py-2 px-4 border-b text-left">Type</th>
                <th className="py-2 px-4 border-b text-left">Status</th>
                <th className="py-2 px-4 border-b text-left">Priority</th>
                <th className="py-2 px-4 border-b text-left">Created</th>
                <th className="py-2 px-4 border-b text-left">Updated</th>
                <th className="py-2 px-4 border-b text-left">Location</th>
              </tr>
            </thead>
            <tbody>
              {data?.situations && Array.isArray(data.situations) ? (
                data.situations.map((situation) => (
                  <>
                    <tr 
                      key={`situation-${situation.id}`}
                      className="hover:bg-gray-50 cursor-pointer"
                      onClick={() => handleSituationClick(situation)}
                    >
                      <td className="py-2 px-4 border-b">{situation.id}</td>
                      <td className="py-2 px-4 border-b">{situation.title}</td>
                      <td className="py-2 px-4 border-b">{getTypeDisplay(situation.type)}</td>
                      <td className="py-2 px-4 border-b">{getStatusDisplay(situation.status)}</td>
                      <td className="py-2 px-4 border-b">{situation.priority}</td>
                      <td className="py-2 px-4 border-b">{formatDate(situation.createTime)}</td>
                      <td className="py-2 px-4 border-b">{formatDate(situation.updateTime)}</td>
                      <td className="py-2 px-4 border-b">
                        {situation.address || 
                         (situation.latitude && situation.longitude ? 
                          `${situation.latitude.toFixed(4)}, ${situation.longitude.toFixed(4)}` : 
                          'N/A')}
                      </td>
                    </tr>
                    {/* Display highlights as a row footer when they exist */}
                    {data.highlights && data.highlights[situation.id] && (
                      <tr 
                        key={`highlight-${situation.id}`}
                        className="bg-gray-50"
                        onClick={(e) => e.stopPropagation()}
                      >
                        <td colSpan={8} className="py-1 px-4 border-b">
                          <div className="text-xs bg-yellow-100 p-2 rounded my-1">
                            <div className="font-semibold mb-1">
                              <span className="text-gray-600">Matched in {getFieldLabel(data.highlights[situation.id].field)}:</span>
                            </div>
                            {data.highlights[situation.id].fragments.map((fragment, idx) => (
                              <div key={idx} className="mb-1 last:mb-0 text-gray-700">
                                &ldquo;{fragment}&rdquo;
                              </div>
                            ))}
                          </div>
                        </td>
                      </tr>
                    )}
                  </>
                ))
              ) : !isLoading && (
                <tr>
                  <td colSpan={8} className="py-4 text-center text-gray-500">
                    No situations found. Try adjusting your filters.
                  </td>
                </tr>
              )}
            </tbody>
          </table>
        </div>
        
        {/* Pagination */}
        {data && (
          <div className="mt-4 flex justify-center items-center gap-2">
            {/* Previous page button */}
            <button
              className={`px-3 py-1 rounded border ${
                !searchParams.pageToken ? 'bg-gray-100 text-gray-400 cursor-not-allowed' : 'bg-white hover:bg-gray-50'
              }`}
              onClick={() => {
                if (searchParams.pageToken) {
                  const currentPage = parseInt(searchParams.pageToken) / searchParams.pageSize;
                  const newPage = currentPage - 1;
                  setSearchParams({
                    ...searchParams,
                    pageToken: newPage > 0 ? (newPage * searchParams.pageSize).toString() : undefined
                  });
                }
              }}
              disabled={!searchParams.pageToken}
            >
              Previous
            </button>

            {/* Page numbers */}
            {(() => {
              const totalPages = Math.ceil(data.totalResults / searchParams.pageSize);
              const currentPage = searchParams.pageToken 
                ? parseInt(searchParams.pageToken) / searchParams.pageSize 
                : 0;
              
              // Show up to 5 page numbers
              const pageNumbers = [];
              const maxVisiblePages = 5;
              let startPage = Math.max(0, currentPage - Math.floor(maxVisiblePages / 2));
              const endPage = Math.min(totalPages - 1, startPage + maxVisiblePages - 1);
              
              // Adjust start page if we're near the end
              if (endPage - startPage + 1 < maxVisiblePages) {
                startPage = Math.max(0, endPage - maxVisiblePages + 1);
              }

              for (let i = startPage; i <= endPage; i++) {
                pageNumbers.push(
                  <button
                    key={i}
                    className={`px-3 py-1 rounded border ${
                      i === currentPage 
                        ? 'bg-blue-500 text-white' 
                        : 'bg-white hover:bg-gray-50'
                    }`}
                    onClick={() => {
                      setSearchParams({
                        ...searchParams,
                        pageToken: i > 0 ? (i * searchParams.pageSize).toString() : undefined
                      });
                    }}
                  >
                    {i + 1}
                  </button>
                );
              }

              return (
                <div className="flex gap-1">
                  {startPage > 0 && (
                    <>
                      <button
                        className="px-3 py-1 rounded border bg-white hover:bg-gray-50"
                        onClick={() => {
                          setSearchParams({
                            ...searchParams,
                            pageToken: '0'
                          });
                        }}
                      >
                        1
                      </button>
                      {startPage > 1 && <span className="px-2">...</span>}
                    </>
                  )}
                  {pageNumbers}
                  {endPage < totalPages - 1 && (
                    <>
                      {endPage < totalPages - 2 && <span className="px-2">...</span>}
                      <button
                        className="px-3 py-1 rounded border bg-white hover:bg-gray-50"
                        onClick={() => {
                          setSearchParams({
                            ...searchParams,
                            pageToken: ((totalPages - 1) * searchParams.pageSize).toString()
                          });
                        }}
                      >
                        {totalPages}
                      </button>
                    </>
                  )}
                </div>
              );
            })()}

            {/* Next page button */}
            <button
              className={`px-3 py-1 rounded border ${
                !data.nextPageToken ? 'bg-gray-100 text-gray-400 cursor-not-allowed' : 'bg-white hover:bg-gray-50'
              }`}
              onClick={() => {
                if (data.nextPageToken) {
                  setSearchParams({
                    ...searchParams,
                    pageToken: data.nextPageToken
                  });
                }
              }}
              disabled={!data.nextPageToken}
            >
              Next
            </button>

            {/* Results count */}
            <span className="text-sm text-gray-500 ml-4">
              Showing {data.situations?.length || 0} of {data.totalResults} results
            </span>
          </div>
        )}
      </div>
    </div>
  );
};

export default SituationSearch; 