import { RecordStatus } from "proto/hero/entity/v1/entity_pb";

/**
 * Converts an entity status to a human-readable display string
 */
export const getEntityStatusDisplay = (status: string | RecordStatus): string => {
  const statusMap: Record<string, string> = {
    "RECORD_STATUS_ACTIVE": "Active",
    "RECORD_STATUS_DRAFT": "Draft",
    "RECORD_STATUS_ARCHIVED": "Archived",
    "RECORD_STATUS_DEPRECATED": "Deprecated",
    "RECORD_STATUS_DELETED": "Deleted",
    "RECORD_STATUS_UNSPECIFIED": "Unknown"
  };
  return statusMap[status] || "Unknown";
};