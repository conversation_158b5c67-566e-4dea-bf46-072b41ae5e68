import { useRouter } from "next/navigation";
import {
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Box,
  Menu,
  MenuItem,
  Checkbox,
} from "@mui/material";
import { Typography } from "../../../../design-system/components/Typography";
import { Button } from "../../../../design-system/components/Button";
import { colors } from "../../../../design-system/tokens";
import SwapVertIcon from "@mui/icons-material/SwapVert";
import KeyboardArrowDownIcon from "@mui/icons-material/KeyboardArrowDown";
import KeyboardArrowLeftIcon from "@mui/icons-material/KeyboardArrowLeft";
import KeyboardArrowRightIcon from "@mui/icons-material/KeyboardArrowRight";
import CheckIcon from "@mui/icons-material/Check";
import { Situation } from "proto/hero/situations/v2/situations_pb";
import {
  SITUATION_STATUS_OPTIONS,
  SITUATION_TYPE_OPTIONS,
} from "../../utils/constants";
import { useState } from "react";
import { SORT_OPTIONS } from "../../utils/constants";
import { TableSkeleton } from "../TableSkeleton";
import { SearchState, saveSearchStateForNavigation } from "../../utils/searchStateManager";
import { ExportModal } from "../../../components/ExportModal";
import { useSearchSituations } from "@/app/apis/services/workflow/situations/hooks";

interface IncidentResultsTableProps {
  data: any;
  isLoading: boolean;
  isError: boolean;
  error: any;
  page: number;
  rowsPerPage: number;
  totalPages: number;
  totalResults: number;
  nextPageToken?: string;
  handleChangePage: (pageIndex: number) => void;
  shouldSearch?: boolean;
  onChangeRowsPerPage?: (newRowsPerPage: number) => void;
  selectedSort: string;
  onSortChange: (sortValue: string) => void;
  getCurrentSearchState: () => SearchState;
}

export const IncidentResultsTable = ({
  data,
  isLoading,
  isError,
  error,
  page,
  rowsPerPage,
  totalPages,
  totalResults,
  nextPageToken,
  handleChangePage,
  shouldSearch,
  onChangeRowsPerPage,
  selectedSort,
  onSortChange,
  getCurrentSearchState,
}: IncidentResultsTableProps) => {
  const router = useRouter();
  const [sortMenuAnchor, setSortMenuAnchor] = useState<null | HTMLElement>(null);
  const [selectedSituations, setSelectedSituations] = useState<Set<string>>(new Set());
  const [isExportModalOpen, setIsExportModalOpen] = useState(false);
  const [exportMenuAnchor, setExportMenuAnchor] = useState<null | HTMLElement>(null);

  // Get the current search state for export
  const currentSearchState = getCurrentSearchState();

  // Fetch all results for export
  const { data: allResultsData, isLoading: isExportDataLoading } = useSearchSituations(
    {
      $typeName: "hero.situations.v2.SearchSituationsRequest",
      query: currentSearchState.searchParams.query ?? "",
      searchFields: [],
      fieldQueries: [],
      status: [],
      type: [],
      triggerSource: [],
      priority: [],
      tags: [],
      pageSize: 1000,
      orderBy: currentSearchState.searchParams.orderBy,
      ascending: currentSearchState.searchParams.ascending,
      minLatitude: 0,
      maxLatitude: 0,
      minLongitude: 0,
      maxLongitude: 0,
      pageToken: currentSearchState.searchParams.pageToken ?? "",
    },
    {
      enabled: isExportModalOpen && !selectedSituations.size,
    }
  );

  const isSortMenuOpen = Boolean(sortMenuAnchor);

  const handleSortMenuOpen = (event: React.MouseEvent<HTMLElement>) => {
    setSortMenuAnchor(event.currentTarget);
  };

  const handleSortMenuClose = () => {
    setSortMenuAnchor(null);
  };

  const handleSortSelect = (sortValue: string) => {
    onSortChange(sortValue);
    handleSortMenuClose();
  };

  // Handle situation click to navigate to details
  const handleSituationClick = (situation: Situation) => {
    // Save current search state before navigating
    const currentSearchState = getCurrentSearchState();
    saveSearchStateForNavigation(currentSearchState);
    
    // Navigate to incident details
    router.push(`/incidents?incidentId=${situation.id}`);
  };

  // Handle select all situations
  const handleSelectAll = (event: React.ChangeEvent<HTMLInputElement>) => {
    if (event.target.checked) {
      const newSelected = new Set<string>(data?.situations?.map((situation: Situation) => situation.id) || []);
      setSelectedSituations(newSelected);
    } else {
      setSelectedSituations(new Set<string>());
    }
  };

  // Handle select individual situation
  const handleSelectSituation = (situationId: string) => {
    const newSelected = new Set(selectedSituations);
    if (newSelected.has(situationId)) {
      newSelected.delete(situationId);
    } else {
      newSelected.add(situationId);
    }
    setSelectedSituations(newSelected);
  };

  // Handle CSV export
  const handleDownloadCsv = () => {
    // Use allResultsData when no situations are selected, otherwise use current page data
    const situationsToExport = selectedSituations.size > 0
      ? (data?.situations || []).filter((situation: Situation) => selectedSituations.has(situation.id))
      : (allResultsData?.situations || data?.situations || []);

    if (!situationsToExport || situationsToExport.length === 0) {
      console.warn("No data to export.");
      return { data: [], headers: [] };
    }

    const csvHeaders = [
      { label: "Incident ID", key: "id" },
      { label: "Title", key: "title" },
      { label: "Type", key: "type" },
      { label: "Status", key: "status" },
      { label: "Description", key: "description" },
      { label: "Address", key: "address" },
      { label: "Reporter Name", key: "reporterName" },
      { label: "Contact Number", key: "contactNo" },
      { label: "Contact Email", key: "contactEmail" },
      { label: "Created Time", key: "createTime" },
      { label: "Updated Time", key: "updateTime" },
      { label: "Incident Time", key: "incidentTime" },
      { label: "Resolved Time", key: "resolvedTime" },
      { label: "Due Time", key: "dueTime" },
      { label: "Priority", key: "priority" },
      { label: "Trigger Source", key: "triggerSource" },
    ];

    const csvData = situationsToExport.map((situation: Situation) => ({
      id: situation.id,
      title: situation.title || "---",
      type: getTypeDisplay(situation.type),
      status: getStatusDisplay(situation.status),
      description: situation.description || "---",
      address: situation.address || "---",
      reporterName: situation.reporterName || "---",
      contactNo: situation.contactNo || "---",
      contactEmail: situation.contactEmail || "---",
      createTime: situation.createTime || "---",
      updateTime: situation.updateTime || "---",
      incidentTime: situation.incidentTime || "---",
      resolvedTime: situation.resolvedTime || "---",
      dueTime: situation.dueTime || "---",
      priority: situation.priority || "---",
      triggerSource: situation.triggerSource || "---",
    }));

    return { data: csvData, headers: csvHeaders };
  };

  // Get status display name
  const getStatusDisplay = (status: any) => {
    if (typeof status === "string") {
      const matchingStatus = SITUATION_STATUS_OPTIONS.find(
        (option) =>
          option.value === status.toLowerCase() ||
          `SITUATION_STATUS_${option.label.toUpperCase()}` === status
      );

      if (matchingStatus) {
        return matchingStatus.label;
      }

      const parts = status.split("_");
      if (parts.length > 0) {
        const lastPart = parts[parts.length - 1];
        return (
          lastPart.charAt(0).toUpperCase() + lastPart.slice(1).toLowerCase()
        );
      }
      return status;
    }

    const matchingStatus = SITUATION_STATUS_OPTIONS.find(
      (option) => option.enumValue === status
    );

    if (matchingStatus) {
      return matchingStatus.label;
    }

    return "Unknown";
  };

  // Get type display name
  const getTypeDisplay = (type: any) => {
    if (typeof type === "string") {
      const matchingType = SITUATION_TYPE_OPTIONS.find(
        (option) =>
          option.value === type.toLowerCase() ||
          `SITUATION_TYPE_${option.label.toUpperCase().replace(" ", "_")}` ===
            type
      );

      if (matchingType) {
        return matchingType.label;
      }

      if (type.startsWith("SITUATION_TYPE_")) {
        const typeName = type?.replace("SITUATION_TYPE_", "");
        return typeName
          ?.split("_")
          ?.map(
            (word) => word.charAt(0).toUpperCase() + word.slice(1).toLowerCase()
          )
          ?.join(" ");
      }
      return type;
    }

    const matchingType = SITUATION_TYPE_OPTIONS.find(
      (option) => option.enumValue === type
    );

    if (matchingType) {
      return matchingType.label;
    }

    return "Unknown";
  };

  // Get caller number from additional info JSON
  const getCallerNumber = (
    additionalInfoJson: string | undefined
  ): string | undefined => {
    if (!additionalInfoJson) return undefined;
    try {
      const parsed = JSON.parse(additionalInfoJson);
      return parsed.callerNo;
    } catch {
      return undefined;
    }
  };

  // Custom pagination component
  const CustomPagination = () => {
    const getPageNumbers = () => {
      if (totalPages <= 1) {
        return [0];
      }

      const pageNumbers = [];

      if (totalPages <= 7) {
        for (let i = 0; i < totalPages; i++) {
          pageNumbers.push(i);
        }
      } else {
        if (page <= 3) {
          // Near beginning: 1 2 3 4 5 ... 10
          for (let i = 0; i < 5; i++) {
            pageNumbers.push(i);
          }
          pageNumbers.push(-1);
          pageNumbers.push(totalPages - 1);
        } else if (page >= totalPages - 4) {
          // Near end: 1 ... 6 7 8 9 10
          pageNumbers.push(0);
          pageNumbers.push(-1);
          for (let i = totalPages - 5; i < totalPages; i++) {
            pageNumbers.push(i);
          }
        } else {
          // Middle: 1 ... 3 4 5 ... 10
          pageNumbers.push(0);
          pageNumbers.push(-1);
          pageNumbers.push(page - 1);
          pageNumbers.push(page);
          pageNumbers.push(page + 1);
          pageNumbers.push(-2);
          pageNumbers.push(totalPages - 1);
        }
      }

      return pageNumbers;
    };

    const pageNumbers = getPageNumbers();

    // Don't render pagination if we don't have results to paginate
    if (totalResults === 0) {
      return null;
    }

    return (
      <Box sx={{ display: "flex", justifyContent: "center", mt: 2, mb: 2 }}>
        <Button
          label="Previous"
          style="ghost"
          color="grey"
          onClick={() => {
            if (page > 0 && !isLoading) {
              handleChangePage(page - 1);
            }
          }}
          disabled={page === 0 || isLoading}
          leftIcon={<KeyboardArrowLeftIcon />}
        />

        {pageNumbers.map((pageNum, index) => {
          if (pageNum === -1 || pageNum === -2) {
            return (
              <Box
                key={`ellipsis-${index}`}
                sx={{
                  display: "flex",
                  alignItems: "center",
                  mx: 1,
                }}
              >
                <Typography style="body2" color={colors.grey[500]}>
                  ...
                </Typography>
              </Box>
            );
          }

          return (
            <Box key={pageNum} sx={{ mx: 0.5 }}>
              <Button
                label={(pageNum + 1).toString()}
                style={page === pageNum ? "filled" : "ghost"}
                color="grey"
                prominence={false}
                onClick={() => !isLoading && handleChangePage(pageNum)}
                disabled={isLoading}
              />
            </Box>
          );
        })}

        <Button
          label="Next"
          style="ghost"
          color="grey"
          onClick={() => {
            if (page < totalPages - 1 && !isLoading) {
              handleChangePage(page + 1);
            }
          }}
          disabled={page >= totalPages - 1 || !nextPageToken || isLoading}
          rightIcon={<KeyboardArrowRightIcon />}
        />
      </Box>
    );
  };

  const csvExportProps = handleDownloadCsv();

  return (
    <Box
      sx={{
        flex: 1,
        display: "flex",
        flexDirection: "column",
        bgcolor: "white",
        borderRadius: 2,
        boxShadow: "none",
        overflow: "hidden",
        border: `1px solid ${colors.grey[200]}`,
      }}
    >
      <Box
        sx={{
          px: "32px",
          py: "24px",
          display: "flex",
          justifyContent: "space-between",
          alignItems: "center",
        }}
      >
        <Typography style="caps1" color={colors.grey[900]}>
          RESULTS
        </Typography>
        <Box sx={{ display: "flex", alignItems: "center" }}>
          <Box sx={{ mr: 3, display: "flex", alignItems: "center" }}>
            <Button
              label="Table Sort"
              size="small"
              style={isSortMenuOpen ? "filled" : "ghost"}
              color="grey"
              prominence={isSortMenuOpen ? true : false}
              leftIcon={<SwapVertIcon />}
              rightIcon={<KeyboardArrowDownIcon />}
              onClick={handleSortMenuOpen}
            />
          </Box>
          <Box sx={{ display: "flex", alignItems: "center" }}>
            <Button
              label="Export to CSV"
              size="small"
              style={selectedSituations.size > 0 ? "filled" : "ghost"}
              color="grey"
              prominence={selectedSituations.size > 0}
              rightIcon={<KeyboardArrowDownIcon />}
              onClick={(e) => {
                const menuAnchor = e.currentTarget;
                setExportMenuAnchor(menuAnchor);
              }}
            />
            <Menu
              anchorEl={exportMenuAnchor}
              open={Boolean(exportMenuAnchor)}
              onClose={() => setExportMenuAnchor(null)}
              PaperProps={{
                sx: {
                  mt: 1,
                  minWidth: 200,
                  borderRadius: 2,
                  border: `1px solid ${colors.grey[200]}`,
                  boxShadow: "0px 4px 16px rgba(0, 0, 0, 0.1)",
                },
              }}
            >
              <MenuItem
                onClick={() => {
                  setExportMenuAnchor(null);
                  setIsExportModalOpen(true);
                }}
                sx={{
                  px: 3,
                  py: 2,
                  display: "flex",
                  justifyContent: "space-between",
                  alignItems: "center",
                  "&:hover": {
                    backgroundColor: colors.grey[50],
                  },
                }}
              >
                <Box sx={{ display: "flex", alignItems: "center", gap: 2 }}>
                  <svg xmlns="http://www.w3.org/2000/svg" width="22" height="22" viewBox="0 0 22 22" fill="none">
                    <path d="M20.1673 4.74825L9.70815 15.2166L5.82148 11.3299L7.11398 10.0374L9.70815 12.6316L18.8748 3.46492L20.1673 4.74825ZM18.1415 9.36825C18.2607 9.89075 18.334 10.4408 18.334 10.9999C18.334 15.0516 15.0523 18.3333 11.0007 18.3333C6.94898 18.3333 3.66732 15.0516 3.66732 10.9999C3.66732 6.94825 6.94898 3.66659 11.0007 3.66659C12.449 3.66659 13.7873 4.08825 14.924 4.81242L16.244 3.49242C14.759 2.44742 12.9532 1.83325 11.0007 1.83325C5.94065 1.83325 1.83398 5.93992 1.83398 10.9999C1.83398 16.0599 5.94065 20.1666 11.0007 20.1666C16.0607 20.1666 20.1673 16.0599 20.1673 10.9999C20.1673 9.90909 19.9657 8.86408 19.6173 7.89242L18.1415 9.36825Z" fill="#364153"/>
                  </svg>
                  <Typography style="body2" color={colors.grey[900]}>
                    Selected Results ({selectedSituations.size})
                  </Typography>
                </Box>
              </MenuItem>
              <MenuItem
                onClick={() => {
                  setExportMenuAnchor(null);
                  setIsExportModalOpen(true);
                  setSelectedSituations(new Set());
                }}
                sx={{
                  px: 3,
                  py: 2,
                  display: "flex",
                  justifyContent: "space-between",
                  alignItems: "center",
                  "&:hover": {
                    backgroundColor: colors.grey[50],
                  },
                }}
              >
                <Box sx={{ display: "flex", alignItems: "center", gap: 2 }}>
                  <svg xmlns="http://www.w3.org/2000/svg" width="22" height="22" viewBox="0 0 22 22" fill="none">
                    <path d="M6.42183 5.50008H15.5885L10.996 11.2751L6.42183 5.50008ZM3.901 5.14258C5.75267 7.51675 9.17183 11.9167 9.17183 11.9167V17.4167C9.17183 17.9209 9.58433 18.3334 10.0885 18.3334H11.9218C12.426 18.3334 12.8385 17.9209 12.8385 17.4167V11.9167C12.8385 11.9167 16.2485 7.51675 18.1002 5.14258C18.5677 4.53758 18.1368 3.66675 17.376 3.66675H4.62517C3.86433 3.66675 3.4335 4.53758 3.901 5.14258Z" fill="#4A5565"/>
                  </svg>
                  <Typography style="body2" color={colors.grey[900]}>
                    {currentSearchState.searchParams.query ? "All Search Results" : "All Results"}
                  </Typography>
                </Box>
              </MenuItem>
            </Menu>
          </Box>
        </Box>
      </Box>

      <TableContainer
        component={Box}
        sx={{
          flexGrow: 1,
          width: "100%",
          overflowY: "auto",
          overflowX: "auto",
          px: "24px",
          height: "calc(100vh - 300px)",
        }}
      >
        {isLoading ? (
          <TableSkeleton tableType="Incidents" />
        ) : isError ? (
          <Box
            sx={{
              display: "flex",
              alignItems: "center",
              justifyContent: "center",
              height: "100%",
              p: 4,
            }}
          >
            <Box
              sx={{
                bgcolor: colors.rose[50],
                color: colors.rose[700],
                p: 4,
                borderRadius: 1,
              }}
            >
              Error: {error?.message || "An error occurred while fetching incidents"}
            </Box>
          </Box>
        ) : (
          <Table
            stickyHeader
            aria-label="incidents table"
            sx={{
              tableLayout: "fixed",
              width: "100%",
            }}
          >
            <TableHead>
              <TableRow>
                <TableCell padding="checkbox" sx={{ width: "48px" }} />
                <TableCell sx={{ width: "15%" }}>
                  <Box sx={{ display: "flex", alignItems: "center" }}>
                    <Typography style="caps3" color={colors.grey[800]}>
                      INCIDENT NUMBER
                    </Typography>
                  </Box>
                </TableCell>
                <TableCell sx={{ width: "20%" }}>
                  <Box sx={{ display: "flex", alignItems: "center" }}>
                    <Typography style="caps3" color={colors.grey[800]}>
                      TYPE
                    </Typography>
                  </Box>
                </TableCell>
                <TableCell sx={{ width: "25%" }}>
                  <Box sx={{ display: "flex", alignItems: "center" }}>
                    <Typography style="caps3" color={colors.grey[800]}>
                      LOCATION
                    </Typography>
                  </Box>
                </TableCell>
                <TableCell sx={{ width: "15%" }}>
                  <Box sx={{ display: "flex", alignItems: "center" }}>
                    <Typography style="caps3" color={colors.grey[800]}>
                      CALLER NUMBER
                    </Typography>
                  </Box>
                </TableCell>
                <TableCell sx={{ width: "15%" }}>
                  <Box sx={{ display: "flex", alignItems: "center" }}>
                    <Typography style="caps3" color={colors.grey[800]}>
                      DATE
                    </Typography>
                  </Box>
                </TableCell>
                <TableCell sx={{ width: "10%" }}>
                  <Box sx={{ display: "flex", alignItems: "center" }}>
                    <Typography style="caps3" color={colors.grey[800]}>
                      STATUS
                    </Typography>
                  </Box>
                </TableCell>
              </TableRow>
            </TableHead>
            <TableBody>
              {data?.situations && data.situations.length > 0 ? (
                data.situations.map((situation: Situation) => (
                  <TableRow
                    hover
                    key={situation.id}
                    onClick={() => handleSituationClick(situation)}
                    sx={{
                      cursor: "pointer",
                      height: '54px',
                      "&:last-child td, &:last-child th": { border: 0 },
                      "&:hover": {
                        backgroundColor: colors.grey[50],
                      },
                    }}
                  >
                    <TableCell padding="checkbox" onClick={(e) => e.stopPropagation()}>
                      <Checkbox
                        checked={selectedSituations.has(situation.id)}
                        onChange={() => handleSelectSituation(situation.id)}
                      />
                    </TableCell>
                    <TableCell component="th" scope="row" sx={{ width: "15%", maxWidth: "15%" }}>
                      <Box sx={{ overflow: "hidden", textOverflow: "ellipsis", whiteSpace: "nowrap", width: "100%" }}>
                        {situation.id?.replace(/[^0-9]/g, "").slice(0, 7)}
                      </Box>
                    </TableCell>
                    <TableCell align="left" sx={{ width: "20%", maxWidth: "20%" }}>
                      <Box sx={{ overflow: "hidden", textOverflow: "ellipsis", whiteSpace: "nowrap", width: "100%" }}>
                        {getTypeDisplay(situation.type)}
                      </Box>
                    </TableCell>
                    <TableCell align="left" sx={{ width: "25%", maxWidth: "25%" }}>
                      <Box sx={{ overflow: "hidden", textOverflow: "ellipsis", whiteSpace: "nowrap", width: "100%" }}>
                        {situation.address || "---"}
                      </Box>
                    </TableCell>
                    <TableCell align="left" sx={{ width: "15%", maxWidth: "15%" }}>
                      <Box sx={{ overflow: "hidden", textOverflow: "ellipsis", whiteSpace: "nowrap", width: "100%" }}>
                        {situation.contactNo ||
                          getCallerNumber(situation.additionalInfoJson) ||
                          "---"}
                      </Box>
                    </TableCell>
                    <TableCell align="left" sx={{ width: "15%", maxWidth: "15%" }}>
                      <Box sx={{ overflow: "hidden", textOverflow: "ellipsis", whiteSpace: "nowrap", width: "100%" }}>
                        {situation.createTime || "---"}
                      </Box>
                    </TableCell>
                    <TableCell align="left" sx={{ width: "10%", maxWidth: "10%" }}>
                      <Box sx={{ overflow: "hidden", textOverflow: "ellipsis", whiteSpace: "nowrap", width: "100%" }}>
                        {getStatusDisplay(situation.status)}
                      </Box>
                    </TableCell>
                  </TableRow>
                ))
              ) : (
                <TableRow sx={{ height: 300 }}>
                  <TableCell colSpan={7} align="center">
                    <Typography style="body1" color={colors.grey[600]}>
                      {data
                        ? "No situations found. Try adjusting your filters."
                        : "Use the search and filter options to find situations."}
                    </Typography>
                  </TableCell>
                </TableRow>
              )}
            </TableBody>
          </Table>
        )}
      </TableContainer>

      {/* Show pagination if we have pages to navigate (persists during page navigation) */}
      {(totalPages > 1 || totalResults > 0) && <CustomPagination />}

      <Menu
        anchorEl={sortMenuAnchor}
        open={isSortMenuOpen}
        onClose={handleSortMenuClose}
        PaperProps={{
          sx: {
            mt: 1,
            minWidth: 200,
            borderRadius: 2,
            border: `1px solid ${colors.grey[200]}`,
            boxShadow: "0px 4px 16px rgba(0, 0, 0, 0.1)",
          },
        }}
      >
        {SORT_OPTIONS.map((option) => (
          <MenuItem
            key={option.value}
            onClick={() => handleSortSelect(option.value)}
            sx={{
              px: 3,
              py: 2,
              display: "flex",
              justifyContent: "space-between",
              alignItems: "center",
              "&:hover": {
                backgroundColor: colors.grey[50],
              },
            }}
          >
            <Typography
              style={selectedSort === option.value ? "body1" : "body2"}
              color={
                selectedSort === option.value
                  ? colors.grey[900]
                  : colors.grey[500]
              }
            >
              {option.label}
            </Typography>
            {selectedSort === option.value && (
              <CheckIcon
                sx={{
                  color: colors.blue[600],
                  fontSize: 20,
                  ml: 2,
                }}
              />
            )}
          </MenuItem>
        ))}
      </Menu>

      <ExportModal
        open={isExportModalOpen}
        onClose={() => setIsExportModalOpen(false)}
        columns={csvExportProps.headers.length}
        rows={selectedSituations.size > 0 ? selectedSituations.size : (allResultsData?.situations?.length ?? data?.situations?.length ?? 0)}
        fileSize={`${Math.round((selectedSituations.size > 0 ? selectedSituations.size : (allResultsData?.situations?.length ?? data?.situations?.length ?? 0)) * 0.5)} KB`}
        csvData={csvExportProps.data}
        csvHeaders={csvExportProps.headers}
        isLoading={isExportDataLoading}
        filename="incidents_export.csv"
      />
    </Box>
  );
};
