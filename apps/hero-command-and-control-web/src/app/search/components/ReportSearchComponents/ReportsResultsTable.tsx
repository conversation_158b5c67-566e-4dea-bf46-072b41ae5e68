import CheckIcon from "@mui/icons-material/Check";
import KeyboardArrowDownIcon from "@mui/icons-material/KeyboardArrowDown";
import KeyboardArrowLeftIcon from "@mui/icons-material/KeyboardArrowLeft";
import KeyboardArrowRightIcon from "@mui/icons-material/KeyboardArrowRight";
import SwapVertIcon from "@mui/icons-material/SwapVert";
import {
  Box,
  Checkbox,
  Menu,
  MenuItem,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
} from "@mui/material";
import { useRouter } from "next/navigation";
import { IncidentDetailsContent, Report, ReviewStatus, SearchOrderBy, SearchReportsRequest } from "proto/hero/reports/v2/reports_pb";
import { useEffect, useState } from "react";
import { Button } from "../../../../design-system/components/Button";
import { Typography } from "../../../../design-system/components/Typography";
import { colors } from "../../../../design-system/tokens";
import { SectionTypeString } from "../../../../export/pdf/utils/sectionTypes";
import { useSearchReports } from "../../../apis/services/workflow/reports/v2/hooks";
import { ExportModal } from "../../../components/ExportModal";
import { getIncidentLabel, toTitleCase } from "../../../utils/utils";
import { SearchState, saveSearchStateForNavigation } from "../../utils/searchStateManager";
import { TableSkeleton } from "../TableSkeleton";

const REPORT_SORT_OPTIONS = [
  { value: SearchOrderBy.RELEVANCE, label: "Relevance" },
  { value: SearchOrderBy.CREATED_AT, label: "Created Date" },
  { value: SearchOrderBy.UPDATED_AT, label: "Updated Date" },
  { value: SearchOrderBy.STATUS, label: "Status" },
];

interface ReportsResultsTableProps {
  data: any;
  isLoading: boolean;
  isError: boolean;
  error: any;
  page: number;
  rowsPerPage: number;
  totalPages: number;
  totalResults: number;
  nextPageToken?: string;
  handleChangePage: (pageIndex: number) => void;
  onChangeRowsPerPage?: (newRowsPerPage: number) => void;
  selectedSort: SearchOrderBy;
  onSortChange: (sortValue: SearchOrderBy) => void;
  getCurrentSearchState: () => SearchState;
}

interface NarrativeContent {
  id: string;
  richText: string;
}

interface EntityRef {
  displayName: string;
  id: string;
}

interface OffenseListContent {
  id: string;
  offenses: Array<any>;
  metadata?: {
    primaryClassification?: string;
  };
}

interface ReportSection {
  id: string;
  type: string;
  narrative?: NarrativeContent;
  incidentDetails?: IncidentDetailsContent;
  entityList?: EntityListContent;
  offenseList?: OffenseListContent;
}

interface EntityListContent {
  id: string;
  entityRefs: EntityRef[];
}

// Helper type to handle the raw section data
interface RawReportSection {
  id: string;
  type: SectionTypeString;
  narrative?: {
    id: string;
    richText: string;
  };
  incidentDetails?: {
    id: string;
    initialType: string;
    finalType: string;
    incidentStartTime: string;
    incidentEndTime: string;
    incidentLocationStreetAddress: string;
    incidentLocationCity: string;
    incidentLocationState: string;
    incidentLocationZipCode: string;
    responders: Array<{
      displayName: string;
      role: string;
    }>;
    reportingPerson: {
      firstName: string;
      middleName?: string;
      lastName: string;
      phoneNumber?: string;
      reporterRole?: string;
    };
  };
  entityList?: {
    id: string;
    entityRefs: Array<{
      displayName: string;
      id: string;
    }>;
  };
  offenses?: Array<any>;
  metadata?: {
    primaryClassification?: string;
  };
  createdAt: string;
  updatedAt: string;
}

export const ReportsResultsTable = ({
  data,
  isLoading,
  isError,
  error,
  page,
  rowsPerPage,
  totalPages,
  totalResults,
  nextPageToken,
  handleChangePage,
  onChangeRowsPerPage,
  selectedSort,
  onSortChange,
  getCurrentSearchState,
}: ReportsResultsTableProps) => {
  const router = useRouter();
  const [sortMenuAnchor, setSortMenuAnchor] = useState<null | HTMLElement>(null);
  const [isExportModalOpen, setIsExportModalOpen] = useState(false);
  const [selectedReports, setSelectedReports] = useState<Set<string>>(new Set());
  const [exportMenuAnchor, setExportMenuAnchor] = useState<null | HTMLElement>(null);
  const [csvData, setCsvData] = useState<{ data: any[]; headers: any[] }>({ data: [], headers: [] });

  const isSortMenuOpen = Boolean(sortMenuAnchor);

  const handleSortMenuOpen = (event: React.MouseEvent<HTMLElement>) => {
    setSortMenuAnchor(event.currentTarget);
  };

  const handleSortMenuClose = () => {
    setSortMenuAnchor(null);
  };

  const handleSortSelect = (sortValue: SearchOrderBy) => {
    onSortChange(sortValue);
    handleSortMenuClose();
  };

  // Handle report click to navigate to details
  const handleReportClick = (report: Report) => {
    // Save current search state before navigating
    const currentSearchState = getCurrentSearchState();
    saveSearchStateForNavigation(currentSearchState);

    // Navigate to report details
    router.push(`/reports?reportId=${report.id}`);
  };

  // Get status display name
  const getStatusDisplay = (status: any) => {
    const statusMap: Record<string, string> = {
      "REPORT_STATUS_UNSPECIFIED": "Unspecified",
      "REPORT_STATUS_ASSIGNED": "Assigned",
      "REPORT_STATUS_IN_PROGRESS": "In Progress",
      "REPORT_STATUS_SUBMITTED_FOR_REVIEW": "Submitted for Review",
      "REPORT_STATUS_UNDER_REVIEW": "Under Review",
      "REPORT_STATUS_CHANGES_REQUESTED": "Changes Requested",
      "REPORT_STATUS_IN_REWORK": "In Rework",
      "REPORT_STATUS_APPROVED": "Approved",
      "REPORT_STATUS_REJECTED": "Rejected",
      "REPORT_STATUS_CANCELLED": "Cancelled",
    };
    return statusMap[status] || "Unknown";
  };

  // Helper function to strip HTML tags from text
  const stripHtmlTags = (html: string): string => {
    const processedHtml = html
      .replace(/<\/(h[1-6])>/gi, ' ')
      .replace(/<\/(p|div|section|article)>/gi, ' ')
      .replace(/<br\s*\/?>/gi, ' ')
      .replace(/<\/(li|ul|ol)>/gi, ' ');

    const tempDiv = document.createElement('div');
    tempDiv.innerHTML = processedHtml;
    return tempDiv.textContent || tempDiv.innerText || '';
  };

  const getReportNarrative = (report: Report) => {
    const rawSections = report.sections as unknown as RawReportSection[];
    const narrativeSection = rawSections?.find(
      section => section.type === 'SECTION_TYPE_NARRATIVE'
    );
    if (narrativeSection?.narrative?.richText) {
      const plainText = stripHtmlTags(narrativeSection.narrative.richText).trim();
      return plainText || "---";
    }
    return "---";
  };

  // Get report type
  const getReportType = (report: Report) => {
    if (!report.reportType) return "---";
    // Convert enum to string and format
    const typeStr = report.reportType.toString();
    if (!typeStr) return "Unknown Type";
    return toTitleCase(typeStr?.replace("REPORT_TYPE_", "")?.replace(/_/g, " ") || "Unknown Type");
  };

  // Get incident type
  const getIncidentType = (incidentDetails: any) => {
    if (!incidentDetails?.finalType && !incidentDetails?.initialType) return "---";
    return getIncidentLabel(incidentDetails.finalType || incidentDetails.initialType);
  };

  // Get full location string
  const getFullLocation = (incidentDetails: any) => {
    if (!incidentDetails) return "---";
    const parts = [
      incidentDetails.incidentLocationStreetAddress,
      incidentDetails.incidentLocationCity,
      incidentDetails.incidentLocationState,
      incidentDetails.incidentLocationZipCode
    ].filter(Boolean);
    return parts.length > 0 ? parts.join(", ") : "---";
  };

  // Get involved agencies
  const getInvolvedAgencies = (incidentDetails: any) => {
    if (!incidentDetails?.responders?.length) return "---";
    return incidentDetails.responders
      .map((responder: { displayName: string; role: string }) => `${responder.displayName} (${responder.role})`)
      .join(", ");
  };

  // Get offences summary
  const getOffencesSummary = (report: Report) => {
    const rawSections = report.sections as unknown as RawReportSection[];
    const offenceSection = rawSections?.find(
      section => section.type === 'SECTION_TYPE_OFFENSE'
    );
    if (offenceSection?.offenses) {
      const offenses = offenceSection.offenses;
      const primaryClassification = offenceSection.metadata?.primaryClassification;
      return `${primaryClassification || "Multiple"} (${offenses.length} offences)`;
    }
    return "---";
  };

  // Get severity from additionalInfoJson
  const getSeverity = (report: Report) => {
    return report.additionalInfoJson?.severity || "---";
  };

  // Get review status
  const getReviewStatus = (report: Report) => {
    if (!report.reviewRounds?.length) return "---";
    const latestRound = report.reviewRounds[report.reviewRounds.length - 1];
    const statusStr = ReviewStatus[latestRound.status];
    return statusStr?.replace("REVIEW_STATUS_", "")?.replace(/_/g, " ") || "Unknown Status";
  };

  // Format date
  const formatDate = (dateString: string | undefined) => {
    if (!dateString) return "---";
    const date = new Date(dateString);
    return date.toLocaleDateString();
  };

  // Custom pagination component
  const CustomPagination = () => {
    const getPageNumbers = () => {
      if (totalPages <= 1) {
        return [0];
      }

      const pageNumbers = [];

      if (totalPages <= 7) {
        for (let i = 0; i < totalPages; i++) {
          pageNumbers.push(i);
        }
      } else {
        if (page <= 3) {
          // Near beginning: 1 2 3 4 5 ... 10
          for (let i = 0; i < 5; i++) {
            pageNumbers.push(i);
          }
          pageNumbers.push(-1);
          pageNumbers.push(totalPages - 1);
        } else if (page >= totalPages - 4) {
          // Near end: 1 ... 6 7 8 9 10
          pageNumbers.push(0);
          pageNumbers.push(-1);
          for (let i = totalPages - 5; i < totalPages; i++) {
            pageNumbers.push(i);
          }
        } else {
          // Middle: 1 ... 3 4 5 ... 10
          pageNumbers.push(0);
          pageNumbers.push(-1);
          pageNumbers.push(page - 1);
          pageNumbers.push(page);
          pageNumbers.push(page + 1);
          pageNumbers.push(-2);
          pageNumbers.push(totalPages - 1);
        }
      }

      return pageNumbers;
    };

    const pageNumbers = getPageNumbers();

    if (totalResults === 0) {
      return null;
    }

    return (
      <Box sx={{ display: "flex", justifyContent: "center", mt: 2, mb: 2 }}>
        <Button
          label="Previous"
          style="ghost"
          color="grey"
          onClick={() => {
            if (page > 0 && !isLoading) {
              handleChangePage(page - 1);
            }
          }}
          disabled={page === 0 || isLoading}
          leftIcon={<KeyboardArrowLeftIcon />}
        />

        {pageNumbers.map((pageNum, index) => {
          if (pageNum === -1 || pageNum === -2) {
            return (
              <Box
                key={`ellipsis-${index}`}
                sx={{
                  display: "flex",
                  alignItems: "center",
                  mx: 1,
                }}
              >
                <Typography style="body2" color={colors.grey[500]}>
                  ...
                </Typography>
              </Box>
            );
          }

          return (
            <Box key={pageNum} sx={{ mx: 0.5 }}>
              <Button
                label={(pageNum + 1).toString()}
                style={page === pageNum ? "filled" : "ghost"}
                color="grey"
                prominence={false}
                onClick={() => !isLoading && handleChangePage(pageNum)}
                disabled={isLoading}
              />
            </Box>
          );
        })}

        <Button
          label="Next"
          style="ghost"
          color="grey"
          onClick={() => {
            if (page < totalPages - 1 && !isLoading) {
              handleChangePage(page + 1);
            }
          }}
          disabled={page >= totalPages - 1 || !nextPageToken || isLoading}
          rightIcon={<KeyboardArrowRightIcon />}
        />
      </Box>
    );
  };

  // Get the current search state for export
  const currentSearchState = getCurrentSearchState();

  // Fetch all results for export
  const { data: allResultsData, isLoading: isExportDataLoading } = useSearchReports(
    {
      query: currentSearchState.searchParams.query ?? "",
      pageSize: 1000,
      orderBy: currentSearchState.searchParams.orderBy as unknown as SearchOrderBy,
      ascending: currentSearchState.searchParams.ascending,
      pageToken: currentSearchState.searchParams.pageToken ?? "",
    } as unknown as SearchReportsRequest,
    {
      enabled: isExportModalOpen && !selectedReports.size,
    }
  );

  // Handle CSV export
  const handleDownloadCsv = () => {
    const reportsToExport = selectedReports.size > 0
      ? (data?.reports || []).filter((report: Report) => selectedReports.has(report.id))
      : (allResultsData?.reports || data?.reports || []);

    if (!reportsToExport || reportsToExport.length === 0) {
      return { data: [], headers: [] };
    }

    const csvHeaders = [
      { label: "Report ID", key: "id" },
      { label: "Title", key: "title" },
      { label: "Status", key: "status" },
      { label: "Review Status", key: "reviewStatus" },
      { label: "Severity", key: "severity" },
      { label: "Created Date", key: "createdAt" },
      { label: "Updated Date", key: "updatedAt" },
      { label: "Assigned Date", key: "assignedAt" },
      { label: "Completed Date", key: "completedAt" },
      { label: "Version", key: "version" },
      { label: "Report Type", key: "reportType" },
      { label: "Incident Type", key: "incidentType" },
      { label: "Location", key: "location" },
      { label: "Involved Agencies", key: "involvedAgencies" },
      { label: "Offences", key: "offences" },
      { label: "Incident Start Time", key: "incidentStartTime" },
      { label: "Incident End Time", key: "incidentEndTime" },
      { label: "Reporter Name", key: "reporterName" },
      { label: "Reporter Role", key: "reporterRole" },
      { label: "Reporter Phone", key: "reporterPhone" },
      { label: "Responders", key: "responders" },
      { label: "Related People", key: "relatedPeople" },
      { label: "Related Vehicles", key: "relatedVehicles" },
      { label: "Related Properties", key: "relatedProperties" },
    ];

    const csvData = reportsToExport.map((report: Report) => {
      const rawSections = report.sections as unknown as RawReportSection[];

      const incidentDetailsSection = rawSections?.find(
        section => section.type === 'SECTION_TYPE_INCIDENT_DETAILS'
      );
      const incidentDetails = incidentDetailsSection?.incidentDetails;

      const peopleSection = rawSections?.find(
        section => section.type === 'SECTION_TYPE_ENTITY_LIST_PEOPLE'
      );
      const peopleContent = peopleSection?.entityList;

      const vehiclesSection = rawSections?.find(
        section => section.type === 'SECTION_TYPE_ENTITY_LIST_VEHICLE'
      );
      const vehiclesContent = vehiclesSection?.entityList;

      const propertiesSection = rawSections?.find(
        section => section.type === 'SECTION_TYPE_ENTITY_LIST_PROPERTIES'
      );
      const propertiesContent = propertiesSection?.entityList;

      const result = {
        id: report.id,
        title: report.title || "---",
        status: getStatusDisplay(report.status),
        reviewStatus: getReviewStatus(report),
        severity: String(getSeverity(report)),
        createdAt: formatDate(report.createdAt),
        updatedAt: formatDate(report.updatedAt),
        assignedAt: formatDate(report.assignedAt),
        completedAt: formatDate(report.completedAt),
        version: report.version || "1",
        reportType: getReportType(report),
        incidentType: getIncidentType(incidentDetails),
        location: getFullLocation(incidentDetails),
        offences: getOffencesSummary(report),
        incidentStartTime: formatDate(incidentDetails?.incidentStartTime) || "---",
        incidentEndTime: formatDate(incidentDetails?.incidentEndTime) || "---",
        reporterName: incidentDetails?.reportingPerson ?
          `${incidentDetails.reportingPerson.firstName} ${incidentDetails.reportingPerson.middleName || ''} ${incidentDetails.reportingPerson.lastName}`.trim() :
          "---",
        reporterRole: incidentDetails?.reportingPerson?.reporterRole || "---",
        reporterPhone: incidentDetails?.reportingPerson?.phoneNumber || "---",
        responders: incidentDetails?.responders?.map((r: { displayName: string; role: string }) => `${r.displayName} (${r.role})`).join(", ") || "---",
        relatedPeople: peopleContent?.entityRefs?.map((e: { displayName: string }) => e.displayName).join(", ") || "---",
        relatedVehicles: vehiclesContent?.entityRefs?.map((e: { displayName: string }) => e.displayName).join(", ") || "---",
        relatedProperties: propertiesContent?.entityRefs?.map((e: { displayName: string }) => e.displayName).join(", ") || "---",
      };
      return result;
    });

    return { data: csvData, headers: csvHeaders };
  };

  // Update CSV data when export modal opens or selected reports change
  useEffect(() => {
    if (isExportModalOpen) {
      const result = handleDownloadCsv();
      setCsvData(result);
    }
  }, [isExportModalOpen, selectedReports, allResultsData, data]);

  const handleSelectAll = (event: React.ChangeEvent<HTMLInputElement>) => {
    if (event.target.checked) {
      const newSelected = new Set<string>(data?.reports?.map((report: Report) => report.id) || []);
      setSelectedReports(newSelected);
    } else {
      setSelectedReports(new Set<string>());
    }
  };

  const handleSelectReport = (reportId: string) => {
    const newSelected = new Set(selectedReports);
    if (newSelected.has(reportId)) {
      newSelected.delete(reportId);
    } else {
      newSelected.add(reportId);
    }
    setSelectedReports(newSelected);
  };

  // Get location from report
  const getReportLocation = (report: Report) => {
    const rawSections = report.sections as unknown as RawReportSection[];
    const incidentDetailsSection = rawSections?.find(
      section => section.type === 'SECTION_TYPE_INCIDENT_DETAILS'
    );
    return getFullLocation(incidentDetailsSection?.incidentDetails);
  };

  return (
    <Box
      sx={{
        flex: 1,
        display: "flex",
        flexDirection: "column",
        bgcolor: "white",
        borderRadius: 2,
        boxShadow: "none",
        overflow: "hidden",
        border: `1px solid ${colors.grey[200]}`,
      }}
    >
      <Box
        sx={{
          px: "32px",
          py: "24px",
          display: "flex",
          justifyContent: "space-between",
          alignItems: "center",
        }}
      >
        <Typography style="caps1" color={colors.grey[900]}>
          RESULTS
        </Typography>
        <Box sx={{ display: "flex", alignItems: "center" }}>
          <Box sx={{ mr: 3, display: "flex", alignItems: "center" }}>
            <Button
              label="Table Sort"
              size="small"
              style={isSortMenuOpen ? "filled" : "ghost"}
              color="grey"
              prominence={isSortMenuOpen ? true : false}
              leftIcon={<SwapVertIcon />}
              rightIcon={<KeyboardArrowDownIcon />}
              onClick={handleSortMenuOpen}
            />
          </Box>
          <Box sx={{ display: "flex", alignItems: "center" }}>
            <Button
              label="Export to CSV"
              size="small"
              style={selectedReports.size > 0 ? "filled" : "ghost"}
              color="grey"
              prominence={selectedReports.size > 0}
              rightIcon={<KeyboardArrowDownIcon />}
              onClick={(e) => {
                const menuAnchor = e.currentTarget;
                setExportMenuAnchor(menuAnchor);
              }}
            />
            <Menu
              anchorEl={exportMenuAnchor}
              open={Boolean(exportMenuAnchor)}
              onClose={() => setExportMenuAnchor(null)}
              PaperProps={{
                sx: {
                  mt: 1,
                  minWidth: 200,
                  borderRadius: 2,
                  border: `1px solid ${colors.grey[200]}`,
                  boxShadow: "0px 4px 16px rgba(0, 0, 0, 0.1)",
                },
              }}
            >
              <MenuItem
                onClick={() => {
                  setExportMenuAnchor(null);
                  setIsExportModalOpen(true);
                }}
                sx={{
                  px: 3,
                  py: 2,
                  display: "flex",
                  justifyContent: "space-between",
                  alignItems: "center",
                  "&:hover": {
                    backgroundColor: colors.grey[50],
                  },
                }}
              >
                <Box sx={{ display: "flex", alignItems: "center", gap: 2 }}>
                  <svg xmlns="http://www.w3.org/2000/svg" width="22" height="22" viewBox="0 0 22 22" fill="none">
                    <path d="M20.1673 4.74825L9.70815 15.2166L5.82148 11.3299L7.11398 10.0374L9.70815 12.6316L18.8748 3.46492L20.1673 4.74825ZM18.1415 9.36825C18.2607 9.89075 18.334 10.4408 18.334 10.9999C18.334 15.0516 15.0523 18.3333 11.0007 18.3333C6.94898 18.3333 3.66732 15.0516 3.66732 10.9999C3.66732 6.94825 6.94898 3.66659 11.0007 3.66659C12.449 3.66659 13.7873 4.08825 14.924 4.81242L16.244 3.49242C14.759 2.44742 12.9532 1.83325 11.0007 1.83325C5.94065 1.83325 1.83398 5.93992 1.83398 10.9999C1.83398 16.0599 5.94065 20.1666 11.0007 20.1666C16.0607 20.1666 20.1673 16.0599 20.1673 10.9999C20.1673 9.90909 19.9657 8.86408 19.6173 7.89242L18.1415 9.36825Z" fill="#364153" />
                  </svg>
                  <Typography style="body2" color={colors.grey[900]}>
                    Selected Results ({selectedReports.size})
                  </Typography>
                </Box>
              </MenuItem>
              <MenuItem
                onClick={() => {
                  setExportMenuAnchor(null);
                  setIsExportModalOpen(true);
                  setSelectedReports(new Set());
                }}
                sx={{
                  px: 3,
                  py: 2,
                  display: "flex",
                  justifyContent: "space-between",
                  alignItems: "center",
                  "&:hover": {
                    backgroundColor: colors.grey[50],
                  },
                }}
              >
                <Box sx={{ display: "flex", alignItems: "center", gap: 2 }}>
                  <svg xmlns="http://www.w3.org/2000/svg" width="22" height="22" viewBox="0 0 22 22" fill="none">
                    <path d="M6.42183 5.50008H15.5885L10.996 11.2751L6.42183 5.50008ZM3.901 5.14258C5.75267 7.51675 9.17183 11.9167 9.17183 11.9167V17.4167C9.17183 17.9209 9.58433 18.3334 10.0885 18.3334H11.9218C12.426 18.3334 12.8385 17.9209 12.8385 17.4167V11.9167C12.8385 11.9167 16.2485 7.51675 18.1002 5.14258C18.5677 4.53758 18.1368 3.66675 17.376 3.66675H4.62517C3.86433 3.66675 3.4335 4.53758 3.901 5.14258Z" fill="#4A5565" />
                  </svg>
                  <Typography style="body2" color={colors.grey[900]}>
                    {currentSearchState.searchParams.query ? "All Search Results" : "All Results"}
                  </Typography>
                </Box>
              </MenuItem>
            </Menu>
          </Box>
        </Box>
      </Box>

      <TableContainer
        component={Box}
        sx={{
          flexGrow: 1,
          width: "100%",
          overflowY: "auto",
          overflowX: "auto",
          px: "24px",
          height: "calc(100vh - 300px)",
        }}
      >
        {isLoading ? (
          <TableSkeleton tableType="Reports" />
        ) : isError ? (
          <Box
            sx={{
              display: "flex",
              alignItems: "center",
              justifyContent: "center",
              height: "100%",
              p: 4,
            }}
          >
            <Box
              sx={{
                bgcolor: colors.rose[50],
                color: colors.rose[700],
                p: 4,
                borderRadius: 1,
              }}
            >
              Error: {error?.message || "An error occurred while fetching reports"}
            </Box>
          </Box>
        ) : (
          <Table
            stickyHeader
            aria-label="reports table"
            sx={{
              tableLayout: "fixed",
              width: "100%",
            }}
          >
            <TableHead>
              <TableRow>
                <TableCell padding="checkbox" sx={{ width: "48px" }} />
                <TableCell sx={{ width: "15%" }}>
                  <Typography style="caps3" color={colors.grey[800]}>
                    REPORT TYPE
                  </Typography>
                </TableCell>
                <TableCell sx={{ width: "25%" }}>
                  <Typography style="caps3" color={colors.grey[800]}>
                    NARRATIVE
                  </Typography>
                </TableCell>
                <TableCell sx={{ width: "15%" }}>
                  <Typography style="caps3" color={colors.grey[800]}>
                    LOCATION
                  </Typography>
                </TableCell>
                <TableCell sx={{ width: "10%" }}>
                  <Typography style="caps3" color={colors.grey[800]}>
                    SEVERITY
                  </Typography>
                </TableCell>
                <TableCell sx={{ width: "10%" }}>
                  <Typography style="caps3" color={colors.grey[800]}>
                    STATUS
                  </Typography>
                </TableCell>
                <TableCell sx={{ width: "10%" }}>
                  <Typography style="caps3" color={colors.grey[800]}>
                    DATE
                  </Typography>
                </TableCell>
              </TableRow>
            </TableHead>
            <TableBody>
              {data?.reports && data.reports.length > 0 ? (
                data.reports.map((report: Report) => (
                  <TableRow
                    hover
                    key={report.id}
                    onClick={() => handleReportClick(report)}
                    sx={{
                      cursor: "pointer",
                      height: '54px',
                      "&:last-child td, &:last-child th": { border: 0 },
                      "&:hover": {
                        backgroundColor: colors.grey[50],
                      },
                    }}
                  >
                    <TableCell padding="checkbox" onClick={(e) => e.stopPropagation()}>
                      <Checkbox
                        checked={selectedReports.has(report.id)}
                        onChange={() => handleSelectReport(report.id)}
                      />
                    </TableCell>
                    <TableCell sx={{ width: "15%" }}>
                      <Box
                        sx={{
                          overflow: "hidden",
                          textOverflow: "ellipsis",
                          whiteSpace: "nowrap",
                        }}
                      >
                        {getReportType(report)}
                      </Box>
                    </TableCell>
                    <TableCell sx={{ width: "25%" }}>
                      <Box
                        sx={{
                          overflow: "hidden",
                          textOverflow: "ellipsis",
                          whiteSpace: "nowrap",
                        }}
                      >
                        {getReportNarrative(report)}
                      </Box>
                    </TableCell>
                    <TableCell sx={{ width: "15%" }}>
                      <Box
                        sx={{
                          overflow: "hidden",
                          textOverflow: "ellipsis",
                          whiteSpace: "nowrap",
                        }}
                      >
                        {getReportLocation(report)}
                      </Box>
                    </TableCell>
                    <TableCell sx={{ width: "10%" }}>
                      <Box
                        sx={{
                          overflow: "hidden",
                          textOverflow: "ellipsis",
                          whiteSpace: "nowrap",
                        }}
                      >
                        {String(getSeverity(report))}
                      </Box>
                    </TableCell>
                    <TableCell sx={{ width: "10%" }}>
                      <Box
                        sx={{
                          overflow: "hidden",
                          textOverflow: "ellipsis",
                          whiteSpace: "nowrap",
                        }}
                      >
                        {getStatusDisplay(report.status)}
                      </Box>
                    </TableCell>
                    <TableCell sx={{ width: "10%" }}>
                      <Box
                        sx={{
                          overflow: "hidden",
                          textOverflow: "ellipsis",
                          whiteSpace: "nowrap",
                        }}
                      >
                        {formatDate(report.createdAt)}
                      </Box>
                    </TableCell>
                  </TableRow>
                ))
              ) : (
                <TableRow sx={{ height: 300 }}>
                  <TableCell colSpan={7} align="center">
                    <Typography style="body1" color={colors.grey[600]}>
                      {data
                        ? "No reports found. Try adjusting your filters."
                        : "Use the search and filter options to find reports."}
                    </Typography>
                  </TableCell>
                </TableRow>
              )}
            </TableBody>
          </Table>
        )}
      </TableContainer>

      {/* Show pagination if we have pages to navigate (persists during page navigation) */}
      {(totalPages > 1 || totalResults > 0) && <CustomPagination />}

      <Menu
        anchorEl={sortMenuAnchor}
        open={isSortMenuOpen}
        onClose={handleSortMenuClose}
        PaperProps={{
          sx: {
            mt: 1,
            minWidth: 200,
            borderRadius: 2,
            border: `1px solid ${colors.grey[200]}`,
            boxShadow: "0px 4px 16px rgba(0, 0, 0, 0.1)",
          },
        }}
      >
        {REPORT_SORT_OPTIONS.map((option) => (
          <MenuItem
            key={option.value}
            onClick={() => handleSortSelect(option.value)}
            sx={{
              px: 3,
              py: 2,
              display: "flex",
              justifyContent: "space-between",
              alignItems: "center",
              "&:hover": {
                backgroundColor: colors.grey[50],
              },
            }}
          >
            <Typography
              style={selectedSort === option.value ? "body1" : "body2"}
              color={
                selectedSort === option.value
                  ? colors.grey[900]
                  : colors.grey[500]
              }
            >
              {option.label}
            </Typography>
            {selectedSort === option.value && (
              <CheckIcon
                sx={{
                  color: colors.blue[600],
                  fontSize: 20,
                  ml: 2,
                }}
              />
            )}
          </MenuItem>
        ))}
      </Menu>

      <ExportModal
        open={isExportModalOpen}
        onClose={() => setIsExportModalOpen(false)}
        columns={csvData.headers.length}
        rows={selectedReports.size > 0 ? selectedReports.size : (allResultsData?.reports?.length ?? data?.reports?.length ?? 0)}
        fileSize={`${Math.round((selectedReports.size > 0 ? selectedReports.size : (allResultsData?.reports?.length ?? data?.reports?.length ?? 0)) * 0.5)} KB`}
        csvData={csvData.data}
        csvHeaders={csvData.headers}
        isLoading={isExportDataLoading}
        filename="reports_export.csv"
      />
    </Box>
  );
}; 