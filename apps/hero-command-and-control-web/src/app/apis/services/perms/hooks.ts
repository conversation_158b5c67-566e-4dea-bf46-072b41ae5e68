import {
    useQuery,
    useMutation,
    useQueryClient,
    UseQueryResult,
    UseMutationResult,
    QueryKey,
    UseQueryOptions,
    UseMutationOptions,
  } from "@tanstack/react-query";
  import {
    ListRolesRequest,
    ListRolesResponse,
    GetRoleRequest,
    GetRoleResponse,
    CreateRoleRequest,
    CreateRoleResponse,
    UpdateRoleRequest,
    UpdateRoleResponse,
    DeleteRoleRequest,
    DeleteRoleResponse,
    ListActionsByCategoryRequest,
    ListActionsByCategoryResponse,
    ListRoleAssetsRequest,
    ListRoleAssetsResponse,
    CreatePermissionSetResponse,
    CreatePermissionSetRequest,
    GetPermissionSetResponse,
    GetPermissionSetRequest,
    ListPermissionSetsRequest,
    ListPermissionSetsResponse,
    UpdatePermissionSetRequest,
    UpdatePermissionSetResponse,
    DeletePermissionSetRequest,
    DeletePermissionSetResponse,
    AddAssetToRoleResponse,
    AddAssetToRoleRequest,
    RemoveAssetFromRoleResponse,
    RemoveAssetFromRoleRequest,
    GetReportRolePermissionRequest,
    GetReportRolePermissionResponse,
    UpdateReportRolePermissionRequest,
    UpdateReportRolePermissionResponse,
    ListReportRolePermissionsRequest,
    ListReportRolePermissionsResponse,
    GetSituationRolePermissionRequest,
    GetSituationRolePermissionResponse,
    UpdateSituationRolePermissionRequest,
    UpdateSituationRolePermissionResponse,
    ListSituationRolePermissionsRequest,
    ListSituationRolePermissionsResponse,
    GetCaseRolePermissionRequest,
    GetCaseRolePermissionResponse,
    UpdateCaseRolePermissionRequest,
    UpdateCaseRolePermissionResponse,
    ListCaseRolePermissionsRequest,
    ListCaseRolePermissionsResponse,
    ObjectPermission,
  } from "proto/hero/permissions/v1/permissions_pb";
  import * as api from "./endpoints";
  
  // Define query keys
  const roleKeys = {
    all: ["roles"] as const,
    lists: () => [...roleKeys.all, "list"] as const,
    list: (params: ListRolesRequest) => [...roleKeys.lists(), params] as const,
    details: () => [...roleKeys.all, "detail"] as const,
    detail: (id: string) => [...roleKeys.details(), id] as const,
  };
  
  // Added categoryKeys
  const categoryKeys = {
      all: ["categories"] as const,
      lists: () => [...categoryKeys.all, "list"] as const,
      list: (params: ListActionsByCategoryRequest) => [...categoryKeys.lists(), params] as const,
  };

  const roleAssetKeys = {
    all: ["roleAssets"] as const,
    lists: () => [...roleAssetKeys.all, "list"] as const,
    list: (params: ListRoleAssetsRequest) => [...roleAssetKeys.lists(), params] as const,
  };

  const permissionSetKeys = {
    all: ["permissionSets"] as const,
    lists: () => [...permissionSetKeys.all, "list"] as const,
    list: (params: ListPermissionSetsRequest) => [...permissionSetKeys.lists(), params] as const,
    details: () => [...permissionSetKeys.all, "detail"] as const,
    detail: (id: string) => [...permissionSetKeys.details(), id] as const,
  };

  const reportRolePermissionKeys = {
    all: ["reportRolePermissions"] as const,
    lists: () => [...reportRolePermissionKeys.all, "list"] as const,
    list: (params: ListReportRolePermissionsRequest) => [...reportRolePermissionKeys.lists(), params] as const,
    detail: (id: string) => [...reportRolePermissionKeys.all, "detail", id] as const,
  };

  export const situationRolePermissionKeys = {
    all: ["situationRolePermissions"] as const,
    lists: () => [...situationRolePermissionKeys.all, "list"] as const,
    list: (params: ListSituationRolePermissionsRequest) => [...situationRolePermissionKeys.lists(), params] as const,
    detail: (id: string) => [...situationRolePermissionKeys.all, "detail", id] as const,
  };

  export const caseRolePermissionKeys = {
    all: ["caseRolePermissions"] as const,
    lists: () => [...caseRolePermissionKeys.all, "list"] as const,
    list: (params: ListCaseRolePermissionsRequest) => [...caseRolePermissionKeys.lists(), params] as const,
    detail: (id: string) => [...caseRolePermissionKeys.all, "detail", id] as const,
  };

  export const useAddAssetToRole = (
    options?: UseMutationOptions<
      AddAssetToRoleResponse,
      Error,
      AddAssetToRoleRequest
    >
  ) => {
    return useMutation({
      mutationFn: api.addAssetToRole,
      ...options,
    });
  };

  export const useRemoveAssetFromRole = (
    options?: UseMutationOptions<
      RemoveAssetFromRoleResponse,
      Error,
      RemoveAssetFromRoleRequest
    >
  ) => {
    return useMutation({
      mutationFn: api.removeAssetFromRole,
      ...options,
    });
  };

  export const useListRoleAssets = (
    params: ListRoleAssetsRequest,
    options?: Omit<
      UseQueryOptions<
        ListRoleAssetsResponse,
        Error,
        ListRoleAssetsResponse,
        QueryKey
      >,
      "queryKey" | "queryFn"
    >
  ): UseQueryResult<ListRoleAssetsResponse, Error> => {
    return useQuery({
      queryKey: roleAssetKeys.list(params),
      queryFn: () => api.listRoleAssets(params),
      ...options,
    });
  };
  
  // Hook to list roles
  export const useListRoles = (
    params: ListRolesRequest,
    options?: Omit<
      UseQueryOptions<
        ListRolesResponse,
        Error,
        ListRolesResponse,
        QueryKey
      >,
      "queryKey" | "queryFn"
    >
  ): UseQueryResult<ListRolesResponse, Error> => {
    return useQuery({
      queryKey: roleKeys.list(params),
      queryFn: () => api.listRoles(params),
      ...options,
    });
  };
  
  // Added Hook to list categories
  export const useListActionsByCategory = (
    params: ListActionsByCategoryRequest,
    options?: Omit<
      UseQueryOptions<
        ListActionsByCategoryResponse,
        Error,
        ListActionsByCategoryResponse,
        QueryKey
      >,
      "queryKey" | "queryFn"
    >
  ): UseQueryResult<ListActionsByCategoryResponse, Error> => {
    return useQuery({
      queryKey: categoryKeys.list(params),
      queryFn: () => api.ListActionsByCategory(params),
      staleTime: 1000 * 60 * 60 * 1, // 1 hour
      ...options,
    });
  };
  
  // Hook to get a single role (optional, maybe not needed if list provides enough info)
  export const useGetRole = (
    params: GetRoleRequest,
    options?: Omit<
      UseQueryOptions<
        GetRoleResponse,
        Error,
        GetRoleResponse,
        QueryKey
      >,
      "queryKey" | "queryFn"
    >
  ): UseQueryResult<GetRoleResponse, Error> => {
    return useQuery({
      queryKey: roleKeys.detail(params.roleId),
      queryFn: () => api.getRole(params),
      enabled: !!params.roleId, // Only run if roleId is provided
      ...options,
    });
  };
  
  // Hook to create a role
  export const useCreateRole = (
    options?: UseMutationOptions<
      CreateRoleResponse,
      Error,
      CreateRoleRequest
    >
  ): UseMutationResult<
    CreateRoleResponse,
    Error,
    CreateRoleRequest
  > => {
    const queryClient = useQueryClient();
    return useMutation({
      mutationFn: api.createRole,
      onSuccess: (data, variables, context) => {
        // Invalidate and refetch roles list after creation
        queryClient.invalidateQueries({ queryKey: roleKeys.lists() });
        options?.onSuccess?.(data, variables, context);
      },
      ...options,
    });
  };
  
  // Hook to update a role
  export const useUpdateRole = (
    options?: UseMutationOptions<
      UpdateRoleResponse,
      Error,
      UpdateRoleRequest
    >
  ): UseMutationResult<
    UpdateRoleResponse,
    Error,
    UpdateRoleRequest
  > => {
    const queryClient = useQueryClient();
    return useMutation({
      mutationFn: api.updateRole,
      onSuccess: (data, variables, context) => {
        // Invalidate and refetch roles list and specific role details after update
        queryClient.invalidateQueries({ queryKey: roleKeys.lists() });
        // Use roleName from the role for invalidation
        if (variables.role?.name) {
          queryClient.invalidateQueries({ queryKey: roleKeys.detail(variables.role.name) });
        }
        options?.onSuccess?.(data, variables, context);
      },
      ...options,
    });
  };
  
  // Hook to delete a role
  export const useDeleteRole = (
    options?: UseMutationOptions<
      DeleteRoleResponse,
      Error,
      DeleteRoleRequest
    >
  ): UseMutationResult<
    DeleteRoleResponse,
    Error,
    DeleteRoleRequest
  > => {
    const queryClient = useQueryClient();
    return useMutation({
      mutationFn: api.deleteRole,
      onSuccess: (data, variables, context) => {
        // Invalidate and refetch roles list after deletion
        queryClient.invalidateQueries({ queryKey: roleKeys.lists() });
        // Optionally remove the specific role query if it exists
        queryClient.removeQueries({ queryKey: roleKeys.detail(variables.roleId) });
        options?.onSuccess?.(data, variables, context);
      },
      ...options,
    });
  };

  // Hook to create a permission set
  export const useCreatePermissionSet = (
    options?: UseMutationOptions<
      CreatePermissionSetResponse,
      Error,
      CreatePermissionSetRequest
    >
  ): UseMutationResult<
    CreatePermissionSetResponse,
    Error,
    CreatePermissionSetRequest
  > => {
    return useMutation({
      mutationFn: api.createPermissionSet,
      ...options,
    });
  };
  
  // Hook to get a permission set
  export const useGetPermissionSet = (
    params: GetPermissionSetRequest,
    options?: Omit<
      UseQueryOptions<
        GetPermissionSetResponse,
        Error,
        GetPermissionSetResponse,
        QueryKey
      >,
      "queryKey" | "queryFn"
    >
  ): UseQueryResult<GetPermissionSetResponse, Error> => {
    return useQuery({
      queryKey: permissionSetKeys.detail(params.id),
      queryFn: () => api.getPermissionSet(params),
      ...options,
    });
  };  

  // Hook to list permission sets
  export const useListPermissionSets = (
    params: ListPermissionSetsRequest,
    options?: Omit<
      UseQueryOptions<
        ListPermissionSetsResponse,
        Error,
        ListPermissionSetsResponse,
        QueryKey
      >,
      "queryKey" | "queryFn"
    >
  ): UseQueryResult<ListPermissionSetsResponse, Error> => {
    return useQuery({
      queryKey: permissionSetKeys.list(params),
      queryFn: () => api.listPermissionSets(params),
      ...options,
    });
  };

  // Hook to update a permission set
  export const useUpdatePermissionSet = (
    options?: UseMutationOptions<
      UpdatePermissionSetResponse,
      Error,
      UpdatePermissionSetRequest
    >
  ): UseMutationResult<
    UpdatePermissionSetResponse,
    Error,
    UpdatePermissionSetRequest
  > => {
    const queryClient = useQueryClient();
    return useMutation({
      mutationFn: api.updatePermissionSet,
      onSuccess: (data, variables, context) => {
        // Invalidate and refetch list and details
        queryClient.invalidateQueries({ queryKey: permissionSetKeys.lists() });
        if (variables.permissionSet?.name) { // Assuming name is identifier
          queryClient.invalidateQueries({ queryKey: permissionSetKeys.detail(variables.permissionSet.name)});
        }
        options?.onSuccess?.(data, variables, context);
      },
      ...options,
    });
  };

  // Hook to delete a permission set
  export const useDeletePermissionSet = (
    options?: UseMutationOptions<
      DeletePermissionSetResponse,
      Error,
      DeletePermissionSetRequest
    >
  ): UseMutationResult<
    DeletePermissionSetResponse,
    Error,
    DeletePermissionSetRequest
  > => {
    const queryClient = useQueryClient();
    return useMutation({
      mutationFn: api.deletePermissionSet,
      onSuccess: (data, variables, context) => {
        // Invalidate list and remove detail query
        queryClient.invalidateQueries({ queryKey: permissionSetKeys.lists() });
        // Assuming the request directly contains the identifier (e.g., 'name')
        // Adjust 'variables.name' if the identifier field is different in DeletePermissionSetRequest
        const identifier = variables.id;
        if (identifier) {
           queryClient.removeQueries({ queryKey: permissionSetKeys.detail(identifier) });
        }
        options?.onSuccess?.(data, variables, context);
      },
      ...options,
    });
  };

  export const useGetReportRolePermission = (
    params: GetReportRolePermissionRequest,
    options?: Omit<
      UseQueryOptions<
        GetReportRolePermissionResponse,
        Error,
        GetReportRolePermissionResponse,
        QueryKey
      >,
      "queryKey" | "queryFn"
    >
  ): UseQueryResult<GetReportRolePermissionResponse, Error> => {
    return useQuery({
      queryKey: reportRolePermissionKeys.detail(params.reportId),
      queryFn: () => api.getReportRolePermission(params),
      ...options,
    });
  };
  
  export const useUpdateReportRolePermission = (
    options?: UseMutationOptions<
      UpdateReportRolePermissionResponse,
      Error,
      UpdateReportRolePermissionRequest
    >
  ) => {
    return useMutation({
      mutationFn: api.updateReportRolePermission,
      ...options,
    });
  };
  
  export const useListReportRolePermissions = (
    params: ListReportRolePermissionsRequest,
    options?: Omit<
      UseQueryOptions<
        ListReportRolePermissionsResponse,
        Error,
        ListReportRolePermissionsResponse,
        QueryKey
      >,
      "queryKey" | "queryFn"
    >
  ): UseQueryResult<ListReportRolePermissionsResponse, Error> => {
    return useQuery({
      queryKey: reportRolePermissionKeys.list(params),
      queryFn: () => api.listReportRolePermissions(params),
      ...options,
    });
  };
  

  export const useGetSituationRolePermission = (
    params: GetSituationRolePermissionRequest,
    options?: Omit<
      UseQueryOptions<
        GetSituationRolePermissionResponse,
        Error,
        GetSituationRolePermissionResponse,
        QueryKey
      >,
      "queryKey" | "queryFn"
    >
  ): UseQueryResult<GetSituationRolePermissionResponse, Error> => {
    return useQuery({
      queryKey: situationRolePermissionKeys.detail(params.situationId),
      queryFn: () => api.getSituationRolePermission(params),
      ...options,
    });
  };
  
  export const useUpdateSituationRolePermission = (
    options?: UseMutationOptions<
      UpdateSituationRolePermissionResponse,
      Error,
      UpdateSituationRolePermissionRequest
    >
  ) => {
    const queryClient = useQueryClient();
    return useMutation({
      mutationFn: api.updateSituationRolePermission,
      onMutate: async (newPermissionRequest: UpdateSituationRolePermissionRequest): Promise<{ previousPermissions: ListSituationRolePermissionsResponse | undefined }> => {
        const { situationId, roleId, permission } = newPermissionRequest;
        const queryKey = situationRolePermissionKeys.list({ situationId } as ListSituationRolePermissionsRequest);
        
        await queryClient.cancelQueries({ queryKey });

        const previousPermissions = queryClient.getQueryData<ListSituationRolePermissionsResponse>(queryKey);

        if (previousPermissions) {
          const permissionName = ObjectPermission[permission];
          const permissionString = `OBJECT_PERMISSION_${permissionName}`;

          queryClient.setQueryData<ListSituationRolePermissionsResponse>(queryKey, {
            ...previousPermissions,
            objectViewers: previousPermissions.objectViewers.map(viewer =>
              viewer.roleId === roleId
                ? { ...viewer, permission: permissionString as any }
                : viewer
            ),
          });
        }
        
        return { previousPermissions };
      },
      onError: (err, newPermissionRequest, context) => {
        const typedContext = context as { previousPermissions: ListSituationRolePermissionsResponse | undefined };
        const queryKey = situationRolePermissionKeys.list({ situationId: newPermissionRequest.situationId } as ListSituationRolePermissionsRequest);
        if (typedContext?.previousPermissions) {
          queryClient.setQueryData(queryKey, typedContext.previousPermissions);
        }
        options?.onError?.(err, newPermissionRequest, context);
      },
      onSettled: (data, error, variables, context) => {
        const queryKey = situationRolePermissionKeys.list({ situationId: variables.situationId } as ListSituationRolePermissionsRequest);
        queryClient.invalidateQueries({ queryKey });
        options?.onSettled?.(data, error, variables, context);
      },
      ...options,
    });
  };
  
  export const useListSituationRolePermissions = (
    params: ListSituationRolePermissionsRequest,
    options?: Omit<
      UseQueryOptions<
        ListSituationRolePermissionsResponse,
        Error,
        ListSituationRolePermissionsResponse,
        QueryKey
      >,
      "queryKey" | "queryFn"
    >
  ): UseQueryResult<ListSituationRolePermissionsResponse, Error> => {
    return useQuery({
      queryKey: situationRolePermissionKeys.list(params),
      queryFn: () => api.listSituationRolePermissions(params),
      ...options,
    });
  };
  
  export const useGetCaseRolePermission = (
    params: GetCaseRolePermissionRequest,
    options?: Omit<
      UseQueryOptions<
        GetCaseRolePermissionResponse,
        Error,
        GetCaseRolePermissionResponse,
        QueryKey
      >,
      "queryKey" | "queryFn"
    >
  ): UseQueryResult<GetCaseRolePermissionResponse, Error> => {
    return useQuery({
      queryKey: caseRolePermissionKeys.detail(params.caseId),
      queryFn: () => api.getCaseRolePermission(params),
      ...options,
    });
  };

  export const useUpdateCaseRolePermission = (
    options?: UseMutationOptions<
      UpdateCaseRolePermissionResponse,
      Error,
      UpdateCaseRolePermissionRequest
    >
  ) => {
    const queryClient = useQueryClient();
    return useMutation({
      mutationFn: api.updateCaseRolePermission,
      onMutate: async (newPermissionRequest: UpdateCaseRolePermissionRequest): Promise<{ previousPermissions: ListCaseRolePermissionsResponse | undefined }> => {
        const { caseId, roleId, permission } = newPermissionRequest;
        const queryKey = caseRolePermissionKeys.list({ caseId } as ListCaseRolePermissionsRequest);
        
        await queryClient.cancelQueries({ queryKey });

        const previousPermissions = queryClient.getQueryData<ListCaseRolePermissionsResponse>(queryKey);

        if (previousPermissions) {
          const permissionName = ObjectPermission[permission];
          const permissionString = `OBJECT_PERMISSION_${permissionName}`;
          
          queryClient.setQueryData<ListCaseRolePermissionsResponse>(queryKey, {
            ...previousPermissions,
            objectViewers: previousPermissions.objectViewers.map(viewer =>
              viewer.roleId === roleId
                ? { ...viewer, permission: permissionString as any }
                : viewer
            ),
          });
        }
        
        return { previousPermissions };
      },
      onError: (err, newPermissionRequest, context) => {
        const typedContext = context as { previousPermissions: ListCaseRolePermissionsResponse | undefined };
        const queryKey = caseRolePermissionKeys.list({ caseId: newPermissionRequest.caseId } as ListCaseRolePermissionsRequest);
        if (typedContext?.previousPermissions) {
          queryClient.setQueryData(queryKey, typedContext.previousPermissions);
        }
        options?.onError?.(err, newPermissionRequest, context);
      },
      onSettled: (data, error, variables, context) => {
        const queryKey = caseRolePermissionKeys.list({ caseId: variables.caseId } as ListCaseRolePermissionsRequest);
        queryClient.invalidateQueries({ queryKey });
        options?.onSettled?.(data, error, variables, context);
      },
      ...options,
    });
  };

  export const useListCaseRolePermissions = (
    params: ListCaseRolePermissionsRequest,
    options?: Omit<
      UseQueryOptions<
        ListCaseRolePermissionsResponse,
        Error,
        ListCaseRolePermissionsResponse,
        QueryKey
      >,
      "queryKey" | "queryFn"
    >
  ): UseQueryResult<ListCaseRolePermissionsResponse, Error> => {
    return useQuery({
      queryKey: caseRolePermissionKeys.list(params),
      queryFn: () => api.listCaseRolePermissions(params),
      ...options,
    });
  };
  