import axiosInstance from "./axiosInstance";

import {
    ListRolesRequest,
    ListRolesResponse,
    GetRoleRequest,
    GetRoleResponse,
    CreateRoleRequest,
    CreateRoleResponse,
    UpdateRoleRequest,
    UpdateRoleResponse,
    DeleteRoleRequest,
    DeleteRoleResponse,
    ListActionsByCategoryResponse,
    ListActionsByCategoryRequest,
    ListRoleAssetsResponse,
    ListRoleAssetsRequest,
    CreatePermissionSetResponse,
    CreatePermissionSetRequest,
    GetPermissionSetResponse,
    GetPermissionSetRequest,
    ListPermissionSetsRequest,
    ListPermissionSetsResponse,
    UpdatePermissionSetResponse,
    UpdatePermissionSetRequest,
    DeletePermissionSetResponse,
    DeletePermissionSetRequest,
    AddAssetToRoleRequest,
    AddAssetToRoleResponse,
    RemoveAssetFromRoleResponse,
    RemoveAssetFromRoleRequest,
    GetReportRolePermissionRequest,
    GetReportRolePermissionResponse,
    UpdateReportRolePermissionResponse,
    UpdateReportRolePermissionRequest,
    ListReportRolePermissionsRequest,
    ListReportRolePermissionsResponse,
    GetSituationRolePermissionResponse,
    GetSituationRolePermissionRequest,
    UpdateSituationRolePermissionResponse,
    UpdateSituationRolePermissionRequest,
    ListSituationRolePermissionsRequest,
    ListSituationRolePermissionsResponse,
    GetCaseRolePermissionRequest,
    GetCaseRolePermissionResponse,
    UpdateCaseRolePermissionRequest,
    UpdateCaseRolePermissionResponse,
    ListCaseRolePermissionsRequest,
    ListCaseRolePermissionsResponse,
  } from "proto/hero/permissions/v1/permissions_pb";

  export const addAssetToRole = async (data: AddAssetToRoleRequest): Promise<AddAssetToRoleResponse> => {
    const response = await axiosInstance.post<AddAssetToRoleResponse>(
      "/hero.permissions.v1.PermissionService/AddAssetToRole",
      data
    );
    return response.data;
  };
  
  export const removeAssetFromRole = async (data: RemoveAssetFromRoleRequest): Promise<RemoveAssetFromRoleResponse> => {
    const response = await axiosInstance.post<RemoveAssetFromRoleResponse>(
      "/hero.permissions.v1.PermissionService/RemoveAssetFromRole",
      data
    );
    return response.data;
  };

  export const listRoleAssets = async (data: ListRoleAssetsRequest): Promise<ListRoleAssetsResponse> => {
    const response = await axiosInstance.post<ListRoleAssetsResponse>(
      "/hero.permissions.v1.PermissionService/ListRoleAssets",
      data
    );
    return response.data;
  };

  export const ListActionsByCategory = async (data: ListActionsByCategoryRequest): Promise<ListActionsByCategoryResponse> => {
    const response = await axiosInstance.post<ListActionsByCategoryResponse>(
      "/hero.permissions.v1.PermissionService/ListActionsByCategory",
      data
    );
    return response.data;
  };
  
  export const listRoles = async (data: ListRolesRequest): Promise<ListRolesResponse> => {
  const response = await axiosInstance.post<ListRolesResponse>(
    "/hero.permissions.v1.PermissionService/ListRoles",
    data
  );
  return response.data;
  };
  
  export const getRole = async (data: GetRoleRequest): Promise<GetRoleResponse> => {
  const response = await axiosInstance.post<GetRoleResponse>(
    "/hero.permissions.v1.PermissionService/GetRole",
    data
  );
  return response.data;
  };

  export const createRole = async (data: CreateRoleRequest): Promise<CreateRoleResponse> => {
    const response = await axiosInstance.post<CreateRoleResponse>(
      "/hero.permissions.v1.PermissionService/CreateRole",
      data
    );
    return response.data;
    };

    export const updateRole = async (data: UpdateRoleRequest): Promise<UpdateRoleResponse> => {
      const response = await axiosInstance.post<UpdateRoleResponse>(
        "/hero.permissions.v1.PermissionService/UpdateRole",
        data
      );
      return response.data;
    };

    export const deleteRole = async (data: DeleteRoleRequest): Promise<DeleteRoleResponse> => {
      const response = await axiosInstance.post<DeleteRoleResponse>(
        "/hero.permissions.v1.PermissionService/DeleteRole",
        data
      );
      return response.data;
    };

    export const createPermissionSet = async (data: CreatePermissionSetRequest): Promise<CreatePermissionSetResponse> => {
      const response = await axiosInstance.post<CreatePermissionSetResponse>(
        "/hero.permissions.v1.PermissionService/CreatePermissionSet",
        data
      );
      return response.data;
    };

    export const getPermissionSet = async (data: GetPermissionSetRequest): Promise<GetPermissionSetResponse> => {
      const response = await axiosInstance.post<GetPermissionSetResponse>(
        "/hero.permissions.v1.PermissionService/GetPermissionSet",
        data
      );
      return response.data;
    };

    export const listPermissionSets = async (data: ListPermissionSetsRequest): Promise<ListPermissionSetsResponse> => {
      const response = await axiosInstance.post<ListPermissionSetsResponse>(
        "/hero.permissions.v1.PermissionService/ListPermissionSets",
        data
      );
      return response.data;
    };
    
    export const updatePermissionSet = async (data: UpdatePermissionSetRequest): Promise<UpdatePermissionSetResponse> => {
      const response = await axiosInstance.post<UpdatePermissionSetResponse>(
        "/hero.permissions.v1.PermissionService/UpdatePermissionSet",
        data
      );
      return response.data;
    };

    export const deletePermissionSet = async (data: DeletePermissionSetRequest): Promise<DeletePermissionSetResponse> => {
      const response = await axiosInstance.post<DeletePermissionSetResponse>(
        "/hero.permissions.v1.PermissionService/DeletePermissionSet",
        data
      );
      return response.data;
    };

    export const getReportRolePermission = async (data: GetReportRolePermissionRequest): Promise<GetReportRolePermissionResponse> => {
      const response = await axiosInstance.post<GetReportRolePermissionResponse>(
        "/hero.permissions.v1.PermissionService/GetReportRolePermission",
        data
      );
      return response.data;
    };

    export const updateReportRolePermission = async (data: UpdateReportRolePermissionRequest): Promise<UpdateReportRolePermissionResponse> => {
      const response = await axiosInstance.post<UpdateReportRolePermissionResponse>(
        "/hero.permissions.v1.PermissionService/UpdateReportRolePermission",
        data
      );
      return response.data;
    };

    export const listReportRolePermissions = async (data: ListReportRolePermissionsRequest): Promise<ListReportRolePermissionsResponse> => {
      const response = await axiosInstance.post<ListReportRolePermissionsResponse>(
        "/hero.permissions.v1.PermissionService/ListReportRolePermissions",
        data
      );
      return response.data;
    };

    export const getSituationRolePermission = async (data: GetSituationRolePermissionRequest): Promise<GetSituationRolePermissionResponse> => {
      const response = await axiosInstance.post<GetSituationRolePermissionResponse>(
        "/hero.permissions.v1.PermissionService/GetSituationRolePermission",
        data
      );
      return response.data;
    };

    export const updateSituationRolePermission = async (data: UpdateSituationRolePermissionRequest): Promise<UpdateSituationRolePermissionResponse> => {
      const response = await axiosInstance.post<UpdateSituationRolePermissionResponse>(
        "/hero.permissions.v1.PermissionService/UpdateSituationRolePermission",
        data
      );
      return response.data;
    };

    export const listSituationRolePermissions = async (data: ListSituationRolePermissionsRequest): Promise<ListSituationRolePermissionsResponse> => {
      const response = await axiosInstance.post<ListSituationRolePermissionsResponse>(
        "/hero.permissions.v1.PermissionService/ListSituationRolePermissions",
        data
      );
      return response.data;
    };

    export const getCaseRolePermission = async (data: GetCaseRolePermissionRequest): Promise<GetCaseRolePermissionResponse> => {
      const response = await axiosInstance.post<GetCaseRolePermissionResponse>(
        "/hero.permissions.v1.PermissionService/GetCaseRolePermission",
        data
      );
      return response.data;
    };

    export const updateCaseRolePermission = async (data: UpdateCaseRolePermissionRequest): Promise<UpdateCaseRolePermissionResponse> => {
      const response = await axiosInstance.post<UpdateCaseRolePermissionResponse>(
        "/hero.permissions.v1.PermissionService/UpdateCaseRolePermission",
        data
      );
      return response.data;
    };

    export const listCaseRolePermissions = async (data: ListCaseRolePermissionsRequest): Promise<ListCaseRolePermissionsResponse> => {
      const response = await axiosInstance.post<ListCaseRolePermissionsResponse>(
        "/hero.permissions.v1.PermissionService/ListCaseRolePermissions",
        data
      );
      return response.data;
    };

