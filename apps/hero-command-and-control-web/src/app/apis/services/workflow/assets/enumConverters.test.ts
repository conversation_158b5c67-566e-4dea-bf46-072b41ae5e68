import {
    stringToAssetType,
    assetTypeToString,
    stringToAssetStatus,
    assetStatusToString,
} from './enumConverters';
import { AssetType, AssetStatus } from 'proto/hero/assets/v2/assets_pb';

describe('AssetType Converters', () => {
    describe('stringToAssetType', () => {
        it('should convert a valid string to the corresponding AssetType enum value', () => {
            // Assuming AssetType.CAMERA is a valid enum value.
            expect(stringToAssetType('ASSET_TYPE_CAMERA')).toBe(AssetType.CAMERA);
        });

        it('should return AssetType.UNSPECIFIED for strings without the proper prefix', () => {
            expect(stringToAssetType('INVALID_CAMERA')).toBe(AssetType.UNSPECIFIED);
        });

        it('should return AssetType.UNSPECIFIED for unknown enum keys', () => {
            // "UNKNOWN" is not defined in AssetType.
            expect(stringToAssetType('ASSET_TYPE_UNKNOWN')).toBe(AssetType.UNSPECIFIED);
        });
    });

    describe('assetTypeToString', () => {
        it('should convert an AssetType enum value to its string representation', () => {
            // Assuming AssetType.BOT is a valid enum value.
            expect(assetTypeToString(AssetType.BOT)).toBe('ASSET_TYPE_BOT');
        });
    });
});

describe('AssetStatus Converters', () => {
    describe('stringToAssetStatus', () => {
        it('should convert a valid string to the corresponding AssetStatus enum value', () => {
            // Assuming AssetStatus.MAINTENANCE is a valid enum value.
            expect(stringToAssetStatus('ASSET_STATUS_MAINTENANCE')).toBe(AssetStatus.MAINTENANCE);
        });

        it('should return AssetStatus.UNSPECIFIED for strings without the proper prefix', () => {
            expect(stringToAssetStatus('INVALID_MAINTENANCE')).toBe(AssetStatus.UNSPECIFIED);
        });

        it('should return AssetStatus.UNSPECIFIED for unknown enum keys', () => {
            // "UNKNOWN" is not defined in AssetStatus.
            expect(stringToAssetStatus('ASSET_STATUS_UNKNOWN')).toBe(AssetStatus.UNSPECIFIED);
        });
    });

    describe('assetStatusToString', () => {
        it('should convert an AssetStatus enum value to its string representation', () => {
            // Assuming AssetStatus.BUSY is a valid enum value.
            expect(assetStatusToString(AssetStatus.BUSY)).toBe('ASSET_STATUS_BUSY');
        });
    });
});
