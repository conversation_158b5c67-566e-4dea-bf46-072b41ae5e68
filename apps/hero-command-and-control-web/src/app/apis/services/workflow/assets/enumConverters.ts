// Function to convert a string (e.g. "ASSET_TYPE_CAMERA")

import { AssetStatus, AssetType } from "proto/hero/assets/v2/assets_pb";

// to the corresponding enum value (e.g. AssetType.CAMERA).
export function stringToAssetType(value: string): AssetType {
    const prefix = "ASSET_TYPE_";
    if (!value.startsWith(prefix)) {
        return AssetType.UNSPECIFIED;
    }
    // Remove the prefix to extract the enum key.
    const enumKey = value.substring(prefix.length) as keyof typeof AssetType;
    if (enumKey in AssetType) {
        return AssetType[enumKey];
    }
    return AssetType.UNSPECIFIED;
}

// Function to convert an enum value (e.g. AssetType.BOT)
// back to its string representation (e.g. "ASSET_TYPE_BOT").
export function assetTypeToString(assetType: AssetType): string {
    // Using reverse mapping: AssetType[asset] returns the key as a string.
    const key = AssetType[assetType];
    return "ASSET_TYPE_" + key;
}

// For crazy reason when you use our hooks to get Asset it gives you not the right Enum 
// It gives you the string representation of the enum
// But type is still AssetType
// You can just type cast it from AssetType
export function hookAssetTypeToString(assetType: AssetType): string {
    return String(assetType)
}


// Function to convert a string (e.g. "ASSET_STATUS_MAINTENANCE")
// to the corresponding enum value (e.g. AssetStatus.MAINTENANCE).
export function stringToAssetStatus(value: string): AssetStatus {
    const prefix = "ASSET_STATUS_";
    if (!value.startsWith(prefix)) {
        return AssetStatus.UNSPECIFIED;
    }
    // Remove the prefix to extract the enum key.
    const enumKey = value.substring(prefix.length) as keyof typeof AssetStatus;
    if (enumKey in AssetStatus) {
        return AssetStatus[enumKey];
    }
    return AssetStatus.UNSPECIFIED;
}

// Function to convert an enum value (e.g. AssetStatus.BUSY)
// back to its string representation (e.g. "ASSET_STATUS_BUSY").
export function assetStatusToString(status: AssetStatus): string {
    // Reverse mapping returns the key as a string.
    const key = AssetStatus[status];
    return "ASSET_STATUS_" + key;
}

// For crazy reason when you use our hooks to get Asset it gives you not the right Enum
// It gives you the string representation of the enum
// But type is still AssetStatus
// You can just type cast it from AssetStatus
export function hookAssetStatusToString(status: AssetStatus): string {
    return String(status)
}