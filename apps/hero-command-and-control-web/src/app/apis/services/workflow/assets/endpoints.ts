import axios, { AxiosResponse } from "axios";
import axiosInstance from "../axiosInstance";

import {
    GetAssetRequest,
    GetAssetResponse,
    GetAssetByCognitoSubRequest,
    GetAssetByCognitoSubResponse,
    CreateAssetRequest,
    CreateAssetResponse,
    ListAssetsRequest,
    ListAssetsResponse,
    DeleteAssetRequest,
    DeleteAssetResponse,
    UpdateAssetRequest,
    UpdateAssetResponse,
    AddAdditionalInfoRequest,
    AddAdditionalInfoResponse,
    GetAssetPrivateRequest,
    GetAssetPrivateResponse,
    GetZelloChannelsResponse,
    GetZelloChannelsRequest,
    ListAssetsByPhoneNumberRequest,
    ListAssetsByPhoneNumberResponse,
    SearchAssetsRequest,
    SearchAssetsResponse,
} from "proto/hero/assets/v2/assets_pb";

// Custom API Error class to include HTTP status codes
class APIError extends Error {
    public statusCode: number;
    constructor(statusCode: number, message: string) {
        super(message);
        this.statusCode = statusCode;
        Object.setPrototypeOf(this, APIError.prototype);
    }
}

// Generic helper function for POST requests with two type parameters:
// T for the response type and D for the request data type.
const postRequest = async <T, D>(url: string, data: D): Promise<T> => {
    try {
        const response: AxiosResponse<T> = await axiosInstance.post<T, AxiosResponse<T>, D>(
            url,
            data
        );
        return response.data;
    } catch (error: unknown) {
        if (axios.isAxiosError(error)) {
            const status = error.response?.status ?? 500;
            const errorMsg = error.response?.data?.message || error.message;
            throw new APIError(status, `Request to ${url} failed: ${errorMsg}`);
        }
        throw error;
    }
};

// Get Asset
export const getAsset = async (
    data: GetAssetRequest
): Promise<GetAssetResponse> => {
    return postRequest<GetAssetResponse, GetAssetRequest>(
        "/hero.assets.v2.AssetRegistryService/GetAsset",
        data
    );
};

// GetAssetPrivate
export const getAssetPrivate = async (data: GetAssetPrivateRequest): Promise<GetAssetPrivateResponse> => {
    return postRequest<GetAssetPrivateResponse, GetAssetPrivateRequest>(
        "/hero.assets.v2.AssetRegistryService/GetAssetPrivate",
        data
    );
  };

// Get Asset by Cognito Sub
export const getAssetByCognitoSub = async (data: GetAssetByCognitoSubRequest): Promise<GetAssetByCognitoSubResponse> => {
    return postRequest<GetAssetByCognitoSubResponse, GetAssetByCognitoSubRequest>(
        "/hero.assets.v2.AssetRegistryService/GetAssetByCognitoSub",
        data
    );
};

// Create Asset
export const createAsset = async (
    data: CreateAssetRequest
): Promise<CreateAssetResponse> => {
    return postRequest<CreateAssetResponse, CreateAssetRequest>(
        "/hero.assets.v2.AssetRegistryService/CreateAsset",
        data
    );
};

// List Assets
export const listAssets = async (
    data: ListAssetsRequest
): Promise<ListAssetsResponse> => {
    return postRequest<ListAssetsResponse, ListAssetsRequest>(
        "/hero.assets.v2.AssetRegistryService/ListAssets",
        data
    );
};

// Delete Asset
export const deleteAsset = async (
    data: DeleteAssetRequest
): Promise<DeleteAssetResponse> => {
    return postRequest<DeleteAssetResponse, DeleteAssetRequest>(
        "/hero.assets.v2.AssetRegistryService/DeleteAsset",
        data
    );
};

// Update Asset
export const updateAsset = async (
    data: UpdateAssetRequest
): Promise<UpdateAssetResponse> => {
    return postRequest<UpdateAssetResponse, UpdateAssetRequest>(
        "/hero.assets.v2.AssetRegistryService/UpdateAsset",
        data
    );
};

// Add Additional Info to Asset
export const addAdditionalInfo = async (
    data: AddAdditionalInfoRequest
): Promise<AddAdditionalInfoResponse> => {
    return postRequest<AddAdditionalInfoResponse, AddAdditionalInfoRequest>(
        "/hero.assets.v2.AssetRegistryService/AddAdditionalInfo",
        data
    );
};

export const getZelloChannels = async (): Promise<GetZelloChannelsResponse> => {
    return postRequest<GetZelloChannelsResponse, GetZelloChannelsRequest>(
        "/hero.assets.v2.AssetRegistryService/GetZelloChannels",
        {} as GetZelloChannelsRequest
    );
};

export const listAssetsByPhoneNumber = (
    request: ListAssetsByPhoneNumberRequest
): Promise<ListAssetsByPhoneNumberResponse> => {
    return postRequest<ListAssetsByPhoneNumberResponse, ListAssetsByPhoneNumberRequest>(
        "/hero.assets.v2.AssetRegistryService/ListAssetsByPhoneNumber",
        request
    );
};

// Search Assets
export const searchAssets = async (
    data: SearchAssetsRequest
): Promise<SearchAssetsResponse> => {
    return postRequest<SearchAssetsResponse, SearchAssetsRequest>(
        "/hero.assets.v2.AssetRegistryService/SearchAssets",
        data
    );
};
