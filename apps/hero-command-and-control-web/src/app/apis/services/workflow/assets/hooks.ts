import {
    useQuery,
    useMutation,
    useQueryClient,
    UseQueryOptions,
    UseMutationOptions
} from "@tanstack/react-query";
import {
    GetAssetRequest,
    GetAssetResponse,
    GetAssetByCognitoSubRequest,
    GetAssetByCognitoSubResponse,
    CreateAssetRequest,
    CreateAssetResponse,
    ListAssetsRequest,
    ListAssetsResponse,
    DeleteAssetRequest,
    DeleteAssetResponse,
    UpdateAssetRequest,
    UpdateAssetResponse,
    AddAdditionalInfoRequest,
    AddAdditionalInfoResponse,
    GetAssetPrivateResponse,
    GetAssetPrivateRequest,
    GetZelloChannelsResponse,
    ListAssetsByPhoneNumberRequest,
    ListAssetsByPhoneNumberResponse,
    SearchAssetsRequest,
    SearchAssetsResponse,
} from "proto/hero/assets/v2/assets_pb";

import {
    getAsset,
    getAssetByCognitoSub,
    createAsset,
    listAssets,
    deleteAsset,
    updateAsset,
    addAdditionalInfo,
    getAssetPrivate,
    getZelloChannels,
    listAssetsByPhoneNumber,
    searchAssets,
} from "./endpoints";

// Cache key constants
const ASSETS_QUERY_KEY = "assets";
const ASSET_QUERY_KEY = "asset";
const ASSET_PRIVATE_QUERY_KEY = "assetPrivate";
const ASSET_BY_COGNITO_QUERY_KEY = "assetByCognitoSub";

/**
 * Hook for fetching an asset by its ID.
 */
export function useAsset(
    assetId: string,
    options?: UseQueryOptions<GetAssetResponse, Error>
) {
    return useQuery({
        queryKey: [ASSET_QUERY_KEY, assetId],
        queryFn: () => getAsset({ id: assetId } as GetAssetRequest),
        enabled: !!assetId,
        staleTime: 5 * 60 * 1000, // 5 minutes
        retry: 2,
        ...options,
    });
}

/**
 * Hook for fetching an asset and its private details by its ID.
 */
export function useAssetPrivate(
    assetId: string,
    options?: UseQueryOptions<GetAssetPrivateResponse, Error>
) {
    return useQuery({
        queryKey: [ASSET_PRIVATE_QUERY_KEY, assetId],
        queryFn: () => getAssetPrivate({ assetId } as GetAssetPrivateRequest),
        enabled: !!assetId,
        staleTime: 5 * 60 * 1000, // 5 minutes
        retry: 2,
        ...options,
    });
}

/**
 * Hook for fetching an asset using the Cognito sub from cookies.
 */
export function useAssetByCognitoSub(
    cognitoJwtSub: string,
    options?: UseQueryOptions<GetAssetByCognitoSubResponse, Error>
) {
    return useQuery({
        queryKey: [ASSET_BY_COGNITO_QUERY_KEY],
        queryFn: () => getAssetByCognitoSub({ cognitoJwtSub } as GetAssetByCognitoSubRequest),
        enabled: !!cognitoJwtSub,
        staleTime: 5 * 60 * 1000,
        retry: 2,
        ...options,
    });
}

/**
 * Hook for creating an asset.
 * On success, invalidates the assets list.
 */
export function useCreateAsset(
    options?: UseMutationOptions<CreateAssetResponse, Error, CreateAssetRequest>
) {
    const queryClient = useQueryClient();
    return useMutation({
        mutationFn: (newAsset: CreateAssetRequest) => createAsset(newAsset),
        onSuccess: () => {
            queryClient.invalidateQueries({ queryKey: [ASSETS_QUERY_KEY] });
        },
        ...options,
    });
}

/**
 * Hook for listing assets.
 * The query key includes the request parameters for proper cache segmentation.
 */
export function useListAssets(
    params: ListAssetsRequest,
    options?: Omit<
      UseQueryOptions<ListAssetsResponse, Error, ListAssetsResponse>,
      "queryKey" | "queryFn"
    >
  ) {
    return useQuery<ListAssetsResponse, Error>({
      queryKey: [ASSETS_QUERY_KEY, "list", params],
      queryFn: () => listAssets(params),
      staleTime: 2 * 60 * 1000,
      retry: 2,
      ...options,
    });
  }
  

/**
 * Hook for deleting an asset.
 * On success, invalidates the assets list.
 */
export function useDeleteAsset(
    options?: UseMutationOptions<DeleteAssetResponse, Error, DeleteAssetRequest>
) {
    const queryClient = useQueryClient();
    return useMutation({
        mutationFn: (req: DeleteAssetRequest) => deleteAsset(req),
        onSuccess: () => {
            queryClient.invalidateQueries({ queryKey: [ASSETS_QUERY_KEY] });
        },
        ...options,
    });
}

/**
 * Hook for updating an asset.
 * On success, invalidates both the assets list and the specific asset cache.
 */
export function useUpdateAsset(
    options?: UseMutationOptions<UpdateAssetResponse, Error, UpdateAssetRequest>
) {
    const queryClient = useQueryClient();
    return useMutation({
        mutationFn: (req: UpdateAssetRequest) => updateAsset(req),
        onSuccess: (data: UpdateAssetResponse) => {
            queryClient.invalidateQueries({ queryKey: [ASSETS_QUERY_KEY] });
            queryClient.invalidateQueries({ queryKey: [ASSET_QUERY_KEY, data.asset?.id] });
        },
        ...options,
    });
}

/**
 * Hook for adding additional info to an asset.
 * On success, invalidates the specific asset cache.
 */
export function useAddAdditionalInfoToAsset(
    options?: UseMutationOptions<AddAdditionalInfoResponse, Error, AddAdditionalInfoRequest>
) {
    const queryClient = useQueryClient();
    return useMutation({
        mutationFn: (req: AddAdditionalInfoRequest) => addAdditionalInfo(req),
        onSuccess: (data: AddAdditionalInfoResponse) => {
            queryClient.invalidateQueries({ queryKey: [ASSET_QUERY_KEY, data.id] });
        },
        ...options,
    });
}

export function useGetZelloChannels(
    options?: UseQueryOptions<GetZelloChannelsResponse, Error>
) {
    return useQuery({
        queryKey: [ASSETS_QUERY_KEY, "zelloChannels"],
        queryFn: () => getZelloChannels(),
        ...options,
    });
}

export function useListAssetsByPhoneNumber(
    phoneNumber: string | null | undefined,
    options?: UseQueryOptions<ListAssetsByPhoneNumberResponse, Error>
) {
    return useQuery({
        queryKey: [ASSET_QUERY_KEY, 'phone', phoneNumber],
        queryFn: () => listAssetsByPhoneNumber({ phoneNumber } as ListAssetsByPhoneNumberRequest),
        enabled: !!phoneNumber,
        retry: 2,
        ...options,
    });
}

/**
 * Hook for searching assets with advanced filtering and text search capabilities.
 * The query key includes the request parameters for proper cache segmentation.
 */
export function useSearchAssets(
    params: SearchAssetsRequest,
    options?: Omit<
        UseQueryOptions<SearchAssetsResponse, Error, SearchAssetsResponse>,
        "queryKey" | "queryFn"
    >
) {
    return useQuery<SearchAssetsResponse, Error>({
        queryKey: [ASSETS_QUERY_KEY, "search", params],
        queryFn: () => searchAssets(params),
        staleTime: 2 * 60 * 1000, // 2 minutes
        retry: 2,
        ...options,
    });
}