import axios, { AxiosResponse } from "axios";
import { Empty } from "google-protobuf/google/protobuf/empty_pb";
import axiosInstance from "../axiosInstance";

import {
    AddAdditionalInfoRequest,
    AddAdditionalInfoResponse,
    AddCaseTagRequest,
    AddCaseUpdateRequest,
    AddEntityRefToCaseRequest,
    AddReportToCaseRequest,
    AddSituationToCaseRequest,
    AddWatcherRequest,
    AssociateAssetToCaseRequest,
    AssociateAssetToCaseResponse,
    BatchGetCasesRequest,
    BatchGetCasesResponse,
    Case,
    CaseSnapshot,
    CreateCaseRequest,
    CreateCaseResponse,
    DeleteCaseRequest,
    DisassociateAssetFromCaseRequest,
    GetCaseRequest,
    GetCaseVersionRequest,
    LinkRelatedCaseRequest,
    ListAssetAssociationsForCaseRequest,
    ListAssetAssociationsForCaseResponse,
    ListCaseAuditLogRequest,
    ListCaseAuditLogResponse,
    ListCaseFileAttachmentsRequest,
    ListCaseFileAttachmentsResponse,
    ListCasesByAssetIdRequest,
    ListCasesByEntityIdRequest,
    ListCasesByReportIdRequest,
    ListCasesBySituationIdRequest,
    ListCasesRequest,
    ListCasesResponse,
    ListCaseStatusHistoryRequest,
    ListCaseStatusHistoryResponse,
    ListCaseUpdatesRequest,
    ListCaseUpdatesResponse,
    ListCaseVersionsRequest,
    ListCaseVersionsResponse,
    RemoveCaseTagRequest,
    RemoveCaseUpdateRequest,
    RemoveEntityRefFromCaseRequest,
    RemoveReportFromCaseRequest,
    RemoveSituationFromCaseRequest,
    RemoveWatcherRequest,
    SearchCasesRequest,
    SearchCasesResponse,
    UnlinkRelatedCaseRequest,
    UpdateAssetAssociationRequest,
    UpdateAssetAssociationResponse,
    UpdateCaseRequest,
    UpdateCaseStatusRequest,
    UpdateCaseStatusResponse,
} from "proto/hero/cases/v1/cases_pb";

// Custom APIError class to include HTTP status codes
class APIError extends Error {
    public statusCode: number;
    constructor(statusCode: number, message: string) {
        super(message);
        this.statusCode = statusCode;
        Object.setPrototypeOf(this, APIError.prototype);
    }
}

// Generic helper function for POST requests with two type parameters:
// T for the response type and D for the request data type.
const postRequest = async <T, D>(url: string, data: D): Promise<T> => {
    try {
        const response: AxiosResponse<T> = await axiosInstance.post<T, AxiosResponse<T>, D>(
            url,
            data
        );
        return response.data;
    } catch (error: unknown) {
        if (axios.isAxiosError(error)) {
            const status = error.response?.status ?? 500;
            const errorMsg = error.response?.data?.message ?? error.message;
            throw new APIError(status, `Request to ${url} failed: ${errorMsg}`);
        }
        throw error;
    }
};

// Core CRUD Operations

// Create Case
export const createCase = async (
    data: CreateCaseRequest
): Promise<CreateCaseResponse> => {
    return postRequest<CreateCaseResponse, CreateCaseRequest>(
        "/hero.cases.v1.CaseService/CreateCase",
        data
    );
};

// Get Case by ID
export const getCase = async (
    data: GetCaseRequest
): Promise<Case> => {
    return postRequest<Case, GetCaseRequest>(
        "/hero.cases.v1.CaseService/GetCase",
        data
    );
};

// Update Case
export const updateCase = async (
    data: UpdateCaseRequest
): Promise<Case> => {
    return postRequest<Case, UpdateCaseRequest>(
        "/hero.cases.v1.CaseService/UpdateCase",
        data
    );
};

// Delete Case
export const deleteCase = async (
    data: DeleteCaseRequest
): Promise<Empty> => {
    return postRequest<Empty, DeleteCaseRequest>(
        "/hero.cases.v1.CaseService/DeleteCase",
        data
    );
};

// Listing Operations

// List Cases
export const listCases = async (
    data: ListCasesRequest
): Promise<ListCasesResponse> => {
    return postRequest<ListCasesResponse, ListCasesRequest>(
        "/hero.cases.v1.CaseService/ListCases",
        data
    );
};

// Batch Get Cases
export const batchGetCases = async (
    data: BatchGetCasesRequest
): Promise<BatchGetCasesResponse> => {
    return postRequest<BatchGetCasesResponse, BatchGetCasesRequest>(
        "/hero.cases.v1.CaseService/BatchGetCases",
        data
    );
};

// List Cases By Situation ID
export const listCasesBySituationId = async (
    data: ListCasesBySituationIdRequest
): Promise<ListCasesResponse> => {
    return postRequest<ListCasesResponse, ListCasesBySituationIdRequest>(
        "/hero.cases.v1.CaseService/ListCasesBySituationId",
        data
    );
};

// List Cases By Report ID
export const listCasesByReportId = async (
    data: ListCasesByReportIdRequest
): Promise<ListCasesResponse> => {
    return postRequest<ListCasesResponse, ListCasesByReportIdRequest>(
        "/hero.cases.v1.CaseService/ListCasesByReportId",
        data
    );
};

// List Cases By Asset ID
export const listCasesByAssetId = async (
    data: ListCasesByAssetIdRequest
): Promise<ListCasesResponse> => {
    return postRequest<ListCasesResponse, ListCasesByAssetIdRequest>(
        "/hero.cases.v1.CaseService/ListCasesByAssetId",
        data
    );
};

// List Cases By Entity ID
export const listCasesByEntityId = async (
    data: ListCasesByEntityIdRequest
): Promise<ListCasesResponse> => {
    return postRequest<ListCasesResponse, ListCasesByEntityIdRequest>(
        "/hero.cases.v1.CaseService/ListCasesByEntityId",
        data
    );
};

// Relationship Management

// Add Situation To Case
export const addSituationToCase = async (
    data: AddSituationToCaseRequest
): Promise<Case> => {
    return postRequest<Case, AddSituationToCaseRequest>(
        "/hero.cases.v1.CaseService/AddSituationToCase",
        data
    );
};

// Remove Situation From Case
export const removeSituationFromCase = async (
    data: RemoveSituationFromCaseRequest
): Promise<Case> => {
    return postRequest<Case, RemoveSituationFromCaseRequest>(
        "/hero.cases.v1.CaseService/RemoveSituationFromCase",
        data
    );
};

// Add Report To Case
export const addReportToCase = async (
    data: AddReportToCaseRequest
): Promise<Case> => {
    return postRequest<Case, AddReportToCaseRequest>(
        "/hero.cases.v1.CaseService/AddReportToCase",
        data
    );
};

// Remove Report From Case
export const removeReportFromCase = async (
    data: RemoveReportFromCaseRequest
): Promise<Case> => {
    return postRequest<Case, RemoveReportFromCaseRequest>(
        "/hero.cases.v1.CaseService/RemoveReportFromCase",
        data
    );
};

// Add Entity Reference To Case
export const addEntityRefToCase = async (
    data: AddEntityRefToCaseRequest
): Promise<Case> => {
    return postRequest<Case, AddEntityRefToCaseRequest>(
        "/hero.cases.v1.CaseService/AddEntityRefToCase",
        data
    );
};

// Remove Entity Reference From Case
export const removeEntityRefFromCase = async (
    data: RemoveEntityRefFromCaseRequest
): Promise<Case> => {
    return postRequest<Case, RemoveEntityRefFromCaseRequest>(
        "/hero.cases.v1.CaseService/RemoveEntityRefFromCase",
        data
    );
};

// Link Related Case
export const linkRelatedCase = async (
    data: LinkRelatedCaseRequest
): Promise<Case> => {
    return postRequest<Case, LinkRelatedCaseRequest>(
        "/hero.cases.v1.CaseService/LinkRelatedCase",
        data
    );
};

// Unlink Related Case
export const unlinkRelatedCase = async (
    data: UnlinkRelatedCaseRequest
): Promise<Case> => {
    return postRequest<Case, UnlinkRelatedCaseRequest>(
        "/hero.cases.v1.CaseService/UnlinkRelatedCase",
        data
    );
};

// Asset Association Management

// Associate Asset To Case
export const associateAssetToCase = async (
    data: AssociateAssetToCaseRequest
): Promise<AssociateAssetToCaseResponse> => {
    return postRequest<AssociateAssetToCaseResponse, AssociateAssetToCaseRequest>(
        "/hero.cases.v1.CaseService/AssociateAssetToCase",
        data
    );
};

// Update Asset Association
export const updateAssetAssociation = async (
    data: UpdateAssetAssociationRequest
): Promise<UpdateAssetAssociationResponse> => {
    return postRequest<UpdateAssetAssociationResponse, UpdateAssetAssociationRequest>(
        "/hero.cases.v1.CaseService/UpdateAssetAssociation",
        data
    );
};

// Disassociate Asset From Case
export const disassociateAssetFromCase = async (
    data: DisassociateAssetFromCaseRequest
): Promise<Empty> => {
    return postRequest<Empty, DisassociateAssetFromCaseRequest>(
        "/hero.cases.v1.CaseService/DisassociateAssetFromCase",
        data
    );
};

// List Asset Associations For Case
export const listAssetAssociationsForCase = async (
    data: ListAssetAssociationsForCaseRequest
): Promise<ListAssetAssociationsForCaseResponse> => {
    return postRequest<ListAssetAssociationsForCaseResponse, ListAssetAssociationsForCaseRequest>(
        "/hero.cases.v1.CaseService/ListAssetAssociationsForCase",
        data
    );
};

// Watcher Management

// Add Watcher
export const addWatcher = async (
    data: AddWatcherRequest
): Promise<Case> => {
    return postRequest<Case, AddWatcherRequest>(
        "/hero.cases.v1.CaseService/AddWatcher",
        data
    );
};

// Remove Watcher
export const removeWatcher = async (
    data: RemoveWatcherRequest
): Promise<Case> => {
    return postRequest<Case, RemoveWatcherRequest>(
        "/hero.cases.v1.CaseService/RemoveWatcher",
        data
    );
};

// Status and Updates

// Update Case Status
export const updateCaseStatus = async (
    data: UpdateCaseStatusRequest
): Promise<UpdateCaseStatusResponse> => {
    return postRequest<UpdateCaseStatusResponse, UpdateCaseStatusRequest>(
        "/hero.cases.v1.CaseService/UpdateCaseStatus",
        data
    );
};

// Add Case Update
export const addCaseUpdate = async (
    data: AddCaseUpdateRequest
): Promise<Case> => {
    return postRequest<Case, AddCaseUpdateRequest>(
        "/hero.cases.v1.CaseService/AddCaseUpdate",
        data
    );
};

// Remove Case Update
export const removeCaseUpdate = async (
    data: RemoveCaseUpdateRequest
): Promise<Case> => {
    return postRequest<Case, RemoveCaseUpdateRequest>(
        "/hero.cases.v1.CaseService/RemoveCaseUpdate",
        data
    );
};

// List Case Updates
export const listCaseUpdates = async (
    data: ListCaseUpdatesRequest
): Promise<ListCaseUpdatesResponse> => {
    return postRequest<ListCaseUpdatesResponse, ListCaseUpdatesRequest>(
        "/hero.cases.v1.CaseService/ListCaseUpdates",
        data
    );
};

// List Case Status History
export const listCaseStatusHistory = async (
    data: ListCaseStatusHistoryRequest
): Promise<ListCaseStatusHistoryResponse> => {
    return postRequest<ListCaseStatusHistoryResponse, ListCaseStatusHistoryRequest>(
        "/hero.cases.v1.CaseService/ListCaseStatusHistory",
        data
    );
};

// Tags and Metadata

// Add Case Tag
export const addCaseTag = async (
    data: AddCaseTagRequest
): Promise<Case> => {
    return postRequest<Case, AddCaseTagRequest>(
        "/hero.cases.v1.CaseService/AddCaseTag",
        data
    );
};

// Remove Case Tag
export const removeCaseTag = async (
    data: RemoveCaseTagRequest
): Promise<Case> => {
    return postRequest<Case, RemoveCaseTagRequest>(
        "/hero.cases.v1.CaseService/RemoveCaseTag",
        data
    );
};

// Add Additional Info
export const addAdditionalInfo = async (
    data: AddAdditionalInfoRequest
): Promise<AddAdditionalInfoResponse> => {
    return postRequest<AddAdditionalInfoResponse, AddAdditionalInfoRequest>(
        "/hero.cases.v1.CaseService/AddAdditionalInfo",
        data
    );
};

// Audit and Versioning

// Get Case Version
export const getCaseVersion = async (
    data: GetCaseVersionRequest
): Promise<CaseSnapshot> => {
    return postRequest<CaseSnapshot, GetCaseVersionRequest>(
        "/hero.cases.v1.CaseService/GetCaseVersion",
        data
    );
};

// List Case Versions
export const listCaseVersions = async (
    data: ListCaseVersionsRequest
): Promise<ListCaseVersionsResponse> => {
    return postRequest<ListCaseVersionsResponse, ListCaseVersionsRequest>(
        "/hero.cases.v1.CaseService/ListCaseVersions",
        data
    );
};

// List Case Audit Log
export const listCaseAuditLog = async (
    data: ListCaseAuditLogRequest
): Promise<ListCaseAuditLogResponse> => {
    return postRequest<ListCaseAuditLogResponse, ListCaseAuditLogRequest>(
        "/hero.cases.v1.CaseService/ListCaseAuditLog",
        data
    );
};

// Search Cases
export const searchCases = async (
    data: SearchCasesRequest
): Promise<SearchCasesResponse> => {
    return postRequest<SearchCasesResponse, SearchCasesRequest>(
        "/hero.cases.v1.CaseService/SearchCases",
        data
    );
};

// List Case File Attachments
export const listCaseFileAttachments = async (
    data: ListCaseFileAttachmentsRequest
): Promise<ListCaseFileAttachmentsResponse> => {
    return postRequest<ListCaseFileAttachmentsResponse, ListCaseFileAttachmentsRequest>(
        "/hero.cases.v1.CaseService/ListCaseFileAttachments",
        data
    );
}; 