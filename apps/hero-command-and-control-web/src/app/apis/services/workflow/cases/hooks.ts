import {
    useMutation,
    UseMutationOptions,
    useQuery,
    useQueryClient,
    UseQueryOptions
} from "@tanstack/react-query";
import { Empty } from "google-protobuf/google/protobuf/empty_pb";
import { useEffect, useState } from "react";

import {
    AddAdditionalInfoRequest,
    AddAdditionalInfoResponse,
    AddCaseTagRequest,
    AddCaseUpdateRequest,
    AddEntityRefToCaseRequest,
    AddReportToCaseRequest,
    AddSituationToCaseRequest,
    AddWatcherRequest,
    AssociateAssetToCaseRequest,
    AssociateAssetToCaseResponse,
    BatchGetCasesRequest,
    BatchGetCasesResponse,
    Case,
    CaseSnapshot,
    CaseUpdateEntry,
    CreateCaseRequest,
    CreateCaseResponse,
    DeleteCaseRequest,
    DisassociateAssetFromCaseRequest,
    GetCaseRequest,
    GetCaseVersionRequest,
    LinkRelatedCaseRequest,
    ListAssetAssociationsForCaseRequest,
    ListAssetAssociationsForCaseResponse,
    ListCaseAuditLogRequest,
    ListCaseAuditLogResponse,
    ListCaseFileAttachmentsRequest,
    ListCaseFileAttachmentsResponse,
    ListCasesByAssetIdRequest,
    ListCasesByEntityIdRequest,
    ListCasesByReportIdRequest,
    ListCasesBySituationIdRequest,
    ListCasesRequest,
    ListCasesResponse,
    ListCaseStatusHistoryRequest,
    ListCaseStatusHistoryResponse,
    ListCaseUpdatesRequest,
    ListCaseUpdatesResponse,
    ListCaseVersionsRequest,
    ListCaseVersionsResponse,
    RemoveCaseTagRequest,
    RemoveCaseUpdateRequest,
    RemoveEntityRefFromCaseRequest,
    RemoveReportFromCaseRequest,
    RemoveSituationFromCaseRequest,
    RemoveWatcherRequest,
    SearchCasesRequest,
    SearchCasesResponse,
    UnlinkRelatedCaseRequest,
    UpdateAssetAssociationRequest,
    UpdateAssetAssociationResponse,
    UpdateCaseRequest,
    UpdateCaseStatusRequest,
    UpdateCaseStatusResponse
} from "proto/hero/cases/v1/cases_pb";

import {
    addAdditionalInfo,
    addCaseTag,
    addCaseUpdate,
    addEntityRefToCase,
    addReportToCase,
    addSituationToCase,
    addWatcher,
    associateAssetToCase,
    batchGetCases,
    createCase,
    deleteCase,
    disassociateAssetFromCase,
    getCase,
    getCaseVersion,
    linkRelatedCase,
    listAssetAssociationsForCase,
    listCaseAuditLog,
    listCaseFileAttachments,
    listCases,
    listCasesByAssetId,
    listCasesByEntityId,
    listCasesByReportId,
    listCasesBySituationId,
    listCaseStatusHistory,
    listCaseUpdates,
    listCaseVersions,
    removeCaseTag,
    removeCaseUpdate,
    removeEntityRefFromCase,
    removeReportFromCase,
    removeSituationFromCase,
    removeWatcher,
    searchCases,
    unlinkRelatedCase,
    updateAssetAssociation,
    updateCase,
    updateCaseStatus
} from "./endpoints";

// Constants for cache keys
const CASES_QUERY_KEY = "cases";
const CASE_QUERY_KEY = "case";

/**
 * Hook for creating a case.
 * On success, it invalidates the cached cases.
 */
export function useCreateCase(
    options?: UseMutationOptions<CreateCaseResponse, Error, CreateCaseRequest>
) {
    const queryClient = useQueryClient();
    return useMutation({
        mutationFn: (newCase: CreateCaseRequest) => createCase(newCase),
        onSuccess: () => {
            queryClient.invalidateQueries({ queryKey: [CASES_QUERY_KEY] });
        },
        ...options,
    });
}

/**
 * Hook for fetching a single case by ID.
 * Uses the caseId as part of the cache key.
 */
export function useCase(
    caseId: string,
    refetchInterval?: number,
    options?: Omit<UseQueryOptions<Case, Error>, 'queryKey'>
) {
    return useQuery({
        queryKey: [CASE_QUERY_KEY, caseId],
        queryFn: () => getCase({ id: caseId } as GetCaseRequest),
        enabled: !!caseId,
        staleTime: refetchInterval ? refetchInterval - 1 : 5 * 60 * 1000, // 5 minutes
        retry: 2,
        refetchInterval: refetchInterval ? refetchInterval : 5 * 60 * 1000, // 5 minutes
        ...options,
    });
}

/**
 * Hook for updating a case.
 * On success, invalidates both the cases list and the specific case cache.
 */
export function useUpdateCase(
    options?: UseMutationOptions<Case, Error, UpdateCaseRequest>
) {
    const queryClient = useQueryClient();
    return useMutation({
        mutationFn: (caseData: UpdateCaseRequest) => updateCase(caseData),
        onSuccess: (data: Case) => {
            queryClient.invalidateQueries({ queryKey: [CASES_QUERY_KEY] });
            queryClient.invalidateQueries({ queryKey: [CASE_QUERY_KEY, data.id] });
        },
        ...options,
    });
}

/**
 * Hook for deleting a case.
 * On success, invalidates the cases cache.
 */
export function useDeleteCase(
    options?: UseMutationOptions<Empty, Error, DeleteCaseRequest>
) {
    const queryClient = useQueryClient();
    return useMutation({
        mutationFn: (req: DeleteCaseRequest) => deleteCase(req),
        onSuccess: () => {
            queryClient.invalidateQueries({ queryKey: [CASES_QUERY_KEY] });
        },
        ...options,
    });
}

/**
 * Hook for listing cases.
 * The query key includes the request parameters for proper cache segmentation.
 */
export function useListCases(
    params: ListCasesRequest,
    options?: Omit<
        UseQueryOptions<ListCasesResponse, Error, ListCasesResponse>,
        "queryKey" | "queryFn"
    >
) {
    return useQuery({
        queryKey: [CASES_QUERY_KEY, "list", params],
        queryFn: () => listCases(params),
        retry: 2,
        ...options,
    });
}

/**
 * Hook for batch getting cases.
 * The query key includes the ids for proper cache segmentation.
 */
export function useBatchGetCases(
    params: BatchGetCasesRequest,
    options?: Omit<
        UseQueryOptions<BatchGetCasesResponse, Error, BatchGetCasesResponse>,
        "queryKey" | "queryFn"
    >
) {
    return useQuery({
        queryKey: [CASES_QUERY_KEY, "batchGet", params.ids],
        queryFn: () => batchGetCases(params),
        retry: 2,
        ...options,
    });
}

/**
 * Hook for listing cases by situation ID.
 * The query key includes the situation ID for proper cache segmentation.
 */
export function useListCasesBySituationId(
    params: ListCasesBySituationIdRequest,
    options?: Omit<
        UseQueryOptions<ListCasesResponse, Error, ListCasesResponse>,
        "queryKey" | "queryFn"
    >
) {
    return useQuery({
        queryKey: [CASES_QUERY_KEY, "bySituation", params.situationId, params],
        queryFn: () => listCasesBySituationId(params),
        retry: 2,
        ...options,
    });
}

/**
 * Hook for listing cases by report ID.
 * The query key includes the report ID for proper cache segmentation.
 */
export function useListCasesByReportId(
    params: ListCasesByReportIdRequest,
    options?: Omit<
        UseQueryOptions<ListCasesResponse, Error, ListCasesResponse>,
        "queryKey" | "queryFn"
    >
) {
    return useQuery({
        queryKey: [CASES_QUERY_KEY, "byReport", params.reportId, params],
        queryFn: () => listCasesByReportId(params),
        retry: 2,
        ...options,
    });
}

/**
 * Hook for listing cases by asset ID.
 * The query key includes the asset ID for proper cache segmentation.
 */
export function useListCasesByAssetId(
    params: ListCasesByAssetIdRequest,
    options?: Omit<
        UseQueryOptions<ListCasesResponse, Error, ListCasesResponse>,
        "queryKey" | "queryFn"
    >
) {
    return useQuery({
        queryKey: [CASES_QUERY_KEY, "byAsset", params.assetId, params],
        queryFn: () => listCasesByAssetId(params),
        retry: 2,
        ...options,
    });
}

/**
 * Hook for listing cases by entity ID.
 * The query key includes the entity ID for proper cache segmentation.
 */
export function useListCasesByEntityId(
    params: ListCasesByEntityIdRequest,
    options?: Omit<
        UseQueryOptions<ListCasesResponse, Error, ListCasesResponse>,
        "queryKey" | "queryFn"
    >
) {
    return useQuery({
        queryKey: [CASES_QUERY_KEY, "byEntity", params.entityId, params],
        queryFn: () => listCasesByEntityId(params),
        retry: 2,
        ...options,
    });
}

/**
 * Hook for adding a situation to a case.
 * On success, invalidates the specific case cache.
 */
export function useAddSituationToCase(
    options?: UseMutationOptions<Case, Error, AddSituationToCaseRequest>
) {
    const queryClient = useQueryClient();
    return useMutation({
        mutationFn: (req: AddSituationToCaseRequest) => addSituationToCase(req),
        onSuccess: (data: Case) => {
            queryClient.invalidateQueries({ queryKey: [CASE_QUERY_KEY, data.id] });
        },
        ...options,
    });
}

/**
 * Hook for removing a situation from a case.
 * On success, invalidates the specific case cache.
 */
export function useRemoveSituationFromCase(
    options?: UseMutationOptions<Case, Error, RemoveSituationFromCaseRequest>
) {
    const queryClient = useQueryClient();
    return useMutation({
        mutationFn: (req: RemoveSituationFromCaseRequest) => removeSituationFromCase(req),
        onSuccess: (data: Case) => {
            queryClient.invalidateQueries({ queryKey: [CASE_QUERY_KEY, data.id] });
        },
        ...options,
    });
}

/**
 * Hook for adding a report to a case.
 * On success, invalidates the specific case cache.
 */
export function useAddReportToCase(
    options?: UseMutationOptions<Case, Error, AddReportToCaseRequest>
) {
    const queryClient = useQueryClient();
    return useMutation({
        mutationFn: (req: AddReportToCaseRequest) => addReportToCase(req),
        onSuccess: (data: Case) => {
            queryClient.invalidateQueries({ queryKey: [CASE_QUERY_KEY, data.id] });
        },
        ...options,
    });
}

/**
 * Hook for removing a report from a case.
 * On success, invalidates the specific case cache.
 */
export function useRemoveReportFromCase(
    options?: UseMutationOptions<Case, Error, RemoveReportFromCaseRequest>
) {
    const queryClient = useQueryClient();
    return useMutation({
        mutationFn: (req: RemoveReportFromCaseRequest) => removeReportFromCase(req),
        onSuccess: (data: Case) => {
            queryClient.invalidateQueries({ queryKey: [CASE_QUERY_KEY, data.id] });
        },
        ...options,
    });
}

/**
 * Hook for adding an entity reference to a case.
 * On success, invalidates the specific case cache.
 */
export function useAddEntityRefToCase(
    options?: UseMutationOptions<Case, Error, AddEntityRefToCaseRequest>
) {
    const queryClient = useQueryClient();
    return useMutation({
        mutationFn: (req: AddEntityRefToCaseRequest) => addEntityRefToCase(req),
        onSuccess: (data: Case) => {
            queryClient.invalidateQueries({ queryKey: [CASE_QUERY_KEY, data.id] });
        },
        ...options,
    });
}

/**
 * Hook for removing an entity reference from a case.
 * On success, invalidates the specific case cache.
 */
export function useRemoveEntityRefFromCase(
    options?: UseMutationOptions<Case, Error, RemoveEntityRefFromCaseRequest>
) {
    const queryClient = useQueryClient();
    return useMutation({
        mutationFn: (req: RemoveEntityRefFromCaseRequest) => removeEntityRefFromCase(req),
        onSuccess: (data: Case) => {
            queryClient.invalidateQueries({ queryKey: [CASE_QUERY_KEY, data.id] });
        },
        ...options,
    });
}

/**
 * Hook for linking a related case.
 * On success, invalidates the specific case cache.
 */
export function useLinkRelatedCase(
    options?: UseMutationOptions<Case, Error, LinkRelatedCaseRequest>
) {
    const queryClient = useQueryClient();
    return useMutation({
        mutationFn: (req: LinkRelatedCaseRequest) => linkRelatedCase(req),
        onSuccess: (data: Case) => {
            queryClient.invalidateQueries({ queryKey: [CASE_QUERY_KEY, data.id] });
        },
        ...options,
    });
}

/**
 * Hook for unlinking a related case.
 * On success, invalidates the specific case cache.
 */
export function useUnlinkRelatedCase(
    options?: UseMutationOptions<Case, Error, UnlinkRelatedCaseRequest>
) {
    const queryClient = useQueryClient();
    return useMutation({
        mutationFn: (req: UnlinkRelatedCaseRequest) => unlinkRelatedCase(req),
        onSuccess: (data: Case) => {
            queryClient.invalidateQueries({ queryKey: [CASE_QUERY_KEY, data.id] });
        },
        ...options,
    });
}

/**
 * Hook for associating an asset to a case.
 * On success, invalidates the specific case cache.
 */
export function useAssociateAssetToCase(
    options?: UseMutationOptions<AssociateAssetToCaseResponse, Error, AssociateAssetToCaseRequest>
) {
    const queryClient = useQueryClient();
    return useMutation({
        mutationFn: (req: AssociateAssetToCaseRequest) => associateAssetToCase(req),
        onSuccess: (data: AssociateAssetToCaseResponse) => {
            queryClient.invalidateQueries({ queryKey: [CASE_QUERY_KEY, data.association?.caseId] });
        },
        ...options,
    });
}

/**
 * Hook for updating an asset association.
 * On success, invalidates the specific case cache.
 */
export function useUpdateAssetAssociation(
    options?: UseMutationOptions<UpdateAssetAssociationResponse, Error, UpdateAssetAssociationRequest>
) {
    const queryClient = useQueryClient();
    return useMutation({
        mutationFn: (req: UpdateAssetAssociationRequest) => updateAssetAssociation(req),
        onSuccess: (data: UpdateAssetAssociationResponse) => {
            queryClient.invalidateQueries({ queryKey: [CASE_QUERY_KEY, data.association?.caseId] });
        },
        ...options,
    });
}

/**
 * Hook for disassociating an asset from a case.
 * On success, invalidates the specific case cache.
 */
export function useDisassociateAssetFromCase(
    options?: UseMutationOptions<Empty, Error, DisassociateAssetFromCaseRequest>
) {
    const queryClient = useQueryClient();
    return useMutation({
        mutationFn: (req: DisassociateAssetFromCaseRequest) => disassociateAssetFromCase(req),
        onSuccess: (_, variables) => {
            queryClient.invalidateQueries({ queryKey: [CASE_QUERY_KEY, variables.caseId] });
        },
        ...options,
    });
}

/**
 * Hook for listing asset associations for a case.
 * The query key includes the case ID for proper cache segmentation.
 */
export function useListAssetAssociationsForCase(
    params: ListAssetAssociationsForCaseRequest,
    options?: Omit<
        UseQueryOptions<ListAssetAssociationsForCaseResponse, Error, ListAssetAssociationsForCaseResponse>,
        "queryKey" | "queryFn"
    >
) {
    return useQuery({
        queryKey: [CASE_QUERY_KEY, params.caseId, "assetAssociations", params],
        queryFn: () => listAssetAssociationsForCase(params),
        retry: 2,
        ...options,
    });
}

/**
 * Hook for adding a watcher to a case.
 * On success, invalidates the specific case cache.
 */
export function useAddWatcher(
    options?: UseMutationOptions<Case, Error, AddWatcherRequest>
) {
    const queryClient = useQueryClient();
    return useMutation({
        mutationFn: (req: AddWatcherRequest) => addWatcher(req),
        onSuccess: (data: Case) => {
            queryClient.invalidateQueries({ queryKey: [CASE_QUERY_KEY, data.id] });
        },
        ...options,
    });
}

/**
 * Hook for removing a watcher from a case.
 * On success, invalidates the specific case cache.
 */
export function useRemoveWatcher(
    options?: UseMutationOptions<Case, Error, RemoveWatcherRequest>
) {
    const queryClient = useQueryClient();
    return useMutation({
        mutationFn: (req: RemoveWatcherRequest) => removeWatcher(req),
        onSuccess: (data: Case) => {
            queryClient.invalidateQueries({ queryKey: [CASE_QUERY_KEY, data.id] });
        },
        ...options,
    });
}

/**
 * Hook for updating a case status.
 * On success, invalidates both the cases list and the specific case cache.
 */
export function useUpdateCaseStatus(
    options?: UseMutationOptions<UpdateCaseStatusResponse, Error, UpdateCaseStatusRequest>
) {
    const queryClient = useQueryClient();
    return useMutation({
        mutationFn: (req: UpdateCaseStatusRequest) => updateCaseStatus(req),
        onSuccess: (data: UpdateCaseStatusResponse) => {
            queryClient.invalidateQueries({ queryKey: [CASES_QUERY_KEY] });
            queryClient.invalidateQueries({ queryKey: [CASE_QUERY_KEY, data.case?.id] });
        },
        ...options,
    });
}

/**
 * Hook for adding a case update.
 * On success, invalidates the specific case cache.
 */
export function useAddCaseUpdate(
    options?: UseMutationOptions<Case, Error, AddCaseUpdateRequest>
) {
    const queryClient = useQueryClient();
    return useMutation({
        mutationFn: (req: AddCaseUpdateRequest) => addCaseUpdate(req),
        onSuccess: (data: Case) => {
            queryClient.invalidateQueries({ queryKey: [CASE_QUERY_KEY, data.id] });
        },
        ...options,
    });
}

/**
 * Hook for removing a case update.
 * On success, invalidates the specific case cache.
 */
export function useRemoveCaseUpdate(
    options?: UseMutationOptions<Case, Error, RemoveCaseUpdateRequest>
) {
    const queryClient = useQueryClient();
    return useMutation({
        mutationFn: (req: RemoveCaseUpdateRequest) => removeCaseUpdate(req),
        onSuccess: (data: Case) => {
            queryClient.invalidateQueries({ queryKey: [CASE_QUERY_KEY, data.id] });
        },
        ...options,
    });
}

/**
 * Hook for listing case updates with automatic pagination.
 * Fetches all pages of updates with a default page size of 50.
 */
export function useListCaseUpdates(
    params: ListCaseUpdatesRequest,
    options?: Omit<
        UseQueryOptions<ListCaseUpdatesResponse, Error, ListCaseUpdatesResponse>,
        "queryKey" | "queryFn"
    >
) {
    const [allUpdates, setAllUpdates] = useState<CaseUpdateEntry[]>([]);
    const [isLoadingMore, setIsLoadingMore] = useState(false);

    // Set default page size to 50 if not provided
    const updatedParams = {
        ...params,
        pageSize: params.pageSize || 50,
        pageToken: params.pageToken || ""
    };

    // Base query to fetch the first page of updates
    const baseQuery = useQuery<ListCaseUpdatesResponse, Error>({
        queryKey: [CASE_QUERY_KEY, updatedParams.caseId, "updates", updatedParams],
        queryFn: () => listCaseUpdates(updatedParams),
        retry: 2,
        ...options,
    });

    // Effect to fetch all pages of updates when the initial data is loaded
    useEffect(() => {
        if (!baseQuery.data) return;

        let updates: CaseUpdateEntry[] = [...(baseQuery.data.updates || [])];
        let nextToken = baseQuery.data.nextPageToken;

        setAllUpdates(updates);

        if (!nextToken) return;

        const fetchRemainingPages = async () => {
            setIsLoadingMore(true);

            try {
                while (nextToken) {
                    const nextPageParams = {
                        ...updatedParams,
                        pageToken: nextToken
                    };

                    const nextPageData = await listCaseUpdates(nextPageParams);
                    updates = [...updates, ...(nextPageData.updates || [])];
                    nextToken = nextPageData.nextPageToken;

                    setAllUpdates(updates);
                }
            } catch (error) {
                console.error("Error fetching paginated case updates:", error);
            } finally {
                setIsLoadingMore(false);
            }
        };

        fetchRemainingPages();
    }, [baseQuery.data]);

    // Return extended result with all updates
    return {
        ...baseQuery,
        data: baseQuery.data ? {
            ...baseQuery.data,
            updates: allUpdates
        } : undefined,
        isLoading: baseQuery.isLoading || isLoadingMore
    };
}

/**
 * Hook for listing case status history.
 * The query key includes the case ID for proper cache segmentation.
 */
export function useListCaseStatusHistory(
    params: ListCaseStatusHistoryRequest,
    options?: Omit<
        UseQueryOptions<ListCaseStatusHistoryResponse, Error, ListCaseStatusHistoryResponse>,
        "queryKey" | "queryFn"
    >
) {
    return useQuery({
        queryKey: [CASE_QUERY_KEY, params.caseId, "statusHistory", params],
        queryFn: () => listCaseStatusHistory(params),
        retry: 2,
        ...options,
    });
}

/**
 * Hook for listing case file attachments.
 * The query key includes the case ID for proper cache segmentation.
 */
export function useListCaseFileAttachments(
    params: ListCaseFileAttachmentsRequest,
    options?: Omit<
        UseQueryOptions<ListCaseFileAttachmentsResponse, Error, ListCaseFileAttachmentsResponse>,
        "queryKey" | "queryFn"
    >
) {
    return useQuery({
        queryKey: [CASE_QUERY_KEY, params.caseId, "fileAttachments", params],
        queryFn: () => listCaseFileAttachments(params),
        retry: 2,
        ...options,
    });
}

/**
 * Hook for adding a case tag.
 * On success, invalidates the specific case cache.
 */
export function useAddCaseTag(
    options?: UseMutationOptions<Case, Error, AddCaseTagRequest>
) {
    const queryClient = useQueryClient();
    return useMutation({
        mutationFn: (req: AddCaseTagRequest) => addCaseTag(req),
        onSuccess: (data: Case) => {
            queryClient.invalidateQueries({ queryKey: [CASE_QUERY_KEY, data.id] });
        },
        ...options,
    });
}

/**
 * Hook for removing a case tag.
 * On success, invalidates the specific case cache.
 */
export function useRemoveCaseTag(
    options?: UseMutationOptions<Case, Error, RemoveCaseTagRequest>
) {
    const queryClient = useQueryClient();
    return useMutation({
        mutationFn: (req: RemoveCaseTagRequest) => removeCaseTag(req),
        onSuccess: (data: Case) => {
            queryClient.invalidateQueries({ queryKey: [CASE_QUERY_KEY, data.id] });
        },
        ...options,
    });
}

/**
 * Hook for adding additional info to a case.
 * On success, invalidates the specific case cache.
 */
export function useAddAdditionalInfo(
    options?: UseMutationOptions<AddAdditionalInfoResponse, Error, AddAdditionalInfoRequest>
) {
    const queryClient = useQueryClient();
    return useMutation({
        mutationFn: (req: AddAdditionalInfoRequest) => addAdditionalInfo(req),
        onSuccess: (data: AddAdditionalInfoResponse) => {
            queryClient.invalidateQueries({ queryKey: [CASE_QUERY_KEY, data.caseId] });
        },
        ...options,
    });
}

/**
 * Hook for getting a case version.
 * The query key includes the case ID and version for proper cache segmentation.
 */
export function useGetCaseVersion(
    params: GetCaseVersionRequest,
    options?: Omit<
        UseQueryOptions<CaseSnapshot, Error, CaseSnapshot>,
        "queryKey" | "queryFn"
    >
) {
    return useQuery({
        queryKey: [CASE_QUERY_KEY, params.caseId, "version", params.version],
        queryFn: () => getCaseVersion(params),
        retry: 2,
        ...options,
    });
}

/**
 * Hook for listing case versions.
 * The query key includes the case ID for proper cache segmentation.
 */
export function useListCaseVersions(
    params: ListCaseVersionsRequest,
    options?: Omit<
        UseQueryOptions<ListCaseVersionsResponse, Error, ListCaseVersionsResponse>,
        "queryKey" | "queryFn"
    >
) {
    return useQuery({
        queryKey: [CASE_QUERY_KEY, params.caseId, "versions"],
        queryFn: () => listCaseVersions(params),
        retry: 2,
        ...options,
    });
}

/**
 * Hook for listing case audit log.
 * The query key includes the case ID for proper cache segmentation.
 */
export function useListCaseAuditLog(
    params: ListCaseAuditLogRequest,
    options?: Omit<
        UseQueryOptions<ListCaseAuditLogResponse, Error, ListCaseAuditLogResponse>,
        "queryKey" | "queryFn"
    >
) {
    return useQuery({
        queryKey: [CASE_QUERY_KEY, params.caseId, "auditLog", params],
        queryFn: () => listCaseAuditLog(params),
        retry: 2,
        ...options,
    });
}

/**
 * Hook for searching cases with advanced filtering and text search capabilities.
 * Uses the search parameters as part of the cache key for proper cache segmentation.
 */
export function useSearchCases(
    params: SearchCasesRequest,
    options?: Omit<
        UseQueryOptions<SearchCasesResponse, Error, SearchCasesResponse>,
        "queryKey" | "queryFn"
    >
) {
    return useQuery({
        queryKey: [CASES_QUERY_KEY, "search", params],
        queryFn: () => searchCases(params),
        retry: 2,
        ...options,
    });
} 