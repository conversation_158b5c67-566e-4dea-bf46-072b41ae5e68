import {
    CaseAssetAssociationType,
    CaseAuditAction,
    CaseStatus,
    CaseType,
    ReleaseStatus
} from "proto/hero/cases/v1/cases_pb";
import { UpdateSource } from "proto/hero/situations/v2/situations_pb";

// Function to convert a string (e.g. "CASE_STATUS_NEW")
// to the corresponding enum value (e.g. CaseStatus.NEW).
export function stringToCaseStatus(value: string): CaseStatus | undefined {
    const prefix = "CASE_STATUS_";
    if (!value.startsWith(prefix)) {
        return undefined;
    }
    // Remove the prefix to get the key matching the enum property name.
    const enumKey = value.substring(prefix.length) as keyof typeof CaseStatus;
    // Ensure the key exists in the enum.
    if (enumKey in CaseStatus) {
        return CaseStatus[enumKey];
    }
    return undefined;
}

// Function to convert an enum value (e.g. CaseStatus.NEW)
// back to its string representation (e.g. "CASE_STATUS_NEW").
export function caseStatusToString(status: CaseStatus): string {
    // Using reverse mapping: CaseStatus[status] returns the key as a string.
    const key = CaseStatus[status];
    return "CASE_STATUS_" + key;
}

// For crazy reason when you use our hooks to get Case it gives you not the right Enum
// It gives you the string representation of the enum
// You can just string type cast it from CaseStatus
export function hookCaseStatusToString(status: CaseStatus): string {
    return String(status);
}

// Function to convert a string (e.g. "CASE_TYPE_SECURITY_INCIDENT")
// to the corresponding enum value (e.g. CaseType.SECURITY_INCIDENT).
export function stringToCaseType(value: string): CaseType | undefined {
    const prefix = "CASE_TYPE_";
    if (!value.startsWith(prefix)) {
        return undefined;
    }
    // Remove the prefix to get the key matching the enum property name.
    const enumKey = value.substring(prefix.length) as keyof typeof CaseType;
    // Ensure the key exists in the enum.
    if (enumKey in CaseType) {
        return CaseType[enumKey];
    }
    return undefined;
}

// Function to convert an enum value (e.g. CaseType.SECURITY_INCIDENT)
// back to its string representation (e.g. "CASE_TYPE_SECURITY_INCIDENT").
export function caseTypeToString(type: CaseType): string {
    // Using reverse mapping: CaseType[type] returns the key as a string.
    const key = CaseType[type];
    return "CASE_TYPE_" + key;
}

// For crazy reason when you use our hooks to get Case it gives you not the right Enum
// It gives you the string representation of the enum
// You can just string type cast it from CaseType
export function hookCaseTypeToString(type: CaseType): string {
    return String(type);
}

// Function to convert a string (e.g. "ASSET_ASSOCIATION_TYPE_PRIMARY_INVESTIGATOR")
// to the corresponding enum value (e.g. CaseAssetAssociationType.PRIMARY_INVESTIGATOR).
export function stringToCaseAssetAssociationType(value: string): CaseAssetAssociationType | undefined {
    const prefix = "ASSET_ASSOCIATION_TYPE_";
    if (!value.startsWith(prefix)) {
        return undefined;
    }
    // Remove the prefix to get the key matching the enum property name.
    const enumKey = value.substring(prefix.length) as keyof typeof CaseAssetAssociationType;
    // Ensure the key exists in the enum.
    if (enumKey in CaseAssetAssociationType) {
        return CaseAssetAssociationType[enumKey];
    }
    return undefined;
}

// Function to convert an enum value (e.g. CaseAssetAssociationType.PRIMARY_INVESTIGATOR)
// back to its string representation (e.g. "ASSET_ASSOCIATION_TYPE_PRIMARY_INVESTIGATOR").
export function caseAssetAssociationTypeToString(type: CaseAssetAssociationType): string {
    // Using reverse mapping: CaseAssetAssociationType[type] returns the key as a string.
    const key = CaseAssetAssociationType[type];
    return "ASSET_ASSOCIATION_TYPE_" + key;
}

// For crazy reason when you use our hooks to get Case it gives you not the right Enum
// It gives you the string representation of the enum
// You can just string type cast it from CaseAssetAssociationType
export function hookCaseAssetAssociationTypeToString(type: CaseAssetAssociationType): string {
    return String(type);
}

// Function to convert a string (e.g. "CASE_AUDIT_ACTION_CREATE")
// to the corresponding enum value (e.g. CaseAuditAction.CREATE).
export function stringToCaseAuditAction(value: string): CaseAuditAction | undefined {
    const prefix = "CASE_AUDIT_ACTION_";
    if (!value.startsWith(prefix)) {
        return undefined;
    }
    // Remove the prefix to get the key matching the enum property name.
    const enumKey = value.substring(prefix.length) as keyof typeof CaseAuditAction;
    // Ensure the key exists in the enum.
    if (enumKey in CaseAuditAction) {
        return CaseAuditAction[enumKey];
    }
    return undefined;
}

// Function to convert an enum value (e.g. CaseAuditAction.CREATE)
// back to its string representation (e.g. "CASE_AUDIT_ACTION_CREATE").
export function caseAuditActionToString(action: CaseAuditAction): string {
    // Using reverse mapping: CaseAuditAction[action] returns the key as a string.
    const key = CaseAuditAction[action];
    return "CASE_AUDIT_ACTION_" + key;
}

// For crazy reason when you use our hooks to get Case it gives you not the right Enum
// It gives you the string representation of the enum
// You can just string type cast it from CaseAuditAction
export function hookCaseAuditActionToString(action: CaseAuditAction): string {
    return String(action);
}

// Function to convert a string (e.g. "RELEASE_STATUS_PUBLIC")
// to the corresponding enum value (e.g. ReleaseStatus.PUBLIC).
export function stringToReleaseStatus(value: string): ReleaseStatus | undefined {
    const prefix = "RELEASE_STATUS_";
    if (!value.startsWith(prefix)) {
        return undefined;
    }
    // Remove the prefix to get the key matching the enum property name.
    const enumKey = value.substring(prefix.length) as keyof typeof ReleaseStatus;
    // Ensure the key exists in the enum.
    if (enumKey in ReleaseStatus) {
        return ReleaseStatus[enumKey];
    }
    return undefined;
}

// Function to convert an enum value (e.g. ReleaseStatus.PUBLIC)
// back to its string representation (e.g. "RELEASE_STATUS_PUBLIC").
export function releaseStatusToString(status: ReleaseStatus): string {
    // Using reverse mapping: ReleaseStatus[status] returns the key as a string.
    const key = ReleaseStatus[status];
    return "RELEASE_STATUS_" + key;
}

// For crazy reason when you use our hooks to get Case it gives you not the right Enum
// It gives you the string representation of the enum
// You can just string type cast it from ReleaseStatus
export function hookReleaseStatusToString(status: ReleaseStatus): string {
    return String(status);
}

// Re-exporting the UpdateSource converters from the situations implementation
// Function to convert a string (e.g. "UPDATE_SOURCE_THIRD_PARTY_API")
// to the corresponding enum value (e.g. UpdateSource.THIRD_PARTY_API).
export function stringToUpdateSource(value: string): UpdateSource {
    const prefix = "UPDATE_SOURCE_";
    if (!value.startsWith(prefix)) {
        return UpdateSource.UNKNOWN;
    }
    // Remove the prefix to extract the enum key.
    const enumKey = value.substring(prefix.length) as keyof typeof UpdateSource;
    if (enumKey in UpdateSource) {
        return UpdateSource[enumKey];
    }
    return UpdateSource.UNKNOWN;
}

// Function to convert an enum value (e.g. UpdateSource.API_SIDE_EFFECT)
// back to its string representation (e.g. "UPDATE_SOURCE_API_SIDE_EFFECT").
export function updateSourceToString(source: UpdateSource): string {
    // Using reverse mapping: UpdateSource[source] returns the enum key as a string.
    const key = UpdateSource[source];
    return "UPDATE_SOURCE_" + key;
}

// For crazy reason when you use our hooks to get Case it gives you not the right Enum
// It gives you the string representation of the enum
// You can just string type cast it from UpdateSource
export function hookUpdateSourceToString(source: UpdateSource): string {
    return String(source);
}