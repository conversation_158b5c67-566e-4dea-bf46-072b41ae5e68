import {
  useMutation,
  UseMutationOptions,
  useQuery,
  useQueryClient,
  UseQueryOptions,
} from "@tanstack/react-query";
import { Empty } from "google-protobuf/google/protobuf/empty_pb";

import {
  BatchGetLatestEntitiesRequest,
  BatchGetLatestEntitiesResponse,
  CheckEntityPermissionsRequest,
  CheckEntityPermissionsResponse,
  CreateEntityRequest,
  CreateEntityResponse,
  CreateEntitySchemaRequest,
  CreateEntitySchemaResponse,
  DeleteAllVersionsOfEntityRequest,
  DeleteAllVersionsOfEntitySchemaRequest,
  DeleteSpecificVersionOfEntityRequest,
  DeleteSpecificVersionOfEntitySchemaRequest,
  DiffEntityVersionsRequest,
  DiffEntityVersionsResponse,
  Entity,
  EntitySchema,
  GetEntityByVersionRequest,
  GetEntitySchemaByVersionRequest,
  GetLatestActiveEntityRequest,
  GetLatestActiveEntitySchemaRequest,
  GetLatestEntityRequest,
  GetLatestEntitySchemaRequest,
  ListAllVersionsOfEntityRequest,
  ListAllVersionsOfEntityResponse,
  ListLatestEntitiesRequest,
  ListLatestEntitiesResponse,
  ListLatestEntitySchemasRequest,
  ListLatestEntitySchemasResponse,
  RestoreEntityVersionRequest,
  RestoreEntityVersionResponse,
  SearchEntitiesRequest,
  SearchEntitiesResponse,
  UpdateEntityRequest,
  UpdateEntitySchemaRequest
} from "proto/hero/entity/v1/entity_pb";

import {
  batchGetLatestEntities,
  checkEntityPermissions,
  createEntity,
  createEntitySchema,
  deleteAllVersionsOfEntity,
  deleteAllVersionsOfEntitySchema,
  deleteSpecificVersionOfEntity,
  deleteSpecificVersionOfEntitySchema,
  diffEntityVersions,
  getEntityByVersion,
  getEntitySchemaByVersion,
  getLatestActiveEntity,
  getLatestActiveEntitySchema,
  getLatestEntity,
  getLatestEntitySchema,
  listAllVersionsOfEntity,
  listLatestEntities,
  listLatestEntitySchemas,
  restoreEntityVersion,
  searchEntities,
  updateEntity,
  updateEntitySchema
} from "./endpoints";

// Constants for cache keys
const ENTITIES_QUERY_KEY = "entities";
const ENTITY_QUERY_KEY = "entity";
const ENTITY_SCHEMAS_QUERY_KEY = "entitySchemas";
const ENTITY_SCHEMA_QUERY_KEY = "entitySchema";

// Entity Hooks

/**
 * Hook for creating an entity.
 * On success, it invalidates the cached entities.
 */
export function useCreateEntity(
  options?: UseMutationOptions<CreateEntityResponse, Error, CreateEntityRequest>
) {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: (newEntity: CreateEntityRequest) => createEntity(newEntity),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: [ENTITIES_QUERY_KEY] });
    },
    ...options,
  });
}

/**
 * Hook for fetching a single entity by ID.
 * Uses the entityId as part of the cache key.
 */
export function useGetLatestEntity(
  entityId: string,
  refetchInterval?: number,
  options?: UseQueryOptions<Entity, Error>
) {
  return useQuery({
    queryKey: [ENTITY_QUERY_KEY, entityId],
    queryFn: () => getLatestEntity({ id: entityId } as GetLatestEntityRequest),
    enabled: !!entityId,
    staleTime: refetchInterval ? refetchInterval - 1 : 5 * 60 * 1000, // 5 minutes
    retry: 2,
    refetchInterval: refetchInterval ? refetchInterval : 10 * 60 * 1000, // 10 minutes
    ...options,
  });
}

/**
 * Hook for getting entity by version
 */
export function useGetEntityByVersion(
  entityId: string,
  version: number,
  options?: UseQueryOptions<Entity, Error>
) {
  return useQuery({
    queryKey: [ENTITY_QUERY_KEY, entityId, version],
    queryFn: () =>
      getEntityByVersion({
        id: entityId,
        version,
      } as GetEntityByVersionRequest),
    enabled: !!entityId && version > 0,
    ...options,
  });
}

/**
 * Hook for getting latest active entity
 */
export function useGetLatestActiveEntity(
  entityId: string,
  options?: UseQueryOptions<Entity, Error>
) {
  return useQuery({
    queryKey: [ENTITY_QUERY_KEY, entityId, "active"],
    queryFn: () =>
      getLatestActiveEntity({ id: entityId } as GetLatestActiveEntityRequest),
    enabled: !!entityId,
    ...options,
  });
}

/**
 * Hook for fetching multiple entities by their IDs in a single operation.
 * Uses the entityIds array as part of the cache key.
 */
export function useBatchGetLatestEntities(
  entityIds: string[],
  options?: UseQueryOptions<BatchGetLatestEntitiesResponse, Error>
) {
  return useQuery({
    queryKey: [ENTITY_QUERY_KEY, "batch", entityIds],
    queryFn: () => batchGetLatestEntities({ ids: entityIds } as BatchGetLatestEntitiesRequest),
    enabled: !!entityIds && entityIds.length > 0,
    ...options,
  });
}

/**
 * Hook for updating an entity.
 * On success, invalidates both the entities list and the specific entity cache.
 */
export function useUpdateEntity(
  options?: UseMutationOptions<Entity, Error, UpdateEntityRequest>
) {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: (entity: UpdateEntityRequest) => updateEntity(entity),
    onSuccess: (data: Entity) => {
      queryClient.invalidateQueries({ queryKey: [ENTITIES_QUERY_KEY] });
      queryClient.invalidateQueries({ queryKey: [ENTITY_QUERY_KEY, data.id] });
    },
    ...options,
  });
}

/**
 * Hook for listing entities.
 * The query key includes the request parameters for proper cache segmentation.
 */
export function useListLatestEntities(
  params: ListLatestEntitiesRequest,
  options?: UseQueryOptions<ListLatestEntitiesResponse, Error>
) {
  return useQuery({
    queryKey: [ENTITIES_QUERY_KEY, "list", params],
    queryFn: () => listLatestEntities(params),
    staleTime: 2 * 60 * 1000, // 2 minutes
    retry: 2,
    ...options,
  });
}

/**
 * Hook for searching entities with advanced filters and options.
 * The query key includes the search parameters for proper cache segmentation.
 * Results are cached for 1 minute due to search queries being more dynamic.
 */
export function useSearchEntities(
  params: SearchEntitiesRequest,
  options?: UseQueryOptions<SearchEntitiesResponse, Error>
) {
  return useQuery({
    queryKey: [ENTITIES_QUERY_KEY, "search", params],
    queryFn: () => searchEntities(params),
    staleTime: 1 * 60 * 1000, // 1 minute - shorter than list due to search being more dynamic
    retry: 2,
    ...options,
  });
}

/**
 * Hook for listing all versions of an entity.
 */
export function useListAllVersionsOfEntity(
  entityId: string,
  options?: UseQueryOptions<ListAllVersionsOfEntityResponse, Error>
) {
  return useQuery({
    queryKey: [ENTITY_QUERY_KEY, entityId, "versions"],
    queryFn: () =>
      listAllVersionsOfEntity({ entityId } as ListAllVersionsOfEntityRequest),
    enabled: !!entityId,
    ...options,
  });
}

/**
 * Hook for deleting all versions of an entity.
 * On success, invalidates the entities cache.
 */
export function useDeleteAllVersionsOfEntity(
  options?: UseMutationOptions<Empty, Error, DeleteAllVersionsOfEntityRequest>
) {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: (req: DeleteAllVersionsOfEntityRequest) =>
      deleteAllVersionsOfEntity(req),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: [ENTITIES_QUERY_KEY] });
    },
    ...options,
  });
}

/**
 * Hook for deleting a specific version of an entity.
 */
export function useDeleteSpecificVersionOfEntity(
  options?: UseMutationOptions<
    Empty,
    Error,
    DeleteSpecificVersionOfEntityRequest
  >
) {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: (req: DeleteSpecificVersionOfEntityRequest) =>
      deleteSpecificVersionOfEntity(req),
    onSuccess: (_data, variables) => {
      queryClient.invalidateQueries({
        queryKey: [ENTITY_QUERY_KEY, variables.id, "versions"],
      });
      queryClient.invalidateQueries({
        queryKey: [ENTITY_QUERY_KEY, variables.id],
      });
    },
    ...options,
  });
}

/**
 * Hook for restoring an entity version.
 */
export function useRestoreEntityVersion(
  options?: UseMutationOptions<
    RestoreEntityVersionResponse,
    Error,
    RestoreEntityVersionRequest
  >
) {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: (req: RestoreEntityVersionRequest) => restoreEntityVersion(req),
    onSuccess: (data) => {
      if (data.entity) {
        queryClient.invalidateQueries({
          queryKey: [ENTITY_QUERY_KEY, data.entity.id],
        });
        queryClient.invalidateQueries({
          queryKey: [ENTITY_QUERY_KEY, data.entity.id, "versions"],
        });
      }
    },
    ...options,
  });
}

/**
 * Hook for diffing entity versions.
 */
export function useDiffEntityVersions(
  entityId: string,
  version1: number,
  version2: number,
  options?: UseQueryOptions<DiffEntityVersionsResponse, Error>
) {
  return useQuery({
    queryKey: [ENTITY_QUERY_KEY, entityId, "diff", version1, version2],
    queryFn: () =>
      diffEntityVersions({
        id: entityId,
        version1,
        version2,
      } as DiffEntityVersionsRequest),
    enabled: !!entityId && version1 > 0 && version2 > 0,
    ...options,
  });
}

/**
 * Hook for checking entity permissions.
 */
export function useCheckEntityPermissions(
  entityId: string,
  userId: string,
  action: string,
  options?: UseQueryOptions<CheckEntityPermissionsResponse, Error>
) {
  return useQuery({
    queryKey: [ENTITY_QUERY_KEY, entityId, "permissions", userId, action],
    queryFn: () =>
      checkEntityPermissions({
        entityId,
        userId,
        action,
      } as CheckEntityPermissionsRequest),
    enabled: !!entityId && !!userId && !!action,
    ...options,
  });
}

// Entity Schema Hooks

/**
 * Hook for creating an entity schema.
 * On success, it invalidates the cached entity schemas.
 */
export function useCreateEntitySchema(
  options?: UseMutationOptions<
    CreateEntitySchemaResponse,
    Error,
    CreateEntitySchemaRequest
  >
) {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: (newSchema: CreateEntitySchemaRequest) =>
      createEntitySchema(newSchema),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: [ENTITY_SCHEMAS_QUERY_KEY] });
    },
    ...options,
  });
}

/**
 * Hook for fetching a single entity schema by ID.
 * Uses the schemaId as part of the cache key.
 */
export function useGetLatestEntitySchema(
  schemaId: string,
  options?: UseQueryOptions<EntitySchema, Error>
) {
  return useQuery({
    queryKey: [ENTITY_SCHEMA_QUERY_KEY, schemaId],
    queryFn: () =>
      getLatestEntitySchema({ id: schemaId } as GetLatestEntitySchemaRequest),
    enabled: !!schemaId,
    staleTime: 5 * 60 * 1000, // 5 minutes
    retry: 2,
    ...options,
  });
}

/**
 * Hook for getting entity schema by version
 */
export function useGetEntitySchemaByVersion(
  schemaId: string,
  version: number,
  options?: UseQueryOptions<EntitySchema, Error>
) {
  return useQuery({
    queryKey: [ENTITY_SCHEMA_QUERY_KEY, schemaId, version],
    queryFn: () =>
      getEntitySchemaByVersion({
        id: schemaId,
        version,
      } as GetEntitySchemaByVersionRequest),
    enabled: !!schemaId && version > 0,
    ...options,
  });
}

/**
 * Hook for getting latest active entity schema
 */
export function useGetLatestActiveEntitySchema(
  schemaId: string,
  options?: UseQueryOptions<EntitySchema, Error>
) {
  return useQuery({
    queryKey: [ENTITY_SCHEMA_QUERY_KEY, schemaId, "active"],
    queryFn: () =>
      getLatestActiveEntitySchema({
        id: schemaId,
      } as GetLatestActiveEntitySchemaRequest),
    enabled: !!schemaId,
    ...options,
  });
}

/**
 * Hook for updating an entity schema.
 * On success, invalidates both the schemas list and the specific schema cache.
 */
export function useUpdateEntitySchema(
  options?: UseMutationOptions<EntitySchema, Error, UpdateEntitySchemaRequest>
) {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: (schema: UpdateEntitySchemaRequest) =>
      updateEntitySchema(schema),
    onSuccess: (data: EntitySchema) => {
      queryClient.invalidateQueries({ queryKey: [ENTITY_SCHEMAS_QUERY_KEY] });
      queryClient.invalidateQueries({
        queryKey: [ENTITY_SCHEMA_QUERY_KEY, data.id],
      });
    },
    ...options,
  });
}

/**
 * Hook for listing entity schemas.
 * The query key includes the request parameters for proper cache segmentation.
 */
export function useListLatestEntitySchemas(
  params: ListLatestEntitySchemasRequest,
  options?: UseQueryOptions<ListLatestEntitySchemasResponse, Error>
) {
  return useQuery({
    queryKey: [ENTITY_SCHEMAS_QUERY_KEY, "list", params],
    queryFn: () => listLatestEntitySchemas(params),
    staleTime: 2 * 60 * 1000, // 2 minutes
    retry: 2,
    ...options,
  });
}

/**
 * Hook for deleting all versions of an entity schema.
 * On success, invalidates the entity schemas cache.
 */
export function useDeleteAllVersionsOfEntitySchema(
  options?: UseMutationOptions<
    Empty,
    Error,
    DeleteAllVersionsOfEntitySchemaRequest
  >
) {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: (req: DeleteAllVersionsOfEntitySchemaRequest) =>
      deleteAllVersionsOfEntitySchema(req),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: [ENTITY_SCHEMAS_QUERY_KEY] });
    },
    ...options,
  });
}

/**
 * Hook for deleting a specific version of an entity schema.
 */
export function useDeleteSpecificVersionOfEntitySchema(
  options?: UseMutationOptions<
    Empty,
    Error,
    DeleteSpecificVersionOfEntitySchemaRequest
  >
) {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: (req: DeleteSpecificVersionOfEntitySchemaRequest) =>
      deleteSpecificVersionOfEntitySchema(req),
    onSuccess: (_data, variables) => {
      queryClient.invalidateQueries({
        queryKey: [ENTITY_SCHEMA_QUERY_KEY, variables.id],
      });
    },
    ...options,
  });
}
