import { SENSORS_SERVICE_URL } from "../../config";
import { createAuthenticatedAxiosInstance } from '../axiosConfig';

// Only log service URL in development
if (process.env.NODE_ENV === 'development') {
  console.log('Sensors Service URL from env:', SENSORS_SERVICE_URL);
}

// Create sensors axios instance
const sensorsAxiosInstance = createAuthenticatedAxiosInstance(SENSORS_SERVICE_URL || "");

// Only log base URL in development
if (process.env.NODE_ENV === 'development') {
  console.log('Sensors Axios Instance Base URL:', sensorsAxiosInstance.defaults.baseURL);
}

// Add request interceptor with environment-based logging
sensorsAxiosInstance.interceptors.request.use(
  (config) => {
    if (process.env.NODE_ENV === 'development') {
      // Detailed logging for development
      console.log('Sensors Service Request:', {
        baseURL: config.baseURL,
        url: config.url,
        fullUrl: `${config.baseURL}${config.url}`,
        method: config.method,
        headers: config.headers
      });
    } else {
      // Minimal logging for production
      console.log(`Sensors Service Request: ${config.method?.toUpperCase()} ${config.url}`);
    }
    return config;
  },
  (error) => {
    // Always log errors, but with different detail levels
    if (process.env.NODE_ENV === 'development') {
      console.error('Sensors Service Request Error:', error);
    } else {
      console.error('Sensors Service Request Error:', error.message);
    }
    return Promise.reject(error);
  }
);

export default sensorsAxiosInstance;
