import axiosInstance from './axiosInstance';
import {
  Ack,
  Camera,
  CreateCameraRequest,
  CreateStreamRequest,
  CreateStreamResponse,
  GetCameraRequest,
  GetStreamStatusRequest,
  ListCamerasRequest,
  ListCamerasResponse,
  PlayStreamRequest,
  PlayStreamResponse,
  StopStreamRequest,
  StreamStatusResponse,
  LiveFeedRequest,
  LiveFeedResponse,
} from "proto/hero/sensors/v1/sensors_pb";

// Use Connect protocol paths
const sensorServiceUrl = '/hero.sensors.v1.CameraService';
const orchestrationServiceUrl = '/hero.sensors.v1.CameraOrchestrationService';
const cameraFeedServiceUrl = '/hero.sensors.v1.CameraFeedService';

/**
 * Get list of available cameras
 */
export const getCameraList = async (request: ListCamerasRequest): Promise<ListCamerasResponse> => {
  const response = await axiosInstance.post<ListCamerasResponse>(
    `${sensorServiceUrl}/ListCameras`,
    request
  );
  return response.data;
};

/**
 * Get a specific camera by ID
 */
export const getCamera = async (request: GetCameraRequest): Promise<Camera> => {
  const response = await axiosInstance.post<Camera>(
    `${sensorServiceUrl}/GetCamera`,
    request
  );
  return response.data;
};

/**
 * Create a new stream for a camera
 */
export const createStream = async (request: CreateStreamRequest): Promise<CreateStreamResponse> => {
  const response = await axiosInstance.post<CreateStreamResponse>(
    `${orchestrationServiceUrl}/CreateStream`,
    request
  );
  return response.data;
};

/**
 * Play a stream
 */
export const playStream = async (
  data: PlayStreamRequest
): Promise<PlayStreamResponse> => {
  console.log('PlayStream Request:', {
    baseURL: axiosInstance.defaults.baseURL,
    url: `${orchestrationServiceUrl}/PlayStream`,
    fullUrl: `${axiosInstance.defaults.baseURL}${orchestrationServiceUrl}/PlayStream`,
    data
  });
  
  const response = await axiosInstance.post<PlayStreamResponse>(
    `${orchestrationServiceUrl}/PlayStream`,
    data
  );
  return response.data;
};

/**
 * Stop a stream
 */
export const stopStream = async (
  data: StopStreamRequest
): Promise<Ack> => {
  const response = await axiosInstance.post<Ack>(
    `${orchestrationServiceUrl}/StopStream`,
    data
  );
  return response.data;
};

/**
 * Get stream status
 */
export const getStreamStatus = async (request: GetStreamStatusRequest): Promise<StreamStatusResponse> => {
  const response = await axiosInstance.post<StreamStatusResponse>(
    `${orchestrationServiceUrl}/GetStreamStatus`,
    request
  );
  return response.data;
};

/**
 * Create a new camera
 */
export const createCamera = async (request: CreateCameraRequest): Promise<Camera> => {
  const response = await axiosInstance.post<Camera>(
    `${sensorServiceUrl}/CreateCamera`,
    request
  );
  return response.data;
};

/**
 * Get live feed HLS URL for a camera
 */
export const getLiveFeed = async (request: LiveFeedRequest): Promise<LiveFeedResponse> => {
  const response = await axiosInstance.post<LiveFeedResponse>(
    `${cameraFeedServiceUrl}/GetLiveFeed`,
    request
  );
  return response.data;
};
