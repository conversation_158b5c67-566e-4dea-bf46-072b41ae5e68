import { useQuery, useMutation } from '@tanstack/react-query';
import { 
  getCamera,
  getStreamStatus,
  getCameraList,
  playStream,
  stopStream,
  createCamera,
  getLiveFeed
} from './endpoints';
import { 
  ListCamerasRequest,
  GetCameraRequest,
  GetStreamStatusRequest,
  PlayStreamRequest,
  StopStreamRequest,
  CreateCameraRequest,
  LiveFeedRequest,
  LiveFeedResponse
} from 'proto/hero/sensors/v1/sensors_pb';
import { 
  Camera,
  StreamState,
  StreamType
} from 'proto/hero/sensors/v1/sensors_pb';

interface CameraInfo {
  cameraId: string;
  name: string;
  ipAddress: string;
  rtspUrl: string;
  manufacturer: string;
  model: string;
  firmwareVersion: string;
  lastSeen: Date;
  capabilities: string[];
  streamType: StreamType;
  metadata: Record<string, string>;
  assetId: string;
}

interface StreamInfo {
  streamId: string;
  state: StreamState;
  message?: string;
  currentFramerate?: number;
  currentBitrate?: number;
  pipelineStatus?: string;
}

/**
 * Hook to fetch list of available cameras
 */
export const useGetCameraList = (pageSize: number = 10, filter?: string) => {
  return useQuery<CameraInfo[]>({
    queryKey: ['camera-list', pageSize, filter],
    queryFn: async () => {
      const request = {
        pageSize,
        filter
      } as ListCamerasRequest;
      
      const response = await getCameraList(request);
      return response.cameras.map((camera: Camera) => ({
        cameraId: camera.assetId,
        name: camera.name,
        ipAddress: camera.ipAddress,
        rtspUrl: camera.rtspUrl,
        manufacturer: camera.metadata?.manufacturer || '',
        model: camera.metadata?.model || '',
        firmwareVersion: camera.metadata?.firmwareVersion || '',
        lastSeen: new Date(),
        capabilities: [],
        streamType: camera.streamType,
        metadata: camera.metadata,
        assetId: camera.assetId,
      }));
    },
    // Refresh camera list every minute
    refetchInterval: 60 * 1000,
    // Keep camera list fresh for the same duration
    staleTime: 60 * 1000,
  });
};

/**
 * Hook to fetch a specific camera
 */
export const useGetCamera = (cameraId: string) => {
  return useQuery<CameraInfo>({
    queryKey: ['camera', cameraId],
    queryFn: async () => {
      if (!cameraId) {
        throw new Error('Camera ID is required');
      }
      const request = {
        assetId: cameraId
      } as GetCameraRequest;
      
      const camera = await getCamera(request);
      return {
        cameraId: camera.assetId,
        name: camera.name,
        ipAddress: camera.ipAddress,
        rtspUrl: camera.rtspUrl,
        manufacturer: camera.metadata?.manufacturer || '',
        model: camera.metadata?.model || '',
        firmwareVersion: camera.metadata?.firmwareVersion || '',
        lastSeen: new Date(),
        capabilities: [],
        streamType: camera.streamType,
        metadata: camera.metadata,
        assetId: camera.assetId,
      };
    },
    enabled: !!cameraId,
  });
};

/**
 * Hook to play a stream
 */
export const usePlayStream = () => {
  return useMutation({
    mutationFn: async (cameraId: string) => {
      const request = {
        assetId: cameraId,
        $typeName: 'hero.sensors.v1.PlayStreamRequest'
      } as PlayStreamRequest;
      return await playStream(request);
    },
  });
};

/**
 * Hook to stop a stream
 */
export const useStopStream = () => {
  return useMutation({
    mutationFn: async (cameraId: string) => {
      const request = {
        assetId: cameraId
      } as StopStreamRequest;
      return await stopStream(request);
    },
  });
};

/**
 * Hook to get stream status
 */
export const useGetStreamStatus = (cameraId: string) => {
  return useQuery<StreamInfo>({
    queryKey: ['stream-status', cameraId],
    queryFn: async () => {
      const request = {
        assetId: cameraId
      } as GetStreamStatusRequest;
      
      const response = await getStreamStatus(request);
      return {
        streamId: cameraId,
        state: response.state,
        message: response.message,
        currentFramerate: response.currentFramerate,
        currentBitrate: response.currentBitrate,
        pipelineStatus: response.pipelineStatus,
      };
    },
    refetchInterval: 1000,
    staleTime: 1000,
  });
};

/**
 * Hook to create a new camera
 */
export const useCreateCamera = () => {
  return useMutation({
    mutationFn: async (request: CreateCameraRequest) => {
      return await createCamera(request);
    },
  });
};

// Placeholder for old video playback functionality
export const useGetVideoPlayback = () => {
  const { data, ...rest } = usePlayStream();
  
  return {
    data: data ? {
      streamUrl: data.streamEndpoint || '',
      status: 0 // STREAM_STATE_UNKNOWN
    } : undefined,
    ...rest
  };
};

// Placeholder for old event playback functionality
export const useGetEventPlayback = () => {
  const { data, ...rest } = usePlayStream();
  
  return {
    data: data ? {
      streamUrl: data.streamEndpoint || '',
      status: 0 // STREAM_STATE_UNKNOWN
    } : undefined,
    ...rest
  };
};

/**
 * Hook to get live feed HLS URL for a camera
 */
export const useGetLiveFeed = (streamName: string) => {
  return useQuery<LiveFeedResponse>({
    queryKey: ['live-feed', streamName],
    queryFn: async () => {
      if (!streamName) {
        throw new Error('Stream name is required');
      }
      const request: LiveFeedRequest = {
        streamName,
        $typeName: 'hero.sensors.v1.LiveFeedRequest'
      };
      return await getLiveFeed(request);
    },
    enabled: !!streamName,
  });
};
