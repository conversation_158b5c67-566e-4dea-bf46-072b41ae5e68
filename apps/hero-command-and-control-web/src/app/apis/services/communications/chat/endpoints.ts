import axiosInstance from "../axiosInstance";
import {
  GetChatUserTokenRequest,
  GetChatUserTokenResponse,
  GetChatAppTokenRequest,
  GetChatAppTokenResponse,
  CreateGroupChatRequest,
  CreateGroupChatResponse,
  GetGroupChatIdRequest,
  GetGroupChatIdResponse,
} from "proto/hero/communications/v1/conversation_pb";

const CHAT_SERVICE_BASE_URL = "/hero.conversation.v1.ChatService";

/**
 * Get a user-specific Chat token (Agor<PERSON> Chat).
 * @param data - GetChatUserTokenRequest.
 * @returns GetChatUserTokenResponse.
 */
export const getChatUserToken = async (
  data: GetChatUserTokenRequest
): Promise<GetChatUserTokenResponse> => {
  const response = await axiosInstance.post<GetChatUserTokenResponse>(
    `${CHAT_SERVICE_BASE_URL}/GetChatUserToken`,
    data
  );
  return response.data;
};

/**
 * Get an application-level Chat token (Agora Chat).
 * @param data - GetChatAppTokenRequest.
 * @returns GetChatAppTokenResponse.
 */
export const getChatAppToken = async (
  data: GetChatAppTokenRequest
): Promise<GetChatAppTokenResponse> => {
  const response = await axiosInstance.post<GetChatAppTokenResponse>(
    `${CHAT_SERVICE_BASE_URL}/GetChatAppToken`,
    data
  );
  return response.data;
};

/**
 * Create a new group chat channel (Agora Chat).
 * @param data - CreateGroupChatRequest.
 * @returns CreateGroupChatResponse.
 */
export const createGroupChat = async (
  data: CreateGroupChatRequest
): Promise<CreateGroupChatResponse> => {
  const response = await axiosInstance.post<CreateGroupChatResponse>(
    `${CHAT_SERVICE_BASE_URL}/CreateGroupChat`,
    data
  );
  return response.data;
};

/**
 * Fetch group chat IDs given a groupName.
 * @param data - GetGroupChatIdRequest.
 * @returns GetGroupChatIdResponse containing all matching groupIds.
 */
export const getGroupChatId = async (
  data: GetGroupChatIdRequest
): Promise<GetGroupChatIdResponse> => {
  const response = await axiosInstance.post<GetGroupChatIdResponse>(
    `${CHAT_SERVICE_BASE_URL}/GetGroupChatId`,
    data
  );
  return response.data;
};