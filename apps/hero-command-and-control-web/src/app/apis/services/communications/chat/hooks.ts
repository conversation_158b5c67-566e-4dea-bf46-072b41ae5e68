import { useMutation } from "@tanstack/react-query";
import {
  getChatUserToken,
  getChatAppToken,
  createGroupChat,
  getGroupChatId,
} from "./endpoints";

import {
  GetChatUserTokenRequest,
  GetChatUserTokenResponse,
  GetChatAppTokenRequest,
  GetChatAppTokenResponse,
  CreateGroupChatRequest,
  CreateGroupChatResponse,
  GetGroupChatIdResponse,
  GetGroupChatIdRequest,
} from "proto/hero/communications/v1/conversation_pb";
/**
 * Hook to request a user-level chat token (Agora Chat).
 */
export const useGetChatUserToken = () => {
    return useMutation<
        GetChatUserTokenResponse,
        Error,
        GetChatUserTokenRequest
    >({
        mutationFn: (data) => getChatUserToken(data),
        retry: 5,
    });
};

/**
 * Hook to request an application-level chat token (Agora Chat).
 */
export const useGetChatAppToken = () => {
    return useMutation<
        GetChatAppTokenResponse,
        <PERSON>rror,
        GetChatAppTokenRequest
    >({
        mutationFn: (data) => getChatAppToken(data),
        retry: 5,
    });
};

/**
 * Hook to create a new group chat in Agora Chat.
 */
export const useCreateGroupChat = () => {
    return useMutation<
        CreateGroupChatResponse,
        Error,
        CreateGroupChatRequest
    >({
        mutationFn: (data) => createGroupChat(data),
        retry: 5,
    });
};

/**
 * Hook to get matching group chat IDs by group name.
 */
export const useGetGroupChatId = () => {
    return useMutation<GetGroupChatIdResponse, Error, GetGroupChatIdRequest>({
        mutationFn: (data) => getGroupChatId(data),
        retry: 5,
    });
};