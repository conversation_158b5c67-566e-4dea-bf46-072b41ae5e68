import axiosInstance from "../axiosInstance";
import {
  // ==== Cellular Call ====
  GetCellularCallAccessTokenRequest,
  GetCellularCallAccessTokenResponse,
  // ==== Queue Operations ====
  DequeueCallRequest,
  DequeueCallResponse,
  DequeueCallBySidRequest,
  DequeueCallBySidResponse,
  GetQueueStatusRequest,
  GetQueueStatusResponse,
  HoldCallRequest,
  HoldCallResponse,
  ResumeCallRequest,
  ResumeCallResponse,
  GetAssetHeldCallsRequest,
  GetAssetHeldCallsResponse,
  // ==== Call Management ====
  EndCallRequest,
  EndCallResponse,
  RevertSelectiveClaimRequest,
  RevertSelectiveClaimResponse,
  // ==== Situation Integration ====
  GetSituationForCallRequest,
  GetSituationForCallResponse
} from "proto/hero/communications/v1/conversation_pb";

/* --------------------------------------------- *
 *         CellularCallService Endpoints
 * --------------------------------------------- */
const CELLULAR_CALL_BASE_URL = "/hero.conversation.v1.CellularCallService";

/**
 * Generate a Twilio Cellular Access Token for voice calls.
 * @param data - GetCellularCallAccessTokenRequest.
 * @returns GetCellularCallAccessTokenResponse.
 */
export const getCellularCallAccessToken = async (
  data: GetCellularCallAccessTokenRequest
): Promise<GetCellularCallAccessTokenResponse> => {
  const response = await axiosInstance.post<GetCellularCallAccessTokenResponse>(
    `${CELLULAR_CALL_BASE_URL}/GetCellularCallAccessToken`,
    data
  );
  return response.data;
};

/**
 * Connect an asset to the next call in queue.
 * @param data - DequeueCallRequest with asset identity.
 * @returns DequeueCallResponse with call information.
 */
export const dequeueCall = async (
  data: DequeueCallRequest
): Promise<DequeueCallResponse> => {
  const response = await axiosInstance.post<DequeueCallResponse>(
    `${CELLULAR_CALL_BASE_URL}/DequeueCall`,
    data
  );
  return response.data;
};

/**
 * Get current status of the call queue (size, next caller).
 * @param data - GetQueueStatusRequest (empty).
 * @returns GetQueueStatusResponse with queue metrics.
 */
export const getQueueStatus = async (
  data: GetQueueStatusRequest
): Promise<GetQueueStatusResponse> => {
  const response = await axiosInstance.post<GetQueueStatusResponse>(
    `${CELLULAR_CALL_BASE_URL}/GetQueueStatus`,
    data
  );
  return response.data;
};

/**
 * Place an active call on hold.
 * @param data - HoldCallRequest with call SID.
 * @returns HoldCallResponse with success flag.
 */
export const holdCall = async (
  data: HoldCallRequest
): Promise<HoldCallResponse> => {
  const response = await axiosInstance.post<HoldCallResponse>(
    `${CELLULAR_CALL_BASE_URL}/HoldCall`,
    data
  );
  return response.data;
};

/**
 * Resume a call from hold.
 * @param data - ResumeCallRequest with call SID and asset identity.
 * @returns ResumeCallResponse with success flag.
 */
export const resumeCall = async (
  data: ResumeCallRequest
): Promise<ResumeCallResponse> => {
  const response = await axiosInstance.post<ResumeCallResponse>(
    `${CELLULAR_CALL_BASE_URL}/ResumeCall`,
    data
  );
  return response.data;
};

/**
 * Get all calls held by a specific asset.
 * @param data - GetAssetHeldCallsRequest with asset identity.
 * @returns GetAssetHeldCallsResponse with list of held calls.
 */
export const getAssetHeldCalls = async (
  data: GetAssetHeldCallsRequest
): Promise<GetAssetHeldCallsResponse> => {
  const response = await axiosInstance.post<GetAssetHeldCallsResponse>(
    `${CELLULAR_CALL_BASE_URL}/GetAssetHeldCalls`,
    data
  );
  return response.data;
};

/**
 * End an active call.
 * @param data - EndCallRequest with call SID and asset identity.
 * @returns EndCallResponse with success flag.
 */
export const endCall = async (
  data: EndCallRequest
): Promise<EndCallResponse> => {
  const response = await axiosInstance.post<EndCallResponse>(
    `${CELLULAR_CALL_BASE_URL}/EndCall`,
    data
  );
  return response.data;
};


/**
 * Get the situation associated with a call.
 * @param data - GetSituationForCallRequest with call SID.
 * @returns GetSituationForCallResponse with associated situation.
 */
export const getSituationForCall = async (
  data: GetSituationForCallRequest
): Promise<GetSituationForCallResponse> => {
  const response = await axiosInstance.post<GetSituationForCallResponse>(
    `${CELLULAR_CALL_BASE_URL}/GetSituationForCall`,
    data
  );
  return response.data;
};

/**
 * Connect an asset to a specific call by SID.
 * @param data - DequeueCallBySidRequest with asset identity and call SID.
 * @returns DequeueCallBySidResponse with call information.
 */
export const dequeueCallBySid = async (
  data: DequeueCallBySidRequest
): Promise<DequeueCallBySidResponse> => {
  const response = await axiosInstance.post<DequeueCallBySidResponse>(
    `${CELLULAR_CALL_BASE_URL}/DequeueCallBySid`,
    data
  );
  return response.data;
};

/**
 * Revert a call from pending_selective_assignment back to waiting state.
 * @param data - RevertSelectiveClaimRequest with call SID.
 * @returns RevertSelectiveClaimResponse with success flag.
 */
export const revertSelectiveClaim = async (
  data: RevertSelectiveClaimRequest
): Promise<RevertSelectiveClaimResponse> => {
  const response = await axiosInstance.post<RevertSelectiveClaimResponse>(
    `${CELLULAR_CALL_BASE_URL}/RevertSelectiveClaim`,
    data
  );
  return response.data;
};
