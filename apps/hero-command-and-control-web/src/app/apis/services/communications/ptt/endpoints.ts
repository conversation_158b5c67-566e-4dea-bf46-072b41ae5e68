

import axiosInstance from "../axiosInstance";
import {
    GetHistoryMetadataRequest,
    GetHistoryMetadataResponse
} from "proto/hero/communications/v1/conversation_pb";

const PTT_BASE_URL = "/hero.conversation.v1.PTTService";

// Get History Metadata
export const getHistoryMetadata = async (
    data: GetHistoryMetadataRequest
): Promise<GetHistoryMetadataResponse> => {
    const response = await axiosInstance.post<GetHistoryMetadataResponse>(
        `${PTT_BASE_URL}/GetHistoryMetadata`,
        data
    );
    return response.data;
};
