import {
    useQuery,
} from "@tanstack/react-query";
import {
    GetHistoryMetadataRequest
} from "proto/hero/communications/v1/conversation_pb";

import {
    getHistoryMetadata
} from "./endpoints";

// Get History Metadata Hook
export const useGetHistoryMetadata = (data: GetHistoryMetadataRequest, refetchInterval?: number) =>
    useQuery({
      queryKey: ["getHistoryMetadata", data],
      queryFn: () => getHistoryMetadata(data),
      refetchInterval: refetchInterval,
      enabled: <PERSON><PERSON><PERSON>(data)
    });
  