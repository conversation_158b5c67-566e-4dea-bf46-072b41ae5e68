import { useMutation } from "@tanstack/react-query";
import {
  getVideoCallAccessToken,
} from "./endpoints";

import {
  GetVideoCallAccessTokenRequest,
  GetVideoCallAccessTokenResponse,
} from "proto/hero/communications/v1/conversation_pb";

/**
 * 1) Use this hook to request an Agora Video Access Token.
 */
export const useGetVideoCallAccessToken = () => {
  return useMutation<
    GetVideoCallAccessTokenResponse,
    unknown, 
    GetVideoCallAccessTokenRequest
  >({
    mutationFn: (data) => getVideoCallAccessToken(data),
    retry: 5,
  });
};
