import axiosInstance from "../axiosInstance";
import {
  // ==== Video Call ====
  GetVideoCallAccessTokenRequest,
  GetVideoCallAccessTokenResponse,
} from "proto/hero/communications/v1/conversation_pb";

/* --------------------------------------------- *
 *         VideoCallService Endpoints
 * --------------------------------------------- */
const VIDEO_CALL_BASE_URL = "/hero.conversation.v1.VideoCallService";

/**
 * Generate an Agora Video Access Token.
 * @param data - GetVideoCallAccessTokenRequest.
 * @returns GetVideoCallAccessTokenResponse.
 */
export const getVideoCallAccessToken = async (
  data: GetVideoCallAccessTokenRequest
): Promise<GetVideoCallAccessTokenResponse> => {
  const response = await axiosInstance.post<GetVideoCallAccessTokenResponse>(
    `${VIDEO_CALL_BASE_URL}/GetVideoCallAccessToken`,
    data
  );
  return response.data;
};
