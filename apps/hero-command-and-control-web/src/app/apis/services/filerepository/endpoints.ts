import axios, { AxiosResponse } from "axios";
import axiosInstance from "./axiosInstance";

import {
    PresignedUploadRequest,
    PresignedUploadResponse,
    PresignedDownloadRequest,
    PresignedDownloadResponse,
    GetFileMetadataRequest,
    GetFileMetadataResponse,
    SearchFilesRequest,
    SearchFilesResponse,
    ListFilesRequest,
    ListFilesResponse,
    UpdateFileMetadataRequest,
    UpdateFileMetadataResponse,
    ConfirmUploadRequest,
    ConfirmUploadResponse,
    CleanupPendingUploadsRequest,
    CleanupPendingUploadsResponse,
    DeleteFileRequest,
    DeleteFileResponse,
    UndeleteFileRequest,
    UndeleteFileResponse,
    PurgeFileRequest,
    PurgeFileResponse,
    GetAccessLogRequest,
    GetAccessLogResponse,
    RecordAccessRequest,
    RecordAccessResponse,
} from "proto/hero/filerepository/v1/filerepository_pb";

// Custom APIError class to include HTTP status codes
class APIError extends Error {
    public statusCode: number;
    constructor(statusCode: number, message: string) {
        super(message);
        this.statusCode = statusCode;
        Object.setPrototypeOf(this, APIError.prototype);
    }
}

// Generic helper function for POST requests with two type parameters:
// T for the response type and D for the request data type.
const postRequest = async <T, D>(url: string, data: D): Promise<T> => {
    try {
        const response: AxiosResponse<T> = await axiosInstance.post<T, AxiosResponse<T>, D>(
            url,
            data
        );
        return response.data;
    } catch (error: unknown) {
        if (axios.isAxiosError(error)) {
            const status = error.response?.status ?? 500;
            const errorMsg = error.response?.data?.message ?? error.message;
            throw new APIError(status, `Request to ${url} failed: ${errorMsg}`);
        }
        throw error;
    }
};

// URL Operations

// Get Presigned Upload URL
export const getPresignedUploadUrl = async (
    data: PresignedUploadRequest
): Promise<PresignedUploadResponse> => {
    return postRequest<PresignedUploadResponse, PresignedUploadRequest>(
        "/hero.filerepository.v1.FileRepositoryService/GetPresignedUploadUrl",
        data
    );
};

// Get Presigned Download URL
export const getPresignedDownloadUrl = async (
    data: PresignedDownloadRequest
): Promise<PresignedDownloadResponse> => {
    return postRequest<PresignedDownloadResponse, PresignedDownloadRequest>(
        "/hero.filerepository.v1.FileRepositoryService/GetPresignedDownloadUrl",
        data
    );
};

// Metadata Operations

// Get File Metadata
export const getFileMetadata = async (
    data: GetFileMetadataRequest
): Promise<GetFileMetadataResponse> => {
    return postRequest<GetFileMetadataResponse, GetFileMetadataRequest>(
        "/hero.filerepository.v1.FileRepositoryService/GetFileMetadata",
        data
    );
};

// Update File Metadata
export const updateFileMetadata = async (
    data: UpdateFileMetadataRequest
): Promise<UpdateFileMetadataResponse> => {
    return postRequest<UpdateFileMetadataResponse, UpdateFileMetadataRequest>(
        "/hero.filerepository.v1.FileRepositoryService/UpdateFileMetadata",
        data
    );
};

// Search and List Operations

// Search Files
export const searchFiles = async (
    data: SearchFilesRequest
): Promise<SearchFilesResponse> => {
    return postRequest<SearchFilesResponse, SearchFilesRequest>(
        "/hero.filerepository.v1.FileRepositoryService/SearchFiles",
        data
    );
};

// List Files
export const listFiles = async (
    data: ListFilesRequest
): Promise<ListFilesResponse> => {
    return postRequest<ListFilesResponse, ListFilesRequest>(
        "/hero.filerepository.v1.FileRepositoryService/ListFiles",
        data
    );
};

// Upload Management

// Confirm Upload
export const confirmUpload = async (
    data: ConfirmUploadRequest
): Promise<ConfirmUploadResponse> => {
    return postRequest<ConfirmUploadResponse, ConfirmUploadRequest>(
        "/hero.filerepository.v1.FileRepositoryService/ConfirmUpload",
        data
    );
};

// Cleanup Pending Uploads
export const cleanupPendingUploads = async (
    data: CleanupPendingUploadsRequest
): Promise<CleanupPendingUploadsResponse> => {
    return postRequest<CleanupPendingUploadsResponse, CleanupPendingUploadsRequest>(
        "/hero.filerepository.v1.FileRepositoryService/CleanupPendingUploads",
        data
    );
};

// File Lifecycle Management

// Delete File (Soft Delete)
export const deleteFile = async (
    data: DeleteFileRequest
): Promise<DeleteFileResponse> => {
    return postRequest<DeleteFileResponse, DeleteFileRequest>(
        "/hero.filerepository.v1.FileRepositoryService/DeleteFile",
        data
    );
};

// Undelete File
export const undeleteFile = async (
    data: UndeleteFileRequest
): Promise<UndeleteFileResponse> => {
    return postRequest<UndeleteFileResponse, UndeleteFileRequest>(
        "/hero.filerepository.v1.FileRepositoryService/UndeleteFile",
        data
    );
};

// Purge File (Hard Delete)
export const purgeFile = async (
    data: PurgeFileRequest
): Promise<PurgeFileResponse> => {
    return postRequest<PurgeFileResponse, PurgeFileRequest>(
        "/hero.filerepository.v1.FileRepositoryService/PurgeFile",
        data
    );
};

// Access Logging

// Get Access Log
export const getAccessLog = async (
    data: GetAccessLogRequest
): Promise<GetAccessLogResponse> => {
    return postRequest<GetAccessLogResponse, GetAccessLogRequest>(
        "/hero.filerepository.v1.FileRepositoryService/GetAccessLog",
        data
    );
};

// Record Access
export const recordAccess = async (
    data: RecordAccessRequest
): Promise<RecordAccessResponse> => {
    return postRequest<RecordAccessResponse, RecordAccessRequest>(
        "/hero.filerepository.v1.FileRepositoryService/RecordAccess",
        data
    );
}; 