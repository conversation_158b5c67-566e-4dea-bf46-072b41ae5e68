import { 
    FileStatus, 
    StorageClass, 
    StorageP<PERSON>ider, 
    SortField,
    SortOrder
} from "proto/hero/filerepository/v1/filerepository_pb";

// FileStatus enum converters
export function stringToFileStatus(value: string): FileStatus | undefined {
    const prefix = "FILE_STATUS_";
    if (!value.startsWith(prefix)) {
        return undefined;
    }
    const enumKey = value.substring(prefix.length) as keyof typeof FileStatus;
    if (enumKey in FileStatus) {
        return FileStatus[enumKey];
    }
    return undefined;
}

export function fileStatusToString(status: FileStatus): string {
    const key = FileStatus[status];
    return "FILE_STATUS_" + key;
}

export function hookFileStatusToString(status: FileStatus): string {
    return String(status);
}

// StorageClass enum converters
export function stringToStorageClass(value: string): StorageClass | undefined {
    const prefix = "STORAGE_CLASS_";
    if (!value.startsWith(prefix)) {
        return undefined;
    }
    const enumKey = value.substring(prefix.length) as keyof typeof StorageClass;
    if (enumKey in StorageClass) {
        return StorageClass[enumKey];
    }
    return undefined;
}

export function storageClassToString(storageClass: StorageClass): string {
    const key = StorageClass[storageClass];
    return "STORAGE_CLASS_" + key;
}

export function hookStorageClassToString(storageClass: StorageClass): string {
    return String(storageClass);
}

// StorageProvider enum converters
export function stringToStorageProvider(value: string): StorageProvider | undefined {
    const prefix = "STORAGE_PROVIDER_";
    if (!value.startsWith(prefix)) {
        return undefined;
    }
    const enumKey = value.substring(prefix.length) as keyof typeof StorageProvider;
    if (enumKey in StorageProvider) {
        return StorageProvider[enumKey];
    }
    return undefined;
}

export function storageProviderToString(provider: StorageProvider): string {
    const key = StorageProvider[provider];
    return "STORAGE_PROVIDER_" + key;
}

export function hookStorageProviderToString(provider: StorageProvider): string {
    return String(provider);
}

// SortField enum converters
export function stringToSortField(value: string): SortField | undefined {
    const prefix = "SORT_FIELD_";
    if (!value.startsWith(prefix)) {
        return undefined;
    }
    const enumKey = value.substring(prefix.length) as keyof typeof SortField;
    if (enumKey in SortField) {
        return SortField[enumKey];
    }
    return undefined;
}

export function sortFieldToString(field: SortField): string {
    const key = SortField[field];
    return "SORT_FIELD_" + key;
}

export function hookSortFieldToString(field: SortField): string {
    return String(field);
}

// SortOrder enum converters
export function stringToSortOrder(value: string): SortOrder | undefined {
    const prefix = "SORT_ORDER_";
    if (!value.startsWith(prefix)) {
        return undefined;
    }
    const enumKey = value.substring(prefix.length) as keyof typeof SortOrder;
    if (enumKey in SortOrder) {
        return SortOrder[enumKey];
    }
    return undefined;
}

export function sortOrderToString(order: SortOrder): string {
    const key = SortOrder[order];
    return "SORT_ORDER_" + key;
}

export function hookSortOrderToString(order: SortOrder): string {
    return String(order);
} 