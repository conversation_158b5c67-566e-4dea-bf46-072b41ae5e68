import axios, { AxiosResponse } from "axios";
import orgsAxiosInstance from "./axiosInstance";

import {
    AddToContactBookRequest,
    AddToContactBookResponse,
    DeleteFromContactBookRequest,
    DeleteFromContactBookResponse,
    GetContactByPhoneNumberRequest,
    GetContactByPhoneNumberResponse,
    GetContactFromContactBookRequest,
    GetContactFromContactBookResponse,
    ListContactsInContactBookRequest,
    ListContactsInContactBookResponse,
    UpdateContactInContactBookRequest,
    UpdateContactInContactBookResponse,
} from "proto/hero/orgs/v1/orgs_pb";

// Custom API Error class to include HTTP status codes
class APIError extends Error {
    public statusCode: number;
    constructor(statusCode: number, message: string) {
        super(message);
        this.statusCode = statusCode;
        Object.setPrototypeOf(this, APIError.prototype);
    }
}

// Generic helper function for POST requests with two type parameters:
// T for the response type and D for the request data type.
const postRequest = async <T, D>(url: string, data: D): Promise<T> => {
    try {
        const response: AxiosResponse<T> = await orgsAxiosInstance.post<T, AxiosResponse<T>, D>(
            url,
            data
        );
        return response.data;
    } catch (error: unknown) {
        if (axios.isAxiosError(error)) {
            const statusCode = error.response?.status || 500;
            const message = error.response?.data?.message || error.message || "Unknown error";
            throw new APIError(statusCode, message);
        } else {
            throw new APIError(500, "An unexpected error occurred");
        }
    }
};

/**
 * Lists all contacts in an organization's contact book
 */
export const listContactsInContactBook = (request: ListContactsInContactBookRequest): Promise<ListContactsInContactBookResponse> =>
    postRequest<ListContactsInContactBookResponse, ListContactsInContactBookRequest>(
        "/hero.orgs.v1.OrgsService/ListContactsInContactBook",
        request
    );

/**
 * Gets a contact by phone number from the organization's contact book
 */
export const getContactByPhoneNumber = (request: GetContactByPhoneNumberRequest): Promise<GetContactByPhoneNumberResponse> =>
    postRequest<GetContactByPhoneNumberResponse, GetContactByPhoneNumberRequest>(
        "/hero.orgs.v1.OrgsService/GetContactByPhoneNumber",
        request
    );

/**
 * Adds a new contact to the organization's contact book
 */
export const addToContactBook = (request: AddToContactBookRequest): Promise<AddToContactBookResponse> =>
    postRequest<AddToContactBookResponse, AddToContactBookRequest>(
        "/hero.orgs.v1.OrgsService/AddToContactBook",
        request
    );

/**
 * Updates an existing contact in the organization's contact book
 */
export const updateContactInContactBook = (request: UpdateContactInContactBookRequest): Promise<UpdateContactInContactBookResponse> =>
    postRequest<UpdateContactInContactBookResponse, UpdateContactInContactBookRequest>(
        "/hero.orgs.v1.OrgsService/UpdateContactInContactBook",
        request
    );

/**
 * Deletes a contact from the organization's contact book
 */
export const deleteFromContactBook = (request: DeleteFromContactBookRequest): Promise<DeleteFromContactBookResponse> =>
    postRequest<DeleteFromContactBookResponse, DeleteFromContactBookRequest>(
        "/hero.orgs.v1.OrgsService/DeleteFromContactBook",
        request
    );

/**
 * Gets a specific contact from the organization's contact book by ID
 */
export const getContactFromContactBook = (request: GetContactFromContactBookRequest): Promise<GetContactFromContactBookResponse> =>
    postRequest<GetContactFromContactBookResponse, GetContactFromContactBookRequest>(
        "/hero.orgs.v1.OrgsService/GetContactFromContactBook",
        request
    );
