import axiosInstance from "./axiosInstance"; 
import {
  GetStateRequest,
  GetStateResponse,
  SetStateRequest,
  SetStateResponse,
} from "proto/hero/command/v1/command_pb";

// Get State
export const getState = async (data: GetStateRequest): Promise<GetStateResponse> => {
const response = await axiosInstance.post<GetStateResponse>(
  "/hero.command.v1.CommandService/GetState",
  data
);
return response.data;
};

// Set State
export const setState = async (data: SetStateRequest): Promise<SetStateResponse> => {
const response = await axiosInstance.post<SetStateResponse>(
  "/hero.command.v1.CommandService/SetState",
  data
);
return response.data;
};