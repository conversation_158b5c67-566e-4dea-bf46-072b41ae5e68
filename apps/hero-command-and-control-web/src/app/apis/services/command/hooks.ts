import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { getState, setState } from "./endpoints";
import { GetStateRequest, SetStateRequest } from "proto/hero/command/v1/command_pb";

// Get State Hook
export const useGetState = (data: GetStateRequest) =>
  useQuery({
    queryKey: ["getState", data],
    queryFn: () => getState(data)
  });

// Set State Hook
export const useSetState = () => {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: (data: SetStateRequest) => setState(data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["getState"] });
    },
  });
};
