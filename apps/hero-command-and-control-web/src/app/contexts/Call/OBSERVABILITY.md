# Sentry Observability for CallContext

This document outlines the comprehensive observability implementation for the CallContext component using Sentry and the `useCallMetrics` hook.

## Overview

The CallContext includes comprehensive monitoring for:
- Call operation success/failure rates with asset-based tracking
- Device health and initialization
- State consistency validation with auto-healing detection
- Performance metrics with automatic slow operation alerts
- Error tracking with rich business context

## Span Naming Convention

All spans follow the `calls.category.operation` pattern for consistent querying:
- **Call Operations**: `calls.operation.dequeue_specific`, `calls.operation.outbound_call`
- **Device Health**: `calls.device.initialization`, `calls.device.microphone_access`
- **State Management**: `calls.state.validation`, `calls.state.missing_agent_leg`
- **Performance**: `calls.performance.dequeue_specific_call`, `calls.performance.call_connection`

## Metrics Implemented

### Call Operations (Asset-Centric Tracking)

**Inbound Calls:**
- `calls.operation.dequeue_specific` - Complete dequeue flow with success/failure
- `calls.operation.backend_claim` - Backend API claim operation
- `calls.operation.call_connection` - Twilio connection establishment

**Outbound Calls:**
- `calls.operation.outbound_call` - Complete outbound call flow
- `calls.operation.outbound_setup` - Call setup and validation
- `calls.operation.twilio_connect` - Twilio connection for outbound

**Call Management:**
- `calls.operation.hold_call` - Hold operation with duration tracking
- `calls.operation.resume_call` - Resume operation with state validation
- `calls.operation.end_call` - End call operation
- `calls.operation.end_held_call` - End held call operation

**Tags Set for All Call Operations:**
```typescript
attributes: {
  success: "true|false",
  assetId: dispatcherAssetId,
  errorType?: "connection_timeout|validation_failed|twilio_error",
  duration?: operationDurationMs
}
```

### Device Health
- `calls.device.initialization` - Device and Twilio SDK initialization
- `calls.device.microphone_access` - Microphone permission requests
- `calls.device.status_change` - Device status transitions
- `calls.device.token_refresh` - Token expiration and refresh
- `calls.device.error` - Device-level errors

**Tags Set for Device Operations:**
```typescript
attributes: {
  success: "true|false",
  deviceStatus: "ready|not_ready|error",
  assetId: dispatcherAssetId,
  microphoneAccess?: "true|false",
  retryCount?: retryAttempts
}
```

### Performance Metrics
- `calls.performance.dequeue_specific_call` - Total dequeue flow (threshold: 5s)
- `calls.performance.call_connection` - Connection establishment (threshold: 3s)
- `calls.performance.device_initialization` - Device setup (threshold: 2s)
- `calls.performance.backend_api` - Backend API response times

### State Consistency
- `calls.state.missing_agent_leg` - Missing agent leg references detected
- `calls.state.orphaned_reference` - Orphaned call references
- `calls.state.device_call_mismatch` - Device/call state inconsistencies
- `calls.state.validation` - General state validation operations

**Tags Set for State Operations:**
```typescript
attributes: {
  operation: "validation|cleanup|auto_fix",
  autoFixed: "true|false",
  assetId: dispatcherAssetId,
  expectedState?: "ready|busy|error",
  actualState?: currentState
}
```

## Error Categories

All errors are tagged with specific categories for easy filtering:
- `category: call-operation` - Call-related operations with asset context
- `category: device-health` - Device initialization and status tracking
- `category: state-consistency` - State validation issues with auto-healing info
- `category: performance` - Slow operations exceeding thresholds

## Business Context (Asset-Centric)

All metrics include dispatcher asset context:
- **Primary Identifier**: `assetId` (dispatcher asset ID)
- **Call Correlation**: `callSid` for cross-service trace correlation
- **Operation Context**: Business operation type and stage
- **Error Classification**: Structured error types for pattern analysis

## Alert Configuration

### Critical Alerts (Immediate notification)
1. **Device Initialization Failures**
   - Query: `calls.device.initialization success:false`
   - Threshold: Any occurrence
   - Reason: Dispatcher cannot take calls

2. **Call Success Rate Drop**
   - Query: `calls.operation.dequeue_specific success:false OR calls.operation.outbound_call success:false`
   - Threshold: Success rate < 95% over 5 minutes
   - Reason: Core functionality degradation

3. **State Consistency Issues**
   - Query: `category:state-consistency autoFixed:false`
   - Threshold: Any occurrence
   - Reason: Potential memory leaks or state corruption

### Warning Alerts (15-minute delay)
1. **Slow Performance**
   - Query: `calls.performance.dequeue_specific_call duration:>5000 OR calls.performance.call_connection duration:>3000`
   - Threshold: P95 > thresholds
   - Reason: Poor user experience

2. **Connection Timeouts**
   - Query: `calls.operation.* errorType:connection_timeout`
   - Threshold: Rate > 1%
   - Reason: Infrastructure issues

3. **Outbound Call Failures**
   - Query: `calls.operation.outbound_call success:false`
   - Threshold: Rate > 5%
   - Reason: Twilio connectivity or phone number issues

4. **Microphone Access Issues**
   - Query: `calls.device.microphone_access microphoneAccess:false`
   - Threshold: Rate > 5%
   - Reason: Browser/permission issues

## Dashboard Setup

### Call Operations Dashboard
```
Key Metrics:
- Overall call success rate (inbound + outbound) (%)
- Average connection time (ms)
- Inbound vs Outbound call volume
- Number of active dispatchers
- Recent failures (last hour)

Charts:
- Call success rate over time (split by inbound/outbound)
- Connection time distribution
- Failure breakdown by error type and call direction
- Device health status by dispatcher
- Outbound call success rate vs phone number format issues
```

### Device Health Dashboard
```
Key Metrics:
- Device initialization success rate
- Microphone access success rate
- Token refresh health
- Device status distribution

Charts:
- Device status over time
- Initialization failure reasons
- Browser/OS breakdown for issues
```

### Performance Dashboard
```
Key Metrics:
- Average operation times
- P95/P99 latencies
- Slow operation alerts
- Backend API performance

Charts:
- Performance trends
- Operation latency distribution
- Backend response time trends
```

## Error Context

Each error includes rich business context:
- **Asset ID**: Dispatcher identifier for correlation
- **Call SID**: For cross-service trace correlation
- **Operation Type**: Specific business operation failing
- **Device Status**: Device state at error time
- **Performance Data**: Operation timing and thresholds
- **State Information**: Expected vs actual states for debugging
- **Breadcrumbs**: Previous actions for reproducing issues

## Usage in Production

### Monitoring Call Health
1. Check the Call Operations Dashboard for success rates
2. Monitor device initialization failures
3. Watch for state consistency alerts

### Debugging Issues
1. Use breadcrumbs to trace user actions
2. Filter errors by category and dispatcher
3. Check performance metrics for slow operations

### Performance Optimization
1. Monitor P95 latencies for operations
2. Identify slow backend API calls
3. Track connection timeout patterns

## Custom Queries

### High-Impact Errors
```
category:call-operation OR category:device-health OR category:state-consistency
```

### Call Operation Failures
```
calls.operation.* success:false
```

### Outbound vs Inbound Call Analysis
```
calls.operation.outbound_call OR calls.operation.dequeue_specific
```

### Performance Issues
```
calls.performance.* duration:>5000
```

### State Inconsistencies
```
calls.state.* autoFixed:false
```

### Device Health Issues
```
calls.device.* success:false
```

### Asset-Specific Issues
```
assetId:YOUR_ASSET_ID category:call-operation
```

### Cross-Service Correlation
```
callSid:CALL_SID_123 # Traces across frontend and backend
```

## Development vs Production

The observability system follows environment-aware patterns:
- **Production**: 10% sampling rate with full Sentry instrumentation
- **Development**: Console logging with same structure when `NEXT_PUBLIC_ENABLE_SENTRY=true`
- **Local Testing**: Enable via `.env.local` to test observability features

## Benefits

1. **Proactive Issue Detection**: Know about problems before users report them
2. **Faster Debugging**: Rich context reduces time to resolution
3. **Performance Baselines**: Establish benchmarks for optimization
4. **Operational Confidence**: Data-driven insights into system health
5. **Launch Readiness**: Comprehensive monitoring for 2-week launch

## Next Steps

1. Configure Sentry project alerts based on the metrics above
2. Set up dashboards for different stakeholder needs
3. Establish on-call procedures for critical alerts
4. Review and tune alert thresholds based on initial data
5. Consider adding user journey tracking for complex flows