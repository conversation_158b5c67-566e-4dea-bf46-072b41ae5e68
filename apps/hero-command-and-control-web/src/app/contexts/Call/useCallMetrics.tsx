"use client";

import * as Sentry from '@sentry/nextjs';
import { useCallback } from 'react';
import { useDispatcher } from '../User/DispatcherContext';

// Types for structured metrics
export interface CallMetadata {
  callSid: string;
  assetId?: string;
  operation: string;
  duration?: number;
  stage?: string;
  errorType?: string;
  errorMessage?: string;
  deviceStatus?: string;
  timestamp?: number;
}

export interface DeviceMetadata {
  assetId?: string;
  deviceStatus: string;
  operation: string;
  duration?: number;
  error?: string;
  retryCount?: number;
  microphoneAccess?: boolean;
  tokenExpiry?: number;
}

export interface StateMetadata {
  operation: string;
  callSid?: string;
  assetId?: string;
  expectedState?: string;
  actualState?: string;
  inconsistencyType?: string;
  autoFixed?: boolean;
}

/**
 * Custom hook for comprehensive call operations monitoring with Sentry
 * Provides structured logging, metrics, and error tracking for the CallContext
 */
export const useCallMetrics = () => {
  const { asset: dispatcherAsset } = useDispatcher();
  const assetId = dispatcherAsset?.id;

  /**
   * Track call operation success/failure with detailed context
   */
  const trackCallOperation = useCallback((metadata: CallMetadata) => {
    const enrichedMetadata = {
      ...metadata,
      assetId: metadata.assetId || assetId,
      timestamp: metadata.timestamp || Date.now(),
    };

    const operation = metadata.operation;
    const success = !metadata.errorType;
    
    Sentry.startSpan({ 
      name: `calls.operation.${operation}`,
      op: 'call-operation',
      attributes: {
        success: success.toString(),
        assetId: enrichedMetadata.assetId || 'unknown',
        ...(metadata.errorType && { errorType: metadata.errorType }),
        ...(metadata.duration && { duration: metadata.duration.toString() })
      }
    }, () => {
      // Span completes immediately for tracking purposes
    });

    // Add structured breadcrumb for debugging context
    Sentry.addBreadcrumb({
      category: 'call-operation',
      message: `${operation}: ${metadata.callSid} - ${success ? 'SUCCESS' : 'FAILED'}`,
      data: enrichedMetadata,
      level: success ? 'info' : 'error',
      timestamp: enrichedMetadata.timestamp / 1000, // Sentry expects seconds
    });

    // Log to console with consistent format for local development
    const logLevel = success ? 'info' : 'error';
    console[logLevel](
      `[CallMetrics][${operation}][${new Date(enrichedMetadata.timestamp).toISOString()}]`,
      enrichedMetadata
    );

    // If it's an error, capture it with rich context
    if (!success) {
      Sentry.withScope((scope) => {
        scope.setTag('category', 'call-operation');
        scope.setTag('operation', operation);
        scope.setTag('callSid', metadata.callSid);
        scope.setContext('call_metadata', enrichedMetadata);
        
        const error = new Error(`Call operation failed: ${operation} - ${metadata.errorMessage || 'Unknown error'}`);
        Sentry.captureException(error);
      });
    }
  }, [assetId]);

  /**
   * Track device health and initialization events
   */
  const trackDeviceHealth = useCallback((metadata: DeviceMetadata) => {
    const enrichedMetadata = {
      ...metadata,
      assetId: metadata.assetId || assetId,
      timestamp: Date.now(),
    };

    const operation = metadata.operation;
    const success = !metadata.error;

    // Track device health using Sentry spans
    Sentry.startSpan({ 
      name: `calls.device.${operation}`,
      op: 'device-health',
      attributes: {
        success: success.toString(),
        deviceStatus: metadata.deviceStatus,
        assetId: enrichedMetadata.assetId || 'unknown',
        ...(metadata.duration && { duration: metadata.duration.toString() }),
        ...(metadata.error && { error: metadata.error }),
        ...(metadata.microphoneAccess !== undefined && { microphoneAccess: metadata.microphoneAccess.toString() }),
        ...(metadata.retryCount !== undefined && { retryCount: metadata.retryCount.toString() })
      }
    }, () => {
      // Span completes immediately for tracking purposes
    });

    Sentry.addBreadcrumb({
      category: 'device-health',
      message: `Device ${operation}: ${metadata.deviceStatus} - ${success ? 'SUCCESS' : 'FAILED'}`,
      data: enrichedMetadata,
      level: success ? 'info' : 'warning',
      timestamp: enrichedMetadata.timestamp / 1000,
    });

    console[success ? 'info' : 'warn'](
      `[DeviceMetrics][${operation}][${new Date(enrichedMetadata.timestamp).toISOString()}]`,
      enrichedMetadata
    );

    // Critical device failures should be captured as errors
    if (!success && ['initialization', 'token_refresh'].includes(operation)) {
      Sentry.withScope((scope) => {
        scope.setTag('category', 'device-health');
        scope.setTag('operation', operation);
        scope.setTag('deviceStatus', metadata.deviceStatus);
        scope.setContext('device_metadata', enrichedMetadata);
        
        const error = new Error(`Device operation failed: ${operation} - ${metadata.error || 'Unknown error'}`);
        Sentry.captureException(error);
      });
    }
  }, [assetId]);

  /**
   * Track state consistency issues and auto-healing
   */
  const trackStateConsistency = useCallback((metadata: StateMetadata) => {
    const enrichedMetadata = {
      ...metadata,
      assetId: metadata.assetId || assetId,
      timestamp: Date.now(),
    };

    const operation = metadata.operation;
    const isInconsistent = metadata.inconsistencyType !== undefined;

    if (isInconsistent) {
      // Track state consistency issues using Sentry spans
      Sentry.startSpan({ 
        name: `calls.state.${metadata.inconsistencyType}`,
        op: 'state-consistency',
        attributes: {
          operation,
          autoFixed: metadata.autoFixed ? 'true' : 'false',
          assetId: enrichedMetadata.assetId || 'unknown',
          expectedState: metadata.expectedState || 'unknown',
          actualState: metadata.actualState || 'unknown'
        }
      }, () => {
        // Span completes immediately for tracking purposes
      });

      Sentry.addBreadcrumb({
        category: 'state-consistency',
        message: `State inconsistency detected: ${metadata.inconsistencyType} during ${operation}`,
        data: enrichedMetadata,
        level: metadata.autoFixed ? 'warning' : 'error',
        timestamp: enrichedMetadata.timestamp / 1000,
      });

      console[metadata.autoFixed ? 'warn' : 'error'](
        `[StateMetrics][${operation}][${new Date(enrichedMetadata.timestamp).toISOString()}]`,
        enrichedMetadata
      );

      // Capture state inconsistencies as errors for immediate attention
      try {
        Sentry.withScope((scope) => {
          scope.setTag('category', 'state-consistency');
          scope.setTag('operation', operation);
          scope.setTag('inconsistencyType', metadata.inconsistencyType);
          scope.setTag('autoFixed', metadata.autoFixed ? 'true' : 'false');
          scope.setContext('state_metadata', enrichedMetadata);
          
          const error = new Error(`State inconsistency: ${metadata.inconsistencyType} - Expected: ${metadata.expectedState}, Actual: ${metadata.actualState}`);
          Sentry.captureException(error);
        });
      } catch (sentryError) {
        console.warn('Sentry error capture failed:', sentryError);
      }
    } else {
      // Track successful state operations
      Sentry.startSpan({ 
        name: `calls.state.${operation}`,
        op: 'state-operation',
        attributes: {
          success: 'true',
          assetId: enrichedMetadata.assetId || 'unknown'
        }
      }, () => {
        // Span completes immediately for tracking purposes
      });
    }
  }, [assetId]);

  /**
   * Track performance for critical call operations
   */
  const trackPerformance = useCallback((operationName: string, duration: number, metadata?: Record<string, any>) => {
    // Track performance using Sentry spans
    Sentry.startSpan({ 
      name: `calls.performance.${operationName}`,
      op: 'performance',
      attributes: {
        duration: duration.toString(),
        assetId: assetId || 'unknown',
        ...(metadata && Object.fromEntries(
          Object.entries(metadata).map(([k, v]) => [k, String(v)])
        ))
      }
    }, () => {
      // Span completes immediately for tracking purposes
    });

    // Alert on slow operations
    const slowThresholds: Record<string, number> = {
      'dequeue_specific_call': 5000, // 5 seconds
      'call_connection': 3000, // 3 seconds
      'device_initialization': 2000, // 2 seconds
    };

    const threshold = slowThresholds[operationName];
    if (threshold && duration > threshold) {
      Sentry.addBreadcrumb({
        category: 'performance',
        message: `Slow operation detected: ${operationName} took ${duration}ms (threshold: ${threshold}ms)`,
        data: { operationName, duration, threshold, metadata },
        level: 'warning',
      });
    }
  }, [assetId]);

  return {
    trackSentryCallOperation: trackCallOperation,
    trackSentryDeviceHealth: trackDeviceHealth,
    trackSentryStateConsistency: trackStateConsistency,
    trackSentryPerformance: trackPerformance,
  };
};