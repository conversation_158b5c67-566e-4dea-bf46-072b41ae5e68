# Hero Core Authentication System

Multi-layer auth: CloudFront Edge Lambda → Amplify Auth SDK → Microservices

## Architecture & Execution Order

**Critical:** Edge Lambda runs before Next.js (in Web App). Don't fight this hierarchy.

```
┌─────────────────────┐    ┌─────────────────────┐    ┌─────────────────────┐
│ CloudFront Edge     │    │ Next.js App         │    │ Microservices       │
│ (FIRST)             │    │ (SECOND)            │    │ (THIRD)             │
├─ Intercepts /logout │    ├─ Reads Edge cookies │    ├─ Validates Bearer   │
├─ Sets auth cookies  │    ├─ Client auth state  │    │   tokens            │
├─ Fixes .html/.txt   │    ├─ Token refresh      │    └─ Business logic     │
│   routing           │    ├─ API calls          │                          │
└─ OAuth redirects    │    └─ Dev-only direct    │                          │
                      │      auth               │                          │
└─────────────────────┘    └─────────────────────┘    └─────────────────────┘
```

## Authentication Flows

### Production Sign In Flow
```
1. Browser requests command.gethero.com (any path)
2. CloudFront Edge Lambda (`cognito-at-edge`) detects **no** auth cookies and issues a **302 redirect** to Cognito Hosted UI (`auth.gethero.com`).
3. User authenticates with their OAuth/SAML provider in the Hosted UI.
4. Hosted UI redirects back to `command.gethero.com` with `?code=...&state=...`.
5. Edge Lambda intercepts the callback, exchanges the **auth code** for tokens, and sets **authentication cookies**.
6. The original request (now authenticated) is forwarded to the S3/Next.js origin.
7. Next.js serves the app; `AuthContext` copies cookies → localStorage → sets authenticated state.
8. User sees the authenticated application.
```

### Production Logout Flow
```
1. User clicks **Logout** → browser navigates to `/logout`.
2. Edge Lambda intercepts `/logout` **before** Next.js runs.
3. `cognito-at-edge` returns a redirect response to
   `https://auth.gethero.com/logout?redirect_uri=https%3A%2F%2Fcommand.gethero.com&...`.
4. Cognito Hosted UI performs server-side logout & token invalidation.
5. Cognito redirects the user back to `command.gethero.com`.
6. Edge Lambda now detects **no valid auth cookies** and immediately redirects the user to the Cognito Hosted UI **login page**.
7. User lands on the Hosted UI login screen (no "unauthenticated app" is ever served).
```

**Critical:** The Next.js `/logout` page **never runs in production**. The `/logout` URL is a "trigger" for Edge Lambda, not an actual page.

### Development  
1. Username/password form → `AuthContext.login()` → Amplify `signIn()`
2. Logout: `/logout` page → `localLogout()` → `signOut({ global: true })`

## Common Pitfalls

### ❌ Don't use `signOut()` in production (through Amplify)
Causes .txt file redirect errors. Edge Lambda handles logout via URL interception.

### ❌ Don't ignore dual token storage  
Edge Lambda sets cookies, Amplify needs localStorage. Use provided utilities.

### ❌ Don't apply dev patterns to production
Development = direct Amplify auth. Production = Edge Lambda + cookies.

## Key Files

```
infra/cloud/apps/authorizationLambda/index.js    # Edge Lambda (production auth)
src/app/contexts/Auth/AuthContext.tsx            # Client auth state  
src/app/utils/auth.ts                           # Token storage utilities
src/app/apis/services/axiosConfig.ts            # Authenticated API calls
src/app/login/page.tsx                          # Login page (dev only)
src/app/logout/page.tsx                         # Logout page (dev only)
```

## Usage

### Auth State
```javascript
const { isAuthenticated, user, logout } = useAuth();
```

### API Calls
```javascript
const api = createAuthenticatedAxiosInstance('https://workflow.gethero.com');
const response = await api.get('/assets'); // Auto Bearer token
```

### Manual Token Access
```javascript
const session = await fetchAuthSession();
const token = session.tokens?.accessToken?.toString();
```

## Configuration

### Next.js Environment Variables
```bash
NEXT_PUBLIC_COGNITO_USER_POOL_ID=us-west-2_7SyrT2GBd
NEXT_PUBLIC_COGNITO_CLIENT_ID=52s9qm0anac1sohgioj2upgp1s
NEXT_PUBLIC_COGNITO_IDENTITY_POOL_ID=us-west-2:...
NODE_ENV=development|production (not currently there yet)
```

### Edge Lambda (Hardcoded in index.js)
```javascript
userPoolId: 'us-west-2_7SyrT2GBd'
userPoolAppId: '52s9qm0anac1sohgioj2upgp1s'
userPoolDomain: 'auth.gethero.com'
commandDomain: 'command.gethero.com'
```

## Development vs Production Setup

### Development
- Direct username/password authentication
- Uses Amplify `signIn()`, `signOut()` methods
- Tokens stored in localStorage only
- No Edge Lambda auth interception
- Login form visible at `/login`

### Production (Web App)
- OAuth through Cognito Hosted UI
- Edge Lambda intercepts auth flows
- Tokens stored in cookies (by Edge) + localStorage (by Amplify)
- `/logout` handled by Edge Lambda, not Next.js
- Initial unauthenticated requests are automatically redirected by Edge Lambda to the Cognito Hosted UI (no client-side code runs)

### Mobile Apps (Different Architecture)
- **No Edge Lambda involvement** - direct API calls
- **Different client IDs:** responder-app (`52s9qm0anac1sohgioj2upgp1s`), member-app (`6qa92fhko35r4hubv1g8bjno3u`)
- **expo-auth-session** instead of Amplify Auth SDK
- **Manual token management** with `expo-secure-store`
- **Direct Cognito logout:** Opens browser for `auth.gethero.com/logout`

```
Web App:    Browser → CloudFront (Edge) → S3 → APIs
Mobile App: App → APIs (direct, no Edge Lambda)
```

**Why this matters:** Mobile apps don't have the .txt redirect issue because they don't serve static files. The complex Edge Lambda setup is **web-specific**.

## Component Responsibilities

### AWS Cognito (Identity Provider)
- **User Pool:** Stores user accounts, handles OAuth/SAML providers
- **Hosted UI:** Login/logout pages (`auth.gethero.com`)
- **JWT Tokens:** Issues access/ID/refresh tokens
- **Session Management:** Server-side session validation

### cognito-at-edge (Edge Lambda Library)
- **Request Interception:** Runs on every CloudFront request via `authenticator.handle(event)`
- **Token Validation:** Checks JWT tokens in cookies
- **OAuth Flow Management:** Handles redirects to/from Cognito
- **Cookie Management:** Sets/reads authentication cookies
- **URL Routing:** Intercepts special URLs like `/logout` (configured via `logoutUri: '/logout'`)

### Amplify Auth SDK (Client-Side Library)
**In production, Amplify is simply a *token utility*:**
- **`fetchAuthSession()`** – Reads tokens (copied from cookies) for API calls
- **Token refresh** – Automatically refreshes expired tokens
- **`getCurrentUser()`** – Retrieves user profile data

It does **NOT** initiate login or logout in production; those flows are fully handled by Edge Lambda & Cognito.

### Next.js App (Client Application)
- **AuthContext:** Manages client-side auth state
- **Token Storage Sync:** Copies cookies to localStorage for Amplify
- **UI State:** Shows authenticated/unauthenticated views
- **API Integration:** Uses Amplify tokens for microservice calls

```
┌─────────────────┐  ┌──────────────────┐  ┌─────────────────┐  ┌─────────────────┐
│ AWS Cognito     │  │ cognito-at-edge  │  │ Amplify Auth    │  │ Next.js App     │
├─ User accounts  │  ├─ Request filter  │  ├─ Token refresh  │  ├─ UI state       │
├─ OAuth/SAML     │  ├─ Token validation│  ├─ API calls      │  ├─ Token sync     │
├─ JWT tokens     │  ├─ Cookie mgmt     │  ├─ User profile   │  ├─ Components     │
└─ Hosted UI      │  └─ Logout intercept│  └─ Token utils     │  └─ Business logic│
     ↑                      ↑                      ↑                      ↑
Server-side ID        Edge validation        Client token utils    Application layer
```

**Key insight:** In production, Amplify is **just a token utility library**. The heavy lifting (auth flows, session management, logout) is handled by Edge Lambda + Cognito.

## Debugging

```javascript
// Check auth state
console.log(await fetchAuthSession());

// Check token storage
console.log(document.cookie); // Production cookies
console.log(localStorage.getItem('CognitoIdentityServiceProvider.52s9qm0anac1sohgioj2upgp1s.LastAuthUser'));
```