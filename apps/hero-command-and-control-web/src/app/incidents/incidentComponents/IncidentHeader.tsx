"use client";

import ManageModal from "@/app/components/ManageModal";
import { useBreadcrumbHeader } from "@/app/hooks/useBreadcrumbHeader";
import { useRecentlyViewedTracker } from "@/app/hooks/useRecentlyViewedTracker";
import { getIncidentLabel, getIncidentStatusLabel } from "@/app/utils/utils";
import { Header } from "@/design-system/components/Header";
import { useRouter } from "next/navigation";
import { Situation } from "proto/hero/situations/v2/situations_pb";
import { useState } from "react";

interface IncidentHeaderProps {
  situation: Situation | undefined;
  onClose: () => void;
}

export default function IncidentHeader({
  situation,
  onClose,
}: IncidentHeaderProps) {
  const incidentId =
    situation?.id?.replace(/[^0-9]/g, "")?.slice(0, 7) || "Unknown ID";

  // Get incident type for subtitle
  const incidentType = situation?.type
    ? getIncidentLabel(situation.type.toString())
    : "Unknown";

  const { breadcrumbs } = useBreadcrumbHeader({
    id: `incident-${situation?.id}`,
    label: `Incident ${incidentId}`,
    path: `/incidents?incidentId=${situation?.id}`,
  });
  const router = useRouter();
  const [manageModalOpen, setManageModalOpen] = useState(false);

  // Track this incident in recently viewed
  useRecentlyViewedTracker({
    id: `incident-${situation?.id}`,
    title: `INC ${incidentId}`,
    subtitle: `${incidentType} Incident`,
    path: `/incidents?incidentId=${situation?.id}`,
  });

  const formatDate = (timestamp: any): string => {
    if (!timestamp) return "N/A";
    try {
      if (timestamp.seconds) {
        return new Date(Number(timestamp.seconds) * 1000).toLocaleDateString(
          "en-US",
          {
            weekday: "long",
            month: "long",
            day: "numeric",
            year: "numeric",
          }
        );
      }
      if (
        typeof timestamp === "string" &&
        timestamp !== "0001-01-01T00:00:00Z"
      ) {
        return new Date(timestamp).toLocaleDateString("en-US", {
          weekday: "long",
          month: "long",
          day: "numeric",
          year: "numeric",
        });
      }
      return "N/A";
    } catch (e) {
      return "N/A";
    }
  };

  // Get incident status
  const status = situation?.status || "Unknown";

  // Get dispatcher name
  const dispatcherName = "Unknown";

  return (
    <>
      <Header
        breadcrumbs={breadcrumbs}
        title={incidentId}
        tags={[
          {
            // @ts-expect-error TODO: Fix type issue
            label: getIncidentStatusLabel(status),
            // @ts-expect-error TODO: Fix type issue
            color: status === "SITUATION_STATUS_RESOLVED" ? "rose" : "blue",
          },
        ]}
        actions={[
          {
            label: "Manage",
            onClick: () => setManageModalOpen(true),
          },
        ]}
        metadata={[
          {
            label: "Date Reported",
            value: formatDate(situation?.createTime),
          },
          {
            label: "Date Closed",
            value: formatDate(situation?.resolvedTime),
          },
          {
            label: "Dispatcher",
            value: dispatcherName,
          },
        ]}
      />

      {/* Manage Modal */}
      {manageModalOpen && (
        <ManageModal
          onClose={() => setManageModalOpen(false)}
          objectId={situation?.id || ''}
          objectType="situation"
          showAssignTab={false}
          showDetailsTab={false}
          objectName="Incident"
        />
      )}
    </>
  );
}
