import React from "react";
import { Box, Paper, Chip } from "@mui/material";
import { Typography } from "@/design-system/components/Typography";
import { colors } from "@/design-system/tokens";
import { Situation } from "proto/hero/situations/v2/situations_pb";

interface EventLogCardProps {
  situation: Situation;
}

interface Update {
  timestamp?: any;
  eventType?: string;
  message?: string;
  updaterId?: string;
}

interface DayGroup {
  date: Date;
  updates: Update[];
}

const groupUpdatesByDay = (updates: Update[]): DayGroup[] => {
  const grouped = updates.reduce((acc, update) => {
    if (!update.timestamp) return acc;

    const date = new Date(update.timestamp);
    const dayKey = date.toISOString().split("T")[0];

    if (!acc[dayKey]) {
      acc[dayKey] = {
        date,
        updates: [],
      };
    }
    acc[dayKey].updates.push(update);
    return acc;
  }, {} as Record<string, DayGroup>);

  return Object.entries(grouped)
    .sort(([a], [b]) => b.localeCompare(a))
    .map(([_, value]) => value);
};

const EventLogCard: React.FC<EventLogCardProps> = ({ situation }) => {
  return (
    <Paper
      elevation={1}
      sx={{
        borderRadius: "12px",
        overflow: "hidden",
        mb: 3,
        border: `1px solid ${colors.grey[200]}`,
        boxShadow: "none",
        height: "100%",
        display: "flex",
        flexDirection: "column",
      }}
    >
      <Box
        sx={{
          padding: "16px 24px",
        }}
      >
        <Typography style="caps1" color={colors.grey[500]}>
          EVENT LOG
        </Typography>
      </Box>

      <Box
        sx={{
          padding: "24px",
          paddingTop: "0px",
          maxHeight: "400px",
          overflowY: "auto",
        }}
      >
        {situation?.updates?.length ? (
          groupUpdatesByDay(situation.updates).map((dayGroup) => (
            <Box key={dayGroup.date.toISOString()} sx={{ mb: 3 }}>
              <Box
                sx={{
                  display: "flex",
                  justifyContent: "center",
                  mb: 2,
                  "&:not(:first-of-type)": {
                    mt: 4,
                  },
                }}
              >
                <Typography style="tag1" color={colors.grey[400]}>
                  {dayGroup.date.toLocaleDateString("en-US", {
                    weekday: "long",
                    month: "long",
                    day: "numeric",
                    year: "numeric",
                  })}
                </Typography>
              </Box>
              {dayGroup.updates
                .sort((a, b) => {
                  const aTime = a.timestamp
                    ? new Date(a.timestamp).getTime()
                    : 0;
                  const bTime = b.timestamp
                    ? new Date(b.timestamp).getTime()
                    : 0;
                  return bTime - aTime;
                })
                .map((update, index) => {
                  const isDispatcher = update.updaterId === "dispatch";
                  return (
                    <Box
                      key={index}
                      sx={{
                        display: "flex",
                        flexWrap: "nowrap",
                        alignItems: "flex-start",
                        gap: 1,
                        mb: 2,
                        width: "100%",
                      }}
                    >
                      <Box
                        sx={{
                          maxWidth: "100px",
                          flexShrink: 0,
                          alignSelf: "flex-start",
                          display: "flex",
                          mr: "12px",
                        }}
                      >
                        <Typography
                          color={colors.grey[400]}
                          style="tag1"
                          lineHeight="14px"
                        >
                          {new Date(
                            update.timestamp
                          ).toLocaleTimeString("en-US", {
                            hour12: false,
                            hour: "2-digit",
                            minute: "2-digit",
                          })}
                        </Typography>
                      </Box>

                      <Box
                        sx={{
                          flex: 1,
                          wordWrap: "break-word",
                          display: "flex",
                          alignItems: "flex-start",
                          gap: 1,
                        }}
                      >
                        {update.eventType === "info change" && (
                          <svg
                            xmlns="http://www.w3.org/2000/svg"
                            width="16"
                            height="16"
                            viewBox="0 0 16 16"
                            fill="none"
                          >
                            <mask
                              id="mask0_3762_111242"
                              maskUnits="userSpaceOnUse"
                              x="0"
                              y="0"
                              width="16"
                              height="16"
                            >
                              <rect width="16" height="16" fill="#D9D9D9" />
                            </mask>
                            <g mask="url(#mask0_3762_111242)">
                              <path
                                d="M2 13.3335V12.0002H3.83333L3.56667 11.7668C2.98889 11.2557 2.58333 10.6724 2.35 10.0168C2.11667 9.36128 2 8.70017 2 8.03351C2 6.80017 2.36944 5.70295 3.10833 4.74184C3.84722 3.78073 4.81111 3.14462 6 2.83351V4.23351C5.2 4.5224 4.55556 5.01406 4.06667 5.70851C3.57778 6.40295 3.33333 7.17795 3.33333 8.03351C3.33333 8.53351 3.42778 9.01962 3.61667 9.49184C3.80556 9.96406 4.1 10.4002 4.5 10.8002L4.66667 10.9668V9.33351H6V13.3335H2ZM13.95 7.33351H12.6C12.5444 6.94462 12.425 6.5724 12.2417 6.21684C12.0583 5.86128 11.8111 5.52239 11.5 5.20017L11.3333 5.03351V6.66684H10V2.66684H14V4.00017H12.1667L12.4333 4.23351C12.8889 4.70017 13.2389 5.19462 13.4833 5.71684C13.7278 6.23906 13.8833 6.77795 13.95 7.33351ZM11.3333 15.3335L11.1333 14.3335C11 14.278 10.875 14.2196 10.7583 14.1585C10.6417 14.0974 10.5222 14.0224 10.4 13.9335L9.43333 14.2335L8.76667 13.1002L9.53333 12.4335C9.51111 12.278 9.5 12.1335 9.5 12.0002C9.5 11.8668 9.51111 11.7224 9.53333 11.5668L8.76667 10.9002L9.43333 9.76684L10.4 10.0668C10.5222 9.97795 10.6417 9.90295 10.7583 9.84184C10.875 9.78073 11 9.72239 11.1333 9.66684L11.3333 8.66684H12.6667L12.8667 9.66684C13 9.72239 13.125 9.78351 13.2417 9.85017C13.3583 9.91684 13.4778 10.0002 13.6 10.1002L14.5667 9.76684L15.2333 10.9335L14.4667 11.6002C14.4889 11.7335 14.5 11.8724 14.5 12.0168C14.5 12.1613 14.4889 12.3002 14.4667 12.4335L15.2333 13.1002L14.5667 14.2335L13.6 13.9335C13.4778 14.0224 13.3583 14.0974 13.2417 14.1585C13.125 14.2196 13 14.278 12.8667 14.3335L12.6667 15.3335H11.3333ZM12 13.3335C12.3667 13.3335 12.6806 13.203 12.9417 12.9418C13.2028 12.6807 13.3333 12.3668 13.3333 12.0002C13.3333 11.6335 13.2028 11.3196 12.9417 11.0585C12.6806 10.7974 12.3667 10.6668 12 10.6668C11.6333 10.6668 11.3194 10.7974 11.0583 11.0585C10.7972 11.3196 10.6667 11.6335 10.6667 12.0002C10.6667 12.3668 10.7972 12.6807 11.0583 12.9418C11.3194 13.203 11.6333 13.3335 12 13.3335Z"
                                fill="#697282"
                              />
                            </g>
                          </svg>
                        )}
                        {(update.eventType === "status change" ||
                          update.eventType === "order created") &&
                          !isDispatcher && (
                            <Box
                              sx={{
                                display: "flex",
                                alignItems: "flex-end",
                                gap: 0.5,
                              }}
                            >
                              <svg
                                xmlns="http://www.w3.org/2000/svg"
                                width="16"
                                height="16"
                                viewBox="0 0 16 16"
                                fill="none"
                              >
                                <mask
                                  id="mask0_3762_111250"
                                  maskUnits="userSpaceOnUse"
                                  x="0"
                                  y="0"
                                  width="16"
                                  height="16"
                                >
                                  <rect width="16" height="16" fill="#D9D9D9" />
                                </mask>
                                <g mask="url(#mask0_3762_111250)">
                                  <path
                                    d="M7.99984 8.66668C8.64428 8.66668 9.19428 8.4389 9.64984 7.98334C10.1054 7.52779 10.3332 6.97779 10.3332 6.33334C10.3332 5.6889 10.1054 5.1389 9.64984 4.68334C9.19428 4.22779 8.64428 4.00001 7.99984 4.00001C7.35539 4.00001 6.80539 4.22779 6.34984 4.68334C5.89428 5.1389 5.6665 5.6889 5.6665 6.33334C5.6665 6.97779 5.89428 7.52779 6.34984 7.98334C6.80539 8.4389 7.35539 8.66668 7.99984 8.66668ZM7.99984 14.6667C6.37761 14.2556 5.08317 13.3667 4.1165 12C3.14984 10.6333 2.6665 9.10001 2.6665 7.40001V3.33334L7.99984 1.33334L13.3332 3.33334V7.40001C13.3332 9.10001 12.8498 10.6333 11.8832 12C10.9165 13.3667 9.62206 14.2556 7.99984 14.6667ZM7.99984 13.2667C8.65539 13.0556 9.23595 12.725 9.7415 12.275C10.2471 11.825 10.6887 11.3167 11.0665 10.75C10.5887 10.5056 10.0915 10.3195 9.57484 10.1917C9.05817 10.0639 8.53317 10 7.99984 10C7.4665 10 6.9415 10.0639 6.42484 10.1917C5.90817 10.3195 5.41095 10.5056 4.93317 10.75C5.31095 11.3167 5.75262 11.825 6.25817 12.275C6.76373 12.725 7.34428 13.0556 7.99984 13.2667Z"
                                    fill="#697282"
                                  />
                                </g>
                              </svg>
                              <Typography style="tag1" color={colors.grey[500]}>
                              {update.updaterId
                                ? update.updaterId?.replace(/[^0-9]/g, "")?.slice(0, 3)
                                : ""}
                              </Typography>
                            </Box>
                          )}

                        {update.eventType === "order created" ||
                        update.eventType === "status change" ? (
                          <Chip
                            label={update.message || ""}
                            sx={{
                              backgroundColor: colors.blue[100],
                              color: colors.blue[600],
                              fontSize: 9,
                              fontWeight: 400,
                              borderRadius: "5px",
                              height: 24,
                              ml: 1,
                              "& .MuiChip-label": { padding: "0 8px" },
                            }}
                          />
                        ) : (
                          <Typography
                            color={
                              update.eventType === "info change"
                                ? colors.grey[500]
                                : colors.grey[900]
                            }
                            style="body4"
                            className={
                              update.eventType === "info change" ? "italic" : ""
                            }
                          >
                            {update.message || ""}
                          </Typography>
                        )}
                      </Box>
                    </Box>
                  );
                })}
            </Box>
          ))
        ) : (
          <Typography style="body4" color={colors.grey[500]}>
            No updates available
          </Typography>
        )}
      </Box>
    </Paper>
  );
};

export default EventLogCard;
