import { useListAssets } from "@/app/apis/services/workflow/assets/hooks";
import { hookOrderStatusToString, stringToOrderStatus } from "@/app/apis/services/workflow/orders/enumConverters";
import { useListOrdersForSituation } from "@/app/apis/services/workflow/orders/hooks";
import { toTitleCase } from "@/app/utils/utils";
import { Typography } from "@/design-system/components/Typography";
import { colors } from "@/design-system/tokens";
import {
  Box,
  Paper,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
} from "@mui/material";
import { Asset, ListAssetsRequest } from "proto/hero/assets/v2/assets_pb";
import { ListOrdersForSituationRequest, OrderStatus } from "proto/hero/orders/v2/orders_pb";
import { Situation } from "proto/hero/situations/v2/situations_pb";
import React from "react";

interface ResponderDetailsCardProps {
  situation: Situation;
}

const ResponderDetailsCard: React.FC<ResponderDetailsCardProps> = ({
  situation,
}) => {
  // Fetch orders for the situation
  const { data: ordersForSituation } = useListOrdersForSituation({
    situationId: situation.id || "",
    pageSize: 100,
    pageToken: "",
  } as ListOrdersForSituationRequest);

  // Fetch all assets/responders
  const { data: assetsResponse } = useListAssets({
    pageSize: 100,
    pageToken: "",
  } as ListAssetsRequest);

  const assets = assetsResponse?.assets || [];

  // Extract responder information from orders, grouping by responder
  const assignedResponders = React.useMemo(() => {
    if (!ordersForSituation?.orders || !assets.length) return [];

    // Get all orders with assetIds and type ASSIST_MEMBER task
    const ordersWithAssetIds = ordersForSituation.orders.filter(
      // @ts-expect-error TODO: Fix type issue
      (order) => order.assetId && order.type === "ORDER_TYPE_ASSIST_MEMBER"
    );

    // Group orders by assetId
    const ordersByAssetId: Record<string, typeof ordersWithAssetIds> = {};

    ordersWithAssetIds.forEach((order) => {
      const assetId = order.assetId || "";
      if (!ordersByAssetId[assetId]) {
        ordersByAssetId[assetId] = [];
      }
      ordersByAssetId[assetId].push(order);
    });

    // Get unique asset IDs
    const uniqueAssetIds = Object.keys(ordersByAssetId);

    // Process each unique responder
    return uniqueAssetIds
      .filter((assetId) => assetId && assetId.length > 0) // Filter out empty or undefined assetIds
      .map((assetId) => {
        // Get all orders for this responder
        const responderOrders = ordersByAssetId[assetId];

      // Find the asset information
      const asset = assets.find((asset: Asset) => asset.id === assetId);

      // Initialize timestamps with the earliest possible values
      const timestamps = {
        dispatched: "",
        enRoute: "",
        onScene: "",
        done: "",
      };

      // Process all orders for this responder to find the latest timestamps
      responderOrders.forEach((order) => {
        // Get the status updates for this order
        const statusUpdates = order.statusUpdates || [];

        // Process each status update
        statusUpdates.forEach((statusUpdate) => {
          // Use statusUpdateTimestamp if available, otherwise fall back to entryTimestamp
          const timestampToUse = statusUpdate.statusUpdateTimestamp || statusUpdate.entryTimestamp;
          if (!timestampToUse) return;

          const updateTime = new Date(timestampToUse).toLocaleTimeString(
            "en-US",
            {
              hour12: false,
              hour: "2-digit",
              minute: "2-digit",
            }
          );

          const newStatus = (statusUpdate.newStatus || "") as string;
          const note = statusUpdate.note?.toLowerCase() || "";

          // Convert string status to enum for proper comparison
          const statusEnum = stringToOrderStatus(newStatus);

          // Map status updates to timestamp fields
          if (statusEnum === OrderStatus.CREATED) {
            // This is the dispatched time
            if (!timestamps.dispatched || updateTime > timestamps.dispatched) {
              timestamps.dispatched = updateTime;
            }
          } else if (statusEnum === OrderStatus.ACKNOWLEDGED || note.includes("en route") || note.includes("acknowledged")) {
            // This is the en-route time
            if (!timestamps.enRoute || updateTime > timestamps.enRoute) {
              timestamps.enRoute = updateTime;
            }
          } else if (statusEnum === OrderStatus.IN_PROGRESS || note.includes("assisting") || note.includes("in progress") || note.includes("on scene")) {
            // This is the on-scene time
            if (!timestamps.onScene || updateTime > timestamps.onScene) {
              timestamps.onScene = updateTime;
            }
          } else if (statusEnum === OrderStatus.COMPLETED || note.includes("completed") || note.includes("resolved")) {
            // This is the done time
            if (!timestamps.done || updateTime > timestamps.done) {
              timestamps.done = updateTime;
            }
          }
        });
      });

      // Fill in any missing timestamps with "-"
      if (!timestamps.dispatched) timestamps.dispatched = "-";
      if (!timestamps.enRoute) timestamps.enRoute = "-";
      if (!timestamps.onScene) timestamps.onScene = "-";
      if (!timestamps.done) timestamps.done = "-";

      // Get the current status (from the most recent order)
      const mostRecentOrder = responderOrders.sort((a, b) => {
        const aTime = a.updateTime ? new Date(a.updateTime).getTime() : 0;
        const bTime = b.updateTime ? new Date(b.updateTime).getTime() : 0;
        return bTime - aTime;
      })[0];

      return {
        id: assetId,
        displayId: assetId?.replace(/[^0-9]/g, "")?.slice(0, 3),
        name:
          asset?.name ||
                      `Responder ${assetId?.replace(/[^0-9]/g, "")?.slice(0, 3)}`,
        status: hookOrderStatusToString(mostRecentOrder.status),
        timestamps,
      };
    });
  }, [ordersForSituation?.orders, assets]);

  return (
    <Paper
      elevation={1}
      sx={{
        borderRadius: "12px",
        overflow: "hidden",
        mb: 3,
        border: `1px solid ${colors.grey[200]}`,
        boxShadow: "none",
        height: "100%",
        display: "flex",
        flexDirection: "column",
      }}
    >
      <Box
        sx={{
          padding: "16px 24px",
        }}
      >
        <Typography style="caps1" color={colors.grey[500]}>
          RESPONDER DETAILS
        </Typography>
      </Box>

      <Box sx={{ padding: "24px", paddingTop: "0px" }}>
        {assignedResponders.length > 0 ? (
          <TableContainer
            sx={{
              border: "1px solid #E1E4E9",
              borderRadius: "8px",
              maxWidth: "100%",
              opacity: 0.85,
              backgroundColor: "#F9FAFB",
              "& .MuiTableCell-root": {
                borderColor: "#E1E4E9",
                textAlign: "left",
                py: 0.5,
                px: 1,
                borderRight: "1px solid #E1E4E9",
                whiteSpace: "nowrap",
                "&:last-child": {
                  borderRight: "none",
                },
              },
              "& .MuiTableRow-root": {
                "&:last-child td": {
                  borderBottom: "none",
                },
              },
            }}
          >
            <Table size="small">
              <TableHead>
                <TableRow>
                  <TableCell
                    sx={{
                      width: "40%",
                      fontSize: "12px",
                      color: "#6E757D",
                      fontWeight: 400,
                    }}
                  >
                    Responding Unit Name & Badge Number
                  </TableCell>
                  <TableCell
                    sx={{
                      width: "15%",
                      fontSize: "12px",
                      color: "#6E757D",
                      fontWeight: 400,
                    }}
                  >
                    Dispatched
                  </TableCell>
                  <TableCell
                    sx={{
                      width: "15%",
                      fontSize: "12px",
                      color: "#6E757D",
                      fontWeight: 400,
                    }}
                  >
                    En-route
                  </TableCell>
                  <TableCell
                    sx={{
                      width: "15%",
                      fontSize: "12px",
                      color: "#6E757D",
                      fontWeight: 400,
                    }}
                  >
                    On Scene
                  </TableCell>
                  <TableCell
                    sx={{
                      width: "15%",
                      fontSize: "12px",
                      color: "#6E757D",
                      fontWeight: 400,
                    }}
                  >
                    Cleared
                  </TableCell>
                </TableRow>
              </TableHead>
              <TableBody>
                {assignedResponders.map((res) => (
                  <TableRow key={res.id}>
                    <TableCell>
                      <Box
                        sx={{
                          display: "flex",
                          alignItems: "center",
                          gap: 0.5,
                          maxWidth: "100%",
                        }}
                      >
                        <Box>
                          <Typography style="body2" color={colors.grey[900]}>
                            <Box
                              component="span"
                              sx={{
                                fontWeight: 500,
                                fontSize: "14px",
                                overflow: "hidden",
                                textOverflow: "ellipsis",
                                whiteSpace: "nowrap",
                              }}
                            >
                              {res.name
                                ? res.name.includes(" ")
                                  ? toTitleCase(res.name.split(" ")[0].trim())
                                  : toTitleCase(res.name)
                                : ""}
                            </Box>
                          </Typography>
                        </Box>
                        <Box>
                          <Typography style="body2" color={colors.grey[900]}>
                            <Box
                              component="span"
                              sx={{ fontWeight: 500, fontSize: "14px" }}
                            >
                              • {res.name[0].toUpperCase()}-{res.displayId}
                            </Box>
                          </Typography>
                        </Box>
                      </Box>
                    </TableCell>
                    <TableCell
                      sx={{
                        fontSize: "14px",
                        fontWeight: 500,
                        color: "#111827",
                      }}
                    >
                      {res.timestamps.dispatched}
                    </TableCell>
                    <TableCell
                      sx={{
                        fontSize: "14px",
                        fontWeight: 500,
                        color: "#111827",
                      }}
                    >
                      {res.timestamps.enRoute}
                    </TableCell>
                    <TableCell
                      sx={{
                        fontSize: "14px",
                        fontWeight: 500,
                        color: "#111827",
                      }}
                    >
                      {res.timestamps.onScene}
                    </TableCell>
                    <TableCell
                      sx={{
                        fontSize: "14px",
                        fontWeight: 500,
                        color: "#111827",
                      }}
                    >
                      {res.timestamps.done}
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </TableContainer>
        ) : (
          <Box sx={{ textAlign: "center", py: 2 }}>
            <Typography style="body2" color={colors.grey[500]}>
              No units assigned
            </Typography>
          </Box>
        )}
      </Box>
    </Paper>
  );
};

export default ResponderDetailsCard;
