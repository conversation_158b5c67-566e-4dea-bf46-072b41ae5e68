'use client';

import { useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { useAuth } from '@/app/contexts/Auth/AuthContext';

const LogoutPage = () => {
  const router = useRouter();
  const { localLogout } = useAuth();

  useEffect(() => {
    const doLogout = async () => {
      await localLogout();
      router.push('/');
    };
    doLogout();
  }, [localLogout, router]);

  return null;
};

export default LogoutPage;
