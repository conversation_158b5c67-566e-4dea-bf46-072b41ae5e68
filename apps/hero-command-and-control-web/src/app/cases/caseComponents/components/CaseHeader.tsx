"use client";

import { useBatchGetLatestEntities, useListLatestEntitySchemas } from "@/app/apis/services/workflow/entity/hooks";
import { useBatchGetReports } from "@/app/apis/services/workflow/reports/v2/hooks";
import ExportModal, { ExportOptions } from "@/app/cases/caseComponents/components/CaseSelector";
import ManageModal from "@/app/components/ManageModal";
import { useBreadcrumbHeader } from "@/app/hooks/useBreadcrumbHeader";
import { useRecentlyViewedTracker } from "@/app/hooks/useRecentlyViewedTracker";
import { getCaseStatusLabel, getCaseTypeLabel } from "@/app/utils/utils";
import { Header } from "@/design-system/components/Header";
import ExportDocument from "@/export/pdf/CaseExport";
import { registerRoboto } from "@/export/pdf/utils/fonts";
import AdfScannerIcon from '@mui/icons-material/AdfScanner';
import ManageAccountsIcon from '@mui/icons-material/ManageAccounts';
import TaskIcon from '@mui/icons-material/Task';
import { pdf } from "@react-pdf/renderer";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { getCurrentUser } from 'aws-amplify/auth';
import { Case } from "proto/hero/cases/v1/cases_pb";
import { EntityType } from "proto/hero/entity/v1/entity_pb";
import { useState } from "react";

// Register fonts
registerRoboto();

// Create a client for PDF generation
const pdfQueryClient = new QueryClient({
  defaultOptions: {
    queries: {
      retry: false,
      staleTime: 0,
      gcTime: 0,
    },
  },
});

interface CaseHeaderProps {
  caseData: Case;
  onBack?: () => void;
}

// Helper function to format user ID
const formatUserId = (userId: string): string => {
  if (!userId) return "USER UNKNOWN";
  const digits = String(userId).replace(/[^0-9]/g, "").slice(0, 8);
  return digits ? `USER ${digits}` : "USER UNKNOWN";
};

export default function CaseHeader({ caseData, onBack }: CaseHeaderProps) {
  const caseIdNumber = caseData.id
    ? String(caseData.id).replace(/[^0-9]/g, "").slice(0, 8)
    : "";

  const formattedCaseId = caseIdNumber.length >= 8
    ? caseIdNumber.slice(0, 8)
    : caseIdNumber;

  const caseType = caseData.type ? getCaseTypeLabel(caseData.type.toString()) : "Unspecified";

  const { breadcrumbs } = useBreadcrumbHeader({
    id: `case-${caseData.id}`,
    label: `Case ${formattedCaseId}`,
    path: `/cases?caseId=${caseData.id}`,
  });

  useRecentlyViewedTracker({
    id: `case-${caseData.id}`,
    title: `CS ${formattedCaseId}`,
    subtitle: `${caseType} Case`,
    path: `/cases?caseId=${caseData.id}`,
  });

  // Format date from timestamp or ISO string
  const formatDate = (timestamp: string): string => {
    if (!timestamp) return "N/A";
    try {
      const date = new Date(timestamp);
      return date.toLocaleDateString("en-US", {
        weekday: "long",
        month: "long",
        day: "numeric",
        year: "numeric",
      });
    } catch (e) {
      return "N/A";
    }
  };

  // Get priority as a string (P0, P1, etc.)
  const priorityLabel = caseData.priority ? `P${caseData.priority}` : "P --";
  const priorityColor = caseData.priority
    ? caseData.priority === 1
      ? "rose"
      : caseData.priority >= 2 && caseData.priority <= 4
        ? "amber"
        : caseData.priority === 5
          ? "vine"
          : "grey"
    : "grey";

  const tags = [
    caseData.status
      ? {
        label: getCaseStatusLabel(caseData.status.toString()),
        color: "blue" as const
      }
      : null,
    {
      label: priorityLabel,
      color: priorityColor,
    }
  ].filter((tag): tag is { label: string; color: "blue" | "rose" | "grey" | "amber" | "vine" } => !!tag);

  // Export E2E state
  const [isExportOpen, setIsExportOpen] = useState(false);
  const [isManageModalOpen, setIsManageModalOpen] = useState(false);

  // Get entity IDs from case data
  const entityIds = caseData.entityRefs?.map(ref => ref.id) || [];

  // Fetch reports using the hook
  const { data: reportsResponse, isLoading: isLoadingReports } = useBatchGetReports(caseData.reportIds || [], {
    enabled: Array.isArray(caseData.reportIds) && caseData.reportIds.length > 0,
    staleTime: 5 * 60 * 1000, // 5 minutes
    retry: 2
  });

  // Fetch full entity data using the hook
  const { data: entitiesResponse, isLoading: isLoadingEntities } = useBatchGetLatestEntities(entityIds, {
    queryKey: ["entity", "batch", entityIds],
    enabled: entityIds.length > 0,
    staleTime: 5 * 60 * 1000, // 5 minutes
    retry: 2
  });

  // Fetch latest schemas for each entity type
  const { data: personSchemas } = useListLatestEntitySchemas({
    entityType: EntityType.PERSON,
    pageSize: 1,
    pageToken: "",
  } as any);

  const { data: propertySchemas } = useListLatestEntitySchemas({
    entityType: EntityType.PROPERTY,
    pageSize: 1,
    pageToken: "",
  } as any);

  const { data: vehicleSchemas } = useListLatestEntitySchemas({
    entityType: EntityType.VEHICLE,
    pageSize: 1,
    pageToken: "",
  } as any);

  const handleExport = async (options: ExportOptions) => {
    console.log('Starting PDF export with options:', options);
    setIsExportOpen(false);

    try {
      // Get current user
      const currentUser = await getCurrentUser();
      const printedBy = formatUserId(currentUser.username || "");

      // Get reports from the hook's response
      const reports = reportsResponse?.reports || [];
      console.log('Using reports:', {
        count: reports.length,
        reportIds: reports.map(r => r.id),
        reportDetails: reports.map(r => ({
          id: r.id,
          title: r.title,
          status: r.status,
          createdAt: r.createdAt
        }))
      });

      // Get full entity data
      const entities = entitiesResponse?.entities || [];
      console.log('Using entities:', {
        count: entities.length,
        entityIds: entities.map(e => e.id),
        entityTypes: entities.map(e => e.entityType)
      });

      if (reports.length === 0) {
        console.warn('No reports found for case:', caseData.id);
        // You might want to show a warning to the user here
      }

      // Get the latest update for reviewer info
      const latestUpdate = caseData.updates && caseData.updates.length > 0
        ? caseData.updates[caseData.updates.length - 1]
        : null;

      // Generate and download PDF immediately
      console.log('Creating PDF document for case:', caseData.id);
      const doc = (
        <QueryClientProvider client={pdfQueryClient}>
          <ExportDocument
            caseData={caseData}
            reports={reports}
            entities={entities}
            options={{
              printedBy: printedBy,
              reviewer: latestUpdate?.displayName || printedBy,
              approvedDateTime: latestUpdate?.eventTime,
              includeSections: options.includeSections,
              excludeIdentifiers: options.excludeIdentifiers
            }}
            schemas={{
              person: personSchemas?.schemas?.[0]?.schemaDefinition,
              property: propertySchemas?.schemas?.[0]?.schemaDefinition,
              vehicle: vehicleSchemas?.schemas?.[0]?.schemaDefinition
            }}
          />
        </QueryClientProvider>
      );

      console.log('Document created, generating PDF blob...');

      // Create PDF blob
      const blob = await pdf(doc).toBlob();
      console.log('PDF blob created:', {
        type: blob.type,
        size: blob.size,
        hasValidHeader: await validatePdfHeader(blob),
        firstBytes: await getFirstBytes(blob, 10)
      });

      // Validate PDF blob
      if (!blob || blob.size === 0) {
        throw new Error('Generated PDF blob is empty');
      }

      if (blob.type !== 'application/pdf') {
        throw new Error(`Invalid blob type: ${blob.type}`);
      }

      const isValidHeader = await validatePdfHeader(blob);
      if (!isValidHeader) {
        throw new Error('Invalid PDF header');
      }

      // Create download link and trigger download
      const url = URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;
      link.download = `case-${caseData.id}-export.pdf`;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      URL.revokeObjectURL(url);

      console.log('PDF download completed successfully');

    } catch (error) {
      console.error('Error generating PDF:', error);
      // TODO: Show error to user
    }
  };

  // Helper function to validate PDF header
  const validatePdfHeader = async (blob: Blob): Promise<boolean> => {
    try {
      const arrayBuffer = await blob.arrayBuffer();
      const uint8Array = new Uint8Array(arrayBuffer);
      const header = String.fromCharCode.apply(null, Array.from(uint8Array.slice(0, 5)));
      return header === '%PDF-';
    } catch (error) {
      console.error('Error validating PDF header:', error);
      return false;
    }
  };

  // Helper function to get first N bytes of a blob
  const getFirstBytes = async (blob: Blob, n: number): Promise<string> => {
    try {
      const arrayBuffer = await blob.arrayBuffer();
      const uint8Array = new Uint8Array(arrayBuffer);
      return Array.from(uint8Array.slice(0, n))
        .map(b => b.toString(16).padStart(2, '0'))
        .join(' ');
    } catch (error) {
      console.error('Error getting first bytes:', error);
      return '';
    }
  };

  return (
    <>
      <Header
        breadcrumbs={breadcrumbs}
        title={formattedCaseId}
        tags={tags}
        metadata={[
          {
            label: "Date Opened",
            value: formatDate(caseData.createTime || "")
          },
          {
            label: "Last Updated",
            value: formatDate(caseData.updateTime || "")
          }
        ]}
        actions={[
          {
            label: "Export",
            leftIcon: <AdfScannerIcon />,
            size: "medium",
            color: "grey",
            prominence: false,
            onClick: () => {
              console.log('Export button clicked');
              setIsExportOpen(true);
            }
          },
          {
            label: "Audit",
            leftIcon: <TaskIcon />,
            size: "medium",
            color: "grey",
            prominence: false,
            onClick: () => console.log("Audit")
          },
          {
            label: "Manage",
            leftIcon: <ManageAccountsIcon />,
            size: "medium",
            color: "grey",
            prominence: false,
            onClick: () => {
              console.log('Manage button clicked');
              setIsManageModalOpen(true);
            }
          }
        ]}
      />
      <ExportModal
        isOpen={isExportOpen}
        onClose={() => {
          console.log('Export modal closed');
          setIsExportOpen(false);
        }}
        onExport={(options) => {
          console.log('Export modal triggered export with options:', options);
          handleExport(options);
        }}
      />
      {isManageModalOpen && (
        <ManageModal
          onClose={() => setIsManageModalOpen(false)}
          objectId={caseData.id || ''}
          objectType="case"
          showAssignTab={false}
          showDetailsTab={false}
          objectName="Case"
        />
      )}
    </>
  );
} 