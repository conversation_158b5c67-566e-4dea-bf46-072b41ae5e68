/**
 * @fileoverview useCaseFileUpload Hook - Custom hook for managing case file attachments
 * 
 * This custom hook encapsulates all the logic for managing file attachments in case timeline entries.
 * It provides a complete interface for file staging, validation, upload, and state management.
 * 
 * Core Responsibilities:
 * - File staging and local preview management
 * - File validation (size, count, type)
 * - Upload orchestration with progress tracking
 * - Drag and drop interaction handling
 * - Error management and user feedback
 * - Integration with filerepository service
 * 
 * State Management:
 * - Maintains array of staged files with metadata
 * - Tracks upload progress and errors
 * - Manages drag and drop UI states
 * - Handles file preview URL lifecycle
 * 
 * Upload Process:
 * 1. Files are added and validated
 * 2. Preview URLs are generated for supported types
 * 3. Files are staged with editable metadata
 * 4. On upload trigger, files are sent to filerepository
 * 5. Progress is tracked and CaseFileReference objects returned
 * 6. Cleanup is performed for preview URLs
 * 
 * Integration:
 * - Used by CaseFileAttachments component
 * - Integrates with filerepository service hooks
 * - Returns protobuf-compatible CaseFileReference objects
 * - Supports both individual and batch file operations
 * 
 * @hook
 * @example
 * // Basic usage in component
 * const {
 *   stagedFiles,
 *   addFiles,
 *   uploadAllFiles,
 *   hasFiles,
 *   dragHandlers
 * } = useCaseFileUpload({
 *   caseId: 'case-123',
 *   maxFiles: 10,
 *   maxSizeMB: 250
 * });
 * 
 */

import { useFileUpload } from '@/app/apis/services/filerepository/hooks';
import { create } from '@bufbuild/protobuf';
import { CaseFileReferenceSchema, type CaseFileReference } from 'proto/hero/cases/v1/cases_pb';
import { useCallback, useState } from 'react';
import { CaseStagedFile } from '../types';
import { cleanupPreviewUrls, createStagedFile, validateFile } from '../utils/fileHelpers';

/**
 * Props interface for the useCaseFileUpload hook
 * 
 * @interface UseCaseFileUploadProps
 */
interface UseCaseFileUploadProps {
    /** The case ID that files will be associated with */
    caseId: string;

    /** Maximum number of files allowed (default: 10) */
    maxFiles?: number;

    /** Maximum file size in MB (default: 250) */
    maxSizeMB?: number;
}

/**
 * Custom hook for managing case file attachments
 * 
 * This hook provides a complete file management system for case timeline entries.
 * It handles the entire lifecycle from file selection through upload completion,
 * including validation, progress tracking, and error handling.
 * 
 * Key Features:
 * - File staging with local preview generation
 * - Comprehensive validation (size, count, type)
 * - Upload progress tracking with speed calculations
 * - Drag and drop support with visual feedback
 * - Error handling with user-friendly messages
 * - Memory management for preview URLs
 * - Integration with filerepository service
 * 
 * File Lifecycle:
 * 1. File Selection: Files are added via drag-drop or file picker
 * 2. Validation: Size and count limits are enforced
 * 3. Staging: Files are stored locally with preview URLs
 * 4. Metadata Editing: Users can edit names, captions, categories
 * 5. Upload: Files are uploaded to filerepository service
 * 6. Progress Tracking: Real-time upload progress and speed
 * 7. Completion: CaseFileReference objects are returned
 * 8. Cleanup: Preview URLs are revoked to prevent memory leaks
 * 
 * State Management:
 * - stagedFiles: Array of files with metadata and upload state
 * - uploadErrors: Array of error messages for user feedback
 * - isDragging: Boolean for drag-drop visual feedback
 * 
 * Error Handling:
 * - File size validation with configurable limits
 * - File count validation with user feedback
 * - Upload failure recovery with detailed error messages
 * - Network error handling with retry capabilities
 * 
 * @param props - Configuration options for file management
 * @returns Object containing file management state and methods
 */
export const useCaseFileUpload = ({
    caseId,
    maxFiles = 10,
    maxSizeMB = 250
}: UseCaseFileUploadProps) => {
    // Core state for file management
    const [stagedFiles, setStagedFiles] = useState<CaseStagedFile[]>([]);
    const [isDragging, setIsDragging] = useState(false);
    const [uploadErrors, setUploadErrors] = useState<string[]>([]);

    // Hook for uploading files to filerepository service
    const { uploadFile } = useFileUpload();

    /**
     * Adds files to the staged list with comprehensive validation
     * 
     * This function handles the initial file processing when users select
     * or drop files. It performs validation, creates preview URLs, and
     * adds files to the staging area for metadata editing.
     * 
     * Validation Process:
     * 1. Check total file count against maxFiles limit
     * 2. Validate each file size against maxSizeMB limit
     * 3. Create preview URLs for supported file types
     * 4. Generate unique IDs for file tracking
     * 5. Initialize metadata fields for user editing
     * 
     * Error Handling:
     * - Collects all validation errors for batch display
     * - Continues processing valid files even if some fail
     * - Provides specific error messages for each failure type
     * 
     * @param files - FileList or File array from input/drop events
     */
    const addFiles = useCallback((files: FileList | File[]) => {
        // Immediate file count validation using files.length directly (fastest)
        if (stagedFiles.length + files.length > maxFiles) {
            setUploadErrors([`Maximum ${maxFiles} files allowed`]);
            return;
        }

        // Clear any previous errors immediately
        setUploadErrors([]);

        // Process files asynchronously to avoid blocking UI
        setTimeout(() => {
            const fileArray = Array.from(files);
            const newFiles: CaseStagedFile[] = [];
            const errors: string[] = [];

            // Process each file with individual validation
            fileArray.forEach(file => {
                // Quick size validation first before creating staged file
                const validation = validateFile(file, maxSizeMB);
                if (validation.valid) {
                    newFiles.push(createStagedFile(file));
                } else {
                    errors.push(`${file.name}: ${validation.error}`);
                }
            });

            // Update error state and add valid files
            if (errors.length > 0) {
                setUploadErrors(errors);
            }

            if (newFiles.length > 0) {
                setStagedFiles(prev => [...prev, ...newFiles]);
            }
        }, 0);
    }, [stagedFiles.length, maxFiles, maxSizeMB]);

    /**
     * Removes a file from the staged list with cleanup
     * 
     * This function removes a file from staging and properly cleans up
     * any associated preview URLs to prevent memory leaks.
     * 
     * Cleanup Process:
     * 1. Find the file to remove by ID
     * 2. Revoke any preview URLs to free memory
     * 3. Remove file from staged files array
     * 4. Update component state
     * 
     * @param id - Unique identifier of the file to remove
     */
    const removeFile = useCallback((id: string) => {
        setStagedFiles(prev => {
            const fileToRemove = prev.find(fileData => fileData.id === id);
            if (fileToRemove?.preview) {
                URL.revokeObjectURL(fileToRemove.preview);
            }
            return prev.filter(fileData => fileData.id !== id);
        });
    }, []);

    /**
     * Updates metadata for a staged file
     * 
     * This function allows users to edit file metadata (display name,
     * caption, category) before upload. The metadata is included in
     * the final CaseFileReference objects.
     * 
     * Supported Fields:
     * - displayName: User-friendly name for the file
     * - caption: Descriptive text about the file content
     * - fileCategory: Categorization tag for organization
     * 
     * @param id - File ID to update
     * @param field - Metadata field to update
     * @param value - New value for the field
     */
    const updateFileMetadata = useCallback((
        id: string,
        field: keyof Pick<CaseStagedFile, 'displayName' | 'caption' | 'fileCategory'>,
        value: string
    ) => {
        setStagedFiles(prev => prev.map(file =>
            file.id === id ? { ...file, [field]: value } : file
        ));
    }, []);

    /**
     * Uploads all staged files and returns CaseFileReference objects
     * 
     * This is the main upload function that processes all staged files,
     * uploads them to the filerepository service, and returns protobuf-compatible
     * CaseFileReference objects for inclusion in case updates.
     * 
     * Upload Process:
     * 1. Filter files that haven't been uploaded yet
     * 2. Set uploading state for UI feedback
     * 3. Upload files sequentially to avoid overwhelming server
     * 4. Track progress and update file states
     * 5. Create CaseFileReference objects with metadata
     * 6. Handle errors and provide user feedback
     * 7. Return array of file references for case update
     * 
     * Progress Tracking:
     * - Individual file upload progress
     * - Overall upload completion percentage
     * - Upload speed calculations
     * - Estimated time remaining
     * 
     * Error Recovery:
     * - Continue uploading other files if one fails
     * - Mark failed files with error messages
     * - Collect all errors for user review
     * - Allow retry of failed uploads
     * 
     * @returns Promise resolving to array of CaseFileReference objects
     */
    const uploadAllFiles = useCallback(async (): Promise<CaseFileReference[]> => {
        const filesToUpload = stagedFiles.filter(fileData => !fileData.fileId && !fileData.isUploading);
        if (filesToUpload.length === 0) {
            // Return already uploaded files as CaseFileReference objects
            return stagedFiles
                .filter(fileData => fileData.fileId)
                .map((file, index) => create(CaseFileReferenceSchema, {
                    id: '',
                    caseId,
                    fileId: file.fileId!,
                    caption: file.caption,
                    displayName: file.displayName,
                    displayOrder: index,
                    fileCategory: file.fileCategory,
                    metadata: {
                        originalFilename: file.file.name,
                        fileSize: file.file.size,
                        fileType: file.file.type,
                    },
                }));
        }

        // Set uploading state for UI feedback
        setStagedFiles(prev => prev.map(file =>
            filesToUpload.some(fileData => fileData.id === file.id)
                ? { ...file, isUploading: true, uploadProgress: 0 }
                : file
        ));

        const uploadResults: CaseFileReference[] = [];
        const errors: string[] = [];

        // Upload files sequentially to avoid overwhelming the server
        for (const file of filesToUpload) {
            try {
                const result = await uploadFile(file.file, undefined, {
                    category: 'case-attachment',
                    uploadSource: 'case-timeline-entry',
                    originalSize: file.file.size
                });

                // Update file state with successful upload
                setStagedFiles(prev => prev.map(fileData =>
                    fileData.id === file.id
                        ? { ...fileData, fileId: result.fileId, isUploading: false, uploadProgress: 100 }
                        : fileData
                ));

                // Create CaseFileReference for protobuf integration
                const caseFileRef: CaseFileReference = create(CaseFileReferenceSchema, {
                    id: '',
                    caseId,
                    fileId: result.fileId,
                    caption: file.caption,
                    displayName: file.displayName,
                    displayOrder: uploadResults.length,
                    fileCategory: file.fileCategory,
                    metadata: {
                        originalFilename: file.file.name,
                        fileSize: file.file.size,
                        fileType: file.file.type,
                    },
                });

                uploadResults.push(caseFileRef);
            } catch (error) {
                const errorMessage = error instanceof Error ? error.message : 'Upload failed';
                errors.push(`${file.file.name}: ${errorMessage}`);

                // Update file state with error
                setStagedFiles(prev => prev.map(fileData =>
                    fileData.id === file.id
                        ? { ...fileData, isUploading: false, uploadError: errorMessage }
                        : fileData
                ));
            }
        }

        // Update error state for user feedback
        if (errors.length > 0) {
            setUploadErrors(errors);
        }

        return uploadResults;
    }, [stagedFiles, caseId, uploadFile]);

    /**
     * Clears all files and resets component state
     * 
     * This function provides a way to reset the component to its initial state,
     * properly cleaning up all preview URLs and clearing all state.
     * 
     * Cleanup Process:
     * 1. Revoke all preview URLs to prevent memory leaks
     * 2. Clear staged files array
     * 3. Clear error messages
     * 4. Reset drag state
     * 
     * Use Cases:
     * - Modal close/cancel actions
     * - Starting fresh after successful upload
     * - Error recovery scenarios
     */
    const clearAll = useCallback(() => {
        cleanupPreviewUrls(stagedFiles);
        setStagedFiles([]);
        setUploadErrors([]);
        setIsDragging(false);
    }, [stagedFiles]);

    /**
     * Drag and drop event handlers
     * 
     * These handlers provide a complete drag-and-drop interface for file selection.
     * They manage visual feedback and file processing when users drag files over
     * and drop them on the upload zone.
     * 
     * Event Handling:
     * - onDragEnter: Initialize drag state and visual feedback
     * - onDragOver: Maintain drag state during hover
     * - onDragLeave: Clear drag state when leaving drop zone
     * - onDrop: Process dropped files and reset drag state
     * 
     * Visual Feedback:
     * - isDragging state controls UI appearance
     * - Upload zone changes style during drag operations
     * - Clear visual indicators for valid drop zones
     */
    const dragHandlers = {
        onDragEnter: useCallback((event: React.DragEvent) => {
            event.preventDefault();
            event.stopPropagation();
            setIsDragging(true);
        }, []),

        onDragOver: useCallback((event: React.DragEvent) => {
            event.preventDefault();
            event.stopPropagation();
            setIsDragging(true);
        }, []),

        onDragLeave: useCallback((event: React.DragEvent) => {
            event.preventDefault();
            event.stopPropagation();
            setIsDragging(false);
        }, []),

        onDrop: useCallback((event: React.DragEvent) => {
            event.preventDefault();
            event.stopPropagation();
            setIsDragging(false);
            if (event.dataTransfer.files) {
                addFiles(event.dataTransfer.files);
            }
        }, [addFiles])
    };

    // Return comprehensive interface for file management
    return {
        // Core State
        /** Array of staged files with metadata and upload status */
        stagedFiles,
        /** Boolean indicating if drag operation is in progress */
        isDragging,
        /** Array of error messages for user feedback */
        uploadErrors,

        // File Management Actions
        /** Add files to staging with validation */
        addFiles,
        /** Remove a file from staging with cleanup */
        removeFile,
        /** Update metadata for a staged file */
        updateFileMetadata,
        /** Upload all staged files and return CaseFileReference objects */
        uploadAllFiles,
        /** Clear all files and reset state */
        clearAll,

        // Drag and Drop Support
        /** Event handlers for drag and drop functionality */
        dragHandlers,

        // Computed State Values
        /** Whether any files are currently staged */
        hasFiles: stagedFiles.length > 0,
        /** Whether any validation errors exist */
        hasErrors: uploadErrors.length > 0,
        /** Whether any files are currently uploading */
        isUploading: stagedFiles.some(fileData => fileData.isUploading),
        /** Whether there are files ready to upload */
        canUpload: stagedFiles.some(fileData => !fileData.fileId && !fileData.isUploading),
    };
}; 