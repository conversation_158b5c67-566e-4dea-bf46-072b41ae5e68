/**
 * @fileoverview Type Definitions - TypeScript interfaces for case file attachment system
 * 
 * This module defines all the TypeScript interfaces and types used throughout
 * the case file attachment system. These types ensure type safety and provide
 * clear contracts between components.
 * 
 * Core Types:
 * - CaseStagedFile: Represents files in the staging area before upload
 * - Component Props: Interfaces for component properties
 * - Upload Results: Types for upload operation results
 * 
 * Type Safety:
 * - Ensures consistent data structures across components
 * - Provides IDE autocomplete and error checking
 * - Documents expected data shapes and requirements
 * - Enables safe refactoring and maintenance
 * 
 * Integration:
 * - Used by all file attachment components
 * - Integrates with protobuf types for API communication
 * - Supports both upload and display functionality
 * - Provides extension points for future features
 * 
 * @module types
 */

/**
 * Represents a file in the staging area before upload
 * 
 * This interface defines the structure for files that are selected by users
 * but not yet uploaded to the server. It includes the original File object,
 * user-editable metadata, upload state, and progress tracking information.
 * 
 * Lifecycle States:
 * - Initial: File selected, metadata can be edited
 * - Uploading: File being uploaded, progress tracked
 * - Completed: File uploaded successfully, fileId available
 * - Failed: Upload failed, error message available
 * 
 * Progress Tracking:
 * - Supports real-time upload progress monitoring
 * - Includes speed calculations and time estimates
 * - Enables smooth progress UI transitions
 * 
 * Memory Management:
 * - preview URLs must be cleaned up to prevent leaks
 * - File objects are references to browser File API
 * 
 * @interface CaseStagedFile
 */
export interface CaseStagedFile {
    /** Unique identifier for tracking the file through the system */
    id: string;

    /** Original browser File object from input/drop event */
    file: File;

    /** Blob URL for preview (images/videos only), must be cleaned up */
    preview?: string;

    /** User-editable display name for the file */
    displayName: string;

    /** User-editable descriptive caption for the file */
    caption: string;

    /** User-editable category tag for file organization */
    fileCategory: string;

    /** File ID from filerepository service after successful upload */
    fileId?: string;

    /** Whether the file is currently being uploaded */
    isUploading: boolean;

    /** Error message if upload failed */
    uploadError?: string;

    /** Upload progress percentage (0-100) */
    uploadProgress?: number;

    /** Number of bytes uploaded so far (for progress calculation) */
    bytesUploaded?: number;

    /** Current upload speed in bytes per second */
    uploadSpeed?: number;

    /** Timestamp when upload started (for speed/time calculations) */
    uploadStartTime?: number;

    /** Estimated time remaining for upload completion in seconds */
    timeRemaining?: number;
}

/**
 * Result object returned from file upload operations
 * 
 * This interface defines the structure for upload results, providing
 * success/failure status and relevant data or error information.
 * 
 * Success Case:
 * - success: true
 * - fileId: ID from filerepository service
 * - error: undefined
 * 
 * Failure Case:
 * - success: false
 * - fileId: undefined
 * - error: User-friendly error message
 * 
 * @interface CaseFileUploadResult
 */
export interface CaseFileUploadResult {
    /** Whether the upload operation succeeded */
    success: boolean;

    /** File ID from filerepository service (success case) */
    fileId?: string;

    /** Error message for user display (failure case) */
    error?: string;
}

/**
 * Props for the main CaseFileAttachments component
 * 
 * This interface defines the configuration options for the primary
 * file attachment component used in case timeline entry modals.
 * 
 * Configuration Options:
 * - caseId: Required for associating files with cases
 * - readOnly: Optional for display-only mode
 * - maxFiles: Optional limit on number of files
 * 
 * @interface CaseFileAttachmentProps
 */
export interface CaseFileAttachmentProps {
    /** The case ID that files will be associated with */
    caseId: string;

    /** Whether the component is in read-only mode (default: false) */
    readOnly?: boolean;

    /** Maximum number of files allowed (default: 10) */
    maxFiles?: number;
}

/**
 * Props for the CaseFilePreview component
 * 
 * This interface defines the properties for individual file preview
 * components that show file details and allow metadata editing.
 * 
 * Event Handlers:
 * - All handlers are optional for read-only mode
 * - Provide callbacks for user interactions
 * - Enable parent components to manage state
 * 
 * @interface CaseFilePreviewProps
 */
export interface CaseFilePreviewProps {
    /** The staged file to preview */
    file: CaseStagedFile;

    /** Whether the preview is in read-only mode */
    readOnly?: boolean;

    /** Callback when user requests file removal */
    onRemove?: (id: string) => void;

    /** Callback when user changes the display name */
    onDisplayNameChange?: (id: string, displayName: string) => void;

    /** Callback when user changes the caption */
    onCaptionChange?: (id: string, caption: string) => void;

    /** Callback when user changes the category */
    onCategoryChange?: (id: string, category: string) => void;
}

/**
 * Props for the CaseFileUploadZone component
 * 
 * This interface defines the properties for the drag-and-drop upload zone
 * component that handles initial file selection.
 * 
 * State Management:
 * - onFilesAdded: Required callback for file processing
 * - disabled: Optional for preventing uploads
 * - isDragging: Optional for visual feedback
 * 
 * @interface CaseFileUploadZoneProps
 */
export interface CaseFileUploadZoneProps {
    /** Callback when files are added via drag-drop or file picker */
    onFilesAdded: (files: FileList) => void;

    /** Whether the upload zone is disabled */
    disabled?: boolean;

    /** Whether a drag operation is currently in progress */
    isDragging?: boolean;

    /** Maximum number of files allowed (for display purposes) */
    maxFiles?: number;
} 