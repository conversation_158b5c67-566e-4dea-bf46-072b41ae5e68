/**
 * @fileoverview CaseFileAttachmentDisplay Component - Display file attachments in case timeline
 * 
 * This component provides a read-only display of file attachments within case timeline entries.
 * It renders files in a responsive side-by-side layout with download and view functionality.
 * 
 * Key Features:
 * - Displays multiple file attachments in a flex layout that wraps to new lines
 * - Shows appropriate file type icons based on MIME type
 * - Clickable file names that open files in new browser tabs
 * - Download buttons with loading states and progress indicators
 * - Displays file metadata (caption, category) when available
 * - Responsive design that adapts to container width
 * - Consistent styling with the Hero design system
 * 
 * File Operations:
 * - Uses presigned URLs from filerepository service for secure file access
 * - Handles both file viewing (new tab) and downloading (blob download)
 * - Provides proper error handling and user feedback
 * - Manages loading states during file operations
 * 
 * Layout Behavior:
 * - Files display side by side with minimum width of 220px, maximum 320px
 * - Wraps to new lines when container width is exceeded
 * - Each file card contains: icon, file info, and download button
 * - Hover effects provide visual feedback for interactive elements
 * 
 * Integration:
 * - Used within CaseTimelineCard to display attachments in timeline entries
 * - Follows the same patterns as MediaSection in reports for consistency
 * - Integrates with filerepository service hooks for file operations
 * 
 * @component
 * @example
 * // Basic usage in timeline entry
 * <CaseFileAttachmentDisplay 
 *   fileAttachments={update.fileAttachments}
 *   readOnly={true}
 * />
 * 
 */

import { useGetPresignedDownloadUrl } from "@/app/apis/services/filerepository/hooks";
import { Typography } from "@/design-system/components/Typography";
import { colors } from "@/design-system/tokens";
import DownloadIcon from "@mui/icons-material/Download";
import { Box, IconButton } from "@mui/material";
import { CaseFileReference } from "proto/hero/cases/v1/cases_pb";
import React, { useState } from "react";
import {
    FaFile,
    FaFileAlt,
    FaFileAudio,
    FaFileImage,
    FaFilePdf,
    FaFileVideo
} from "react-icons/fa";

/**
 * Props interface for the CaseFileAttachmentDisplay component
 * 
 * @interface CaseFileAttachmentDisplayProps
 */
interface CaseFileAttachmentDisplayProps {
    /** Array of file references to display - comes from CaseUpdateEntry.fileAttachments */
    fileAttachments: CaseFileReference[];

    /** Whether the component is in read-only mode (currently always true for timeline display) */
    readOnly?: boolean;
}

/**
 * CaseFileAttachmentDisplay Component
 * 
 * A specialized component for displaying file attachments within case timeline entries.
 * This component provides a clean, responsive layout for viewing and downloading files
 * that have been attached to case updates.
 * 
 * The component handles the complete file viewing workflow:
 * 1. Displays files in a responsive grid layout
 * 2. Shows appropriate icons based on file MIME types
 * 3. Provides click-to-view functionality (opens in new tab)
 * 4. Offers dedicated download buttons with loading states
 * 5. Displays file metadata (names, captions, categories)
 * 
 * File Type Support:
 * - Images: Shows image icon, supports preview in new tab
 * - Videos: Shows video icon, supports playback in new tab
 * - Audio: Shows audio icon, supports playback in new tab
 * - PDFs: Shows PDF icon, supports viewing in new tab
 * - Documents: Shows document icon, supports viewing in new tab
 * - Other: Shows generic file icon, supports download
 * 
 * Security:
 * - All file access uses presigned URLs with 5-minute expiration
 * - No direct file URLs are exposed to the client
 * - Proper error handling for failed file operations
 * 
 * @param props - The props object containing file attachments and configuration
 * @returns A rendered component displaying file attachments in a responsive layout
 */
const CaseFileAttachmentDisplay: React.FC<CaseFileAttachmentDisplayProps> = ({
    fileAttachments,
    readOnly = false,
}) => {
    // Track which file is currently being downloaded/opened to show loading state
    const [downloadingFileId, setDownloadingFileId] = useState<string | null>(null);

    // Hook for generating presigned download URLs from filerepository service
    const getDownloadUrlMutation = useGetPresignedDownloadUrl({
        onSuccess: () => {
            console.log("Download URL generated successfully");
        },
        onError: (error) => {
            console.error("Error generating download URL:", error);
        },
    });

    /**
     * Determines the appropriate file icon based on MIME type
     * 
     * This function analyzes the file metadata to determine the file type
     * and returns the corresponding React icon component. It provides
     * fallback handling for unknown or missing MIME types.
     * 
     * Supported Types:
     * - image/*: Blue image icon
     * - video/*: Blue video icon  
     * - audio/*: Blue audio icon
     * - application/pdf: Blue PDF icon
     * - text/* or document types: Blue document icon
     * - Unknown/other: Gray generic file icon
     * 
     * @param metadata - File metadata object containing type information from filerepository
     * @returns JSX element representing the file type icon
     */
    const getFileIcon = (metadata?: any) => {
        const fileType = (typeof metadata?.fileType === 'string' ? metadata.fileType.toLowerCase() : '') || '';

        // Detection based ONLY on MIME type for reliability
        if (fileType.startsWith('image/')) {
            return <FaFileImage style={{ fontSize: 16, color: colors.blue[600] }} />;
        }
        if (fileType.startsWith('video/')) {
            return <FaFileVideo style={{ fontSize: 16, color: colors.blue[600] }} />;
        }
        if (fileType.startsWith('audio/')) {
            return <FaFileAudio style={{ fontSize: 16, color: colors.blue[600] }} />;
        }
        if (fileType === 'application/pdf') {
            return <FaFilePdf style={{ fontSize: 16, color: colors.blue[600] }} />;
        }
        if (fileType.includes('text/') || fileType.includes('document') || fileType.includes('word')) {
            return <FaFileAlt style={{ fontSize: 16, color: colors.blue[600] }} />;
        }

        // Default icon if MIME type is unknown or missing
        return <FaFile style={{ fontSize: 16, color: colors.grey[500] }} />;
    };

    /**
     * Handles file downloads with presigned URLs
     * 
     * This function generates a secure presigned URL, fetches the file content,
     * and triggers a browser download with the correct filename. It includes
     * proper error handling and loading states.
     * 
     * Download Process:
     * 1. Set loading state for the specific file
     * 2. Request presigned URL from filerepository service (5 min expiration)
     * 3. Fetch file content using the presigned URL
     * 4. Create blob URL and trigger browser download
     * 5. Clean up blob URL to prevent memory leaks
     * 6. Clear loading state
     * 
     * Error Handling:
     * - Network errors during URL generation
     * - File fetch failures
     * - User-friendly error messages via alerts
     * 
     * @param fileId - The unique identifier of the file in the repository
     * @param fileName - The display name for the downloaded file
     * @param fileRefId - The reference ID for tracking the file in the UI
     */
    const handleDownloadFile = async (fileId: string, fileName: string, fileRefId: string) => {
        try {
            setDownloadingFileId(fileRefId);
            console.log('Downloading file:', fileId, fileName);

            // Get presigned download URL with 5-minute expiration for security
            const response = await getDownloadUrlMutation.mutateAsync({
                id: fileId,
                expiresIn: 300 // 5 minutes
            } as any);

            // Fetch the file content and create a blob URL to force download
            const fileResponse = await fetch(response.presignedUrl);
            const blob = await fileResponse.blob();

            // Create a blob URL and trigger download with correct filename
            const blobUrl = URL.createObjectURL(blob);
            const link = document.createElement('a');
            link.href = blobUrl;
            link.download = fileName; // This forces download with the correct filename
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);

            // Clean up the blob URL to free memory
            URL.revokeObjectURL(blobUrl);

            console.log('File download initiated successfully');
        } catch (error) {
            console.error('Download failed:', error);
            alert('Failed to download file. Please try again.');
        } finally {
            setDownloadingFileId(null);
        }
    };

    /**
     * Handles opening files in new browser tabs
     * 
     * This function generates a presigned URL and opens the file in a new tab,
     * allowing users to view files without downloading them. This is the default
     * behavior when clicking on file names.
     * 
     * View Process:
     * 1. Set loading state for the specific file
     * 2. Request presigned URL from filerepository service (5 min expiration)
     * 3. Open the presigned URL in a new browser tab
     * 4. Clear loading state
     * 
     * Browser Behavior:
     * - Images: Display in browser image viewer
     * - PDFs: Open in browser PDF viewer
     * - Videos/Audio: Play in browser media player
     * - Documents: Browser will handle based on type/plugins
     * - Unknown types: Browser will prompt for download
     * 
     * @param fileId - The unique identifier of the file in the repository
     * @param fileName - The display name of the file (for logging)
     * @param fileRefId - The reference ID for tracking the file in the UI
     */
    const handleOpenFile = async (fileId: string, fileName: string, fileRefId: string) => {
        try {
            setDownloadingFileId(fileRefId);
            console.log('Opening file in new tab:', fileId, fileName);

            // Get presigned download URL with 5-minute expiration
            const response = await getDownloadUrlMutation.mutateAsync({
                id: fileId,
                expiresIn: 300 // 5 minutes
            } as any);

            // Open the file in a new tab (default behavior for file names)
            window.open(response.presignedUrl, '_blank');

            console.log('File opened in new tab successfully');
        } catch (error) {
            console.error('Failed to open file:', error);
            alert('Failed to open file. Please try again.');
        } finally {
            setDownloadingFileId(null);
        }
    };

    // Early return if no files to display
    if (!fileAttachments || fileAttachments.length === 0) {
        return null;
    }

    return (
        <Box
            sx={{
                mt: 1,
                display: "flex",
                flexWrap: "wrap", // Allow files to wrap to new lines
                gap: 1, // Consistent spacing between file cards
            }}
        >
            {fileAttachments.map((fileRef, index) => {
                const fileName = fileRef.displayName || 'Untitled';
                const isDownloading = downloadingFileId === fileRef.id;

                return (
                    <Box
                        key={fileRef.id || index}
                        sx={{
                            display: "flex",
                            alignItems: "center",
                            gap: 1,
                            p: 1.5,
                            border: `1px solid ${colors.grey[200]}`,
                            borderRadius: 1,
                            backgroundColor: colors.grey[50],
                            // Responsive sizing: min 220px, max 320px, grows to fill space
                            minWidth: "220px",
                            maxWidth: "320px",
                            flex: "1 1 auto",
                            '&:hover': {
                                backgroundColor: colors.grey[100],
                            },
                        }}
                    >
                        {/* File Type Icon */}
                        <Box
                            sx={{
                                display: "flex",
                                alignItems: "center",
                                justifyContent: "center",
                                minWidth: 24,
                                height: 24,
                            }}
                        >
                            {getFileIcon(fileRef.metadata)}
                        </Box>

                        {/* File Information - Clickable to open in new tab */}
                        <Box
                            onClick={() => handleOpenFile(fileRef.fileId, fileName, fileRef.id)}
                            sx={{
                                flex: 1,
                                cursor: isDownloading ? 'wait' : 'pointer',
                                display: 'flex',
                                flexDirection: 'column',
                                gap: 0.25,
                                minWidth: 0, // Allow text to shrink and wrap properly
                                '&:hover .file-name': {
                                    textDecoration: 'underline',
                                },
                            }}
                        >
                            {/* File Name - Primary clickable element */}
                            <Box
                                className="file-name"
                                sx={{
                                    wordBreak: "break-word", // Handle long filenames gracefully
                                    lineHeight: 1.3,
                                }}
                            >
                                <Typography
                                    style="body3"
                                    color={isDownloading ? colors.blue[300] : colors.blue[600]}
                                >
                                    {fileName}
                                </Typography>
                            </Box>

                            {/* File Caption - Optional descriptive text */}
                            {fileRef.caption && (
                                <Typography
                                    style="tag2"
                                    color={colors.grey[600]}
                                >
                                    {fileRef.caption}
                                </Typography>
                            )}

                            {/* File Category - Optional categorization tag */}
                            {fileRef.fileCategory && (
                                <Box
                                    sx={{
                                        display: "inline-flex",
                                        px: 1,
                                        py: 0.25,
                                        borderRadius: "8px",
                                        backgroundColor: colors.grey[200],
                                        alignSelf: "flex-start",
                                    }}
                                >
                                    <Typography style="tag2" color={colors.grey[700]}>
                                        {fileRef.fileCategory}
                                    </Typography>
                                </Box>
                            )}
                        </Box>

                        {/* Download Button - Dedicated download action */}
                        <IconButton
                            onClick={(e) => {
                                e.stopPropagation(); // Prevent triggering the file open action
                                handleDownloadFile(fileRef.fileId, fileName, fileRef.id);
                            }}
                            size="small"
                            disabled={isDownloading}
                            sx={{
                                color: isDownloading ? colors.grey[300] : colors.blue[600],
                                "&:hover": {
                                    color: isDownloading ? colors.grey[300] : colors.blue[700],
                                    backgroundColor: isDownloading ? 'transparent' : colors.blue[50],
                                },
                            }}
                            aria-label={`Download ${fileName}`}
                        >
                            {/* Show loading spinner during download, download icon otherwise */}
                            {isDownloading ? (
                                <Box
                                    sx={{
                                        width: 16,
                                        height: 16,
                                        border: `2px solid ${colors.blue[200]}`,
                                        borderTop: `2px solid ${colors.blue[600]}`,
                                        borderRadius: '50%',
                                        animation: 'spin 1s linear infinite',
                                        '@keyframes spin': {
                                            '0%': { transform: 'rotate(0deg)' },
                                            '100%': { transform: 'rotate(360deg)' },
                                        },
                                    }}
                                />
                            ) : (
                                <DownloadIcon sx={{ fontSize: 16 }} />
                            )}
                        </IconButton>
                    </Box>
                );
            })}
        </Box>
    );
};

export default CaseFileAttachmentDisplay; 