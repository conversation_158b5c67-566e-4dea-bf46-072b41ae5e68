/**
 * @fileoverview CaseFileAttachments Component - Main file attachment management for case timeline entries
 * 
 * This is the primary component for managing file attachments within case timeline entry modals.
 * It provides a complete interface for uploading, previewing, and managing files before they
 * are attached to case updates.
 * 
 * Core Functionality:
 * - File upload with drag-and-drop support
 * - File preview and metadata editing
 * - Upload progress tracking with smooth transitions
 * - File validation and error handling
 * - Integration with case timeline entry creation
 * 
 * Component Architecture:
 * - Uses forwardRef to expose upload methods to parent components
 * - Integrates multiple sub-components for modular functionality
 * - Manages complex state for upload progress and file staging
 * - Provides seamless transitions between upload zones and progress dashboards
 * 
 * Upload Workflow:
 * 1. Files are staged locally with metadata editing
 * 2. On modal submit, parent calls uploadAllFiles() via ref
 * 3. Files are uploaded to filerepository service
 * 4. CaseFileReference objects are returned for case update creation
 * 5. Progress is tracked and displayed with smooth animations
 * 
 * State Management:
 * - Staged files with upload progress tracking
 * - Upload errors and validation messages
 * - Drag and drop interaction states
 * - Overall upload progress calculations
 * 
 * Integration Points:
 * - Used within CaseTimelineEntryModal for file attachment
 * - Exposes uploadAllFiles method via ref for parent control
 * - Returns CaseFileReference objects for protobuf integration
 * - Uses filerepository service hooks for actual uploads
 * 
 * @component
 * @example
 * // Usage in modal with ref for upload control
 * const fileAttachmentsRef = useRef<CaseFileAttachmentsRef>(null);
 * 
 * const handleSubmit = async () => {
 *   if (fileAttachmentsRef.current?.hasFiles) {
 *     const fileRefs = await fileAttachmentsRef.current.uploadAllFiles();
 *     // Use fileRefs in case update creation
 *   }
 * };
 * 
 * <CaseFileAttachments
 *   ref={fileAttachmentsRef}
 *   caseId={caseId}
 *   maxFiles={10}
 * />
 */

import { UploadProgressDashboard } from '@/app/reports/ReportComponents/panels/uiComponents/MediaPanel';
import { Typography } from '@/design-system/components/Typography';
import { colors } from '@/design-system/tokens';
import { Box } from '@mui/material';
import { CaseFileReference } from 'proto/hero/cases/v1/cases_pb';
import { forwardRef, useEffect, useImperativeHandle, useState } from 'react';
import { useCaseFileUpload } from '../hooks/useCaseFileUpload';
import { CaseFileAttachmentProps } from '../types';
import { CaseFileUploadZone } from './CaseFileUploadZone';
import { CaseFilesList } from './CaseFilesList';

/**
 * Ref interface for parent components to control file uploads
 * 
 * This interface defines the methods that parent components can call
 * on the CaseFileAttachments component via ref.
 * 
 * @interface CaseFileAttachmentsRef
 */
export interface CaseFileAttachmentsRef {
    /** 
     * Uploads all staged files and returns CaseFileReference objects
     * @returns Promise resolving to array of file references for case update
     */
    uploadAllFiles: () => Promise<CaseFileReference[]>;

    /** 
     * Whether the component currently has staged files
     * @returns Boolean indicating if files are present
     */
    hasFiles: boolean;
}

/**
 * CaseFileAttachments Component
 * 
 * The main component for file attachment management in case timeline entries.
 * This component orchestrates the entire file upload workflow from initial
 * file selection through upload completion and error handling.
 * 
 * Key Features:
 * - Drag and drop file upload zone
 * - File preview with editable metadata (name, caption, category)
 * - Real-time upload progress with smooth UI transitions
 * - File validation and error reporting
 * - Seamless integration with case timeline entry creation
 * 
 * Upload Progress Transitions:
 * 1. Initial state: Shows upload zone for file selection
 * 2. Files staged: Shows file list with metadata editing
 * 3. Upload initiated: Transitions to progress dashboard
 * 4. Upload complete: Returns to file list or hides component
 * 
 * Parent Integration:
 * - Exposes uploadAllFiles() method via forwardRef
 * - Parent controls when uploads are initiated (typically on form submit)
 * - Returns CaseFileReference objects for inclusion in case updates
 * 
 * Error Handling:
 * - File size validation (max 250MB per file)
 * - File count limits (configurable, default 10)
 * - Upload failure recovery with retry options
 * - User-friendly error messages and validation feedback
 * 
 * @param props - Component props including caseId and configuration
 * @param ref - Forwarded ref exposing upload control methods
 * @returns Rendered file attachment management interface
 */
export const CaseFileAttachments = forwardRef<CaseFileAttachmentsRef, CaseFileAttachmentProps>(({
    caseId,
    readOnly = false,
    maxFiles = 10,
}, ref) => {
    // Extract file upload functionality from custom hook
    const {
        stagedFiles,
        uploadErrors,
        addFiles,
        removeFile,
        updateFileMetadata,
        uploadAllFiles,
        isUploading,
        hasFiles,
        isDragging,
        dragHandlers,
    } = useCaseFileUpload({
        caseId,
        maxFiles,
    });

    // Upload progress state for smooth transitions between UI states
    const [overallProgress, setOverallProgress] = useState<number>(0);
    const [uploadingSummary, setUploadingSummary] = useState<{
        totalFiles: number;
        completedFiles: number;
        failedFiles: number;
        totalBytes: number;
        uploadedBytes: number;
        averageSpeed: number;
        estimatedTimeRemaining: number;
        isComplete?: boolean;
    } | null>(null);

    /**
     * Expose upload control methods to parent via ref
     * 
     * This allows parent components (like CaseTimelineEntryModal) to:
     * - Check if files are present before attempting upload
     * - Trigger uploads at the appropriate time (form submission)
     * - Receive file references for case update creation
     */
    useImperativeHandle(ref, () => ({
        uploadAllFiles,
        hasFiles,
    }), [uploadAllFiles, hasFiles]);

    /**
     * Calculate and update overall upload progress
     * 
     * This effect monitors staged files and calculates comprehensive
     * upload statistics for the progress dashboard display.
     * 
     * Progress Calculation:
     * - Tracks bytes uploaded vs total bytes across all files
     * - Calculates average upload speed from active uploads
     * - Estimates time remaining based on current speed
     * - Determines completion status for UI transitions
     */
    useEffect(() => {
        const updateOverallProgress = () => {
            const uploadingFiles = stagedFiles.filter(fileData => fileData.isUploading || fileData.uploadProgress !== undefined);
            if (uploadingFiles.length === 0) {
                setOverallProgress(0);
                setUploadingSummary(null);
                return;
            }

            const totalFiles = uploadingFiles.length;
            const completedFiles = stagedFiles.filter(fileData => fileData.fileId && !fileData.isUploading).length;
            const failedFiles = stagedFiles.filter(fileData => fileData.uploadError).length;

            const totalBytes = uploadingFiles.reduce((sum, file) => sum + (file.file?.size || 0), 0);
            const uploadedBytes = uploadingFiles.reduce((sum, file) => sum + (file.bytesUploaded || 0), 0);

            const activeUploads = uploadingFiles.filter(fileData => fileData.isUploading && (fileData.uploadSpeed || 0) > 0);
            const averageSpeed = activeUploads.length > 0
                ? activeUploads.reduce((sum, file) => sum + (file.uploadSpeed || 0), 0) / activeUploads.length
                : 0;

            const remainingBytes = totalBytes - uploadedBytes;
            const estimatedTimeRemaining = averageSpeed > 0 ? remainingBytes / averageSpeed : 0;
            const overallProgressPercent = totalBytes > 0 ? (uploadedBytes / totalBytes) * 100 : 0;

            const allUploadsComplete = uploadingFiles.length > 0 &&
                uploadingFiles.every(fileData => !fileData.isUploading && (fileData.fileId || fileData.uploadError));

            setOverallProgress(overallProgressPercent);
            setUploadingSummary({
                totalFiles,
                completedFiles,
                failedFiles,
                totalBytes,
                uploadedBytes,
                averageSpeed,
                estimatedTimeRemaining,
                isComplete: allUploadsComplete,
            });
        };

        updateOverallProgress();
    }, [stagedFiles]);

    /**
     * Format bytes to human-readable string
     * 
     * Utility function for displaying file sizes and upload progress
     * in user-friendly format (Bytes, KB, MB, GB).
     * 
     * @param bytes - Number of bytes to format
     * @returns Formatted string with appropriate unit
     */
    const formatBytes = (bytes: number): string => {
        if (bytes === 0) return '0 Bytes';
        const k = 1024;
        const sizes = ['Bytes', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    };

    /**
     * Format duration to human-readable string
     * 
     * Utility function for displaying estimated time remaining
     * in user-friendly format (seconds, minutes).
     * 
     * @param seconds - Duration in seconds
     * @returns Formatted duration string
     */
    const formatDuration = (seconds: number): string => {
        if (seconds < 60) return `${Math.round(seconds)}s`;
        const minutes = Math.floor(seconds / 60);
        const remainingSeconds = Math.round(seconds % 60);
        return `${minutes}m ${remainingSeconds}s`;
    };

    /**
     * Handle file metadata updates
     * 
     * Wrapper function to provide a consistent interface for updating
     * file metadata (display name, caption, category) from child components.
     * 
     * @param id - File ID to update
     * @param field - Metadata field to update
     * @param value - New value for the field
     */
    const handleUpdateMetadata = (
        id: string,
        field: 'displayName' | 'caption' | 'fileCategory',
        value: string
    ) => {
        updateFileMetadata(id, field, value);
    };

    // Early return for read-only mode with no files
    if (readOnly && !hasFiles) {
        return null;
    }

    return (
        <Box sx={{ display: 'flex', flexDirection: 'column', gap: 4 }}>
            {/* Upload Zone / Progress Dashboard - Seamless Transition */}
            {!readOnly && (
                <Box
                    sx={{
                        transition: "all 0.3s ease-in-out",
                        minHeight: uploadingSummary ? "auto" : "140px", // Maintain consistent height during transitions
                    }}
                >
                    {uploadingSummary ? (
                        // Upload Progress Dashboard - Shows during active uploads
                        <UploadProgressDashboard
                            overallProgress={overallProgress}
                            uploadingSummary={uploadingSummary}
                            formatBytes={formatBytes}
                            formatDuration={formatDuration}
                        />
                    ) : stagedFiles.length < maxFiles ? (
                        // File Upload Drop Zone - Shows when ready for new files
                        <Box {...dragHandlers}>
                            <CaseFileUploadZone
                                onFilesAdded={addFiles}
                                disabled={isUploading}
                                isDragging={isDragging}
                                maxFiles={maxFiles}
                            />
                        </Box>
                    ) : null}
                </Box>
            )}

            {/* Files List - Shows staged files with metadata editing */}
            <CaseFilesList
                files={stagedFiles}
                errors={uploadErrors}
                readOnly={readOnly}
                onRemoveFile={removeFile}
                onUpdateMetadata={handleUpdateMetadata}
            />

            {/* Status Information - Shows file count when files are staged */}
            {!readOnly && stagedFiles.length > 0 && !uploadingSummary && (
                <Box>
                    <Typography style="body3" color={colors.grey[500]}>
                        {stagedFiles.length} {stagedFiles.length === 1 ? 'file' : 'files'} ready to upload
                    </Typography>
                </Box>
            )}
        </Box>
    );
});

CaseFileAttachments.displayName = 'CaseFileAttachments'; 