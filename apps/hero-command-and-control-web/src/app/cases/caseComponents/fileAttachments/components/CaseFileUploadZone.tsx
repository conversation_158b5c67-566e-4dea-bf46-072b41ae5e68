import { Typography } from '@/design-system/components/Typography';
import { colors } from '@/design-system/tokens';
import { Box } from '@mui/material';
import { File, Upload } from 'lucide-react';
import React, { useRef } from 'react';
import { CaseFileUploadZoneProps } from '../types';

export const CaseFileUploadZone: React.FC<CaseFileUploadZoneProps> = ({
    onFilesAdded,
    disabled = false,
    isDragging = false,
    maxFiles = 10,
}) => {
    const fileInputRef = useRef<HTMLInputElement>(null);

    const handleClick = () => {
        if (!disabled) {
            fileInputRef.current?.click();
        }
    };

    const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
        if (e.target.files) {
            onFilesAdded(e.target.files);
        }
    };

    return (
        <Box
            sx={{
                border: `2px dashed ${isDragging ? colors.blue[600] : colors.grey[300]}`,
                borderRadius: 2,
                p: 6,
                textAlign: 'center',
                cursor: disabled ? 'not-allowed' : 'pointer',
                backgroundColor: isDragging ? colors.blue[50] : 'white',
                opacity: disabled ? 0.5 : 1,
                transition: 'all 0.2s ease-in-out',
                '&:hover': !disabled ? {
                    borderColor: colors.grey[400],
                } : {},
            }}
            onClick={handleClick}
        >
            <Box sx={{ display: 'flex', flexDirection: 'column', alignItems: 'center', gap: 3 }}>
                {isDragging ? (
                    <Upload size={48} style={{ color: colors.blue[600] }} />
                ) : (
                    <File size={48} style={{ color: colors.grey[400] }} />
                )}

                <Box sx={{ display: 'flex', flexDirection: 'column', gap: 1 }}>
                    <Typography style="h4" color={colors.grey[700]}>
                        {isDragging ? 'Drop files here' : 'Upload files'}
                    </Typography>
                    <Typography style="body2" color={colors.grey[500]}>
                        Drag and drop files here, or click to select
                    </Typography>
                    <Typography style="body3" color={colors.grey[400]}>
                        Maximum {maxFiles} files, 250MB per file
                    </Typography>
                </Box>
            </Box>

            <input
                ref={fileInputRef}
                type="file"
                multiple
                style={{ display: 'none' }}
                onChange={handleFileChange}
                disabled={disabled}
                accept="*/*"
            />
        </Box>
    );
}; 