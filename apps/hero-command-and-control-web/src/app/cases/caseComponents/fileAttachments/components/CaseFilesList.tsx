import { Typography } from '@/design-system/components/Typography';
import { colors } from '@/design-system/tokens';
import { Box } from '@mui/material';
import { AlertCircle } from 'lucide-react';
import React from 'react';
import { CaseStagedFile } from '../types';
import { CaseFilePreview } from './CaseFilePreview';

interface CaseFilesListProps {
    files: CaseStagedFile[];
    errors: string[];
    readOnly?: boolean;
    onRemoveFile?: (id: string) => void;
    onUpdateMetadata?: (id: string, field: 'displayName' | 'caption' | 'fileCategory', value: string) => void;
}

export const CaseFilesList: React.FC<CaseFilesListProps> = ({
    files,
    errors,
    readOnly = false,
    onRemoveFile,
    onUpdateMetadata,
}) => {
    if (files.length === 0 && errors.length === 0) {
        return null;
    }

    const handleDisplayNameChange = (id: string, displayName: string) => {
        onUpdateMetadata?.(id, 'displayName', displayName);
    };

    const handleCaptionChange = (id: string, caption: string) => {
        onUpdateMetadata?.(id, 'caption', caption);
    };

    const handleCategoryChange = (id: string, category: string) => {
        onUpdateMetadata?.(id, 'fileCategory', category);
    };

    return (
        <Box sx={{ display: 'flex', flexDirection: 'column', gap: 3 }}>
            {/* Error messages */}
            {errors.length > 0 && (
                <Box
                    sx={{
                        p: 3,
                        borderRadius: 2,
                        backgroundColor: colors.rose[50],
                        border: `1px solid ${colors.rose[200]}`
                    }}
                >
                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mb: 1 }}>
                        <AlertCircle size={16} style={{ color: colors.rose[600] }} />
                        <Typography style="body2" color={colors.rose[700]}>
                            Upload Errors
                        </Typography>
                    </Box>
                    <Box sx={{ display: 'flex', flexDirection: 'column', gap: 1 }}>
                        {errors.map((error, index) => (
                            <Typography key={index} style="body3" color={colors.rose[600]}>
                                {error}
                            </Typography>
                        ))}
                    </Box>
                </Box>
            )}

            {/* Files list */}
            {files.length > 0 && (
                <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}>
                    <Typography style="body2" color={colors.grey[700]}>
                        <strong>Files ({files.length})</strong>
                    </Typography>
                    <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}>
                        {files.map((file) => (
                            <CaseFilePreview
                                key={file.id}
                                file={file}
                                readOnly={readOnly}
                                onRemove={onRemoveFile}
                                onDisplayNameChange={handleDisplayNameChange}
                                onCaptionChange={handleCaptionChange}
                                onCategoryChange={handleCategoryChange}
                            />
                        ))}
                    </Box>
                </Box>
            )}
        </Box>
    );
}; 