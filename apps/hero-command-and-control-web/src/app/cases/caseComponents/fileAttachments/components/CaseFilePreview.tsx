import { Button } from '@/design-system/components/Button';
import { TextInput } from '@/design-system/components/TextInput';
import { Typography } from '@/design-system/components/Typography';
import { colors } from '@/design-system/tokens';
import { Box, IconButton } from '@mui/material';
import {
    AlertCircle,
    Archive,
    FileText,
    Image,
    Loader2,
    Music,
    Video,
    X
} from 'lucide-react';
import React, { useState } from 'react';
import { CaseFilePreviewProps } from '../types';
import { formatFileSize, getFileTypeColor, supportsPreview } from '../utils/fileHelpers';

export const CaseFilePreview: React.FC<CaseFilePreviewProps> = ({
    file,
    readOnly = false,
    onRemove,
    onDisplayNameChange,
    onCaptionChange,
    onCategoryChange,
}) => {
    const [editingField, setEditingField] = useState<string | null>(null);
    const [tempValues, setTempValues] = useState({
        displayName: file.displayName,
        caption: file.caption,
        fileCategory: file.fileCategory,
    });

    const getFileIcon = (mimeType: string) => {
        if (mimeType.startsWith('image/')) return Image;
        if (mimeType.startsWith('video/')) return Video;
        if (mimeType.startsWith('audio/')) return Music;
        if (mimeType.includes('zip') || mimeType.includes('archive')) return Archive;
        return FileText;
    };

    const FileIcon = getFileIcon(file.file.type);
    const iconColor = getFileTypeColor(file.file.type);

    const handleSaveField = (field: string) => {
        const value = tempValues[field as keyof typeof tempValues];
        switch (field) {
            case 'displayName':
                onDisplayNameChange?.(file.id, value);
                break;
            case 'caption':
                onCaptionChange?.(file.id, value);
                break;
            case 'fileCategory':
                onCategoryChange?.(file.id, value);
                break;
        }
        setEditingField(null);
    };

    const handleCancelEdit = () => {
        setTempValues({
            displayName: file.displayName,
            caption: file.caption,
            fileCategory: file.fileCategory,
        });
        setEditingField(null);
    };

    const EditableField: React.FC<{
        field: string;
        value: string;
        placeholder: string;
        multiline?: boolean;
    }> = ({ field, value, placeholder, multiline = false }) => {
        const isEditing = editingField === field;
        const tempValue = tempValues[field as keyof typeof tempValues];

        if (readOnly) {
            return (
                <Typography style="body2" color={colors.grey[600]}>
                    {value || placeholder}
                </Typography>
            );
        }

        if (isEditing) {
            return (
                <Box sx={{ display: 'flex', gap: 1, alignItems: 'flex-start' }}>
                    <Box
                        sx={{
                            flex: 1,
                            direction: 'ltr',
                            '& input, & textarea': {
                                direction: 'ltr !important',
                                textAlign: 'left !important',
                            }
                        }}
                    >
                        <TextInput
                            value={tempValue}
                            onChange={(e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) =>
                                setTempValues(prev => ({ ...prev, [field]: e.target.value }))
                            }
                            placeholder={placeholder}
                            type={multiline ? 'multiline' : 'text'}
                            numOfLines={multiline ? 3 : undefined}
                            onKeyDown={(e: React.KeyboardEvent) => {
                                if (e.key === 'Enter' && !multiline) {
                                    handleSaveField(field);
                                } else if (e.key === 'Escape') {
                                    handleCancelEdit();
                                }
                            }}
                            autoFocus
                        />
                    </Box>
                    <Button
                        label="Save"
                        size="small"
                        color="blue"
                        style="filled"
                        onClick={() => handleSaveField(field)}
                    />
                    <Button
                        label="Cancel"
                        size="small"
                        color="grey"
                        style="ghost"
                        onClick={handleCancelEdit}
                    />
                </Box>
            );
        }

        return (
            <Box
                sx={{
                    cursor: 'pointer',
                    padding: '4px 8px',
                    borderRadius: 1,
                    transition: 'background-color 0.2s',
                    '&:hover': {
                        backgroundColor: colors.grey[50],
                    }
                }}
                onClick={() => setEditingField(field)}
            >
                <Typography style="body2" color={colors.grey[600]}>
                    {value || <span style={{ fontStyle: 'italic', color: colors.grey[400] }}>{placeholder}</span>}
                </Typography>
            </Box>
        );
    };

    return (
        <Box
            sx={{
                border: `1px solid ${colors.grey[300]}`,
                borderRadius: 2,
                p: 3,
                backgroundColor: 'white'
            }}
        >
            <Box sx={{ display: 'flex', gap: 3 }}>
                {/* File preview/icon */}
                <Box sx={{ flexShrink: 0 }}>
                    {file.preview && supportsPreview(file.file.type) ? (
                        <Box
                            sx={{
                                width: 64,
                                height: 64,
                                borderRadius: 1,
                                overflow: 'hidden',
                                border: `1px solid ${colors.grey[300]}`
                            }}
                        >
                            {file.file.type.startsWith('image/') ? (
                                <img
                                    src={file.preview}
                                    alt={file.displayName}
                                    style={{ width: '100%', height: '100%', objectFit: 'cover' }}
                                />
                            ) : (
                                <video
                                    src={file.preview}
                                    style={{ width: '100%', height: '100%', objectFit: 'cover' }}
                                    muted
                                />
                            )}
                        </Box>
                    ) : (
                        <Box
                            sx={{
                                width: 64,
                                height: 64,
                                borderRadius: 1,
                                border: `2px dashed ${colors.grey[300]}`,
                                display: 'flex',
                                alignItems: 'center',
                                justifyContent: 'center',
                                backgroundColor: colors.grey[50]
                            }}
                        >
                            <FileIcon size={32} style={{ color: iconColor }} />
                        </Box>
                    )}
                </Box>

                {/* File details */}
                <Box sx={{ flex: 1, minWidth: 0, display: 'flex', flexDirection: 'column', gap: 2 }}>
                    {/* Header with name and remove button */}
                    <Box sx={{ display: 'flex', alignItems: 'flex-start', justifyContent: 'space-between', gap: 2 }}>
                        <Box sx={{ flex: 1, minWidth: 0 }}>
                            <Box sx={{ mb: 0.5 }}>
                                <Typography style="body2" color={colors.grey[700]}>
                                    <strong>Display Name</strong>
                                </Typography>
                            </Box>
                            <EditableField
                                field="displayName"
                                value={file.displayName}
                                placeholder="Enter display name"
                            />
                        </Box>

                        {!readOnly && (
                            <IconButton
                                size="small"
                                onClick={() => onRemove?.(file.id)}
                                sx={{
                                    color: colors.rose[600],
                                    '&:hover': { color: colors.rose[700] },
                                    width: 24,
                                    height: 24
                                }}
                            >
                                <X size={16} />
                            </IconButton>
                        )}
                    </Box>

                    {/* File info */}
                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 3 }}>
                        <Typography style="body3" color={colors.grey[500]}>
                            {formatFileSize(file.file.size)}
                        </Typography>
                        <Typography style="body3" color={colors.grey[500]}>
                            {file.file.type || 'Unknown type'}
                        </Typography>
                    </Box>

                    {/* Caption */}
                    <Box>
                        <Box sx={{ mb: 0.5 }}>
                            <Typography style="body2" color={colors.grey[700]}>
                                <strong>Caption</strong>
                            </Typography>
                        </Box>
                        <EditableField
                            field="caption"
                            value={file.caption}
                            placeholder="Add a caption..."
                        />
                    </Box>

                    {/* Category */}
                    <Box>
                        <Box sx={{ mb: 0.5 }}>
                            <Typography style="body2" color={colors.grey[700]}>
                                <strong>Category</strong>
                            </Typography>
                        </Box>
                        <EditableField
                            field="fileCategory"
                            value={file.fileCategory}
                            placeholder="Add category..."
                        />
                    </Box>

                    {/* Status indicators */}
                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                        {file.isUploading && (
                            <Box
                                sx={{
                                    display: 'flex',
                                    alignItems: 'center',
                                    gap: 1,
                                    px: 2,
                                    py: 0.5,
                                    borderRadius: 1,
                                    backgroundColor: colors.blue[50],
                                    border: `1px solid ${colors.blue[200]}`
                                }}
                            >
                                <Loader2 size={12} style={{ animation: 'spin 1s linear infinite' }} />
                                <Typography style="body3" color={colors.blue[700]}>
                                    Uploading...
                                </Typography>
                            </Box>
                        )}

                        {file.uploadError && (
                            <Box
                                sx={{
                                    display: 'flex',
                                    alignItems: 'center',
                                    gap: 1,
                                    px: 2,
                                    py: 0.5,
                                    borderRadius: 1,
                                    backgroundColor: colors.rose[50],
                                    border: `1px solid ${colors.rose[200]}`
                                }}
                            >
                                <AlertCircle size={12} />
                                <Typography style="body3" color={colors.rose[700]}>
                                    Upload failed
                                </Typography>
                            </Box>
                        )}

                        {file.fileId && !file.isUploading && (
                            <Box
                                sx={{
                                    display: 'flex',
                                    alignItems: 'center',
                                    gap: 1,
                                    px: 2,
                                    py: 0.5,
                                    borderRadius: 1,
                                    backgroundColor: colors.vine[50],
                                    border: `1px solid ${colors.vine[200]}`
                                }}
                            >
                                <Typography style="body3" color={colors.vine[700]}>
                                    ✓ Uploaded
                                </Typography>
                            </Box>
                        )}
                    </Box>

                    {/* Error message */}
                    {file.uploadError && (
                        <Typography style="body3" color={colors.rose[600]}>
                            {file.uploadError}
                        </Typography>
                    )}
                </Box>
            </Box>
        </Box>
    );
}; 