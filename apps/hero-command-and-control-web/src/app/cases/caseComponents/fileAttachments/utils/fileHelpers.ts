/**
 * @fileoverview File Helper Utilities - Utility functions for file attachment management
 * 
 * This module provides a comprehensive set of utility functions for handling file operations
 * in the case file attachment system. These functions support the entire file lifecycle
 * from initial processing through cleanup.
 * 
 * Core Functionality:
 * - File preview URL generation and management
 * - File validation with configurable limits
 * - File metadata processing and formatting
 * - Memory management for preview URLs
 * - File type detection and categorization
 * 
 * Integration:
 * - Used by useCaseFileUpload hook for file processing
 * - Supports CaseFileAttachments component functionality
 * - Provides consistent file handling across the application
 * - Integrates with browser File API for preview generation
 * 
 * Memory Management:
 * - Proper cleanup of blob URLs to prevent memory leaks
 * - Automatic preview URL generation for supported types
 * - Lifecycle management for temporary file objects
 * 
 * @module fileHelpers
 */

import { CaseStagedFile } from '../types';

/**
 * Creates a preview URL for supported file types
 * 
 * This function generates blob URLs for files that can be previewed in the browser.
 * Currently supports images and videos. The generated URLs must be properly cleaned
 * up using URL.revokeObjectURL() to prevent memory leaks.
 * 
 * Supported Types:
 * - Images (image/*): Creates blob URL for image preview
 * - Videos (video/*): Creates blob URL for video preview/thumbnail
 * - Other types: Returns empty string (no preview available)
 * 
 * Memory Management:
 * - Generated URLs are stored in browser memory
 * - Must be revoked using URL.revokeObjectURL() when no longer needed
 * - Component unmount and file removal should trigger cleanup
 * 
 * Security Considerations:
 * - Blob URLs are safe and don't expose file system paths
 * - URLs are unique and temporary, tied to the current session
 * - No server-side processing required for preview generation
 * 
 * @param file - The File object to create a preview for
 * @returns Blob URL string for preview, or empty string if not supported
 * 
 * @example
 * // Create preview for an image file
 * const previewUrl = createFilePreview(imageFile);
 * if (previewUrl) {
 *   // Use previewUrl in img src
 *   // Remember to call URL.revokeObjectURL(previewUrl) when done
 * }
 */
export const createFilePreview = (file: File): string => {
    if (file.type.startsWith("image/") || file.type.startsWith("video/")) {
        return URL.createObjectURL(file);
    }
    return "";
};

/**
 * Generates a unique ID for staged files
 * 
 * This function creates unique identifiers for files in the staging area.
 * The IDs are used for tracking files through the upload process and
 * for React key props in component rendering.
 * 
 * ID Format:
 * - Timestamp prefix for rough chronological ordering
 * - Random suffix for uniqueness within the same millisecond
 * - Total length approximately 16 characters
 * - Safe for use as React keys and object properties
 * 
 * Uniqueness Guarantees:
 * - Timestamp ensures uniqueness across different time periods
 * - Random component handles multiple files added simultaneously
 * - Collision probability is extremely low for practical use cases
 * 
 * @returns Unique string identifier for file tracking
 * 
 * @example
 * // Generate ID for a new staged file
 * const fileId = generateFileId();
 * console.log(fileId); // "1704123456789abc123"
 */
export const generateFileId = (): string => {
    return Date.now().toString() + Math.random().toString(36).substring(2, 9);
};

/**
 * Creates a staged file object from a File
 * 
 * This function takes a browser File object and creates a CaseStagedFile
 * with all the necessary metadata and state properties for the upload system.
 * It initializes preview URLs, metadata fields, and upload state.
 * 
 * Initialization Process:
 * 1. Generate unique ID for tracking
 * 2. Create preview URL if file type is supported
 * 3. Initialize display name from original filename
 * 4. Set empty metadata fields for user editing
 * 5. Set initial upload state (not uploading)
 * 
 * Default Values:
 * - displayName: Original filename from File object
 * - caption: Empty string (user can add description)
 * - fileCategory: Empty string (user can categorize)
 * - isUploading: false (upload hasn't started)
 * - uploadProgress: undefined (no progress yet)
 * 
 * @param file - Browser File object from input or drop event
 * @returns CaseStagedFile object ready for metadata editing and upload
 * 
 * @example
 * // Create staged file from user selection
 * const stagedFile = createStagedFile(fileFromInput);
 * console.log(stagedFile.displayName); // Original filename
 * console.log(stagedFile.preview); // Blob URL if image/video, empty otherwise
 */
export const createStagedFile = (file: File): CaseStagedFile => {
    return {
        id: generateFileId(),
        file,
        preview: createFilePreview(file),
        displayName: file.name,
        caption: '',
        fileCategory: '',
        isUploading: false,
    };
};

/**
 * Formats bytes to human-readable format
 * 
 * This function converts byte values to user-friendly strings with appropriate
 * units (Bytes, KB, MB, GB). It uses binary (1024) rather than decimal (1000)
 * conversion for consistency with operating system file size displays.
 * 
 * Formatting Rules:
 * - Uses 1024 as the conversion factor (binary)
 * - Rounds to 2 decimal places for clarity
 * - Automatically selects appropriate unit
 * - Handles edge case of 0 bytes
 * 
 * Unit Selection:
 * - < 1024 bytes: Shows as "Bytes"
 * - < 1024 KB: Shows as "KB" 
 * - < 1024 MB: Shows as "MB"
 * - >= 1024 MB: Shows as "GB"
 * 
 * @param bytes - Number of bytes to format
 * @returns Formatted string with appropriate unit
 * 
 * @example
 * // Format various file sizes
 * formatFileSize(0);          // "0 Bytes"
 * formatFileSize(1024);       // "1 KB"
 * formatFileSize(1536);       // "1.5 KB"
 * formatFileSize(1048576);    // "1 MB"
 * formatFileSize(2621440);    // "2.5 MB"
 */
export const formatFileSize = (bytes: number): string => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
};

/**
 * Cleans up preview URLs to prevent memory leaks
 * 
 * This function properly revokes all blob URLs associated with staged files
 * to free browser memory. It should be called when files are removed or
 * when the component unmounts to prevent memory leaks.
 * 
 * Memory Management:
 * - Iterates through all files and revokes their preview URLs
 * - Safe to call multiple times (no error if URL already revoked)
 * - Should be called on component unmount and file removal
 * - Critical for preventing memory leaks in long-running applications
 * 
 * When to Call:
 * - Component unmount (useEffect cleanup)
 * - File removal from staging area
 * - Clearing all files
 * - Modal close/cancel actions
 * 
 * @param files - Array of staged files to clean up
 * 
 * @example
 * // Clean up on component unmount
 * useEffect(() => {
 *   return () => {
 *     cleanupPreviewUrls(stagedFiles);
 *   };
 * }, [stagedFiles]);
 * 
 * // Clean up when removing a file
 * const removeFile = (id: string) => {
 *   const fileToRemove = files.find(f => f.id === id);
 *   if (fileToRemove?.preview) {
 *     URL.revokeObjectURL(fileToRemove.preview);
 *   }
 * };
 */
export const cleanupPreviewUrls = (files: CaseStagedFile[]): void => {
    files.forEach(file => {
        if (file.preview) {
            URL.revokeObjectURL(file.preview);
        }
    });
};

/**
 * Gets file type icon color based on MIME type
 * 
 * This function returns appropriate colors for file type icons to provide
 * visual distinction between different file categories. Colors are chosen
 * to be accessible and consistent with the design system.
 * 
 * Color Mapping:
 * - Images: Blue (#2563eb) - Visual content
 * - Videos: Purple (#7c3aed) - Media content
 * - Audio: Green (#059669) - Audio content
 * - PDFs: Red (#dc2626) - Document format
 * - Other: Gray (#6b7280) - Generic files
 * 
 * Design Considerations:
 * - Colors provide clear visual distinction
 * - Accessible contrast ratios
 * - Consistent with Hero design system
 * - Semantic color associations (red for PDFs, blue for images)
 * 
 * @param mimeType - MIME type string from File object
 * @returns Hex color code for the file type
 * 
 * @example
 * // Get color for different file types
 * getFileTypeColor('image/jpeg');        // '#2563eb' (blue)
 * getFileTypeColor('video/mp4');         // '#7c3aed' (purple)
 * getFileTypeColor('application/pdf');   // '#dc2626' (red)
 * getFileTypeColor('text/plain');        // '#6b7280' (gray)
 */
export const getFileTypeColor = (mimeType: string): string => {
    if (mimeType.startsWith('image/')) return '#2563eb'; // blue
    if (mimeType.startsWith('video/')) return '#7c3aed'; // purple
    if (mimeType.startsWith('audio/')) return '#059669'; // green
    if (mimeType === 'application/pdf') return '#dc2626'; // red
    return '#6b7280'; // gray
};

/**
 * Checks if a file type supports preview
 * 
 * This function determines whether a file can be previewed directly in the browser
 * without requiring download. Currently supports images and videos that can be
 * displayed using HTML img and video elements.
 * 
 * Supported Preview Types:
 * - Images: All standard web formats (JPEG, PNG, GIF, WebP, SVG, etc.)
 * - Videos: Standard web formats (MP4, WebM, OGG, etc.)
 * - Not supported: Documents, PDFs, audio files, archives
 * 
 * Browser Compatibility:
 * - Relies on browser's native support for file types
 * - Different browsers may support different formats
 * - Graceful fallback to file icon if preview fails
 * 
 * Performance Considerations:
 * - Preview generation is done client-side
 * - No server processing required
 * - Large files may take time to generate previews
 * 
 * @param mimeType - MIME type string from File object
 * @returns Boolean indicating if preview is supported
 * 
 * @example
 * // Check if file can be previewed
 * if (supportsPreview(file.type)) {
 *   // Show preview image/video
 *   return <img src={file.preview} alt={file.name} />;
 * } else {
 *   // Show file icon instead
 *   return <FileIcon type={file.type} />;
 * }
 */
export const supportsPreview = (mimeType: string): boolean => {
    return mimeType.startsWith('image/') || mimeType.startsWith('video/');
};

/**
 * Validates file size and type constraints
 * 
 * This function performs comprehensive validation of uploaded files against
 * configurable constraints. It checks file size limits and can be extended
 * to include other validation rules as needed.
 * 
 * Current Validations:
 * - File size: Configurable maximum size in MB
 * - Future: File type restrictions, filename validation, etc.
 * 
 * Validation Process:
 * 1. Convert maxSizeMB to bytes for comparison
 * 2. Check file size against limit
 * 3. Return validation result with specific error message
 * 4. Allow for future extension with additional checks
 * 
 * Error Messages:
 * - Specific and user-friendly error descriptions
 * - Include actual limits for user reference
 * - Suitable for direct display to users
 * 
 * @param file - File object to validate
 * @param maxSizeMB - Maximum allowed file size in megabytes (default: 50MB)
 * @returns Validation result object with success status and error message
 * 
 * @example
 * // Validate a file with custom size limit
 * const validation = validateFile(file, 100); // 100MB limit
 * if (!validation.valid) {
 *   console.error(validation.error); // "File size must be less than 100MB"
 * }
 * 
 * // Validate with default limit
 * const validation = validateFile(file); // 50MB default
 * if (validation.valid) {
 *   // Proceed with file processing
 * }
 */
export const validateFile = (file: File, maxSizeMB: number = 50): { valid: boolean; error?: string } => {
    const maxSizeBytes = maxSizeMB * 1024 * 1024;

    // File size validation
    if (file.size > maxSizeBytes) {
        return {
            valid: false,
            error: `File size must be less than ${maxSizeMB}MB`
        };
    }

    // Future validations can be added here:
    // - File type restrictions
    // - Filename validation
    // - Content validation
    // - Virus scanning integration

    return { valid: true };
}; 