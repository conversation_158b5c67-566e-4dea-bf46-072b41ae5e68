/**
 * @fileoverview Case File Attachments Module - Public API exports
 * 
 * This module provides a complete file attachment system for case timeline entries.
 * It exports all components, hooks, types, and utilities needed to implement
 * file upload and display functionality in the Hero application.
 * 
 * ## Architecture Overview
 * 
 * The file attachment system is built with a modular architecture:
 * 
 * ### Core Components
 * - **CaseFileAttachments**: Main component with upload orchestration
 * - **CaseFileAttachmentDisplay**: Read-only display for timeline entries
 * - **CaseFileUploadZone**: Drag-and-drop upload interface
 * - **CaseFilesList**: List view with metadata editing
 * - **CaseFilePreview**: Individual file preview cards
 * 
 * ### State Management
 * - **useCaseFileUpload**: Custom hook managing all file operations
 * - Staged files with real-time progress tracking
 * - Upload error handling and validation
 * - Drag-and-drop state management
 * 
 * ### Utilities
 * - **fileHelpers**: File processing and validation utilities
 * - Preview URL generation and cleanup
 * - File size formatting and type detection
 * - Memory management for blob URLs
 * 
 * ## Usage Patterns
 * 
 * ### Upload Flow (CaseTimelineEntryModal)
 * ```tsx
 * const fileAttachmentsRef = useRef<CaseFileAttachmentsRef>(null);
 * 
 * const handleSubmit = async () => {
 *   if (fileAttachmentsRef.current?.hasFiles) {
 *     const fileRefs = await fileAttachmentsRef.current.uploadAllFiles();
 *     // Include fileRefs in case update
 *   }
 * };
 * 
 * <CaseFileAttachments
 *   ref={fileAttachmentsRef}
 *   caseId={caseId}
 *   maxFiles={10}
 * />
 * ```
 * 
 * ### Display Flow (CaseTimelineCard)
 * ```tsx
 * {update.fileAttachments && update.fileAttachments.length > 0 && (
 *   <CaseFileAttachmentDisplay
 *     fileAttachments={update.fileAttachments}
 *     readOnly={true}
 *   />
 * )}
 * ```
 * 
 * ## Key Features
 * 
 * ### File Upload
 * - Drag-and-drop with visual feedback
 * - File picker fallback
 * - Real-time progress tracking
 * - Batch upload with individual error handling
 * - File size validation (250MB limit)
 * - File count limits (configurable)
 * 
 * ### File Management
 * - Editable metadata (name, caption, category)
 * - File preview for images and videos
 * - File type detection with appropriate icons
 * - Memory-safe preview URL management
 * - Upload retry capabilities
 * 
 * ### File Display
 * - Responsive side-by-side layout
 * - Click-to-view in new tab
 * - Download with progress indicators
 * - File metadata display
 * - Consistent design system integration
 * 
 * ## Integration Points
 * 
 * ### Services
 * - **filerepository**: File upload and download operations
 * - **workflow/cases**: Case update creation with file attachments
 * 
 * ### Protobuf
 * - **CaseFileReference**: File metadata for case updates
 * - **CaseUpdateEntry**: Timeline entries with file attachments
 * 
 * ### Design System
 * - Hero Typography, Button, and color tokens
 * - Material-UI components for layout and interactions
 * - Consistent styling and responsive design
 * 
 * @module fileAttachments
 */

// Components
/** Main file attachment component with upload orchestration and progress tracking */
export { default as CaseFileAttachmentDisplay } from './components/CaseFileAttachmentDisplay';

/** Primary file attachment interface for case timeline entry modals */
export { CaseFileAttachments, type CaseFileAttachmentsRef } from './components/CaseFileAttachments';

/** Individual file preview card with metadata editing capabilities */
export { CaseFilePreview } from './components/CaseFilePreview';

/** File list container with error handling and batch operations */
export { CaseFilesList } from './components/CaseFilesList';

/** Drag-and-drop upload zone with visual feedback and file selection */
export { CaseFileUploadZone } from './components/CaseFileUploadZone';

// Hooks
/** Custom hook providing complete file management functionality */
export { useCaseFileUpload } from './hooks/useCaseFileUpload';

// Types
/**
 * TypeScript interfaces for type safety and component contracts
 * 
 * Core Types:
 * - CaseStagedFile: Files in staging area with upload progress
 * - CaseFileUploadResult: Upload operation results
 * - Component Props: Interface definitions for all components
 */
export type {
    CaseFileAttachmentProps,
    CaseFilePreviewProps,
    CaseFileUploadResult,
    CaseFileUploadZoneProps,
    CaseStagedFile
} from './types';

// Utils
/**
 * Utility functions for file processing and management
 * 
 * Key Functions:
 * - createStagedFile: Initialize files for staging
 * - validateFile: File size and type validation
 * - formatFileSize: Human-readable size formatting
 * - cleanupPreviewUrls: Memory leak prevention
 * - File type detection and preview support
 */
export {
    cleanupPreviewUrls,
    createFilePreview,
    createStagedFile,
    formatFileSize,
    generateFileId,
    getFileTypeColor,
    supportsPreview,
    validateFile
} from './utils/fileHelpers';

