import { useListCaseUpdates } from "@/app/apis/services/workflow/cases/hooks";
import { Button } from "@/design-system/components/Button";
import { Typography } from "@/design-system/components/Typography";
import { colors } from "@/design-system/tokens";
import { Add, PublishedWithChanges } from "@mui/icons-material";
import { Box, CircularProgress, Fade, Grow, Paper, Stack } from "@mui/material";
import { ListCaseUpdatesRequest } from "proto/hero/cases/v1/cases_pb";
import React, { useCallback, useEffect, useRef, useState } from "react";
import { CaseFileAttachmentDisplay } from "../fileAttachments";
import CaseTimelineEntryModal from "../modals/CaseTimelineEntryModal";

interface CaseTimelineCardProps {
  caseId: string;
}

const CaseTimelineCard: React.FC<CaseTimelineCardProps> = ({ caseId }) => {
  const [isModalOpen, setIsModalOpen] = useState<boolean>(false);
  const [newEntryId, setNewEntryId] = useState<string | null>(null);
  const [prevUpdatesLength, setPrevUpdatesLength] = useState<number>(0);
  const timelineRef = useRef<HTMLDivElement>(null);
  const newEntryRef = useRef<HTMLDivElement>(null);
  const [optimisticEntries, setOptimisticEntries] = useState<any[]>([]);

  // Fetch all case updates with automatic pagination
  const {
    data: updatesData,
    isLoading,
    isError,
    refetch,
  } = useListCaseUpdates(
    {
      caseId,
      pageSize: 50,
    } as ListCaseUpdatesRequest,
    {
      staleTime: 0,
      refetchOnWindowFocus: true,
      refetchOnMount: true,
      refetchOnReconnect: true,
    }
  );

  const allUpdates = [...(optimisticEntries || []), ...(updatesData?.updates || [])];

  // Track updates length changes to detect new entries
  useEffect(() => {
    const currentLength = updatesData?.updates?.length || 0;
    if (currentLength > 0 && optimisticEntries.length > 0) {
      // Once we have real data and had optimistic entries, we can clear them
      // This happens after the real entries are loaded, preventing flickering
      setOptimisticEntries([]);
    }
    setPrevUpdatesLength(currentLength);
  }, [updatesData?.updates?.length, optimisticEntries.length]);

  // Scroll to new entry when ref is available
  useEffect(() => {
    if (newEntryRef.current) {
      newEntryRef.current.scrollIntoView({ behavior: 'smooth', block: 'start' });
    }
  }, [newEntryId, optimisticEntries]);

  // Clear the new entry ID after animation
  useEffect(() => {
    if (newEntryId) {
      const timer = setTimeout(() => {
        setNewEntryId(null);
      }, 2000);
      return () => clearTimeout(timer);
    }
  }, [newEntryId]);

  // Handle opening and closing the modal
  const handleOpenModal = () => setIsModalOpen(true);
  const handleCloseModal = () => setIsModalOpen(false);

  // Handle optimistic entry addition and successful API update
  const handleEntryAdded = useCallback((newEntryId?: string, entryData?: any) => {
    if (entryData) {
      // Add optimistic entry
      const optimisticEntry = {
        id: `temp-${Date.now()}`,
        ...entryData,
        isOptimistic: true,
      };
      setOptimisticEntries(prev => [optimisticEntry, ...prev]);
      setNewEntryId(optimisticEntry.id);
    } else if (newEntryId) {
      // Set the real entry ID for highlighting after API success
      setNewEntryId(newEntryId);
    }

    refetch();
  }, [refetch]);

  // Group updates by date
  const groupUpdatesByDay = (updates: any[] = []) => {
    const grouped = updates.reduce((acc: Record<string, any>, update) => {
      // Try to get time from data field first, then fall back to eventTime
      const timeSource = update.data && typeof update.data === 'object' && update.data.selectedTime
        ? new Date(update.data.selectedTime)
        : update.eventTime ? new Date(update.eventTime) : null;

      if (!timeSource) return acc;

      const date = timeSource;
      const dayKey = date.toISOString().split("T")[0];

      if (!acc[dayKey]) {
        acc[dayKey] = {
          date,
          updates: [],
        };
      }
      acc[dayKey].updates.push(update);
      return acc;
    }, {});

    return Object.entries(grouped)
      .sort(([a], [b]) => b.localeCompare(a)) // Sort by date, newest first
      .map(([_, value]) => value);
  };

  // Group the updates by date
  const groupedUpdates = groupUpdatesByDay(allUpdates);

  return (
    <>
      <Paper
        elevation={0}
        sx={{
          p: 3,
          borderRadius: 2,
          bgcolor: "white",
          height: "100%",
          maxHeight: "468px",
          display: "flex",
          flexDirection: "column",
          border: `1px solid ${colors.grey[200]}`,
          transition: "height 0.5s ease, max-height 0.5s ease",
        }}
      >
        <Box
          sx={{
            display: "flex",
            justifyContent: "space-between",
            alignItems: "center",
            mb: 2,
            position: "sticky",
            top: 0,
            zIndex: 1,
            bgcolor: "white",
          }}
        >
          <Typography style="caps1" color={colors.grey[500]}>
            CASE TIMELINE
          </Typography>
          <Button
            label="Add Entry"
            size="small"
            prominence={false}
            style="filled"
            color="blue"
            leftIcon={<Add />}
            onClick={handleOpenModal}
          />
        </Box>

        {isLoading && !optimisticEntries.length ? (
          <Box sx={{ display: "flex", justifyContent: "center", py: 4 }}>
            <CircularProgress size={24} />
          </Box>
        ) : isError && !optimisticEntries.length ? (
          <Box sx={{ py: 4, textAlign: "center" }}>
            <Typography style="body1" color={colors.grey[600]}>
              Error loading timeline
            </Typography>
          </Box>
        ) : (
          <Box
            ref={timelineRef}
            sx={{
              flex: 1,
              overflow: "auto",
              transition: "all 0.3s ease-in-out",
            }}
          >
            {groupedUpdates.length > 0 ? (
              <Stack spacing={2}>
                {groupedUpdates.map((dayGroup, i) => (
                  <Fade in={true} key={i} timeout={300}>
                    <Box>
                      <Box
                        sx={{
                          display: "flex",
                          justifyContent: "flex-start",
                          mb: 2,
                        }}
                      >
                        <Typography style="tag2" color={colors.grey[500]}>
                          {dayGroup.date.toLocaleDateString("en-US", {
                            weekday: "long",
                            month: "long",
                            day: "numeric",
                            year: "numeric",
                          })}
                        </Typography>
                      </Box>
                      <Stack spacing={1.5}>
                        {dayGroup.updates
                          .sort((a: any, b: any) => {
                            // Try to get time from data field first, then fall back to eventTime
                            const aTimeSource = a.data && typeof a.data === 'object' && a.data.selectedTime
                              ? new Date(a.data.selectedTime).getTime()
                              : a.eventTime ? new Date(a.eventTime).getTime() : 0;

                            const bTimeSource = b.data && typeof b.data === 'object' && b.data.selectedTime
                              ? new Date(b.data.selectedTime).getTime()
                              : b.eventTime ? new Date(b.eventTime).getTime() : 0;

                            return bTimeSource - aTimeSource;
                          })
                          .map((update: any, index: number) => {
                            let selectedTime;
                            if (update.data && typeof update.data === 'object') {
                              selectedTime = update.data.selectedTime;
                            }

                            const timestamp = selectedTime
                              ? new Date(selectedTime)
                              : new Date(update.eventTime);

                            const time = timestamp.toLocaleTimeString("en-US", {
                              hour12: false,
                              hour: "2-digit",
                              minute: "2-digit",
                            });

                            // Check if this is the newly added entry
                            const isNewEntry = newEntryId === update.id;
                            const isOptimistic = update.isOptimistic;
                            const isSystemUpdate = update.eventType === "SYSTEM_UPDATE";

                            return (
                              <Grow
                                in={true}
                                key={`${update.id || index}`}
                                style={{ transformOrigin: "0 0 0" }}
                                timeout={800}
                              >
                                <Box
                                  ref={isNewEntry || isOptimistic ? newEntryRef : null}
                                  sx={{
                                    display: "flex",
                                    alignItems: "flex-start",
                                    transition: "background-color 2s ease",
                                    borderRadius: 1,
                                  }}
                                >
                                  <Box sx={{ mr: 2, minWidth: "40px" }}>
                                    <Typography
                                      style="caps3"
                                      color={colors.grey[400]}
                                      lineHeight={"16px"}
                                      as="div"
                                    >
                                      {time}
                                    </Typography>
                                  </Box>

                                  <Box
                                    sx={{
                                      display: "flex",
                                      alignItems: "center",
                                      gap: 1,
                                    }}
                                  >
                                    <Box>
                                      <Box
                                        sx={{
                                          display: "flex",
                                          alignItems: "center",
                                          gap: 1,
                                          mb: 0.5,
                                        }}
                                      >
                                        {isSystemUpdate ? (
                                          <>
                                            <PublishedWithChanges
                                              sx={{
                                                color: colors.grey[500],
                                                fontSize: 22
                                              }}
                                            />
                                            <Typography
                                              style="tag1"
                                              color={colors.grey[500]}
                                            >
                                              {update.displayName || "System"}
                                            </Typography>
                                            <Typography
                                              style="tag2"
                                              color={colors.grey[500]}
                                              className="italic"
                                            >
                                              {update.message}
                                            </Typography>
                                          </>
                                        ) : (
                                          <>
                                            <Box
                                              sx={{
                                                width: 22,
                                                height: 22,
                                                borderRadius: "50%",
                                                bgcolor: colors.blue[700],
                                                color: "white",
                                                display: "flex",
                                                alignItems: "center",
                                                justifyContent: "center",
                                              }}
                                            >
                                              <Typography
                                                style="caps3"
                                                color="white"
                                              >
                                                {update.displayName
                                                  ? update.displayName
                                                    .split(" ")
                                                    .slice(0, 2)
                                                    .map(
                                                      (name: string) => name[0]
                                                    )
                                                    .join("")
                                                  : "UN"}
                                              </Typography>
                                            </Box>
                                            <Typography
                                              style="tag1"
                                              color={colors.grey[900]}
                                            >
                                              {update.displayName || "Unknown User"}
                                            </Typography>
                                            {update.eventType && (
                                              <Typography
                                                style="tag1"
                                                color={colors.grey[500]}
                                              >
                                                {update.eventType
                                                  ?.replace("EVENT_TYPE_", "")
                                                  ?.replace(/_/g, " ")}
                                              </Typography>
                                            )}
                                          </>
                                        )}
                                      </Box>

                                      {!isSystemUpdate && (
                                        <Typography
                                          style="tag2"
                                          color={colors.grey[900]}
                                        >
                                          {update.message}
                                        </Typography>
                                      )}

                                      {/* Render file attachments if present */}
                                      {update.fileAttachments && update.fileAttachments.length > 0 && (
                                        <CaseFileAttachmentDisplay
                                          fileAttachments={update.fileAttachments}
                                          readOnly={true}
                                        />
                                      )}
                                    </Box>
                                  </Box>
                                </Box>
                              </Grow>
                            );
                          })}
                      </Stack>
                    </Box>
                  </Fade>
                ))}
              </Stack>
            ) : (
              <Box sx={{ py: 4, textAlign: "center" }}>
                <Typography style="body1" color={colors.grey[600]}>
                  No timeline entries yet
                </Typography>
              </Box>
            )}
          </Box>
        )}
      </Paper>

      <CaseTimelineEntryModal
        open={isModalOpen}
        onClose={handleCloseModal}
        caseId={caseId}
        onSuccess={handleEntryAdded}
      />
    </>
  );
};

export default CaseTimelineCard;
