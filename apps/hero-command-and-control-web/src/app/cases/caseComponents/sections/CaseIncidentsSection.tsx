"use client";

import { getIncidentLabel, getIncidentStatusLabel } from "@/app/utils/utils";
import { Label } from "@/design-system/components/Label";
import { Typography } from "@/design-system/components/Typography";
import { colors } from "@/design-system/tokens";
import AssistantPhotoIcon from "@mui/icons-material/AssistantPhoto";
import ScheduleIcon from "@mui/icons-material/Schedule";
import { Box, CircularProgress } from "@mui/material";
import { useRouter } from "next/navigation";
import { Case } from "proto/hero/cases/v1/cases_pb";
import { Situation } from "proto/hero/situations/v2/situations_pb";

interface CaseIncidentsSectionProps {
  caseData: Case;
  situationData?: Situation;
  isLoadingSituation?: boolean;
}

export default function CaseIncidentsSection({
  caseData,
  situationData,
  isLoadingSituation = false,
}: CaseIncidentsSectionProps) {
  const situationId = caseData.situationIds?.[0] || "";
  const router = useRouter();

  const incident = situationData
    ? {
      id: situationData.id || "",
      displayId: situationData.id
        ? situationData.id?.replace(/[^0-9]/g, "")?.slice(0, 3)
        : "Unknown Id",
      // @ts-expect-error TODO: Fix type issue
      type: getIncidentLabel(situationData.type) || "Incident",
      description: situationData.description || "",
      date: situationData.createTime
        ?
        formatDate(situationData.createTime)
        : "",
      status:
        // @ts-expect-error TODO: Fix type issue
        getIncidentStatusLabel(situationData.status) || "Unknown Status",
      reporter: situationData.reporterName || "",
    }
    : null;

  // Format date from situation data
  function formatDate(data: string): string {
    try {
      const timestamp = data;
      if (!timestamp) return "";

      const dateObj = new Date(timestamp);

      const datePart = dateObj
        .toLocaleDateString("en-US", {
          month: "short",
          day: "numeric",
          year: "numeric",
        })
        .replace(/,/g, "");

      const timePart = dateObj.toLocaleTimeString("en-US", {
        hour: "numeric",
        minute: "2-digit",
        hour12: true,
      });

      return `${datePart} • ${timePart}`;
    } catch (e) {
      console.error("Error formatting date:", e);
      return "";
    }
  }

  // Handle click on an incident
  const handleIncidentClick = (incidentId: string) => {
    router.push(`/incidents?incidentId=${incidentId}`);
  };

  return (
    <Box
      sx={{
        borderRadius: 2,
        border: `1px solid ${colors.grey[200]}`,
        overflow: "hidden",
        height: "326px",
        display: "flex",
        flexDirection: "column",
        backgroundColor: "white",
      }}
    >
      <Box
        sx={{
          p: 2,
          py: 3,
          display: "flex",
          justifyContent: "space-between",
          alignItems: "center",
          position: "sticky",
          top: 0,
          zIndex: 1,
        }}
      >
        <Typography style="caps1" color={colors.grey[500]} lineHeight="22px">
          INCIDENTS
        </Typography>
      </Box>

      {isLoadingSituation ? (
        <Box
          sx={{
            p: 3,
            display: "flex",
            justifyContent: "center",
            alignItems: "center",
            flexGrow: 1,
          }}
        >
          <CircularProgress size={24} />
        </Box>
      ) : !situationId || !incident ? (
        <Box
          sx={{
            p: 3,
            display: "flex",
            justifyContent: "center",
            alignItems: "center",
            flexGrow: 1,
          }}
        >
          <Typography style="body2" color={colors.grey[500]}>
            No incidents found for this case
          </Typography>
        </Box>
      ) : (
        <Box
          sx={{
            overflowY: "auto",
            flexGrow: 1,
            mx: 1,
            borderTop: `1px solid ${colors.grey[200]}`,
          }}
        >
          <Box
            onClick={() => handleIncidentClick(incident.id)}
            sx={{
              p: 2,
              cursor: "pointer",
              "&:hover": {
                bgcolor: colors.grey[50],
              },
            }}
          >
            <Box sx={{ mb: 1 }}>
              <Box sx={{ display: "flex", alignItems: "center", gap: 1 }}>
                <AssistantPhotoIcon
                  sx={{
                    color: colors.grey[500],
                    fontSize: 18,
                  }}
                />
                <Typography style="body3" color={colors.grey[900]}>
                  {incident.displayId}
                  <Typography style="tag2" color={colors.grey[500]}>
                    {" "}
                    • {incident.type}
                  </Typography>
                </Typography>
                <Box sx={{ ml: 1 }}>
                  <Label color="blue" label={incident.status} size="small" />
                </Box>
              </Box>
            </Box>

            <Box
              sx={{
                display: "-webkit-box",
                WebkitLineClamp: 2,
                WebkitBoxOrient: "vertical",
                overflow: "hidden",
              }}
            >
              <Typography style="tag2" color={colors.grey[900]}>
                {incident.description}
              </Typography>
            </Box>

            <Box
              sx={{
                display: "flex",
                color: colors.grey[500],
                mt: 2,
                alignItems: "center",
                gap: 1,
              }}
            >
              <Typography style="tag2" color={colors.grey[500]}>
                Reporting Party:{" "}
                <Typography style="tag1" color={colors.grey[800]}>
                  {incident.reporter || "N/A"}
                </Typography>
              </Typography>
            </Box>

            <Box
              sx={{
                display: "flex",
                color: colors.grey[700],
                mt: 2,
                alignItems: "center",
                gap: 1,
              }}
            >
              <ScheduleIcon sx={{ color: colors.grey[700], fontSize: 14 }} />
              <Typography style="tag2" color="inherit">
                {incident.date}
              </Typography>
            </Box>
          </Box>
        </Box>
      )}
    </Box>
  );
}
