"use client";

import { useAddReportToCase } from "@/app/apis/services/workflow/cases/hooks";
import { useCreateOrder } from "@/app/apis/services/workflow/orders/hooks";
import {
  useBatchGetReports,
  useCreateReport,
  useCreateReportSection,
} from "@/app/apis/services/workflow/reports/v2/hooks";
import { Button } from "@/design-system/components/Button";
import { Label } from "@/design-system/components/Label";
import { Typography } from "@/design-system/components/Typography";
import { colors } from "@/design-system/tokens";
import AddIcon from "@mui/icons-material/Add";
import MoreVertIcon from "@mui/icons-material/MoreVert";
import ScheduleIcon from "@mui/icons-material/Schedule";
import { Box, CircularProgress, IconButton, Menu, MenuItem } from "@mui/material";
import { useRouter } from "next/navigation";
import { AddReportToCaseRequest, Case } from "proto/hero/cases/v1/cases_pb";
import {
  CreateReportRequest,
  Report,
  ReportStatus,
  ReportType
} from "proto/hero/reports/v2/reports_pb";
import { useState } from "react";
import { ReassignReportPopup } from "../../../reports/ReportComponents/common/ReassignReportPopup";

// Convert raw status string (e.g., "IN_PROGRESS") to Title Case human-readable text
function formatReportStatus(status: string): string {
  const cleanStatus = status.replace(/^REPORT_STATUS_/, "");
  return cleanStatus
    .toLowerCase()
    .split("_")
    .map((word) => word.charAt(0).toUpperCase() + word.slice(1))
    .join(" ");
}

function getReportStatusColor(
  status: string
): "grey" | "vine" | "amber" | "rose" {
  switch (status) {
    case "REPORT_STATUS_APPROVED":
      return "vine";
    case "REPORT_STATUS_CHANGES_REQUESTED":
    case "REPORT_STATUS_REJECTED":
      return "rose";
    case "REPORT_STATUS_IN_PROGRESS":
    case "REPORT_STATUS_SUBMITTED_FOR_REVIEW":
    case "REPORT_STATUS_UNDER_REVIEW":
    case "REPORT_STATUS_IN_REWORK":
      return "amber";
    case "REPORT_STATUS_UNSPECIFIED":
    case "REPORT_STATUS_ASSIGNED":
    case "REPORT_STATUS_CANCELLED":
    default:
      return "grey";
  }
}

interface CaseReportsSectionProps {
  caseData: Case;
}

export default function CaseReportsSection({
  caseData,
}: CaseReportsSectionProps) {
  const router = useRouter();
  const reportIds = caseData.reportIds || [];

  const {
    data: reportsResponse,
    isLoading,
    refetch: refetchReports,
  } = useBatchGetReports(reportIds, {
    enabled: reportIds.length > 0,
  });

  const { mutateAsync: addReportToCase } = useAddReportToCase();
  const { mutateAsync: createReportSection } = useCreateReportSection();
  const { mutate: createOrder } = useCreateOrder();

  // Add loading state for report creation
  const [isCreatingReport, setIsCreatingReport] = useState(false);

  // Menu state for supplemental reports
  const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null);
  const [selectedReportId, setSelectedReportId] = useState<string | null>(null);
  const open = Boolean(anchorEl);

  // Reassign popup state - now also used for creating new reports
  const [reassignPopupOpen, setReassignPopupOpen] = useState(false);
  const [isCreateMode, setIsCreateMode] = useState(false);

  // Get the selected report for reassignment
  const selectedReport = reportsResponse?.reports?.find(report => report.id === selectedReportId);

  const handleMenuClick = (event: React.MouseEvent<HTMLElement>, reportId: string) => {
    event.stopPropagation(); // Prevent report click
    setAnchorEl(event.currentTarget);
    setSelectedReportId(reportId);
  };

  const handleMenuClose = () => {
    setAnchorEl(null);
    setSelectedReportId(null);
  };

  const handleReassignReport = () => {
    // The selectedReportId is already set when the menu is clicked
    // We can directly open the reassign popup
    setReassignPopupOpen(true);
    setIsCreateMode(false);
    handleMenuClose();
  };

  const handleReassignPopupClose = () => {
    setReassignPopupOpen(false);
    setSelectedReportId(null);
    setIsCreateMode(false);
  };

  const handleReassignSuccess = () => {
    // Refetch reports to update the UI
    refetchReports();
  };

  // Process reports data for display
  const reports =
    reportsResponse?.reports?.map((report) => {
      return {
        id: report.id,
        displayId: report.id?.replace(/[^0-9]/g, "")?.slice(0, 9) || "Unknown Id",
        type:
          // @ts-expect-error TODO: Fix type issue
          report.reportType === "REPORT_TYPE_INCIDENT_PRIMARY"
            ? "Primary Report"
            : // @ts-expect-error TODO: Fix type issue
            report.reportType === "REPORT_TYPE_INCIDENT_SUPPLEMENTAL"
              ? "Supplemental Report"
              : "Unspecified Report",
        title: report.title || "",
        date: formatDate(report.createdAt) || "",
        status: report.status || "",
        reportType: report.reportType,
      };
    }) || [];

  // Check if there's already a primary report
  const hasPrimaryReport = reports.some(
    // @ts-expect-error TODO: Fix type issue
    (report) => report.reportType === "REPORT_TYPE_INCIDENT_PRIMARY"
  );

  // Create report sections function
  const createReportSections = async (reportId: string, isPrimary: boolean) => {
    try {
      // If it's a primary report, add incident details section first
      if (isPrimary) {
        const currentDate = new Date();

        await createReportSection({
          reportId,
          section: {
            // @ts-expect-error TODO: Fix type issue
            type: "SECTION_TYPE_INCIDENT_DETAILS",
            incidentDetails: {
              initialType: "SITUATION_TYPE_UNSPECIFIED",
              finalType: "SITUATION_TYPE_UNSPECIFIED",
              incidentStartTime: currentDate.toISOString(),
              incidentEndTime: currentDate.toISOString(),
              reportedTime: currentDate.toISOString(),
              incidentLocationStreetAddress: "",
              incidentLocationCity: "",
              incidentLocationState: "",
              incidentLocationZipCode: "",
              incidentLocationCountry: "",
              responders: [],
              reportingPerson: {
                firstName: "",
                middleName: "",
                lastName: "",
                phoneNumber: "",
                reporterRole: "Caller",
              },
              involvedAgencies: [],
            },
          },
        });
      }

      // Create People Entity List Section
      await createReportSection({
        reportId,
        section: {
          // @ts-expect-error TODO: Fix type issue
          type: "SECTION_TYPE_ENTITY_LIST_PEOPLE",
          entityList: {
            title: "People",
            entityRefs: [],
          },
        },
      });

      // Create Vehicles Entity List Section
      await createReportSection({
        reportId,
        section: {
          // @ts-expect-error TODO: Fix type issue
          type: "SECTION_TYPE_ENTITY_LIST_VEHICLE",
          entityList: {
            title: "Vehicles",
            entityRefs: [],
          },
        },
      });

      // Create Properties Entity List Section
      await createReportSection({
        reportId,
        section: {
          // @ts-expect-error TODO: Fix type issue
          type: "SECTION_TYPE_ENTITY_LIST_PROPERTIES",
          entityList: {
            title: "Properties",
            entityRefs: [],
          },
        },
      });

      // Create Narrative Section
      await createReportSection({
        reportId,
        section: {
          // @ts-expect-error TODO: Fix type issue
          type: "SECTION_TYPE_NARRATIVE",
          narrative: {
            richText: "",
          },
        },
      });

      // Create Media Section
      await createReportSection({
        reportId,
        section: {
          // @ts-expect-error TODO: Fix type issue
          type: "SECTION_TYPE_MEDIA",
        },
      });

      if (isPrimary) {
        // Create Offenses Section
        await createReportSection({
          reportId,
          section: {
            // @ts-expect-error TODO: Fix type issue
            type: "SECTION_TYPE_OFFENSE",
          },
        });
      }
    } catch (error) {
      console.error("Error creating report sections:", error);
    }
  };

  const { mutateAsync: createReport } = useCreateReport();

  const handleNewReport = () => {
    if (!caseData.id || isCreatingReport) return;

    // Open the reassign popup in create mode
    setReassignPopupOpen(true);
    setIsCreateMode(true);
    setSelectedReportId(null); // No existing report in create mode
  };

  // Handle report creation and assignment from the popup
  const handleCreateAndAssignReport = async (assigneeId: string) => {
    if (!caseData.id || isCreatingReport) return;

    setIsCreatingReport(true);

    const isPrimary = !hasPrimaryReport;
    const reportType = isPrimary
      ? ReportType.INCIDENT_PRIMARY
      : ReportType.INCIDENT_SUPPLEMENTAL;

    const reportTitle = isPrimary ? "Incident Report" : "Supplemental Report";

    try {
      // Create the report
      const response = await createReport({
        report: {
          title: reportTitle,
          situationId: caseData.situationIds?.[0] || "",
          status: ReportStatus.ASSIGNED,
          reportType: reportType,
          authorAssetId: assigneeId, // Set the assignee during creation
          assignedAt: new Date().toISOString(),
        } as Report,
      } as CreateReportRequest);

      if (response.report && caseData.id) {
        // Create all report sections
        await createReportSections(response.report.id, isPrimary);

        // Link the new report to the case
        await addReportToCase({
          caseId: caseData.id,
          reportId: response.report.id,
        } as unknown as AddReportToCaseRequest);

        // Note: Order creation is handled automatically by the backend
        // when a report is created with ASSIGNED status, so we don't need
        // to manually create an order here. This prevents duplicate orders.

        // Refetch reports to update the UI
        refetchReports();

        // Close the popup and reset states
        setReassignPopupOpen(false);
        setIsCreateMode(false);
        setSelectedReportId(null);
      }
    } catch (error) {
      console.error("Error in report creation process:", error);
      throw error; // Re-throw to let the popup handle the error
    } finally {
      setIsCreatingReport(false);
    }
  };

  // Handle click on a report
  const handleReportClick = (reportId: string) => {
    router.push(`/reports?reportId=${reportId}`);
  };

  // Format date from situation data
  function formatDate(data: string): string {
    try {
      const timestamp = data;
      if (!timestamp) return "";

      const dateObj = new Date(timestamp);

      const datePart = dateObj
        .toLocaleDateString("en-US", {
          month: "short",
          day: "numeric",
          year: "numeric",
        })
        .replace(/,/g, "");

      const timePart = dateObj.toLocaleTimeString("en-US", {
        hour: "numeric",
        minute: "2-digit",
        hour12: true,
      });

      return `${datePart} • ${timePart}`;
    } catch (e) {
      console.error("Error formatting date:", e);
      return "";
    }
  }

  return (
    <Box
      sx={{
        borderRadius: 2,
        border: `1px solid ${colors.grey[200]}`,
        overflow: "hidden",
        height: "326px",
        display: "flex",
        flexDirection: "column",
        backgroundColor: "white",
      }}
    >
      <Box
        sx={{
          p: 2,
          display: "flex",
          justifyContent: "space-between",
          alignItems: "center",
          position: "sticky",
          top: 0,
          zIndex: 1,
        }}
      >
        <Typography style="caps1" color={colors.grey[500]}>
          REPORTS
        </Typography>
        <Button
          size="small"
          leftIcon={<AddIcon fontSize="small" />}
          label={
            isCreatingReport
              ? "Creating..."
              : hasPrimaryReport
                ? "New supplemental report"
                : "New Report"
          }
          prominence={false}
          onClick={handleNewReport}
          isLoading={isCreatingReport}
          disabled={isCreatingReport}
        />
      </Box>

      {isLoading ? (
        <Box
          sx={{
            p: 3,
            display: "flex",
            justifyContent: "center",
            alignItems: "center",
            flexGrow: 1,
          }}
        >
          <CircularProgress size={24} />
        </Box>
      ) : reports.length === 0 ? (
        <Box
          sx={{
            p: 3,
            display: "flex",
            justifyContent: "center",
            alignItems: "center",
            flexGrow: 1,
          }}
        >
          <Typography style="body2" color={colors.grey[500]}>
            No reports found for this case
          </Typography>
        </Box>
      ) : (
        <Box
          sx={{
            overflowY: "auto",
            flexGrow: 1,
            mx: 1,
            borderTop: `1px solid ${colors.grey[200]}`,
          }}
        >
          {reports.map((report, index) => (
            <Box key={report.id}>
              <Box
                sx={{
                  p: 2,
                  cursor: "pointer",
                  "&:hover": {
                    backgroundColor: colors.grey[50],
                  },
                }}
                onClick={() => handleReportClick(report.id)}
              >
                <Box
                  sx={{ display: "flex", alignItems: "center", gap: 1, mb: 1 }}
                >
                  <Typography style="tag2" color={colors.grey[500]}>
                    {report.displayId}
                  </Typography>
                </Box>
                <Box sx={{ mb: 1 }}>
                  <Box sx={{ display: "flex", alignItems: "center", gap: 1, justifyContent: "space-between" }}>
                    <Box sx={{ display: "flex", alignItems: "center", gap: 1 }}>
                      <Typography style="body3" color={colors.grey[800]}>
                        {report.type}
                      </Typography>
                      <Box sx={{ ml: 1 }}>
                        <Label
                          // @ts-expect-error TODO: Fix type issue
                          color={getReportStatusColor(report.status)}
                          // @ts-expect-error TODO: Fix type issue
                          label={formatReportStatus(report.status)}
                          size="small"
                        />
                      </Box>
                    </Box>

                    {/* Show menu for all report types */}
                    <IconButton
                      aria-label={`menu for report ${report.id}`}
                      aria-controls={open && selectedReportId === report.id ? "report-menu" : undefined}
                      aria-haspopup="true"
                      aria-expanded={open && selectedReportId === report.id ? "true" : undefined}
                      onClick={(event) => handleMenuClick(event, report.id)}
                      size="small"
                      sx={{
                        color: colors.grey[600],
                        padding: "4px",
                        "&:hover": { backgroundColor: colors.grey[100] }
                      }}
                    >
                      <MoreVertIcon fontSize="small" />
                    </IconButton>
                  </Box>
                </Box>

                <Box
                  sx={{
                    display: "flex",
                    color: colors.grey[700],
                    mt: 2,
                    alignItems: "center",
                    gap: 1,
                  }}
                >
                  <ScheduleIcon
                    sx={{ color: colors.grey[700], fontSize: 14 }}
                  />
                  <Typography style="tag2" color="inherit">
                    {report.date}
                  </Typography>
                </Box>
              </Box>
              {index < reports.length - 1 && (
                <Box
                  sx={{ height: "1px", backgroundColor: colors.grey[200] }}
                />
              )}
            </Box>
          ))}
        </Box>
      )}

      <Menu
        id="report-menu"
        anchorEl={anchorEl}
        open={open}
        onClose={handleMenuClose}
        MenuListProps={{
          "aria-labelledby": "report-menu-button",
        }}
        anchorOrigin={{
          vertical: "bottom",
          horizontal: "right",
        }}
        transformOrigin={{
          vertical: "top",
          horizontal: "right",
        }}
        sx={{
          mt: 0.25,
          "& .MuiPaper-root": {
            borderRadius: "8px",
            minWidth: "200px",
          },
          "& .MuiMenuItem-root": {
            py: 1.5,
          },
        }}
      >
        <MenuItem onClick={handleReassignReport}>
          Reassign report
        </MenuItem>
      </Menu>

      {/* Reassign Report Popup */}
      <ReassignReportPopup
        open={reassignPopupOpen}
        onClose={handleReassignPopupClose}
        reportId={selectedReportId || undefined}
        onSuccess={handleReassignSuccess}
        isCreateMode={isCreateMode}
        onAssignReport={handleCreateAndAssignReport}
      />
    </Box>
  );
}
