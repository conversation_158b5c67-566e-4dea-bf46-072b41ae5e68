"use client";

import {
  listActiveAssignedOrdersForAsset,
  listOrdersForSituation,
} from "@/app/apis/services/workflow/orders/endpoints";
import {
  hookOrderStatusToString,
  hookOrderTypeToString,
} from "@/app/apis/services/workflow/orders/enumConverters";
import {
  useCreateOrder,
  useListOrdersForSituation,
} from "@/app/apis/services/workflow/orders/hooks";
import {
  useAddAdditionalInfoToSituation,
  useAddSituationUpdate,
  useUpdateSituation,
} from "@/app/apis/services/workflow/situations/hooks";
import { createUpdateEntry } from "@/app/cad/components/ActionPane/utils";
import { PTTCommsComponent } from "@/app/cad/components/CommsComponents/PTTCommsComponent";
import { getOrderStatusLabel, toTitleCase } from "@/app/utils/utils";
import { useQueries } from "@tanstack/react-query";
import mapboxgl from "mapbox-gl";
import "mapbox-gl/dist/mapbox-gl.css";
import { Asset } from "proto/hero/assets/v2/assets_pb";
import {
  CreateOrderRequest,
  ListActiveAssignedOrdersForAssetRequest,
  ListOrdersForSituationRequest,
  Order,
  OrderStatus,
  OrderType,
} from "proto/hero/orders/v2/orders_pb";
import {
  AddAdditionalInfoRequest,
  AddSituationUpdateRequest,
  Situation,
  UpdateSituationRequest,
} from "proto/hero/situations/v2/situations_pb";
import React, { useCallback, useEffect, useImperativeHandle, useMemo, useRef, useState } from "react";
import ReactDOM from "react-dom/client";
import CameraModal from "./CameraModal";
import CameraSnippetModal from "./CameraSnippetModal";
import CollapsedActionPane from "./CollapsedActionPane";
import ActiveSituationMarker from "./Markers/ActiveSituationMarker";
import ActiveSituationPopup from "./Markers/ActiveSituationPopup";
import AgentMarker from "./Markers/AgentMarker";
import AgentMarkerPopup from "./Markers/AgentMarkerPopup";
import CameraMarker from "./Markers/CameraMarker";
import CameraMarkerPopup from "./Markers/CameraMarkerPopup";
import SearchedLocationMarker from "./Markers/SearchedLocationMarker";
import SearchedLocationPopup from "./Markers/SearchedLocationPopup";
import SearchFilterBar from "./SearchFilterBar";

mapboxgl.accessToken =
  "pk.eyJ1IjoiaGVyby1zYWZldHkiLCJhIjoiY202bDM3bGlzMDRybTJrcHJibm5sYTFzMSJ9.-ekjZGG1E_cWYCOnBrdEag";

interface MapComponentProps {
  selectedSituation: Situation | null;
  agents: Asset[];
  cameras: Asset[];
  selectedLocation?: {
    placeName: string;
    address: string;
    coordinates: [number, number];
  };
  activeSituations: Situation[];
  isActionPaneExpanded: boolean;
  onSelectSituation: (situation: Situation) => void;
  onExpandActionPane: () => void;
}

// Add ref interface for exposing methods
export interface MapComponentRef {
  centerOnAsset: (asset: Asset) => void;
  setSelectedResponder: (asset: Asset | null) => void;
  openAssetPopup: (asset: Asset) => void;
}

const MapComponent = React.forwardRef<MapComponentRef, MapComponentProps>(({
  selectedSituation,
  agents,
  cameras,
  selectedLocation,
  activeSituations,
  isActionPaneExpanded,
  onSelectSituation,
  onExpandActionPane,
}, ref) => {
  // ------------------------------------------------------------------
  // REFS & STATE
  // ------------------------------------------------------------------
  const mapContainer = useRef<HTMLDivElement>(null);
  const map = useRef<mapboxgl.Map | null>(null);
  const searchMarkerRef = useRef<mapboxgl.Marker | null>(null);
  const agentMarkersRef = useRef<Map<string, mapboxgl.Marker>>(new Map());
  const cameraMarkersRef = useRef<Map<string, mapboxgl.Marker>>(new Map());
  const sessionTokenRef = useRef<string>(
    "6b531b57-d837-4ee5-9ea2-e9aa47c03e16"
  );

  const { mutate: createOrder } = useCreateOrder();
  const { mutate: updateSituation } = useUpdateSituation();
  const { mutate: addSituationUpdate } = useAddSituationUpdate();
  const { mutate: addAdditionalInfo } = useAddAdditionalInfoToSituation();

  const [mapSize, setMapSize] = useState({ width: 0, height: 0 });
  const [selectedResponder, setSelectedResponder] = useState<Asset | null>(
    null
  );
  const [selectedCameras, setSelectedCameras] = useState<Asset[] | null>(null);
  const [searchQuery, setSearchQuery] = useState("");
  const [searchResults, setSearchResults] = useState<any[]>([]);
  const [isSearchFocused, setIsSearchFocused] = useState(false);
  const [showCameras, setShowCameras] = useState(false);
  const [unitStatusFilter, setUnitStatusFilter] = useState<string>("All Units");
  const [searchedLocation, setSearchedLocation] = useState<{
    placeName: string;
    address: string;
    coordinates: [number, number];
  } | null>(null);
  const [agentTimes, setAgentTimes] = useState<Record<string, number>>({});
  const [isMapLoaded, setIsMapLoaded] = useState(false);
  const [popupOpenIncidentId, setPopupOpenIncidentId] = useState<
    string | undefined
  >();

  const latitude = selectedSituation?.latitude;
  const longitude = selectedSituation?.longitude;
  const selectedSituationtId = selectedSituation?.id;

  // Dynamic styling based on map size
  const buttonPaddingX = mapSize.width ? Math.max(6, mapSize.width * 0.01) : 14;
  const buttonPaddingY = mapSize.height
    ? Math.max(4, mapSize.height * 0.01)
    : 6;
  const fontSize = mapSize.width ? Math.max(12, mapSize.width * 0.01) : 14;
  const baseSearchInputWidth = mapSize.width
    ? Math.max(250, mapSize.width * 1)
    : 200;
  const expandedSearchInputWidth = baseSearchInputWidth * 5;
  const currentSearchWidth = isSearchFocused
    ? expandedSearchInputWidth
    : baseSearchInputWidth;

  const orderQueries = useQueries({
    queries: agents
      .filter((agent) => {
        // Only fetch orders for agents whose status we actually use
        return (
          agent.status &&
          // @ts-expect-error TODO: Fix type issue
          agent.status !== "ASSET_STATUS_UNSPECIFIED" &&
          // @ts-expect-error TODO: Fix type issue
          agent.status !== "ASSET_STATUS_OFFLINE" &&
          // @ts-expect-error TODO: Fix type issue
          agent.status !== "ASSET_STATUS_DEACTIVATED"
        );
      })
      .map((agent) => ({
        queryKey: ["activeAssignedOrders", agent.id],
        queryFn: () =>
          listActiveAssignedOrdersForAsset({
            assetId: agent.id,
          } as ListActiveAssignedOrdersForAssetRequest),
        staleTime: 2 * 60 * 1000,
        retry: 2,
        refetchInterval: 3000,
        refetchIntervalInBackground: true,
      })),
  });

  const ordersMap = useMemo(() => {
    const map: Record<string, any> = {};
    const filteredAgents = agents.filter((agent) => {
      return (
        agent.status &&
        // @ts-expect-error TODO: Fix type issue
        agent.status !== "ASSET_STATUS_UNSPECIFIED" &&
        // @ts-expect-error TODO: Fix type issue
        agent.status !== "ASSET_STATUS_OFFLINE" &&
        // @ts-expect-error TODO: Fix type issue
        agent.status !== "ASSET_STATUS_DEACTIVATED"
      );
    });

    filteredAgents.forEach((agent, index) => {
      const result = orderQueries[index];
      if (result.data) {
        map[agent.id] = result.data;
      }
    });
    return map;
  }, [orderQueries, agents]);

  const ordersForActiveSituationsQueries = useQueries({
    queries: activeSituations.map((situation) => ({
      queryKey: ["ordersForSituation", situation.id],
      queryFn: () =>
        listOrdersForSituation({
          situationId: situation.id || "",
          pageSize: 100,
          pageToken: "",
        } as ListOrdersForSituationRequest),
      refetchInterval: 3000,
    })),
  });

  const { data: ordersForSituation } = useListOrdersForSituation(
    {
      situationId: selectedSituationtId || "",
      pageSize: 100,
      pageToken: "",
    } as ListOrdersForSituationRequest,
    {
      refetchInterval: 3000,
    }
  );

  const areAssistMemberOrdersCompleted = ordersForSituation?.orders?.every(
    (order) =>
      hookOrderStatusToString(order.status) === "ORDER_STATUS_COMPLETED" ||
      hookOrderStatusToString(order.status) === "ORDER_STATUS_CANCELLED" ||
      hookOrderStatusToString(order.status) === "ORDER_STATUS_REJECTED" ||
      hookOrderTypeToString(order.type) !== "ORDER_TYPE_ASSIST_MEMBER"
  );

  // ------------------------------------------------------------------
  // FETCH AGENT TRAVEL TIMES USING MAPBOX DIRECTIONS
  // ------------------------------------------------------------------
  useEffect(() => {
    if (
      !selectedSituation ||
      typeof selectedSituation.latitude !== "number" ||
      typeof selectedSituation.longitude !== "number"
    ) {
      setAgentTimes({});
      return;
    }

    const destLat =
      selectedLocation?.coordinates[1] || selectedSituation.latitude;
    const destLng =
      selectedLocation?.coordinates[0] || selectedSituation.longitude;

    // For each agent, use Mapbox Directions API to fetch travel time.
    const fetchTimeForAgent = async (
      agent: Asset
    ): Promise<{ id: string; time?: number }> => {
      console.log("Fetching time for agent", agent.id);
      if (
        typeof agent.latitude !== "number" ||
        typeof agent.longitude !== "number"
      ) {
        return { id: agent.id };
      }
      const originLng = agent.longitude;
      const originLat = agent.latitude;
      console.log("Origin:", originLat, originLng);
      const directionsUrl = `https://api.mapbox.com/directions/v5/mapbox/walking/${originLng},${originLat};${destLng},${destLat}?access_token=${mapboxgl.accessToken}&overview=false`;
      try {
        const response = await fetch(directionsUrl);
        const data = await response.json();
        if (data.routes && data.routes[0] && data.routes[0].duration) {
          // Convert duration (in seconds) to minutes.
          return {
            id: agent.id,
            time: Math.round(data.routes[0].duration / 60),
          };
        }
      } catch (error) {
        console.error("Error fetching directions for agent", agent.id, error);
      }
      return { id: agent.id };
    };

    // Compute travel times for all agents.
    Promise.all(agents.map(fetchTimeForAgent)).then((results) => {
      const times: Record<string, number> = {};
      results.forEach((result) => {
        if (result.time !== undefined) {
          times[result.id] = result.time;
        }
      });
      setAgentTimes(times);
    });
  }, [selectedSituation, selectedLocation, agents]);

  // ------------------------------------------------------------------
  // HANDLE MAP ZOOM
  // ------------------------------------------------------------------
  const [zoomLevel, setZoomLevel] = useState(0);

  useEffect(() => {
    if (!map.current) return;
    // Initialize zoom
    setZoomLevel(map.current.getZoom());

    // Listen for zoom changes
    map.current.on("zoom", () => {
      setZoomLevel(map.current!.getZoom());
    });

    // Cleanup
    return () => {
      map.current?.off("zoom", () => { });
    };
  }, [isMapLoaded]);

  // ------------------------------------------------------------------
  // MAP VIEW SWITCH FUNCTIONS
  // ------------------------------------------------------------------
  const switchToPlan = () => {
    if (!map.current) return;
    map.current.easeTo({
      pitch: 0,
      bearing: 0,
      duration: 1000,
    });
  };

  const switchTo3D = () => {
    if (!map.current) return;
    map.current.easeTo({
      pitch: 45,
      bearing: 0,
      duration: 1000,
    });
  };

  const centerMap = () => {
    if (!map.current) return;
    let lngLat: mapboxgl.LngLatLike;
    if (selectedLocation) {
      lngLat = selectedLocation.coordinates;
      map.current.easeTo({
        center: lngLat,
        zoom: 12.5,
        pitch: 0,
        bearing: 0,
        duration: 1000,
      });
    } else if (!latitude || !longitude) {
      lngLat = [-122.4294, 37.7749];
      map.current.easeTo({
        center: lngLat,
        zoom: 12.5,
        pitch: 0,
        bearing: 0,
        duration: 1000,
      });
    } else {
      lngLat = [longitude, latitude];
      map.current.easeTo({
        center: lngLat,
        zoom: 15,
        pitch: 0,
        bearing: 0,
        duration: 1000,
      });
    }
  };

  // Function to center map on a specific asset
  const centerOnAsset = (asset: Asset) => {
    if (!map.current || typeof asset.latitude !== "number" || typeof asset.longitude !== "number") {
      return;
    }

    map.current.flyTo({
      center: [asset.longitude, asset.latitude],
      zoom: 15,
      duration: 1000,
      essential: true,
    });
  };

  // Function to open asset popup
  const openAssetPopup = (asset: Asset) => {
    if (!map.current) return;

    // Find the marker for this asset
    const assetMarker = agentMarkersRef.current.get(asset.id);
    if (assetMarker) {
      // Close all other popups first
      agentMarkersRef.current.forEach((otherMarker) => {
        const popup = otherMarker.getPopup();
        if (popup && popup.isOpen()) {
          popup.remove();
        }
      });

      // Open the popup for this asset
      assetMarker.togglePopup();
    }
  };

  // Expose methods through ref
  useImperativeHandle(ref, () => ({
    centerOnAsset,
    setSelectedResponder,
    openAssetPopup,
  }));

  // ------------------------------------------------------------------
  // MAP INITIALIZATION & RESIZE HANDLING
  // ------------------------------------------------------------------
  useEffect(() => {
    if (!mapContainer.current) return;

    map.current = new mapboxgl.Map({
      container: mapContainer.current,
      style: "mapbox://styles/hero-safety/cm9n1pd7j004d01ss66zrbjwm",
      center: [-122.4294, 37.7749],
      zoom: 12.5,
      attributionControl: false,
      logoPosition: "bottom-right",
    });

    map.current.on("load", () => {
      setIsMapLoaded(true);
    });

    // Deselect responder when clicking on a non-marker area
    map.current.on("click", () => {
      setSelectedResponder(null);
    });

    // Observe container size changes to update the map size state
    const resizeObserver = new ResizeObserver((entries) => {
      for (const entry of entries) {
        const { width, height } = entry.contentRect;
        setMapSize({ width, height });
      }
    });
    resizeObserver.observe(mapContainer.current);

    return () => {
      map.current?.remove();
      resizeObserver.disconnect();
    };
  }, []);

  // Resize map when dimensions change
  useEffect(() => {
    if (map.current && mapSize.width > 0 && mapSize.height > 0) {
      map.current.resize();
    }
  }, [mapSize]);

  // ------------------------------------------------------------------
  // RE-CENTER THE MAP AFTER RESIZE (WAITING FOR PANEL RESIZE TO FINISH)
  // ------------------------------------------------------------------
  useEffect(() => {
    if (
      latitude !== null &&
      longitude !== null &&
      typeof latitude === "number" &&
      typeof longitude === "number" &&
      map.current
    ) {
      const lngLat: mapboxgl.LngLatLike = [longitude, latitude];
      // Delay centering to allow panel resize animation to complete
      const timeoutId = setTimeout(() => {
        map.current?.resize();
        map.current?.flyTo({
          center: lngLat,
          zoom: 15,
          duration: 1000,
          essential: true,
        });
      }, 100);
      return () => clearTimeout(timeoutId);
    }
  }, [latitude, longitude]);

  // ------------------------------------------------------------------
  // AGENT (RESPONDER) MARKERS HANDLING
  // ------------------------------------------------------------------
  useEffect(() => {
    if (!isMapLoaded || !map.current) return;

    // Filter agents
    const filteredAgents = agents.filter((agent) => {
      // Always filter out unknown or offline assets
      if (
        !agent.status ||
        // @ts-expect-error TODO: Fix type issue
        agent.status === "ASSET_STATUS_UNSPECIFIED" ||
        // @ts-expect-error TODO: Fix type issue
        agent.status === "ASSET_STATUS_OFFLINE" ||
        // @ts-expect-error TODO: Fix type issue
        agent.status === "ASSET_STATUS_DEACTIVATED"
      ) {
        return false;
      }

      // Then apply additional filtering based on unitStatusFilter
      if (unitStatusFilter === "All Units") return true;
      if (unitStatusFilter === "Available")
        // @ts-expect-error TODO: Fix type issue
        return agent.status === "ASSET_STATUS_AVAILABLE";
      if (unitStatusFilter === "Busy")
        // @ts-expect-error TODO: Fix type issue
        return agent.status !== "ASSET_STATUS_AVAILABLE";
      if (unitStatusFilter === "None") return false;

      return true;
    });

    // Remove any markers for agents not in filteredAgents
    agentMarkersRef.current.forEach((marker, id) => {
      if (!filteredAgents.find((agent) => agent.id === id)) {
        marker.remove();
        agentMarkersRef.current.delete(id);
      }
    });

    // Update or create markers for each agent
    filteredAgents.forEach((agent) => {
      const { id, latitude: agentLat, longitude: agentLng } = agent;
      if (
        typeof agentLat !== "number" ||
        typeof agentLng !== "number" ||
        isNaN(agentLat) ||
        isNaN(agentLng)
      ) {
        console.warn(`Invalid coordinates for agent ID ${id}:`, {
          latitude: agentLat,
          longitude: agentLng,
        });
        return;
      }
      const lngLat: mapboxgl.LngLatLike = [agentLng, agentLat];

      // Determine if agent is assigned to the selected incident
      const assignedOrders = ordersMap[agent.id]?.orders || [];
      const isAssignedToSelectedIncident = selectedSituation
        ? assignedOrders.some(
          (ord: Order) =>
            ord.situationId === selectedSituation.id &&
            // @ts-expect-error TODO: Fix type issue
            ord.type === "ORDER_TYPE_ASSIST_MEMBER"
        )
        : false;
      const orderForSituation = assignedOrders.find(
        (ord: Order) =>
          ord.situationId === selectedSituation?.id &&
          // @ts-expect-error TODO: Fix type issue
          ord.type === "ORDER_TYPE_ASSIST_MEMBER"
      );
      const time = agentTimes[id];

      // Check if we already have a marker for this agent
      if (agentMarkersRef.current.has(id)) {
        // ---------------------------------------------
        // CASE A: Marker exists -> Update in place
        // ---------------------------------------------
        const marker = agentMarkersRef.current.get(id)!;
        marker.setLngLat(lngLat);

        // Grab our React root from the marker element
        const el = marker.getElement() as HTMLElement & { _reactRoot?: any };
        const markerRoot = el._reactRoot;
        if (markerRoot) {
          markerRoot.render(
            <AgentMarker
              asset={agent}
              isSelected={!!selectedResponder && selectedResponder.id === id}
              isAssignedToSelectedIncident={isAssignedToSelectedIncident}
              orderStatus={
                orderForSituation?.status
                  ? getOrderStatusLabel(orderForSituation?.status) ==
                    "In Progress" && orderForSituation?.typeSpecificStatus
                    ? toTitleCase(orderForSituation?.typeSpecificStatus)
                    : getOrderStatusLabel(orderForSituation?.status)
                  : "Unknown"
              }
              zoomLevel={zoomLevel}
            />
          );
        }

        // Update popup content
        const popup = marker.getPopup();
        if (popup) {
          const popupRoot = (popup as any)._popupRoot;
          if (popupRoot) {
            popupRoot.render(
              <AgentMarkerPopup
                asset={agent}
                selectedIncident={selectedSituation}
                activeAssignedOrders={assignedOrders}
                activeIncidents={activeSituations}
                onGoToIncident={onSelectSituation}
                onDispatch={(assetId) => {
                  handleAssignAgent(assetId);
                  popup.remove();
                }}
                time={time}
              />
            );
          }
        }
      } else {
        // ---------------------------------------------
        // CASE B: Marker does not exist -> Create once
        // ---------------------------------------------
        const el = document.createElement("div");
        el.className = "agent-marker";
        el.style.cursor = "pointer";

        // Create a React root on this element and render the marker
        const markerRoot = ReactDOM.createRoot(el);
        markerRoot.render(
          <AgentMarker
            asset={agent}
            isSelected={
              !!selectedResponder && selectedResponder.id === agent.id
            }
            isAssignedToSelectedIncident={isAssignedToSelectedIncident}
            orderStatus={
              orderForSituation?.status
                ? getOrderStatusLabel(orderForSituation?.status) ==
                  "In Progress" && orderForSituation?.typeSpecificStatus
                  ? toTitleCase(orderForSituation?.typeSpecificStatus)
                  : getOrderStatusLabel(orderForSituation?.status)
                : "Unknown"
            }
            zoomLevel={zoomLevel}
          />
        );
        // Store the root so we can re-render on updates
        (el as any)._reactRoot = markerRoot;

        // Create and place the marker
        const marker = new mapboxgl.Marker(el)
          .setLngLat(lngLat)
          .addTo(map.current!);

        // Popup
        const popupContainer = document.createElement("div");
        const popupRoot = ReactDOM.createRoot(popupContainer);
        popupRoot.render(
          <AgentMarkerPopup
            asset={agent}
            selectedIncident={selectedSituation}
            activeAssignedOrders={assignedOrders}
            activeIncidents={activeSituations}
            onGoToIncident={onSelectSituation}
            onDispatch={(assetId) => {
              handleAssignAgent(assetId);
              popup.remove();
            }}
            time={time}
          />
        );

        const popup = new mapboxgl.Popup({
          offset: 22,
          anchor: "top",
          maxWidth: "300px",
        }).setDOMContent(popupContainer);

        (popup as any)._popupRoot = popupRoot;
        marker.setPopup(popup);

        // When clicking the marker, close other popups and toggle this one
        el.addEventListener("click", (event) => {
          event.stopPropagation();
          agentMarkersRef.current.forEach((m) => {
            const p = m.getPopup();
            if (p && p.isOpen()) {
              p.remove();
            }
          });
          setSelectedResponder(agent);
          marker.togglePopup();
        });

        // Keep track of it
        agentMarkersRef.current.set(id, marker);
      }
    });
  }, [
    agents,
    selectedResponder,
    unitStatusFilter,
    agentTimes,
    selectedSituation,
    isMapLoaded,
    ordersMap,
    zoomLevel,
  ]);

  // ------------------------------------------------------------------
  // CAMERA MARKERS HANDLING
  // ------------------------------------------------------------------
  const camerasRef = useRef(cameras);
  const camerasSourceAddedRef = useRef(false);

  useEffect(() => {
    camerasRef.current = cameras;
  }, [cameras]);

  // Store the camera click handler in a ref to prevent recreation
  const handleCameraClick = useCallback((asset: Asset) => {
    setSelectedCameras([asset]);
  }, []);

  // Effect: Update camera markers
  useEffect(() => {
    if (!map.current) return;

    // Clear existing camera markers if showCameras is false
    if (!showCameras) {
      cameraMarkersRef.current.forEach((marker) => marker.remove());
      cameraMarkersRef.current.clear();
      return;
    }

    // Track which markers we want to keep
    const newMarkerIds = new Set<string>();

    // Add or update camera markers
    cameras.forEach((camera) => {
      if (
        typeof camera.latitude === "number" &&
        typeof camera.longitude === "number" &&
        !isNaN(camera.latitude) &&
        !isNaN(camera.longitude)
      ) {
        newMarkerIds.add(camera.id);

        // If marker already exists, just update its state
        const existingMarker = cameraMarkersRef.current.get(camera.id);
        if (existingMarker) {
          const el = existingMarker.getElement();
          const root = (el as any)._reactRoot;
          if (root) {
            root.render(
              <CameraMarker
                asset={camera}
                isSelected={selectedCameras?.some((c) => c.id === camera.id)}
                zoomLevel={map.current?.getZoom()}
              />
            );
          }
          return;
        }

        // Create new marker if it doesn't exist
        const el = document.createElement("div");
        const root = ReactDOM.createRoot(el);
        (el as any)._reactRoot = root;

        root.render(
          <CameraMarker
            asset={camera}
            isSelected={selectedCameras?.some((c) => c.id === camera.id)}
            zoomLevel={map.current?.getZoom()}
          />
        );

        const marker = new mapboxgl.Marker(el)
          .setLngLat([camera.longitude, camera.latitude])
          .addTo(map.current!);

        // Create popup container and root
        const popupContainer = document.createElement("div");
        const popupRoot = ReactDOM.createRoot(popupContainer);
        (popupContainer as any)._reactRoot = popupRoot;

        popupRoot.render(
          <CameraMarkerPopup
            asset={camera}
            onViewCamera={handleCameraClick}
            onClose={() => marker.getPopup()?.remove()}
          />
        );

        const popup = new mapboxgl.Popup({
          offset: 22,
          anchor: "top",
          maxWidth: "300px",
        }).setDOMContent(popupContainer);

        marker.setPopup(popup);

        // When clicking the marker, close other popups and toggle this one
        el.addEventListener("click", (event) => {
          event.stopPropagation();
          cameraMarkersRef.current.forEach((m) => {
            const p = m.getPopup();
            if (p && p.isOpen()) {
              p.remove();
            }
          });
          marker.togglePopup();
        });

        cameraMarkersRef.current.set(camera.id, marker);
      }
    });

    // Remove any markers that are no longer needed
    cameraMarkersRef.current.forEach((marker, id) => {
      if (!newMarkerIds.has(id)) {
        marker.remove();
        cameraMarkersRef.current.delete(id);
      }
    });
  }, [cameras, showCameras, map.current, handleCameraClick]); // Removed selectedCameras from dependencies

  // ------------------------------------------------------------------
  // SEARCH HANDLERS & CUSTOM MARKER
  // ------------------------------------------------------------------
  const handleSearchChange = async (e: React.ChangeEvent<HTMLInputElement>) => {
    const query = e.target.value;
    setSearchQuery(query);

    if (query.trim() === "") {
      setSearchResults([]);
      return;
    }

    try {
      const suggestUrl = `https://api.mapbox.com/search/searchbox/v1/suggest?q=${encodeURIComponent(
        query
      )}&access_token=${mapboxgl.accessToken}&session_token=${sessionTokenRef.current
        }&proximity=-122.4194,37.7749`;
      const response = await fetch(suggestUrl);
      const data = await response.json();
      console.log("Search suggestions:", JSON.stringify(data.suggestions));
      setSearchResults(data.suggestions || []);
    } catch (error) {
      console.error("Error fetching search suggestions:", error);
    }
  };

  const addCustomMarker = (
    lngLat: mapboxgl.LngLatLike,
    placeName?: string,
    address?: string
  ) => {
    if (!map.current) return;
    if (searchMarkerRef.current) {
      searchMarkerRef.current.remove();
    }

    // Create the marker element and attach a persistent React root
    const markerEl = document.createElement("div");
    const markerRoot = ReactDOM.createRoot(markerEl);
    markerRoot.render(<SearchedLocationMarker />);
    (markerEl as any)._reactRoot = markerRoot;
    markerEl.style.cursor = "pointer";

    // Create a container for the popup content and attach a persistent React root
    const popupContainer = document.createElement("div");
    const popupRoot = ReactDOM.createRoot(popupContainer);
    popupRoot.render(
      <SearchedLocationPopup
        locationName={placeName}
        address={address || "Selected Location"}
        coordinates={{
          lat: (lngLat as [number, number])[1],
          lng: (lngLat as [number, number])[0],
        }}
        selectedSituation={selectedSituation}
        onImageClick={() => { }}
        onAddToIncident={() => {
          handleLocationUpdate(
            address || undefined,
            (lngLat as [number, number])[1],
            (lngLat as [number, number])[0]
          );
          addSituationUpdate(
            {
              id: selectedSituationtId,
              update: createUpdateEntry(
                `Changed location to ${placeName || "Unknown location"}`,
                "info change",
                "dispatch",
                "Dispatch"
              ),
            } as AddSituationUpdateRequest,
            {
              onSuccess: (data) => {
                console.log("Adding situation update result:", data);
              },
              onError: (error) => {
                console.error("Error adding situation update:", error);
              },
            }
          );
        }}
      />
    );
    // Optionally store the root on the popup container if needed later
    (popupContainer as any)._reactRoot = popupRoot;

    // // Create the popup with desired options and set its DOM content
    const popup = new mapboxgl.Popup({
      anchor: "top",
      offset: 10,
      maxWidth: "300px",
    }).setDOMContent(popupContainer);
    (popup as any)._root = popupRoot;

    // Create the marker, attach the popup, and add it to the map
    searchMarkerRef.current = new mapboxgl.Marker(markerEl)
      .setLngLat(lngLat)
      .setPopup(popup)
      .addTo(map.current);

    // Automatically open the popup
    searchMarkerRef.current.togglePopup();
  };

  const parseAdditionalInfo = (additionalInfoJson: string) => {
    try {
      return JSON.parse(additionalInfoJson);
    } catch {
      return {};
    }
  };

  const handleSuggestionClick = async (suggestion: any) => {
    try {
      const retrieveUrl = `https://api.mapbox.com/search/searchbox/v1/retrieve/${suggestion.mapbox_id}?access_token=${mapboxgl.accessToken}&session_token=...`;
      const response = await fetch(retrieveUrl);
      const data = await response.json();

      if (data.features && data.features.length > 0) {
        const feature = data.features[0];

        // Use name for main address field, with fallback to actual address
        const displayAddress =
          feature.properties.name ||
          feature.properties.address ||
          feature.properties.full_address ||
          "";
        const actualAddress =
          feature.properties.address || feature.properties.full_address || "";

        if (map.current) {
          map.current.flyTo({
            center: feature.geometry.coordinates,
            zoom: 18,
            essential: true,
          });
          addCustomMarker(
            feature.geometry.coordinates,
            feature.properties.name,
            displayAddress
          );
        }

        setSearchQuery(displayAddress);
        setSearchedLocation({
          placeName: feature.properties.name || "Unknown",
          address: displayAddress,
          coordinates: feature.geometry.coordinates,
        });

        // Build location metadata for additionalInfoJson (same logic as ActionPane)
        const locationMetadata: any = {};

        // Add addressCommonName with actual street address
        if (actualAddress) {
          locationMetadata.addressCommonName = actualAddress;
        }

        // Add addressCity if place name exists
        if (feature.properties.context?.place?.name) {
          locationMetadata.addressCity = feature.properties.context.place.name;
        }

        // Add addressState if region exists (format: "CA - California")
        if (
          feature.properties.context?.region?.region_code &&
          feature.properties.context?.region?.name
        ) {
          locationMetadata.addressState = `${feature.properties.context.region.region_code} - ${feature.properties.context.region.name}`;
        }

        // Add addressZip if postcode exists
        if (feature.properties.context?.postcode?.name) {
          locationMetadata.addressZip =
            feature.properties.context.postcode.name;
        }

        // Get current additional info and merge with location metadata
        if (selectedSituation) {
          const currentAdditionalInfo = selectedSituation.additionalInfoJson
            ? parseAdditionalInfo(selectedSituation.additionalInfoJson)
            : {};

          const updatedAdditionalInfo = {
            ...currentAdditionalInfo,
            ...locationMetadata,
          };

          // Save the updated additional info
          addAdditionalInfo({
            id: selectedSituation.id,
            additionalInfoJson: JSON.stringify(updatedAdditionalInfo),
          } as AddAdditionalInfoRequest);
        }

        setSearchResults([]);
      }
    } catch (error) {
      console.error("Error retrieving feature:", error);
    }
  };

  const removeSearchMarker = () => {
    if (searchMarkerRef.current) {
      searchMarkerRef.current.remove();
      searchMarkerRef.current = null;
    }
  };

  const handleLocationUpdate = (
    address: string | undefined,
    latitude: number,
    longitude: number
  ) => {
    removeSearchMarker();
    setSearchedLocation(null);

    console.log("Updating situation location:", latitude, longitude);
    updateSituation(
      {
        situation: {
          id: selectedSituationtId,
          latitude,
          longitude,
          address,
        },
      } as UpdateSituationRequest,
      {
        onSuccess: (data) => {
          console.log("Updating situation Mutation result:", data);
        },
        onError: (error) => {
          console.error(" Updating situation Error updating situation:", error);
        },
      }
    );
  };

  useEffect(() => {
    if (selectedLocation) {
      handleLocationUpdate(
        selectedLocation.address,
        selectedLocation.coordinates[1],
        selectedLocation.coordinates[0]
      );
      if (map.current) {
        map.current.flyTo({
          center: selectedLocation.coordinates,
          zoom: 18,
          essential: true,
        });
      }
    }
  }, [selectedLocation]);

  useEffect(() => {
    // If there is a searched location, then remove it
    if (searchedLocation) {
      removeSearchMarker();
      setSearchedLocation(null);
    }
  }, [selectedSituation]);

  useEffect(() => {
    if (!map.current) return;

    const handleMapClickForSearchedLocation = (e: mapboxgl.MapMouseEvent) => {
      if (!searchedLocation) return;
      const { lng, lat } = e.lngLat;
      const updatedLocation = {
        ...searchedLocation,
        coordinates: [lng, lat] as [number, number],
      };
      setSearchedLocation(updatedLocation);
      addCustomMarker([lng, lat]);
      map.current?.flyTo({
        center: [lng, lat],
        essential: true,
        curve: 0,
      });
    };

    // Add the click listener if a searched location exists
    if (searchedLocation) {
      map.current.on("dblclick", handleMapClickForSearchedLocation);
      map.current.doubleClickZoom.disable();
    }

    return () => {
      if (map.current) {
        map.current.off("dblclick", handleMapClickForSearchedLocation);
        map.current.doubleClickZoom.enable();
      }
    };
  }, [searchedLocation]);

  // ------------------------------------------------------------------
  // AGENT ASSIGNMENT
  // ------------------------------------------------------------------
  const handleAssignAgent = async (agentId: string) => {
    if (!selectedSituationtId) {
      return;
    }

    await createOrder(
      {
        order: {
          situationId: selectedSituationtId,
          assetId: agentId,
          status: OrderStatus.CREATED,
          type: OrderType.ASSIST_MEMBER,
        },
      } as CreateOrderRequest,
      {
        onSuccess: () => {
          // Log a situation update after order creation
          addSituationUpdate({
            id: selectedSituationtId,
            update: createUpdateEntry(
              "Created",
              "order created",
              agentId,
              agentId?.replace(/[^0-9]/g, "")?.slice(0, 3)
            ),
          } as AddSituationUpdateRequest);
        },
      }
    );
  };

  // ------------------------------------------------------------------
  // CAMERA INCIDENT HANDLING
  // ------------------------------------------------------------------
  const isCameraIncident =
    // @ts-expect-error TODO: Fix type issue
    selectedSituation?.triggerSource === "TRIGGER_SOURCE_CAMERA_EVENT";

  // ------------------------------------------------------------------
  // ACTIVE SITUATIONS MARKERS HANDLING
  // ------------------------------------------------------------------
  const activeSituationMarkersRef = useRef<Map<string, mapboxgl.Marker>>(
    new Map()
  );

  // Create stable event handlers to avoid stale closures
  const createPopupEventHandlers = useCallback(
    (situationId: string) => ({
      onOpen: () => setPopupOpenIncidentId(situationId),
      onClose: () => setPopupOpenIncidentId(undefined),
    }),
    []
  );

  useEffect(() => {
    // Only proceed when map is loaded
    if (!isMapLoaded) return;

    activeSituations.forEach((situation, index) => {
      const ordersForSituation = ordersForActiveSituationsQueries[index].data;
      const isAllAssistMemberOrdersCompleted =
        ordersForSituation?.orders?.every(
          (order) =>
            hookOrderStatusToString(order.status) ===
            "ORDER_STATUS_COMPLETED" ||
            hookOrderStatusToString(order.status) ===
            "ORDER_STATUS_CANCELLED" ||
            hookOrderStatusToString(order.status) === "ORDER_STATUS_REJECTED" ||
            hookOrderTypeToString(order.type) !== "ORDER_TYPE_ASSIST_MEMBER"
        );
      // Validate coordinates
      if (
        typeof situation.latitude !== "number" ||
        typeof situation.longitude !== "number" ||
        isNaN(situation.latitude) ||
        isNaN(situation.longitude)
      ) {
        console.warn(`Invalid coordinates for situation ID ${situation.id}`);
        return;
      }

      const lngLat: mapboxgl.LngLatLike = [
        situation.longitude,
        situation.latitude,
      ];

      // CASE A: Marker already exists —> Update it
      if (activeSituationMarkersRef.current.has(situation.id)) {
        const marker = activeSituationMarkersRef.current.get(situation.id)!;
        marker.setLngLat(lngLat);

        // Update the marker's icon via the persistent React root
        const el = marker.getElement();
        const markerRoot = (el as any)._reactRoot;
        if (markerRoot) {
          markerRoot.render(
            <ActiveSituationMarker
              incidentId={situation.id}
              selectedIncidentId={selectedSituation?.id}
              selectedIncident={
                situation.id === selectedSituation?.id ? situation : null
              }
              zoomLevel={zoomLevel}
              popupOpenIncidentId={popupOpenIncidentId}
            />
          );
        }

        // Update the popup content using its stored React root
        const popup = marker.getPopup();
        if (popup) {
          const popupRoot = (popup as any)._root;
          if (popupRoot) {
            popupRoot.render(
              <ActiveSituationPopup
                incidentId={situation.id}
                selectedSituationId={selectedSituation?.id}
                activeSituations={activeSituations}
                onGoToIncident={() => {
                  onSelectSituation(situation);
                  marker.getPopup()?.remove();
                }}
                isAllAssistMemberOrdersCompleted={
                  isAllAssistMemberOrdersCompleted
                }
              />
            );
          }

          // Add popup event listeners if not already added
          if (!(popup as any)._hasEventListeners) {
            const { onOpen, onClose } = createPopupEventHandlers(situation.id);
            popup.on("open", onOpen);
            popup.on("close", onClose);
            (popup as any)._hasEventListeners = true;
          }
        }
      }
      // CASE B: Marker does not exist yet —> Create a new one
      else {
        // Create the container element for the marker and attach a persistent React root
        const el = document.createElement("div");
        const markerRoot = ReactDOM.createRoot(el);
        markerRoot.render(
          <ActiveSituationMarker
            incidentId={situation.id}
            selectedIncidentId={selectedSituation?.id}
            selectedIncident={
              situation.id === selectedSituation?.id ? situation : null
            }
            zoomLevel={zoomLevel}
            popupOpenIncidentId={popupOpenIncidentId}
          />
        );
        // Store the root on the element for future updates
        (el as any)._reactRoot = markerRoot;
        el.style.cursor = "pointer";

        // Create a container for the popup content and attach a persistent React root
        const popupContainer = document.createElement("div");
        const popupRoot = ReactDOM.createRoot(popupContainer);

        // Create the popup, attach the container, and store the React root on it
        const popup = new mapboxgl.Popup({
          anchor: "top",
          offset: 22,
          maxWidth: "300px",
        }).setDOMContent(popupContainer);

        // Track when popup opens and closes with stable handlers
        const { onOpen, onClose } = createPopupEventHandlers(situation.id);
        popup.on("open", onOpen);
        popup.on("close", onClose);
        (popup as any)._root = popupRoot;

        // Create the marker and add it to the map
        if (map.current) {
          const marker = new mapboxgl.Marker(el)
            .setLngLat(lngLat)
            .setPopup(popup)
            .addTo(map.current);
          activeSituationMarkersRef.current.set(situation.id, marker);

          // Now render the popup content after marker is created
          popupRoot.render(
            <ActiveSituationPopup
              incidentId={situation.id}
              selectedSituationId={selectedSituation?.id}
              activeSituations={activeSituations}
              onGoToIncident={() => {
                onSelectSituation(situation);
                marker.getPopup()?.remove();
              }}
              isAllAssistMemberOrdersCompleted={
                isAllAssistMemberOrdersCompleted
              }
            />
          );
        }
      }
    });

    // Remove markers for any situations that are no longer active
    activeSituationMarkersRef.current.forEach((marker, id) => {
      if (!activeSituations.some((situation) => situation.id === id)) {
        marker.remove();
        activeSituationMarkersRef.current.delete(id);
      }
    });
  }, [
    activeSituations,
    isMapLoaded,
    selectedSituation,
    onSelectSituation,
    zoomLevel,
    createPopupEventHandlers,
    popupOpenIncidentId,
  ]);

  // ------------------------------------------------------------------
  // COMPONENT RENDER
  // ------------------------------------------------------------------
  return (
    <div style={{ position: "relative", width: "100%", height: "100%" }}>
      <SearchFilterBar
        // Search props
        searchQuery={searchQuery}
        onSearchChange={handleSearchChange}
        onSuggestionClick={handleSuggestionClick}
        searchResults={searchResults}
        isSearchFocused={isSearchFocused}
        onSearchFocus={() => setIsSearchFocused(true)}
        onSearchBlur={() => setIsSearchFocused(false)}
        // Filter props (pass the new unit status filter state)
        showCameras={showCameras}
        setShowCameras={setShowCameras}
        unitStatusFilter={unitStatusFilter}
        setUnitStatusFilter={setUnitStatusFilter}
        responders={agents}
        // Style props
        currentSearchWidth={currentSearchWidth}
        buttonPaddingX={buttonPaddingX}
        buttonPaddingY={buttonPaddingY}
        fontSize={fontSize}
        // View props
        onSwitchToPlan={switchToPlan}
        onSwitchTo3D={switchTo3D}
        onCenterMap={centerMap}
        // Handle agent assignment
        onAssignAgent={handleAssignAgent}
        // Positioning prop
        isCameraIncident={isCameraIncident}
      />

      <div ref={mapContainer} className="w-full h-full" />

      <PTTCommsComponent />

      {selectedCameras && (
        <CameraModal
          selectedCameras={selectedCameras}
          onClose={() => setSelectedCameras(null)}
        />
      )}

      <CameraSnippetModal
        selectedSituation={selectedSituation}
        isCameraIncident={isCameraIncident}
      />

      {!isActionPaneExpanded && selectedSituationtId && (
        <CollapsedActionPane
          selectedSituationId={selectedSituationtId}
          onExpand={onExpandActionPane}
          situationStatus={selectedSituation.status}
          areAssistMemberOrdersCompleted={areAssistMemberOrdersCompleted}
        />
      )}
    </div>
  );
});

MapComponent.displayName = "MapComponent";

export default MapComponent;
