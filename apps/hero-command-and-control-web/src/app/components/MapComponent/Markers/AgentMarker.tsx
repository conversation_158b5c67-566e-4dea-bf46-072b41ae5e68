import { Asset } from "proto/hero/assets/v2/assets_pb";
import React from "react";

interface AgentMarkerProps {
  asset: Asset;
  isSelected?: boolean;
  isAssignedToSelectedIncident?: boolean;
  orderStatus?: string;
  zoomLevel?: number;
}

const AgentMarker: React.FC<AgentMarkerProps> = ({
  asset,
  isSelected = false,
  isAssignedToSelectedIncident = false,
  orderStatus,
  zoomLevel = 0,
}) => {
  // @ts-expect-error TODO: Fix type issue
  const isAvailable = asset.status === "ASSET_STATUS_AVAILABLE";
  const mainColor = isAvailable ? "#009F89" : "#AD0363";
  const showDetails = isAssignedToSelectedIncident && zoomLevel > 13;
  const containerColor = isAssignedToSelectedIncident
    ? "#0060FFF2"
    : isSelected
      ? mainColor
      : "#FFFFFF40";

  const iconColor = isAssignedToSelectedIncident
    ? "#FFFFFF"
    : isSelected
      ? "#FFFFFF"
      : mainColor;

  const bottomCircleColor = isAssignedToSelectedIncident
    ? "#0060FF"
    : mainColor;
  const containerBorderRadius = showDetails ? "25px" : "40px";
  const shortId = asset.name?.slice(0, 1)?.toUpperCase() + "-" + asset.id?.replace(/[^0-9]/g, "")?.slice(0, 3);

  const containerTransition =
    "background-color 0.5s ease, border 0.5s ease, border-radius 0.5s ease";
  const textTransition = showDetails
    ? "margin-left 1s ease 1s, opacity 1s ease 1s, max-width 1s ease 1s, max-height 1s ease 1s"
    : "margin-left 1s ease 0s, opacity 1s ease 0s, max-width 1s ease 0s, max-height 1s ease 0s";
  const bottomTextTransition = "opacity 0.5s ease";

  // Get responder initials (first letter of first word and first letter of last word)
  const getInitials = (name: string | undefined): string => {
    if (!name || name.trim() === "") return "A";

    const words = name.trim().split(/\s+/);
    if (words.length === 1) {
      return words[0].slice(0, 1).toUpperCase();
    }

    const firstInitial = words[0].slice(0, 1).toUpperCase();
    const lastInitial = words[words.length - 1].slice(0, 1).toUpperCase();
    return firstInitial + lastInitial;
  };

  const responderInitials = getInitials(asset.name);

  return (
    <div
      style={{
        display: "flex",
        flexDirection: "column",
        alignItems: "center",
        lineHeight: 1,
      }}
    >
      {/* Expandable marker */}
      <div
        style={{
          display: "flex",
          alignItems: "center",
          justifyContent: showDetails ? "flex-start" : "center",
          backgroundColor: isAssignedToSelectedIncident ? containerColor : (showDetails ? containerColor : (isSelected ? mainColor : "#FFFFFF")),
          border: isAssignedToSelectedIncident
            ? "2px solid #0060FF"
            : (showDetails
              ? (isAssignedToSelectedIncident ? "2px solid #0060FF" : `2px solid ${mainColor}`)
              : (isSelected ? "none" : `2px solid ${mainColor}`)),
          borderRadius: showDetails ? containerBorderRadius : "50%",
          marginBottom: "2px",
          padding: "8px",
          paddingRight: showDetails ? "12px" : "8px",
          transition: containerTransition,
          backdropFilter: "blur(4px)",
          width: showDetails ? "auto" : "44px",
          height: "44px",
        }}
      >
        <span
          style={{
            color: iconColor,
            fontFamily: "Roboto",
            fontSize: "20px",
            fontStyle: "normal",
            fontWeight: 500,
            lineHeight: "45.714px",
            textAlign: "center",
          }}
        >
          {responderInitials}
        </span>

        <div
          style={{
            display: "flex",
            flexDirection: "column",
            alignItems: "flex-start",
            marginLeft: showDetails ? "8px" : "0px",
            opacity: showDetails ? 1 : 0,
            maxWidth: showDetails ? "200px" : "0px",
            whiteSpace: "nowrap",
            overflow: "hidden",
            transition: textTransition,
            maxHeight:
              isAssignedToSelectedIncident && zoomLevel > 13 ? "70px" : "0px",
          }}
        >
          {isAssignedToSelectedIncident && orderStatus && (
            <>
              <div
                style={{
                  color: "#FFFFFF",
                  fontWeight: 600,
                  fontSize: "12px",
                }}
              >
                {shortId}
              </div>
              <div
                style={{
                  backgroundColor: "#FFFFFFCC",
                  borderRadius: "6px",
                  color: "#0060FF",
                  fontSize: "10px",
                  fontWeight: 600,
                  padding: "2px 6px",
                  marginTop: "4px",
                }}
              >
                {orderStatus}
              </div>
            </>
          )}
        </div>
      </div>

      {/* Middle: White circle with inner circle - show when assigned to selected incident or when agent is selected */}
      {(isAssignedToSelectedIncident || isSelected) && (
        <div
          style={{
            position: "relative",
            width: "16px",
            height: "16px",
            marginBottom: "2px",
          }}
        >
          <div
            style={{
              width: "100%",
              height: "100%",
              borderRadius: "50%",
              backgroundColor: "white",
              display: "flex",
              alignItems: "center",
              justifyContent: "center",
            }}
          >
            <div
              style={{
                width: "10px",
                height: "10px",
                borderRadius: "50%",
                backgroundColor: isAssignedToSelectedIncident ? "#0060FFF2" : mainColor,
              }}
            />
          </div>
        </div>
      )}

      <div
        style={{
          backgroundColor: "#FFFFFF",
          color: mainColor,
          padding: "2px 4px",
          borderRadius: "3px",
          fontSize: "10px",
          fontWeight: 500,
          fontFamily: "Roboto",
          opacity: isSelected || isAssignedToSelectedIncident ? 0 : 1,
          transition: bottomTextTransition,
        }}
      >
        {shortId}
      </div>
    </div>
  );
};

export default AgentMarker;
