import { getIncidentLabel } from "@/app/utils/utils";
import { Situation } from "proto/hero/situations/v2/situations_pb";
import React from "react";

interface ActiveSituationMarkerProps {
  incidentId: string;
  selectedIncidentId?: string;
  selectedIncident?: Situation | null;
  zoomLevel: number;
  popupOpenIncidentId?: string;
}

// Helper function to get street address only (cut at first comma)
const getStreetAddressOnly = (address: string): string => {
  if (!address) return "";
  const commaIndex = address.indexOf(",");
  return commaIndex > 0 ? address.substring(0, commaIndex).trim() : address.trim();
};

const ActiveSituationMarker: React.FC<ActiveSituationMarkerProps> = ({
  incidentId,
  selectedIncidentId,
  selectedIncident,
  zoomLevel,
  popupOpenIncidentId,
}) => {
  const isSelected = incidentId === selectedIncidentId;
  const shortId = incidentId?.replace(/[^0-9]/g, "")?.slice(0, 3) || "Unknown";

  // Define transitions conditionally
  const containerTransition =
    isSelected && zoomLevel > 13
      ? "background-color 0.5s ease 0s, transform 0.5s ease 0.5s"
      : "background-color 0.5s ease 0s, transform 0.5s ease 0s";
  const iconTransition =
    isSelected && zoomLevel > 13 ? "transform 0.5s ease 0.5s" : "transform 0.5s ease 0s";
  const textTransition =
    isSelected && zoomLevel > 13
      ? "margin-left 0.5s ease 1s, opacity 0.5s ease 1s, max-width 1s ease 1s, max-height 1s ease 1s"
      : "margin-left 0.5s ease 0s, opacity 0.5s ease 0s, max-width 1s ease 0s, max-height 1s ease 0s";

  return (
    <div
      style={{
        display: "flex",
        flexDirection: "column",
        alignItems: "center",
        lineHeight: 1,
      }}
    >
      {/* Container for flag icon and expanded card */}
      <div
        style={{
          display: "flex",
          alignItems: "center",
          justifyContent: isSelected ? "flex-start" : "center",
          padding: isSelected ? "8px" : "0px",
          width: isSelected ? "auto" : "32px",
          height: isSelected ? "auto" : "32px",
          backgroundColor: isSelected ? "#0060FFF2" : popupOpenIncidentId === incidentId ? "#E17100" : "#FFEDD4",
          borderRadius: isSelected ? "8px" : "3.794px",
          border: isSelected ? "none" : "1.63px solid #E17100",
          marginBottom: "10px",
          transition: containerTransition,
          transform: isSelected && zoomLevel > 13 ? "rotate(0deg)" : "rotate(45deg)",
        }}
      >
        <svg
          xmlns="http://www.w3.org/2000/svg"
          width="23"
          height="22"
          viewBox="0 0 23 22"
          fill="none"
          style={{
            boxSizing: "border-box",
            width: "22px",
            height: "22px",
            flexShrink: 0,
            transition: iconTransition,
            transform: isSelected && zoomLevel > 13 ? "rotate(0deg)" : "rotate(-45deg)",
          }}
        >
          <path
            d="M13.2417 5.04171L13.0217 3.94171C12.9392 3.52004 12.5633 3.20837 12.1233 3.20837H5.54167C5.0375 3.20837 4.625 3.62087 4.625 4.12504V17.875C4.625 18.3792 5.0375 18.7917 5.54167 18.7917C6.04583 18.7917 6.45833 18.3792 6.45833 17.875V12.375H11.5917L11.8117 13.475C11.8942 13.9059 12.27 14.2084 12.71 14.2084H17.4583C17.9625 14.2084 18.375 13.7959 18.375 13.2917V5.95837C18.375 5.45421 17.9625 5.04171 17.4583 5.04171H13.2417Z"
            fill={isSelected ? "#FFFFFF" : popupOpenIncidentId === incidentId ? "#FFFFFF" : "#E17100"}
          />
        </svg>
        <div
          style={{
            overflow: "hidden",
            whiteSpace: "nowrap",
            marginLeft: isSelected && zoomLevel > 13 ? "8px" : "0px",
            opacity: isSelected && zoomLevel > 13 ? 1 : 0,
            maxWidth: isSelected && zoomLevel > 13 ? "200px" : "0px",
            transition: textTransition,
            color: "#FFFFFF",
            fontSize: "14px",
            fontWeight: 500,
            maxHeight: isSelected && zoomLevel > 13 ? "50px" : "0px",
          }}
        >
          {isSelected && selectedIncident ? (
            <div style={{ display: "flex", flexDirection: "column" }}>
              <div style={{ display: "flex", alignItems: "center" }}>
                <span style={{ fontWeight: "700", marginRight: "4px" }}>
                  {shortId}
                </span>
                <span style={{ fontWeight: "500", marginRight: "4px" }}>
                  {/* @ts-expect-error TODO: Fix type issue */}
                  {getIncidentLabel(selectedIncident.type)}
                </span>
              </div>
              <div
                style={{
                  marginTop: "4px",
                  fontSize: "12px",
                  lineHeight: "14px",
                  opacity: 0.9,
                }}
              >
                {getStreetAddressOnly(selectedIncident.address)}
              </div>
            </div>
          ) : null}
        </div>
      </div>

      {/* Middle: White circle with inner circle */}
      <div
        style={{
          position: "relative",
          width: "16px",
          height: "16px",
          marginBottom: "2px",
        }}
      >
        <div
          style={{
            width: "100%",
            height: "100%",
            borderRadius: "50%",
            backgroundColor: "white",
            display: "flex",
            alignItems: "center",
            justifyContent: "center",
          }}
        >
          <div
            style={{
              width: "10px",
              height: "10px",
              borderRadius: "50%",
              backgroundColor: isSelected ? "#0060FFF2" : "#E17100",
            }}
          />
        </div>
      </div>

      {/* Optional: Bottom incident id box (hidden when expanded) */}
      <div
        style={{
          backgroundColor: "#FFFFFFCC",
          color: "#E17100",
          padding: "2px 4px",
          borderRadius: "3px",
          fontSize: "10px",
          fontWeight: 500,
          fontFamily: "Roboto",
          opacity: isSelected ? 0 : 1,
        }}
      >
        INC {shortId}
      </div>
    </div>
  );
};

export default ActiveSituationMarker;
