"use client";

import React, { useState } from "react";
import { Box, Tabs, Tab, Autocomplete, TextField, Table, TableBody, TableCell, TableContainer, TableHead, TableRow, Paper } from "@mui/material";
import { colors } from "@/design-system/tokens";
import { Typography as TypographyDS } from "@/design-system/components/Typography";
import { Button } from "@/design-system/components/Button";
import CloseIcon from "@mui/icons-material/Close";
import AddIcon from "@mui/icons-material/Add";
import DeleteIcon from "@mui/icons-material/Delete";
import CheckIcon from "@mui/icons-material/Check";
import { TextInput, InputType, InputMask } from "@/design-system/components/TextInput";
import { 
  useUpdateSituationRolePermission, 
  useListSituationRolePermissions,
  useUpdateCaseRolePermission,
  useListCaseRolePermissions,
  situationRolePermissionKeys,
  caseRolePermissionKeys
} from "@/app/apis/services/perms/hooks";
import { 
  UpdateSituationRolePermissionRequest, 
  ListSituationRolePermissionsRequest,
  UpdateCaseRolePermissionRequest,
  ListCaseRolePermissionsRequest,
  ObjectViewer 
} from "proto/hero/permissions/v1/permissions_pb";
import { useQueryClient } from "@tanstack/react-query";

const PERMISSION_DISPLAY_MAP: Record<string, string> = {
  'CAN_MANAGE': 'Can Manage',
  'CAN_EDIT': 'Can Edit',
  'CAN_VIEW': 'Can View',
  'CAN_FIND': 'Can Find',
  'BLOCKED': 'Blocked',
};

const toDisplay = (permission: string) => PERMISSION_DISPLAY_MAP[permission] || permission;

const toRaw = (permission: string) => {
  return Object.keys(PERMISSION_DISPLAY_MAP).find(key => PERMISSION_DISPLAY_MAP[key] === permission) || permission;
};

const PERMISSION_OPTIONS = Object.values(PERMISSION_DISPLAY_MAP);

interface RolePermission {
  id: string;
  name: string;
  permission: string;
}

interface UserPermission {
  id: string;
  name: string;
  permission: string;
}

interface ManageModalProps {
  onClose: () => void;
  objectId: string;
  objectType: 'situation' | 'case';
  showAssignTab: boolean;
  showDetailsTab: boolean;
  objectName: string;
}

const ManageModal: React.FC<ManageModalProps> = ({ 
  onClose, 
  objectId, 
  objectType,
  showAssignTab, 
  showDetailsTab,
  objectName,
}) => {
  const queryClient = useQueryClient();
  const [activeTab, setActiveTab] = useState(0);
  const assignTabIndex = showAssignTab ? 0 : -1;
  const permissionTabIndex = showAssignTab ? 1 : 0;
  const detailsTabIndex = showDetailsTab ? showAssignTab ? 2 : 1 : -1;
  const [selectedUser, setSelectedUser] = useState<string | null>(null);
  const [selectedPermission, setSelectedPermission] = useState<string | null>(null);
  const [inputValue, setInputValue] = useState('');
  const [editingUserId, setEditingUserId] = useState<string | null>(null);
  const [editingRoleId, setEditingRoleId] = useState<string | null>(null);
  const [editingPermission, setEditingPermission] = useState<string | null>(null);

  const { data: situationRolePermissionsData } = useListSituationRolePermissions({
    situationId: objectId,
  } as ListSituationRolePermissionsRequest, {
    enabled: objectType === 'situation'
  });

  const { data: caseRolePermissionsData } = useListCaseRolePermissions({
    caseId: objectId,
  } as ListCaseRolePermissionsRequest, {
    enabled: objectType === 'case'
  });

  const rolePermissionsData = objectType === 'situation' ? situationRolePermissionsData : caseRolePermissionsData;

  const { mutate: setSituationObjectPermissions } = useUpdateSituationRolePermission();
  const { mutate: setCaseObjectPermissions } = useUpdateCaseRolePermission();

  const assignedRolePermissions: RolePermission[] = rolePermissionsData?.objectViewers.map((viewer: ObjectViewer) => ({
    id: viewer.roleId,
    name: viewer.roleName,
    permission: viewer.permission ? (viewer.permission as unknown as string).replace('OBJECT_PERMISSION_', '') : 'OTHER'
  })) || [];

  const mockUsers = [
    { id: '1', name: 'John Doe', email: '<EMAIL>' },
    { id: '2', name: 'Jane Smith', email: '<EMAIL>' },
    { id: '3', name: 'Bob Johnson', email: '<EMAIL>' },
    { id: '4', name: 'Alice Brown', email: '<EMAIL>' },
    { id: '5', name: 'Charlie Wilson', email: '<EMAIL>' },
  ];

  const [assignedPermissions, setAssignedPermissions] = useState<UserPermission[]>([
    { id: '1', name: 'John Doe', permission: 'Can Edit' },
    { id: '2', name: 'Jane Smith', permission: 'Can View' },
    { id: '3', name: 'Bob Johnson', permission: 'Can Edit' },
  ]);

  const handleRemovePermission = (userId: string) => {
    setAssignedPermissions(assignedPermissions.filter(user => user.id !== userId));
  };

  const handleRemoveRolePermission = (roleId: string) => {
    const commonPayload = {
      roleId,
      permission: 5, // BLOCKED
    };

    const onSuccess = () => {
      if (objectType === 'situation') {
        queryClient.invalidateQueries({ queryKey: situationRolePermissionKeys.list({ situationId: objectId } as ListSituationRolePermissionsRequest) });
      } else if (objectType === 'case') {
        queryClient.invalidateQueries({ queryKey: caseRolePermissionKeys.list({ caseId: objectId } as ListCaseRolePermissionsRequest) });
      }
    };

    if (objectType === 'situation') {
      setSituationObjectPermissions(
        {
          ...commonPayload,
          situationId: objectId,
        } as UpdateSituationRolePermissionRequest,
        { onSuccess }
      );
    } else if (objectType === 'case') {
      setCaseObjectPermissions(
        {
          ...commonPayload,
          caseId: objectId,
        } as UpdateCaseRolePermissionRequest,
        { onSuccess }
      );
    }
  };

  const handlePermissionChange = (userId: string, newPermission: string) => {
    setAssignedPermissions(assignedPermissions.map(user => 
      user.id === userId ? { ...user, permission: newPermission } : user
    ));
    setEditingUserId(userId);
  };

  const handleRolePermissionChange = (roleId: string, newPermission: string) => {
    setEditingRoleId(roleId);
    setEditingPermission(toRaw(newPermission));
  };

  const handleSaveRolePermission = (roleId: string) => {
    if (!editingPermission) {
      console.error('No permission to save');
      return;
    }

    const permissionMap: Record<string, number> = {
      'CAN_MANAGE': 1,
      'CAN_EDIT': 2,
      'CAN_VIEW': 3,
      'CAN_FIND': 4,
      'BLOCKED': 5,
    };

    const permission = permissionMap[editingPermission];
    if (!permission) {
      console.error('Invalid permission:', editingPermission);
      return;
    }

    const commonPayload = {
      roleId,
      permission,
    };

    const onSuccess = () => {
      setEditingRoleId(null);
      setEditingPermission(null);
      if (objectType === 'situation') {
        queryClient.invalidateQueries({ queryKey: situationRolePermissionKeys.list({ situationId: objectId } as ListSituationRolePermissionsRequest) });
      } else if (objectType === 'case') {
        queryClient.invalidateQueries({ queryKey: caseRolePermissionKeys.list({ caseId: objectId } as ListCaseRolePermissionsRequest) });
      }
    };

    if (objectType === 'situation') {
      setSituationObjectPermissions({
        ...commonPayload,
        situationId: objectId,
      } as UpdateSituationRolePermissionRequest, { onSuccess });
    } else if (objectType === 'case') {
      setCaseObjectPermissions({
        ...commonPayload,
        caseId: objectId,
      } as UpdateCaseRolePermissionRequest, { onSuccess });
    }
  };

  const handleTabChange = (event: React.SyntheticEvent, newValue: number) => {
    setActiveTab(newValue);
  };

  const objectTypeDisplay = objectType === 'situation' ? 'Incident' : 'Case';

  return (
    <div
      className="modal-overlay"
      style={{
        position: "fixed",
        top: 0,
        left: 0,
        right: 0,
        bottom: 0,
        backgroundColor: "rgba(0,0,0,0.5)",
        display: "flex",
        justifyContent: "center",
        alignItems: "center",
        zIndex: 1000,
      }}
      onClick={onClose}
    >
      <div
        className="modal-content"
        style={{
          backgroundColor: "white",
          padding: "24px",
          borderRadius: "8px",
          width: "600px",
          maxWidth: "95vw",
          maxHeight: "95vh",
          boxShadow: "0 4px 8px rgba(0,0,0,0.2)",
          display: "flex",
          flexDirection: "column",
        }}
        onClick={(e) => e.stopPropagation()}
      >
        <div
          style={{
            display: "flex",
            justifyContent: "space-between",
            alignItems: "center",
            marginBottom: "16px",
            flexShrink: 0,
          }}
        >
          <TypographyDS style="h3" color={colors.grey[900]}>
            Manage {objectName}
          </TypographyDS>
          <button
            onClick={onClose}
            style={{
              backgroundColor: "transparent",
              border: "none",
              cursor: "pointer",
              display: "flex",
              alignItems: "center",
              color: colors.grey[600],
            }}
          >
            <CloseIcon />
          </button>
        </div>

        <Box sx={{ borderBottom: 1, borderColor: 'divider', mb: 3, flexShrink: 0 }}>
          <Tabs 
            value={activeTab} 
            onChange={handleTabChange}
            sx={{
              '& .MuiTab-root': {
                textTransform: 'none',
                fontWeight: 500,
                fontSize: '14px',
                minWidth: 100,
              },
              '& .Mui-selected': {
                color: colors.blue[600],
              },
              '& .MuiTabs-indicator': {
                backgroundColor: colors.blue[600],
              },
            }}
          >
            {showAssignTab && <Tab label="Assign" />}
            <Tab label="Permissions" />
            {showDetailsTab && <Tab label="Details & Tags" />}
          </Tabs>
        </Box>

        <div style={{ 
          marginBottom: "16px", 
          minHeight: "200px",
          overflowY: "auto",
          flexGrow: 1,
          paddingRight: "8px",
        }}>
          {activeTab === assignTabIndex && (
            <Box sx={{ width: '100%' }}>
              <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}>
                <Box sx={{ display: 'flex', gap: 2, alignItems: 'center' }}>
                  <Autocomplete
                    value={selectedUser}
                    onChange={(event: any, newValue: string | null) => {
                      setSelectedUser(newValue);
                    }}
                    inputValue={inputValue}
                    onInputChange={(event, newInputValue) => {
                      setInputValue(newInputValue);
                    }}
                    options={mockUsers.map(user => user.name)}
                    renderInput={(params) => (
                      <TextField
                        {...params}
                        placeholder="Enter User"
                        sx={{
                          '& .MuiOutlinedInput-root': {
                            '& fieldset': {
                              borderColor: colors.grey[300],
                            },
                            '&:hover fieldset': {
                              borderColor: colors.grey[400],
                            },
                            '&.Mui-focused fieldset': {
                              borderColor: colors.blue[600],
                            },
                          },
                        }}
                      />
                    )}
                    sx={{
                      flex: 1,
                      '& .MuiAutocomplete-input': {
                        fontSize: '14px',
                      },
                    }}
                  />
                  <Button
                    label={`Assign ${objectTypeDisplay}`}
                    color="blue"
                    prominence={true}
                    onClick={() => {
                      console.log(`Assigning ${objectTypeDisplay.toLowerCase()} to:`, selectedUser);
                    }}
                  />
                </Box>

                <Box sx={{ mt: 4, mb: 3 }}>
                  <Box sx={{ mb: 2 }}>
                    <TypographyDS style="h4" color={colors.grey[900]}>
                      Assigned Users
                    </TypographyDS>
                  </Box>

                  <TableContainer component={Paper} sx={{ boxShadow: 'none', border: `1px solid ${colors.grey[200]}` }}>
                    <Table>
                      <TableHead>
                        <TableRow>
                          <TableCell sx={{ backgroundColor: colors.grey[50], fontWeight: 500 }}>User</TableCell>
                          <TableCell sx={{ backgroundColor: colors.grey[50], fontWeight: 500 }}>Role</TableCell>
                          <TableCell sx={{ backgroundColor: colors.grey[50], width: '50px' }}></TableCell>
                        </TableRow>
                      </TableHead>
                      <TableBody>
                        {assignedPermissions.map((user) => (
                          <TableRow key={user.id}>
                            <TableCell>{user.name}</TableCell>
                            <TableCell>{user.permission}</TableCell>
                            <TableCell>
                              <Button
                                label=""
                                leftIcon={<DeleteIcon sx={{ fontSize: "18px", color: colors.grey[600] }} />}
                                color="grey"
                                prominence={false}
                                onClick={() => handleRemovePermission(user.id)}
                              />
                            </TableCell>
                          </TableRow>
                        ))}
                      </TableBody>
                    </Table>
                  </TableContainer>
                </Box>
              </Box>
            </Box>
          )}
          {activeTab === permissionTabIndex && (
            <Box sx={{ width: '100%' }}>
              <Box sx={{ display: 'flex', gap: 2, alignItems: 'center', mb: 3 }}>
                <Autocomplete
                  value={selectedUser}
                  onChange={(event: any, newValue: string | null) => {
                    setSelectedUser(newValue);
                  }}
                  inputValue={inputValue}
                  onInputChange={(event, newInputValue) => {
                    setInputValue(newInputValue);
                  }}
                  options={mockUsers.map(user => user.name)}
                  renderInput={(params) => (
                    <TextField
                      {...params}
                      placeholder="Enter User"
                      sx={{
                        '& .MuiOutlinedInput-root': {
                          '& fieldset': {
                            borderColor: colors.grey[300],
                          },
                          '&:hover fieldset': {
                            borderColor: colors.grey[400],
                          },
                          '&.Mui-focused fieldset': {
                            borderColor: colors.blue[600],
                          },
                        },
                      }}
                    />
                  )}
                  sx={{
                    flex: 1,
                    '& .MuiAutocomplete-input': {
                      fontSize: '14px',
                    },
                  }}
                />
                <Autocomplete
                  value={selectedPermission}
                  onChange={(event: any, newValue: string | null) => {
                    setSelectedPermission(newValue);
                  }}
                  options={['Can View', 'Can Edit']}
                  renderInput={(params) => (
                    <TextField
                      {...params}
                      placeholder="Select Permission"
                      sx={{
                        '& .MuiOutlinedInput-root': {
                          '& fieldset': {
                            borderColor: colors.grey[300],
                          },
                          '&:hover fieldset': {
                            borderColor: colors.grey[400],
                          },
                          '&.Mui-focused fieldset': {
                            borderColor: colors.blue[600],
                          },
                        },
                      }}
                    />
                  )}
                  sx={{
                    width: '200px',
                    '& .MuiAutocomplete-input': {
                      fontSize: '14px',
                    },
                  }}
                />
                <Button
                  label=""
                  leftIcon={<AddIcon sx={{ fontSize: "20px" }} />}
                  color="blue"
                  prominence={true}
                  onClick={() => {
                    if (selectedUser && selectedPermission) {
                      const newUser = mockUsers.find(u => u.name === selectedUser);
                      if (newUser) {
                        setAssignedPermissions([
                          ...assignedPermissions,
                          { id: newUser.id, name: newUser.name, permission: selectedPermission }
                        ]);
                        setSelectedUser(null);
                        setSelectedPermission(null);
                        setInputValue('');
                      }
                    }
                  }}
                />
              </Box>

              <Box sx={{ mt: 4, mb: 3 }}>
                <Box sx={{ mb: 2 }}>
                  <TypographyDS style="h4" color={colors.grey[900]}>
                    Role Permissions
                  </TypographyDS>
                </Box>

                <TableContainer component={Paper} sx={{ boxShadow: 'none', border: `1px solid ${colors.grey[200]}` }}>
                  <Table>
                    <TableHead>
                      <TableRow>
                        <TableCell sx={{ backgroundColor: colors.grey[50], fontWeight: 500 }}>Role</TableCell>
                        <TableCell sx={{ backgroundColor: colors.grey[50], fontWeight: 500 }}>Permission</TableCell>
                        <TableCell sx={{ backgroundColor: 'transparent', width: '50px' }}></TableCell>
                      </TableRow>
                    </TableHead>
                    <TableBody>
                      {assignedRolePermissions.map((role) => (
                        <TableRow key={role.id}>
                          <TableCell>{role.name}</TableCell>
                          <TableCell>
                            <Autocomplete
                              value={toDisplay(editingRoleId === role.id && editingPermission ? editingPermission : role.permission)}
                              onChange={(event: any, newValue: string | null) => {
                                if (newValue) {
                                  handleRolePermissionChange(role.id, newValue);
                                }
                              }}
                              options={PERMISSION_OPTIONS}
                              disableClearable
                              renderInput={(params) => (
                                <TextField
                                  {...params}
                                  size="small"
                                  sx={{
                                    '& .MuiOutlinedInput-root': {
                                      '& fieldset': {
                                        borderColor: colors.grey[300],
                                      },
                                      '&:hover fieldset': {
                                        borderColor: colors.grey[400],
                                      },
                                      '&.Mui-focused fieldset': {
                                        borderColor: colors.blue[600],
                                      },
                                    },
                                  }}
                                />
                              )}
                              sx={{
                                width: '150px',
                                '& .MuiAutocomplete-input': {
                                  fontSize: '14px',
                                },
                              }}
                            />
                          </TableCell>
                          <TableCell>
                            {editingRoleId === role.id ? (
                              <Button
                                label=""
                                leftIcon={<CheckIcon sx={{ fontSize: "18px", color: colors.blue[600] }} />}
                                color="blue"
                                prominence={false}
                                onClick={() => handleSaveRolePermission(role.id)}
                              />
                            ) : (
                              <Button
                                label=""
                                leftIcon={<DeleteIcon sx={{ fontSize: "18px", color: colors.grey[600] }} />}
                                color="grey"
                                prominence={false}
                                onClick={() => handleRemoveRolePermission(role.id)}
                              />
                            )}
                          </TableCell>
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>
                </TableContainer>
              </Box>

              <Box sx={{ mb: 2 }}>
                <TypographyDS style="h4" color={colors.grey[900]}>
                  User Permissions
                </TypographyDS>
              </Box>

              <TableContainer component={Paper} sx={{ boxShadow: 'none', border: `1px solid ${colors.grey[200]}` }}>
                <Table>
                  <TableHead>
                    <TableRow>
                      <TableCell sx={{ backgroundColor: colors.grey[50], fontWeight: 500 }}>User</TableCell>
                      <TableCell sx={{ backgroundColor: colors.grey[50], fontWeight: 500 }}>Permission</TableCell>
                      <TableCell sx={{ backgroundColor: 'transparent', width: '50px' }}></TableCell>
                    </TableRow>
                  </TableHead>
                  <TableBody>
                    {assignedPermissions.map((user) => (
                      <TableRow key={user.id}>
                        <TableCell>{user.name}</TableCell>
                        <TableCell>
                          <Autocomplete
                            value={user.permission}
                            onChange={(event: any, newValue: string | null) => {
                              if (newValue) {
                                handlePermissionChange(user.id, newValue);
                              }
                            }}
                            options={['Can View', 'Can Edit']}
                            renderInput={(params) => (
                              <TextField
                                {...params}
                                size="small"
                                sx={{
                                  '& .MuiOutlinedInput-root': {
                                    '& fieldset': {
                                      borderColor: colors.grey[300],
                                    },
                                    '&:hover fieldset': {
                                      borderColor: colors.grey[400],
                                    },
                                    '&.Mui-focused fieldset': {
                                      borderColor: colors.blue[600],
                                    },
                                  },
                                }}
                              />
                            )}
                            sx={{
                              width: '150px',
                              '& .MuiAutocomplete-input': {
                                fontSize: '14px',
                               },
                            }}
                          />
                        </TableCell>
                        <TableCell>
                          {editingUserId === user.id ? (
                            <Button
                              label=""
                              leftIcon={<CheckIcon sx={{ fontSize: "18px", color: colors.blue[600] }} />}
                              color="blue"
                              prominence={false}
                              onClick={() => handleSaveRolePermission(user.id)}
                            />
                          ) : (
                            <Button
                              label=""
                              leftIcon={<DeleteIcon sx={{ fontSize: "18px", color: colors.grey[600] }} />}
                              color="grey"
                              prominence={false}
                              onClick={() => handleRemovePermission(user.id)}
                            />
                          )}
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </TableContainer>

              
            </Box>
          )}
          {activeTab === detailsTabIndex && (
            <Box sx={{ width: '100%' }}>
              <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}>
                <Box sx={{ display: 'flex', gap: 2, alignItems: 'flex-start' }}>
                  <TextInput
                    title="Case #"
                    placeholder="Enter case number"
                    type={InputType.Text}
                  />
                  <TextInput
                    title="Case Status"
                    placeholder="Select status"
                    type={InputType.Dropdown}
                    options={[
                      { value: 'open', label: 'Open' },
                      { value: 'in_progress', label: 'In Progress' },
                      { value: 'closed', label: 'Closed' },
                      { value: 'on_hold', label: 'On Hold' }
                    ]}
                  />
                </Box>
                <Box sx={{ display: 'flex', gap: 2, alignItems: 'flex-start' }}>
                  <TextInput
                    title="Case Priority"
                    placeholder="Select priority"
                    type={InputType.Dropdown}
                    options={[
                      { value: 'critical', label: 'Critical' },
                      { value: 'high', label: 'High' },
                      { value: 'medium', label: 'Medium' },
                      { value: 'low', label: 'Low' }
                    ]}
                  />
                  <TextInput
                    title="Case Type"
                    placeholder="Select type"
                    type={InputType.Dropdown}
                    options={[
                      { value: 'investigation', label: 'Investigation' },
                      { value: 'incident', label: 'Incident' },
                      { value: 'complaint', label: 'Complaint' },
                      { value: 'inquiry', label: 'Inquiry' }
                    ]}
                  />
                </Box>
                <Box sx={{ display: 'flex', gap: 2, alignItems: 'flex-start' }}>
                  <TextInput
                    title="Date Opened"
                    placeholder="MM/DD/YYYY"
                    type={InputType.Text}
                    mask={InputMask.Date}
                  />
                  <TextInput
                    title="Date Closed"
                    placeholder="MM/DD/YYYY"
                    type={InputType.Text}
                    mask={InputMask.Date}
                  />
                </Box>
                <Box sx={{ display: 'flex', gap: 2, alignItems: 'flex-start', width: '100%' }}>
                  <TextInput
                    title="User Tags"
                    placeholder="Enter tags (comma separated)"
                    type={InputType.Text}
                  />
                </Box>
                <Box sx={{ display: 'flex', gap: 2, alignItems: 'flex-start', width: '100%' }}>
                  <TextInput
                    title="Release Status"
                    placeholder="Enter tags (comma separated)"
                    type={InputType.Text}
                  />
                </Box>
              </Box>
            </Box>
          )}
        </div>

        <div
          style={{
            display: "flex",
            justifyContent: "flex-end",
            gap: "12px",
            flexShrink: 0,
            marginTop: "16px",
          }}
        >
          <Button
            label="Close"
            color="grey"
            style="outline"
            onClick={onClose}
          />
        </div>
      </div>
    </div>
  );
};

export default ManageModal;
