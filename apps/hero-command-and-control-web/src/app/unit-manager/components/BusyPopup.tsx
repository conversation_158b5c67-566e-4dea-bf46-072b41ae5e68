import { useRouter } from 'next/navigation';
import { AssetStatus } from 'proto/hero/assets/v2/assets_pb';
import React from 'react';
import { useUpdateAsset } from '../../apis/services/workflow/assets/hooks';
import { useListActiveAssignedOrdersForAsset } from '../../apis/services/workflow/orders/hooks';

interface BusyPopupProps {
  open: boolean;
  onClose: () => void;
  assetId: string;
}

const BusyPopup: React.FC<BusyPopupProps> = ({ open, onClose, assetId }) => {
  const router = useRouter();
  const updateAssetMutation = useUpdateAsset();
  const { data, isLoading } = useListActiveAssignedOrdersForAsset({ assetId, $typeName: 'hero.orders.v2.ListActiveAssignedOrdersForAssetRequest', pageSize: 10, pageToken: '' }, { enabled: open });

  // Find the first order with a situationId
  const orderWithIncident = data?.orders?.find((order: any) => order.situationId);
  const canGoToIncident = !!orderWithIncident?.situationId;

  // Check if there are any active ASSIST_MEMBER orders
  const hasActiveAssistMemberOrders = data?.orders?.some((order: any) =>
    order.type === 'ORDER_TYPE_ASSIST_MEMBER' &&
    order.status !== 'ORDER_STATUS_COMPLETED' &&
    order.status !== 'ORDER_STATUS_CANCELLED' &&
    order.status !== 'ORDER_STATUS_REJECTED'
  );

  // Determine if this is the specific case: busy but no active ASSIST_MEMBER orders
  const isBusyWithoutAssistOrders = !hasActiveAssistMemberOrders && data?.orders && data.orders.length > 0;

  const handleGoToIncident = () => {
    if (canGoToIncident) {
      router.push(`/cad?incidentId=${orderWithIncident.situationId}`);
    }
  };

  const handleLogOff = () => {
    updateAssetMutation.mutate({
      $typeName: 'hero.assets.v2.UpdateAssetRequest',
      asset: {
        $typeName: 'hero.assets.v2.Asset',
        id: assetId,
        status: AssetStatus.OFFLINE,
        resourceType: 'ASSET',
      } as any,
    }, {
      onSuccess: () => {
        onClose();
      },
    });
  };

  if (!open) return null;

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-40">
      <div className="bg-white rounded-lg shadow-lg p-8 max-w-md w-full">
        {isBusyWithoutAssistOrders ? (
          <>
            <h2 className="text-lg font-semibold mb-4" style={{ fontFamily: 'Roboto, sans-serif', color: '#000' }}>
              Warning: responder is marked as busy but has no active orders to assist members.
            </h2>
            <p className="mb-6" style={{ fontFamily: 'Roboto, sans-serif', color: '#000' }}>
              Are you sure you want to proceed?
            </p>
            <div className="flex justify-end gap-3">
              <button
                className="px-4 py-2 rounded bg-gray-200 text-gray-800 hover:bg-gray-300"
                style={{ fontFamily: 'Roboto, sans-serif' }}
                onClick={onClose}
              >
                Cancel
              </button>
              <button
                className="px-4 py-2 rounded bg-red-600 text-white hover:bg-red-700"
                style={{ fontFamily: 'Roboto, sans-serif' }}
                onClick={handleLogOff}
                disabled={updateAssetMutation.status === 'pending'}
              >
                {updateAssetMutation.status === 'pending' ? 'Logging Off...' : 'Log Off'}
              </button>
            </div>
          </>
        ) : (
          <>
            <h2 className="text-lg font-semibold mb-4" style={{ fontFamily: 'Roboto, sans-serif', color: '#000' }}>
              Unit has active orders and cannot be logged off.
            </h2>
            <p className="mb-6" style={{ fontFamily: 'Roboto, sans-serif', color: '#000' }}>
              Please resolve orders before logging off.
            </p>
            <div className="flex justify-end gap-3">
              <button
                className="px-4 py-2 rounded bg-gray-200 text-gray-800 hover:bg-gray-300"
                style={{ fontFamily: 'Roboto, sans-serif' }}
                onClick={onClose}
              >
                Close
              </button>
              <button
                className={`px-4 py-2 rounded ${canGoToIncident ? 'bg-blue-600 text-white hover:bg-blue-700' : 'bg-gray-200 text-gray-400 cursor-not-allowed'}`}
                style={{ fontFamily: 'Roboto, sans-serif' }}
                onClick={handleGoToIncident}
                disabled={!canGoToIncident || isLoading}
              >
                {isLoading ? 'Loading...' : 'Go To Incident'}
              </button>
            </div>
          </>
        )}
      </div>
    </div>
  );
};

export default BusyPopup;
