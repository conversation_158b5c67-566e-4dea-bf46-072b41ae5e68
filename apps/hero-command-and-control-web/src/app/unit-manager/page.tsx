'use client';
import { Asset, AssetStatus, AssetType, ListAssetsRequest } from 'proto/hero/assets/v2/assets_pb';
import { useEffect, useMemo, useState } from 'react';
import {
  stringToAssetStatus
} from '../apis/services/workflow/assets/enumConverters';
import { useListAssets } from '../apis/services/workflow/assets/hooks';
import SidebarComponent from '../components/Sidebar';
import UnitStatus from './components/UnitStatus';

// Helper to determine if a status is considered "active" (Available or Busy)
const isActiveOperationalStatus = (status: AssetStatus): boolean => {
  return status === AssetStatus.AVAILABLE || status === AssetStatus.BUSY;
};

function parseDateTime(isoString?: string) {
  if (!isoString) return { date: '', time: '' };
  const d = new Date(isoString);
  return {
    date: d.toLocaleDateString(),
    time: d.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' }),
  };
}

// Custom hook for paginated asset fetching
function usePaginatedAssets(assetType: AssetType) {
  const [allAssets, setAllAssets] = useState<Asset[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<Error | null>(null);
  const [hasMore, setHasMore] = useState(true);
  const [pageToken, setPageToken] = useState('');

  // Initial request
  const { data, isLoading: currentLoading, isError, error: currentError } = useListAssets(
    {
      type: assetType,
      pageSize: 50, // Reasonable page size
      pageToken: pageToken,
    } as ListAssetsRequest,
    {
      refetchInterval: 3000,
      enabled: hasMore,
    }
  );

  // Process new data and append to existing assets
  useEffect(() => {
    if (data?.assets) {
      setAllAssets(prev => {
        // If this is the first page (empty pageToken), replace all assets
        if (!pageToken) {
          return data.assets || [];
        }
        // Otherwise, append new assets to existing ones
        return [...prev, ...(data.assets || [])];
      });

      // Check if there are more pages
      setHasMore(!!data.nextPageToken);
      setPageToken(data.nextPageToken || '');
    }
  }, [data, pageToken]);

  // Handle loading and error states
  useEffect(() => {
    setIsLoading(currentLoading);
    setError(currentError || null);
  }, [currentLoading, currentError]);

  // Function to load more assets
  const loadMore = () => {
    if (hasMore && !isLoading) {
      setPageToken(data?.nextPageToken || '');
    }
  };

  // Function to refresh all assets
  const refresh = () => {
    setAllAssets([]);
    setHasMore(true);
    setPageToken('');
  };

  return {
    assets: allAssets,
    isLoading,
    error,
    hasMore,
    loadMore,
    refresh,
  };
}

export default function UnitManagerPage() {
  // Use paginated hooks for each asset type
  const responderAssets = usePaginatedAssets(AssetType.RESPONDER);
  const supervisorAssets = usePaginatedAssets(AssetType.SUPERVISOR);
  const dispatcherAssets = usePaginatedAssets(AssetType.DISPATCHER);

  // Helper function to process assets
  const processAssets = (assets: Asset[], assetType: string) => {
    if (!assets) return [];
    return assets.map((asset: Asset) => {
      let department = '';
      let beat = '';
      try {
        const info = asset.additionalInfoJson ? JSON.parse(asset.additionalInfoJson) : {};
        department = info.department || '';
        beat = info.beat || '';
      } catch {
        department = '';
        beat = '';
      }
      const { date, time } = parseDateTime(asset.statusChangedTime || asset.createTime);

      // Convert string status to enum value
      const assetStatus = typeof asset.status === 'string'
        ? stringToAssetStatus(asset.status)
        : asset.status;

      return {
        id: asset.id,
        unitNumber: `${asset.id?.replace(/[^0-9]/g, "")?.slice(0, 3)} `,
        name: asset.name,
        department: department || 'PD',
        beat: beat || '1',
        status: assetStatus,
        logonDate: isActiveOperationalStatus(assetStatus) ? date : undefined,
        logonTime: isActiveOperationalStatus(assetStatus) ? time : undefined,
        logoffDate: !isActiveOperationalStatus(assetStatus) ? date : undefined,
        logoffTime: !isActiveOperationalStatus(assetStatus) ? time : undefined,
        assetType: assetType,
      };
    });
  };

  const processedResponderAssets = useMemo(() => {
    return processAssets(responderAssets.assets, 'Responder');
  }, [responderAssets.assets]);

  const processedSupervisorAssets = useMemo(() => {
    return processAssets(supervisorAssets.assets, 'Supervisor');
  }, [supervisorAssets.assets]);

  const processedDispatcherAssets = useMemo(() => {
    return processAssets(dispatcherAssets.assets, 'Dispatcher');
  }, [dispatcherAssets.assets]);

  // Combine all assets
  const allAssets = [...processedResponderAssets, ...processedSupervisorAssets, ...processedDispatcherAssets];

  const activeUnits = allAssets.filter(row =>
    isActiveOperationalStatus(row.status)
  );
  const inactiveUnits = allAssets.filter(row =>
    !isActiveOperationalStatus(row.status)
  );

  const isLoading = responderAssets.isLoading || supervisorAssets.isLoading || dispatcherAssets.isLoading;
  const isError = responderAssets.error || supervisorAssets.error || dispatcherAssets.error;
  const errorMessage = responderAssets.error?.message || supervisorAssets.error?.message || dispatcherAssets.error?.message || 'Failed to load units.';

  // Check if any asset type has more data to load
  const hasMoreData = responderAssets.hasMore || supervisorAssets.hasMore || dispatcherAssets.hasMore;

  return (
    <div className="flex w-full h-screen bg-gray-50">
      <SidebarComponent />
      <main className="flex-1 ml-[75px] flex flex-col overflow-hidden">
        <div className="flex items-center justify-between p-8 border-b border-gray-200 bg-white">
          <h1 className="text-2xl font-semibold text-gray-900">Unit Status Manager</h1>
          {hasMoreData && (
            <button
              onClick={() => {
                responderAssets.loadMore();
                supervisorAssets.loadMore();
                dispatcherAssets.loadMore();
              }}
              disabled={isLoading}
              className="px-4 py-2 text-sm font-medium text-white bg-blue-600 rounded-md hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              {isLoading ? 'Loading...' : 'Load More Units'}
            </button>
          )}
        </div>

        <div className="flex-1 overflow-hidden p-8 pb-12">
          {isLoading && <div className="mb-4">Loading units...</div>}
          {isError && <div className="mb-4 text-red-600">Error: {errorMessage}</div>}
          {!isLoading && !isError && (
            <div className="flex flex-col h-full space-y-6">
              <div className="flex-1 min-h-0 flex flex-col">
                <h2 className="text-lg font-medium text-gray-700 mb-4 flex-shrink-0">
                  Active Units ({activeUnits.length})
                </h2>
                <div className="flex-1 min-h-0 overflow-y-auto">
                  <UnitStatus data={activeUnits} isActive={true} />
                </div>
              </div>
              <div className="flex-1 min-h-0 flex flex-col">
                <h2 className="text-lg font-medium text-gray-700 mb-4 flex-shrink-0">
                  Inactive Units ({inactiveUnits.length})
                </h2>
                <div className="flex-1 min-h-0 overflow-y-auto">
                  <UnitStatus data={inactiveUnits} isActive={false} />
                </div>
              </div>
            </div>
          )}
        </div>
      </main>
    </div>
  );
}
