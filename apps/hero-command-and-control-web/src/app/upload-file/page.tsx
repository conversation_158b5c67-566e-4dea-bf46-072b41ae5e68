"use client";

import { useCallback, useEffect, useState } from 'react';
import { useDropzone } from 'react-dropzone';
import {
    useCleanupPendingUploads,
    useConfirmUpload,
    useDeleteFile,
    useFileMetadata,
    useFileUpload,
    useGetAccessLog,
    useGetPresignedDownloadUrl,
    useGetPresignedUploadUrl,
    useListFiles,
    usePurgeFile,
    useRecordAccess,
    useSearchFiles,
    useUndeleteFile
} from '../apis/services/filerepository/hooks';

interface DemoFile {
    id: string;
    fileName: string;
    status: string;
    fileSize: number;
    createdAt: string;
}

export default function FileRepositoryDemo() {
    const [activeTab, setActiveTab] = useState('upload');
    const [selectedFileId, setSelectedFileId] = useState('');
    const [searchQuery, setSearchQuery] = useState('');
    const [uploadResults, setUploadResults] = useState<any[]>([]);

    // Hooks
    const getPresignedUrl = useGetPresignedUploadUrl();
    const getDownloadUrl = useGetPresignedDownloadUrl();
    const confirmUpload = useConfirmUpload();
    const deleteFile = useDeleteFile();
    const undeleteFile = useUndeleteFile();
    const purgeFile = usePurgeFile();
    const recordAccess = useRecordAccess();
    const cleanupPending = useCleanupPendingUploads();
    const { uploadFile, isLoading: isUploading } = useFileUpload();

    // Queries
    const { data: fileMetadata, refetch: refetchMetadata } = useFileMetadata(selectedFileId);
    const { data: searchResults, refetch: refetchSearch } = useSearchFiles({
        query: searchQuery,
        pageSize: 10,
    } as any, { enabled: !!searchQuery });

    const { data: filesList, refetch: refetchList } = useListFiles({
        pageSize: 20
    } as any);

    const { data: accessLogs, refetch: refetchAccessLogs } = useGetAccessLog({
        fileId: selectedFileId,
        pageSize: 10
    } as any, { enabled: !!selectedFileId });

    // Auto-refresh access logs when selectedFileId changes
    useEffect(() => {
        if (selectedFileId) {
            // Small delay to allow any pending operations to complete
            const timer = setTimeout(() => {
                refetchAccessLogs();
            }, 500);
            return () => clearTimeout(timer);
        }
    }, [selectedFileId, refetchAccessLogs]);

    // Upload handler
    const handleFileUpload = async (file: File) => {
        try {
            console.log('🚀 Starting complete upload workflow for:', file.name);

            const result = await uploadFile(file, undefined, {
                category: 'demo-upload',
                uploadSource: 'comprehensive-demo',
                originalSize: file.size
            });

            if (result.success) {
                setUploadResults(prev => [{
                    fileName: file.name,
                    fileId: result.fileId,
                    status: 'success',
                    timestamp: new Date().toLocaleString()
                }, ...prev]);
                refetchList();
                console.log('✅ Complete upload workflow successful!');
            } else {
                setUploadResults(prev => [{
                    fileName: file.name,
                    status: 'error',
                    error: result.error,
                    timestamp: new Date().toLocaleString()
                }, ...prev]);
            }
        } catch (error) {
            console.error('❌ Upload workflow failed:', error);
        }
    };

    // Download handler
    const handleDownload = async (fileId: string, fileName: string) => {
        try {
            const response = await getDownloadUrl.mutateAsync({ id: fileId, expiresIn: 300 } as any);

            // The backend automatically logs 'download' when GetPresignedDownloadUrl is called
            // No need to manually record access here since backend handles it automatically

            // Open download URL
            window.open(response.presignedUrl, '_blank');

            // Refresh metadata and access logs to show the new access event
            refetchMetadata();
            if (selectedFileId === fileId) {
                refetchAccessLogs();
            }
        } catch (error) {
            console.error('Download failed:', error);
        }
    };

    // Dropzone
    const onDrop = useCallback(async (acceptedFiles: File[]) => {
        for (const file of acceptedFiles) {
            await handleFileUpload(file);
        }
    }, []);

    const { getRootProps, getInputProps, isDragActive } = useDropzone({
        onDrop,
        disabled: isUploading,
        maxSize: 250 * 1024 * 1024 // Updated to 250MB to match backend limit
    });

    const tabs = [
        { id: 'upload', label: '📤 Upload & Workflow', icon: '📤' },
        { id: 'search', label: '🔍 Search & List', icon: '🔍' },
        { id: 'metadata', label: '📋 Metadata & Details', icon: '📋' },
        { id: 'lifecycle', label: '🔄 Lifecycle Management', icon: '🔄' },
        { id: 'analytics', label: '📊 Access Logs & Analytics', icon: '📊' },
        { id: 'admin', label: '⚙️ Admin Operations', icon: '⚙️' }
    ];

    return (
        <div className="min-h-screen bg-gray-50 overflow-y-auto h-screen">
            <div className="container mx-auto p-6 max-w-7xl">
                <div className="mb-8">
                    <h1 className="text-4xl font-bold mb-4">🗂️ File Repository - Complete Demo</h1>
                    <p className="text-gray-600 text-lg">
                        Comprehensive demonstration of all file repository capabilities including upload, download,
                        search, metadata management, lifecycle operations, and access logging.
                    </p>
                </div>

                {/* Global Controls */}
                <div className="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-6">
                    <div className="flex items-center gap-4">
                        <div>
                            <label className="block text-sm font-medium text-blue-800 mb-1">Selected File ID:</label>
                            <input
                                type="text"
                                value={selectedFileId}
                                onChange={(e) => setSelectedFileId(e.target.value)}
                                className="border border-blue-300 rounded px-3 py-1 w-64"
                                placeholder="Enter file ID for operations"
                            />
                        </div>
                        <div className="text-sm text-blue-700">
                            <p>🔒 Organization scope automatically determined from authentication context</p>
                        </div>
                    </div>
                </div>

                {/* Tab Navigation */}
                <div className="border-b border-gray-200 mb-6">
                    <nav className="flex space-x-8">
                        {tabs.map((tab) => (
                            <button
                                key={tab.id}
                                onClick={() => setActiveTab(tab.id)}
                                className={`py-2 px-1 border-b-2 font-medium text-sm ${activeTab === tab.id
                                    ? 'border-blue-500 text-blue-600'
                                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                                    }`}
                            >
                                {tab.label}
                            </button>
                        ))}
                    </nav>
                </div>

                {/* Tab Content */}
                <div className="space-y-6">
                    {/* Upload & Workflow Tab */}
                    {activeTab === 'upload' && (
                        <div className="space-y-6">
                            <div className="bg-white border border-gray-200 rounded-lg p-6">
                                <h2 className="text-2xl font-semibold mb-4">📤 Complete Upload Workflow</h2>

                                {/* Dropzone */}
                                <div
                                    {...getRootProps()}
                                    className={`border-2 border-dashed rounded-lg p-8 text-center cursor-pointer transition-colors mb-6 ${isDragActive ? 'border-blue-400 bg-blue-50' : 'border-gray-300 hover:border-gray-400'
                                        } ${isUploading ? 'opacity-50 cursor-not-allowed' : ''}`}
                                >
                                    <input {...getInputProps()} />
                                    {isUploading ? (
                                        <div className="flex items-center justify-center">
                                            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mr-3"></div>
                                            <p className="text-lg">Processing uploads...</p>
                                        </div>
                                    ) : (
                                        <div>
                                            <p className="text-lg mb-2">📁 Drag & drop files here, or click to select</p>
                                            <p className="text-sm text-gray-500 mb-2">Max 250MB per file</p>
                                            <p className="text-xs text-gray-400 mb-4">
                                                All file types supported - size limit enforced by backend
                                            </p>
                                            <div className="inline-block px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700">
                                                Choose Files
                                            </div>
                                        </div>
                                    )}
                                </div>

                                {/* Upload Results */}
                                <div>
                                    <h3 className="text-lg font-semibold mb-3">Recent Uploads</h3>
                                    {uploadResults.length === 0 ? (
                                        <p className="text-gray-500 italic">No uploads yet</p>
                                    ) : (
                                        <div className="space-y-2">
                                            {uploadResults.slice(0, 5).map((result, index) => (
                                                <div key={index} className={`p-3 border rounded ${result.status === 'success' ? 'bg-green-50 border-green-200' : 'bg-red-50 border-red-200'
                                                    }`}>
                                                    <div className="flex justify-between items-center">
                                                        <div>
                                                            <p className="font-medium">{result.fileName}</p>
                                                            <p className="text-sm text-gray-600">
                                                                {result.status === 'success' ? `✅ ID: ${result.fileId}` : `❌ ${result.error}`}
                                                            </p>
                                                        </div>
                                                        <span className="text-sm text-gray-500">{result.timestamp}</span>
                                                    </div>
                                                </div>
                                            ))}
                                        </div>
                                    )}
                                </div>

                                {/* Workflow Explanation */}
                                <div className="mt-6 bg-blue-50 border border-blue-200 rounded-lg p-4">
                                    <h4 className="font-semibold text-blue-800 mb-2">🔄 Complete Upload Workflow</h4>
                                    <div className="text-sm text-blue-700 space-y-2">
                                        <p><strong>Step 1:</strong> Call GetPresignedUploadUrl - Creates PENDING metadata & secure upload URL</p>
                                        <p><strong>Step 2:</strong> Direct S3 Upload - File uploaded directly to cloud storage (250MB limit)</p>
                                        <p><strong>Step 3:</strong> Call ConfirmUpload - Validates size & changes status PENDING → ACTIVE</p>
                                    </div>
                                    <div className="mt-3 pt-3 border-t border-blue-300">
                                        <h5 className="font-medium text-blue-800 mb-1">🔒 Security Features</h5>
                                        <div className="text-xs text-blue-600 space-y-1">
                                            <p>• Owner ID automatically set from authentication context</p>
                                            <p>• IP address automatically captured for audit logs</p>
                                            <p>• Multi-layer file size validation (client + backend + database)</p>
                                            <p>• Row-level security ensures org-based isolation</p>
                                            <p>• Org-based filtering hides storage details from non-admin org users</p>
                                            <p>• Sensitive fields (bucket_name, storage_key) only visible to org ID 1 users</p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    )}

                    {/* Search & List Tab */}
                    {activeTab === 'search' && (
                        <div className="space-y-6">
                            {/* Search Section */}
                            <div className="bg-white border border-gray-200 rounded-lg p-6">
                                <h2 className="text-2xl font-semibold mb-4">🔍 Advanced Search</h2>

                                <div className="flex gap-4 mb-4">
                                    <input
                                        type="text"
                                        value={searchQuery}
                                        onChange={(e) => setSearchQuery(e.target.value)}
                                        className="flex-1 border border-gray-300 rounded px-3 py-2"
                                        placeholder="Search files by name, content, tags..."
                                    />
                                    <button
                                        onClick={() => refetchSearch()}
                                        className="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700"
                                    >
                                        Search
                                    </button>
                                </div>

                                {searchResults && (
                                    <div>
                                        <p className="text-sm text-gray-600 mb-3">
                                            Found {searchResults.totalCount} results
                                        </p>
                                        <div className="space-y-2">
                                            {searchResults.results?.map((result: any) => (
                                                <div key={result.metadata?.id} className="border border-gray-200 rounded p-3">
                                                    <div className="flex justify-between items-start">
                                                        <div>
                                                            <p className="font-medium">{result.metadata?.fileName}</p>
                                                            <p className="text-sm text-gray-600">
                                                                ID: {result.metadata?.id} | Size: {result.metadata?.fileSize} bytes
                                                            </p>
                                                            <p className="text-sm text-gray-500">
                                                                Score: {result.score} | Status: {result.metadata?.status}
                                                            </p>
                                                        </div>
                                                        <button
                                                            onClick={() => setSelectedFileId(result.metadata?.id || '')}
                                                            className="px-3 py-1 bg-gray-100 text-gray-700 rounded text-sm hover:bg-gray-200"
                                                        >
                                                            Select
                                                        </button>
                                                    </div>
                                                </div>
                                            ))}
                                        </div>
                                    </div>
                                )}
                            </div>

                            {/* List Files Section */}
                            <div className="bg-white border border-gray-200 rounded-lg p-6">
                                <div className="flex justify-between items-center mb-4">
                                    <h2 className="text-2xl font-semibold">📋 Files List</h2>
                                    <button
                                        onClick={() => refetchList()}
                                        className="px-4 py-2 bg-green-600 text-white rounded hover:bg-green-700"
                                    >
                                        Refresh
                                    </button>
                                </div>

                                {filesList && (
                                    <div className="space-y-2">
                                        {filesList.files?.map((file: any) => (
                                            <div key={file.id} className="border border-gray-200 rounded p-3">
                                                <div className="flex justify-between items-center">
                                                    <div>
                                                        <p className="font-medium">{file.fileName}</p>
                                                        <p className="text-sm text-gray-600">
                                                            {file.fileSize} bytes | {file.status} | {file.createdAt}
                                                        </p>
                                                    </div>
                                                    <div className="flex gap-2">
                                                        <button
                                                            onClick={() => setSelectedFileId(file.id)}
                                                            className="px-3 py-1 bg-blue-100 text-blue-700 rounded text-sm hover:bg-blue-200"
                                                        >
                                                            Select
                                                        </button>
                                                        {file.status === 'FILE_STATUS_ACTIVE' && (
                                                            <button
                                                                onClick={() => handleDownload(file.id, file.fileName)}
                                                                className="px-3 py-1 bg-green-100 text-green-700 rounded text-sm hover:bg-green-200"
                                                            >
                                                                Download
                                                            </button>
                                                        )}
                                                    </div>
                                                </div>
                                            </div>
                                        ))}
                                    </div>
                                )}
                            </div>
                        </div>
                    )}

                    {/* Metadata Tab */}
                    {activeTab === 'metadata' && (
                        <div className="bg-white border border-gray-200 rounded-lg p-6">
                            <div className="flex justify-between items-center mb-4">
                                <h2 className="text-2xl font-semibold">📋 File Metadata & Details</h2>
                                {selectedFileId && (
                                    <button
                                        onClick={() => {
                                            refetchMetadata();
                                            refetchAccessLogs(); // Refresh access logs since viewing metadata creates a log entry
                                        }}
                                        className="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700"
                                    >
                                        🔄 Refresh Metadata
                                    </button>
                                )}
                            </div>

                            {selectedFileId ? (
                                fileMetadata ? (
                                    <div className="space-y-4">
                                        {/* Auto-logging notification */}
                                        <div className="bg-green-50 border border-green-200 rounded p-3">
                                            <p className="text-sm text-green-700">
                                                ✅ <strong>Access logged:</strong> Backend automatically recorded &apos;metadata_accessed&apos; event when metadata was fetched.
                                            </p>
                                        </div>

                                        <div className="grid grid-cols-2 gap-4">
                                            <div>
                                                <h3 className="font-semibold text-gray-700 mb-2">Basic Information</h3>
                                                <div className="space-y-2 text-sm">
                                                    <p><strong>ID:</strong> {fileMetadata.metadata?.id}</p>
                                                    <p><strong>Name:</strong> {fileMetadata.metadata?.fileName}</p>
                                                    <p><strong>Type:</strong> {fileMetadata.metadata?.fileType}</p>
                                                    <p><strong>Size:</strong> {fileMetadata.metadata?.fileSize} bytes</p>
                                                    <p><strong>Status:</strong> {fileMetadata.metadata?.status}</p>
                                                    <p><strong>Owner:</strong> {fileMetadata.metadata?.ownerId || 'Auto-set by backend'}</p>
                                                </div>
                                            </div>
                                            <div>
                                                <h3 className="font-semibold text-gray-700 mb-2">Storage Details</h3>
                                                <div className="space-y-2 text-sm">
                                                    <p><strong>Provider:</strong> {fileMetadata.metadata?.provider || '🔒 Org ID 1 only'}</p>
                                                    <p><strong>Bucket:</strong> {fileMetadata.metadata?.bucketName || '🔒 Org ID 1 only'}</p>
                                                    <p><strong>Storage Key:</strong> {fileMetadata.metadata?.storageKey || '🔒 Org ID 1 only'}</p>
                                                    <p><strong>Storage Class:</strong> {fileMetadata.metadata?.storageClass || '🔒 Org ID 1 only'}</p>
                                                    <p><strong>Public:</strong> {fileMetadata.metadata?.isPublic ? 'Yes' : 'No'}</p>
                                                </div>
                                                {(!fileMetadata.metadata?.provider || !fileMetadata.metadata?.bucketName) && (
                                                    <div className="mt-2 p-2 bg-yellow-50 border border-yellow-200 rounded text-xs text-yellow-700">
                                                        <p>🔒 <strong>Security Note:</strong> Storage infrastructure details are hidden for users without org ID 1 access to prevent information disclosure attacks.</p>
                                                    </div>
                                                )}
                                            </div>
                                        </div>

                                        <div>
                                            <h3 className="font-semibold text-gray-700 mb-2">Analytics</h3>
                                            <div className="space-y-2 text-sm">
                                                <p><strong>Downloads:</strong> {fileMetadata.metadata?.downloadCount}</p>
                                                <p><strong>Created:</strong> {fileMetadata.metadata?.createdAt}</p>
                                                <p><strong>Updated:</strong> {fileMetadata.metadata?.updatedAt}</p>
                                                <p><strong>Last Accessed:</strong> {fileMetadata.metadata?.lastAccessed || 'Never'}</p>
                                            </div>
                                        </div>

                                        {fileMetadata.metadata?.extraMetadata && (
                                            <div>
                                                <h3 className="font-semibold text-gray-700 mb-2">Extra Metadata</h3>
                                                <pre className="bg-gray-100 p-3 rounded text-sm overflow-auto">
                                                    {JSON.stringify(fileMetadata.metadata.extraMetadata, null, 2)}
                                                </pre>
                                            </div>
                                        )}
                                    </div>
                                ) : (
                                    <div className="flex items-center justify-center py-8">
                                        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
                                        <span className="ml-2">Loading metadata...</span>
                                    </div>
                                )
                            ) : (
                                <p className="text-gray-500 italic">Enter a file ID above to view metadata</p>
                            )}
                        </div>
                    )}

                    {/* Lifecycle Management Tab */}
                    {activeTab === 'lifecycle' && (
                        <div className="bg-white border border-gray-200 rounded-lg p-6">
                            <h2 className="text-2xl font-semibold mb-4">🔄 File Lifecycle Management</h2>

                            {selectedFileId ? (
                                <div className="space-y-6">
                                    <div className="bg-yellow-50 border border-yellow-200 rounded p-4">
                                        <p className="text-yellow-800">
                                            <strong>Selected File:</strong> {selectedFileId}
                                        </p>
                                        {fileMetadata && (
                                            <p className="text-yellow-700 text-sm mt-1">
                                                Current Status: {fileMetadata.metadata?.status}
                                            </p>
                                        )}
                                    </div>

                                    <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                                        <button
                                            onClick={async () => {
                                                try {
                                                    await deleteFile.mutateAsync({ id: selectedFileId } as any);
                                                    // Backend automatically logs 'file_deleted' - refresh to show it
                                                    refetchMetadata();
                                                    refetchAccessLogs();
                                                    refetchList();
                                                } catch (error) {
                                                    console.error('Delete failed:', error);
                                                }
                                            }}
                                            disabled={deleteFile.isPending}
                                            className="p-4 border border-red-200 rounded-lg hover:bg-red-50 disabled:opacity-50"
                                        >
                                            <div className="text-center">
                                                <div className="text-2xl mb-2">🗑️</div>
                                                <h3 className="font-semibold text-red-700">Soft Delete</h3>
                                                <p className="text-sm text-red-600">Mark as deleted (recoverable)</p>
                                                {deleteFile.isPending && <p className="text-xs text-red-500 mt-1">Deleting...</p>}
                                            </div>
                                        </button>

                                        <button
                                            onClick={async () => {
                                                try {
                                                    await undeleteFile.mutateAsync({ id: selectedFileId } as any);
                                                    // Backend automatically logs 'file_restored' - refresh to show it
                                                    refetchMetadata();
                                                    refetchAccessLogs();
                                                    refetchList();
                                                } catch (error) {
                                                    console.error('Undelete failed:', error);
                                                }
                                            }}
                                            disabled={undeleteFile.isPending}
                                            className="p-4 border border-green-200 rounded-lg hover:bg-green-50 disabled:opacity-50"
                                        >
                                            <div className="text-center">
                                                <div className="text-2xl mb-2">♻️</div>
                                                <h3 className="font-semibold text-green-700">Restore</h3>
                                                <p className="text-sm text-green-600">Undelete file</p>
                                                {undeleteFile.isPending && <p className="text-xs text-green-500 mt-1">Restoring...</p>}
                                            </div>
                                        </button>

                                        <button
                                            onClick={async () => {
                                                if (confirm('This will permanently delete the file. Are you sure?')) {
                                                    try {
                                                        await purgeFile.mutateAsync({ id: selectedFileId } as any);
                                                        // Backend automatically logs 'file_purged' - but file will be gone
                                                        // Clear the selected file ID since it no longer exists
                                                        setSelectedFileId('');
                                                        refetchList();
                                                    } catch (error) {
                                                        console.error('Purge failed:', error);
                                                    }
                                                }
                                            }}
                                            disabled={purgeFile.isPending}
                                            className="p-4 border border-red-300 rounded-lg hover:bg-red-100 disabled:opacity-50"
                                        >
                                            <div className="text-center">
                                                <div className="text-2xl mb-2">💥</div>
                                                <h3 className="font-semibold text-red-800">Purge</h3>
                                                <p className="text-sm text-red-700">Permanent deletion</p>
                                                {purgeFile.isPending && <p className="text-xs text-red-500 mt-1">Purging...</p>}
                                            </div>
                                        </button>
                                    </div>

                                    <div className="bg-blue-50 border border-blue-200 rounded p-4">
                                        <h4 className="font-semibold text-blue-800 mb-2">Lifecycle States</h4>
                                        <div className="text-sm text-blue-700 space-y-1">
                                            <p><strong>PENDING:</strong> Upload URL issued, awaiting file upload</p>
                                            <p><strong>ACTIVE:</strong> File uploaded and available for download</p>
                                            <p><strong>DELETED:</strong> Soft-deleted, can be restored</p>
                                            <p><strong>PURGED:</strong> Permanently deleted from storage and database</p>
                                        </div>
                                    </div>
                                </div>
                            ) : (
                                <p className="text-gray-500 italic">Enter a file ID above to manage lifecycle</p>
                            )}
                        </div>
                    )}

                    {/* Analytics Tab */}
                    {activeTab === 'analytics' && (
                        <div className="bg-white border border-gray-200 rounded-lg p-6">
                            <h2 className="text-2xl font-semibold mb-4">📊 Access Logs & Analytics</h2>

                            {selectedFileId ? (
                                <div className="space-y-6">
                                    {/* Automatic Logging Info */}
                                    <div className="bg-blue-50 border border-blue-200 rounded p-4">
                                        <h3 className="font-semibold text-blue-800 mb-2">🤖 Automatic Access Logging</h3>
                                        <div className="text-sm text-blue-700 space-y-1">
                                            <p><strong>Backend automatically logs:</strong></p>
                                            <p>• <code>download</code> - When GetPresignedDownloadUrl is called</p>
                                            <p>• <code>metadata_accessed</code> - When GetFileMetadata is called</p>
                                            <p>• <code>file_updated</code> - When UpdateFileMetadata is called</p>
                                            <p>• <code>upload_confirmed</code> - When ConfirmUpload is called</p>
                                            <p>• <code>file_deleted</code> - When DeleteFile is called</p>
                                            <p>• <code>file_restored</code> - When UndeleteFile is called</p>
                                            <p>• <code>file_purged</code> - When PurgeFile is called</p>
                                        </div>
                                    </div>

                                    {/* Manual Record Access Section */}
                                    <div className="bg-gray-50 border border-gray-200 rounded p-4">
                                        <h3 className="font-semibold mb-3">📝 Manual Access Recording</h3>
                                        <p className="text-sm text-gray-600 mb-3">
                                            Record additional access events for testing or custom tracking:
                                        </p>
                                        <p className="text-xs text-gray-500 mb-3">
                                            Note: &apos;download&apos; is automatically logged when GetPresignedDownloadUrl is called, so it&apos;s not included here.
                                        </p>
                                        <div className="flex gap-2 flex-wrap">
                                            {['view', 'share', 'edit', 'print', 'export'].map((action) => (
                                                <button
                                                    key={action}
                                                    onClick={async () => {
                                                        try {
                                                            await recordAccess.mutateAsync({
                                                                fileId: selectedFileId,
                                                                action: action,
                                                                userAgent: navigator.userAgent,
                                                                metadata: { source: 'demo-interface', manual: 'true' }
                                                            } as any);
                                                            // Refresh access logs after manual recording
                                                            refetchAccessLogs();
                                                        } catch (error) {
                                                            console.error('Failed to record access:', error);
                                                        }
                                                    }}
                                                    disabled={recordAccess.isPending}
                                                    className="px-3 py-1 bg-blue-600 text-white rounded text-sm hover:bg-blue-700 disabled:opacity-50"
                                                >
                                                    {recordAccess.isPending ? '...' : action}
                                                </button>
                                            ))}
                                        </div>
                                    </div>

                                    {/* Access Logs */}
                                    <div>
                                        <div className="flex justify-between items-center mb-3">
                                            <h3 className="font-semibold">📋 Access History</h3>
                                            <button
                                                onClick={() => refetchAccessLogs()}
                                                className="px-3 py-1 bg-green-600 text-white rounded text-sm hover:bg-green-700"
                                            >
                                                🔄 Refresh
                                            </button>
                                        </div>

                                        {accessLogs ? (
                                            <div>
                                                <p className="text-sm text-gray-600 mb-3">
                                                    Total entries: {accessLogs.totalCount || 0}
                                                </p>
                                                {accessLogs.entries && accessLogs.entries.length > 0 ? (
                                                    <div className="space-y-2">
                                                        {accessLogs.entries.map((entry: any) => (
                                                            <div key={entry.id} className="border border-gray-200 rounded p-3">
                                                                <div className="flex justify-between items-start">
                                                                    <div className="flex-1">
                                                                        <div className="flex items-center gap-2 mb-1">
                                                                            <span className="font-medium text-blue-600">{entry.action}</span>
                                                                            {entry.metadata?.manual === 'true' && (
                                                                                <span className="text-xs bg-yellow-100 text-yellow-700 px-2 py-0.5 rounded">
                                                                                    Manual
                                                                                </span>
                                                                            )}
                                                                            {entry.metadata?.source && (
                                                                                <span className="text-xs bg-gray-100 text-gray-600 px-2 py-0.5 rounded">
                                                                                    {entry.metadata.source}
                                                                                </span>
                                                                            )}
                                                                        </div>
                                                                        <p className="text-sm text-gray-600">
                                                                            <strong>User:</strong> {entry.userId || 'Unknown'} |
                                                                            <strong> IP:</strong> {entry.ipAddress || 'N/A'}
                                                                        </p>
                                                                        <p className="text-sm text-gray-500">
                                                                            <strong>Time:</strong> {new Date(entry.timestamp).toLocaleString()}
                                                                        </p>
                                                                        {entry.userAgent && (
                                                                            <p className="text-xs text-gray-400 mt-1">
                                                                                <strong>User Agent:</strong> {entry.userAgent.substring(0, 80)}...
                                                                            </p>
                                                                        )}
                                                                    </div>
                                                                    <span className="text-xs bg-gray-100 px-2 py-1 rounded font-mono">
                                                                        {entry.id.substring(0, 8)}...
                                                                    </span>
                                                                </div>
                                                            </div>
                                                        ))}
                                                    </div>
                                                ) : (
                                                    <div className="text-center py-8 text-gray-500">
                                                        <p className="text-lg mb-2">📭 No access logs yet</p>
                                                        <p className="text-sm">
                                                            Access logs will appear here when you:
                                                        </p>
                                                        <ul className="text-sm mt-2 space-y-1">
                                                            <li>• View file metadata</li>
                                                            <li>• Generate download URLs</li>
                                                            <li>• Update file information</li>
                                                            <li>• Perform lifecycle operations</li>
                                                        </ul>
                                                    </div>
                                                )}
                                            </div>
                                        ) : (
                                            <div className="flex items-center justify-center py-8">
                                                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
                                                <span className="ml-2">Loading access logs...</span>
                                            </div>
                                        )}
                                    </div>
                                </div>
                            ) : (
                                <p className="text-gray-500 italic">Enter a file ID above to view analytics</p>
                            )}
                        </div>
                    )}

                    {/* Admin Operations Tab */}
                    {activeTab === 'admin' && (
                        <div className="bg-white border border-gray-200 rounded-lg p-6">
                            <h2 className="text-2xl font-semibold mb-4">⚙️ Admin Operations</h2>

                            <div className="space-y-6">
                                {/* Cleanup Pending Uploads */}
                                <div className="border border-orange-200 rounded p-4">
                                    <h3 className="font-semibold text-orange-800 mb-3">🧹 Cleanup Pending Uploads</h3>
                                    <p className="text-sm text-orange-700 mb-3">
                                        Remove stale PENDING upload records older than 1 hour
                                    </p>
                                    <button
                                        onClick={() => {
                                            const oneHourAgo = new Date(Date.now() - 60 * 60 * 1000).toISOString();
                                            cleanupPending.mutate({ olderThan: oneHourAgo } as any);
                                        }}
                                        disabled={cleanupPending.isPending}
                                        className="px-4 py-2 bg-orange-600 text-white rounded hover:bg-orange-700 disabled:opacity-50"
                                    >
                                        {cleanupPending.isPending ? 'Cleaning...' : 'Cleanup Pending'}
                                    </button>
                                    {cleanupPending.data && (
                                        <p className="text-sm text-green-600 mt-2">
                                            ✅ Cleaned up {cleanupPending.data.removedCount} records
                                        </p>
                                    )}
                                </div>

                                {/* System Status */}
                                <div className="bg-blue-50 border border-blue-200 rounded p-4">
                                    <h3 className="font-semibold text-blue-800 mb-3">📊 System Overview</h3>
                                    <div className="grid grid-cols-2 gap-4 text-sm">
                                        <div>
                                            <p><strong>Total Files:</strong> {filesList?.files?.length || 0}</p>
                                            <p><strong>Active Files:</strong> {filesList?.files?.filter((f: any) => f.status === 'FILE_STATUS_ACTIVE').length || 0}</p>
                                        </div>
                                        <div>
                                            <p><strong>Pending Files:</strong> {filesList?.files?.filter((f: any) => f.status === 'FILE_STATUS_PENDING').length || 0}</p>
                                            <p><strong>Deleted Files:</strong> {filesList?.files?.filter((f: any) => f.status === 'FILE_STATUS_DELETED').length || 0}</p>
                                        </div>
                                    </div>
                                </div>

                                {/* API Status */}
                                <div className="bg-green-50 border border-green-200 rounded p-4">
                                    <h3 className="font-semibold text-green-800 mb-3">🔌 API Capabilities</h3>
                                    <div className="grid grid-cols-2 gap-2 text-sm text-green-700">
                                        <div>✅ GetPresignedUploadUrl</div>
                                        <div>✅ GetPresignedDownloadUrl</div>
                                        <div>✅ GetFileMetadata</div>
                                        <div>✅ UpdateFileMetadata</div>
                                        <div>✅ SearchFiles</div>
                                        <div>✅ ListFiles</div>
                                        <div>✅ ConfirmUpload</div>
                                        <div>✅ CleanupPendingUploads</div>
                                        <div>✅ DeleteFile</div>
                                        <div>✅ UndeleteFile</div>
                                        <div>✅ PurgeFile</div>
                                        <div>✅ GetAccessLog</div>
                                        <div>✅ RecordAccess</div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    )}
                </div>
            </div>
        </div>
    );
} 