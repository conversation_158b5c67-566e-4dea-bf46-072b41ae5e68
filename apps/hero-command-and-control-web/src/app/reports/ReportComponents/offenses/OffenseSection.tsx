import { Button } from "@/design-system/components/Button";
import { Typography } from "@/design-system/components/Typography";
import { colors } from "@/design-system/tokens";
import AddIcon from "@mui/icons-material/Add";
import { Box, Divider, Menu, MenuItem } from "@mui/material";
import React, { useEffect, useMemo, useRef, useState } from "react";
import {
  useListReportSections,
  useUpdateReportSection,
} from "../../../apis/services/workflow/reports/v2/hooks";
import { getIncidentLabel } from "../../../utils/utils";
import IncidentFormCard from "./IncidentFormCard";
import OffenseFormCard from "./OffenseFormCard";
import OffenseRelationsCard from "./OffenseRelationsCard";
import { OffenseData, OffenseSearchModal } from "./OffenseSearchModal";
import { PersonData } from "./PersonList";

// Define IncidentData type locally since we removed the modal
export interface IncidentData {
  code: string;
  literal: string;
  citation: string;
  statute: string;
  level: string;
}

interface OffenseSectionProps {
  reportId?: string;
  onSaveStatusChange?: (status: {
    isSaving: boolean;
    hasUnsavedChanges: boolean;
    source: string;
  }) => void;
  readOnly?: boolean;
  people?: PersonData[];
  vehicles?: any[];
  properties?: any[];
  organizations?: any[];
  relations?: any[];
  associatedCases?: any[];
  onOpenSidePanel?: (
    panelType: "PERSON" | "VEHICLE" | "PROPERTY" | "ORGANIZATION"
  ) => void;

  onAddPersonToOffense?: (
    personId: string,
    offenseId: string,
    relationType:
      | "victim"
      | "offender"
      | "witness"
      | "suspect"
      | "involved_party"
  ) => void;
  onAddVehicleToOffense?: (vehicleId: string, offenseId: string) => void;
  onAddPropertyToOffense?: (propertyId: string, offenseId: string) => void;
  onAddOrganizationToOffense?: (
    organizationId: string,
    offenseId: string
  ) => void;
  onAddVictimOrganizationToOffense?: (
    organizationId: string,
    offenseId: string
  ) => void;
  onRemovePersonFromOffense?: (personId: string, offenseId: string) => void;
  onRemoveVehicleFromOffense?: (vehicleId: string, offenseId: string) => void;
  onRemovePropertyFromOffense?: (propertyId: string, offenseId: string) => void;
  onRemoveOrganizationFromOffense?: (
    organizationId: string,
    offenseId: string
  ) => void;
  onRemoveVictimOrganizationFromOffense?: (
    organizationId: string,
    offenseId: string
  ) => void;
  onRemovePersonFromReport?: (personId: string, entityType: "person") => void;
  onRemoveVehicleFromReport?: (
    vehicleId: string,
    entityType: "vehicle"
  ) => void;
  onRemovePropertyFromReport?: (
    propertyId: string,
    entityType: "property"
  ) => void;
  onRemoveOrganizationFromReport?: (
    organizationId: string,
    entityType: "organization"
  ) => void;

  onSetActiveOffenseRelation?: (
    context: {
      offenseId: string;
      relationType:
        | "victim"
        | "offender"
        | "witness"
        | "suspect"
        | "involved_party";
    } | null
  ) => void;
  onSetActiveVehicleOffenseContext?: (
    context: { offenseId: string } | null
  ) => void;
  onSetActivePropertyOffenseContext?: (
    context: { offenseId: string } | null
  ) => void;
  onSetActiveOrganizationOffenseContext?: (
    context: { offenseId: string } | null
  ) => void;
  onSetActiveVictimOrganizationOffenseContext?: (
    context: { offenseId: string } | null
  ) => void;
  onEntityEdit?: (
    entityId: string,
    entityType: "person" | "vehicle" | "property" | "organization"
  ) => void;
  onAddVictimDetails?: (personId: string) => void;
  onAddVictimDetailsOrganization?: (organizationId: string) => void;
}

interface AddedOffense {
  id: string;
  offense: OffenseData;
  data?: any;
}

interface AddedIncident {
  id: string;
  incident: IncidentData;
  data?: any;
}

export default function OffenseSection({
  reportId,
  onSaveStatusChange,
  readOnly = false,
  people,
  vehicles,
  properties,
  organizations,
  relations,
  associatedCases,
  onOpenSidePanel,
  onAddPersonToOffense,
  onAddVehicleToOffense,
  onAddPropertyToOffense,
  onAddOrganizationToOffense,
  onAddVictimOrganizationToOffense,
  onRemovePersonFromOffense,
  onRemoveVehicleFromOffense,
  onRemovePropertyFromOffense,
  onRemoveOrganizationFromOffense,
  onRemoveVictimOrganizationFromOffense,
  onRemovePersonFromReport,
  onRemoveVehicleFromReport,
  onRemovePropertyFromReport,
  onRemoveOrganizationFromReport,
  onSetActiveOffenseRelation,
  onSetActiveVehicleOffenseContext,
  onSetActivePropertyOffenseContext,
  onSetActiveOrganizationOffenseContext,
  onSetActiveVictimOrganizationOffenseContext,
  onEntityEdit,
  onAddVictimDetails,
  onAddVictimDetailsOrganization,
}: OffenseSectionProps) {
  const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null);
  const [isOffenseModalOpen, setIsOffenseModalOpen] = useState(false);
  const [addedOffenses, setAddedOffenses] = useState<AddedOffense[]>([]);
  const [addedIncidents, setAddedIncidents] = useState<AddedIncident[]>([]);
  const [offenseSectionId, setOffenseSectionId] = useState<string | null>(null);
  const [offenseSection, setOffenseSection] = useState<any>(null);
  const [isSaving, setIsSaving] = useState(false);
  const [hasUnsavedChanges, setHasUnsavedChanges] = useState(false);

  // Store which offense is currently adding a person for relation creation
  const [activeOffenseForPerson, setActiveOffenseForPerson] = useState<{
    offenseId: string;
    relationType:
      | "victim"
      | "offender"
      | "witness"
      | "suspect"
      | "involved_party";
  } | null>(null);

  // Ref for scrolling to new offense
  const offensesContainerRef = useRef<HTMLDivElement>(null);

  const open = Boolean(anchorEl);

  // Use a ref to track previous status like IncidentDetailsCard
  const lastStatusRef = useRef<{
    isSaving: boolean;
    hasUnsavedChanges: boolean;
  } | null>(null);

  // Notify parent on save/dirty changes like IncidentDetailsCard
  useEffect(() => {
    if (!onSaveStatusChange) return;

    const currentStatus = { isSaving, hasUnsavedChanges };
    const lastStatus = lastStatusRef.current;
    const changed =
      !lastStatus ||
      lastStatus.isSaving !== currentStatus.isSaving ||
      lastStatus.hasUnsavedChanges !== currentStatus.hasUnsavedChanges;

    if (changed) {
      lastStatusRef.current = { ...currentStatus };
      onSaveStatusChange({ ...currentStatus, source: "offenses" });
    }
  }, [isSaving, hasUnsavedChanges, onSaveStatusChange]);

  // Fetch report sections to get the offense section
  const { data: reportSections } = useListReportSections(reportId || "", {
    enabled: !!reportId,
    staleTime: 5 * 60 * 1000,
    refetchOnWindowFocus: true,
  });

  // Update report section mutation
  const updateReportSectionMutation = useUpdateReportSection({
    onSuccess: (data) => {
      setOffenseSection(data);
      setOffenseSectionId(data.id);

      // Update local state with backend IDs
      // @ts-expect-error TODO: Fix type issue
      const offenseList = data.offenseList;
      if (offenseList?.offenses && Array.isArray(offenseList.offenses)) {
        // Separate offenses and incidents with updated backend IDs
        const updatedOffenses: AddedOffense[] = [];
        const updatedIncidents: AddedIncident[] = [];

        offenseList.offenses.forEach((item: any) => {
          if (item.data?.isIncident) {
            // This is an incident
            updatedIncidents.push({
              id: item.id, // Use backend ID
              incident: {
                code: item.data?.offense_code || item.offense_type || "UNKNOWN",
                literal:
                  item.data?.offense_literal ||
                  item.offense_type ||
                  "Unknown Incident",
                citation: item.data?.offense_citation || "",
                statute: item.data?.offense_statute || "",
                level: item.data?.offense_level || "",
              },
              data: item.data || {},
            });
          } else {
            // This is an offense
            updatedOffenses.push({
              id: item.id, // Use backend ID
              offense: {
                code: item.data?.offense_code || item.offense_type || "UNKNOWN",
                literal:
                  item.data?.offense_literal ||
                  item.offense_type ||
                  "Unknown Offense",
                citation: item.data?.offense_citation || "",
                statute: item.data?.offense_statute || "",
                level: item.data?.offense_level || "",
              },
              data: item.data || {},
            });
          }
        });

        setAddedOffenses(updatedOffenses);
        setAddedIncidents(updatedIncidents);
      }

      setIsSaving(false);
      setHasUnsavedChanges(false);
    },
    onError: (error) => {
      console.error("Error updating offense section:", error);
      setIsSaving(false);
    },
  });

  // Load existing offenses from the report section
  useEffect(() => {
    if (reportSections?.sections) {
      const foundSection = reportSections.sections.find(
        // @ts-expect-error TODO: Fix type issue
        (section) => section.type === "SECTION_TYPE_OFFENSE"
      );

      if (foundSection) {
        setOffenseSection(foundSection);
        setOffenseSectionId(foundSection.id);

        // Load existing offenses and incidents
        // @ts-expect-error TODO: Fix type issue
        const offenseList = foundSection.offenseList;
        if (offenseList?.offenses && Array.isArray(offenseList.offenses)) {
          // Separate offenses and incidents
          const existingOffenses: AddedOffense[] = [];
          const existingIncidents: AddedIncident[] = [];

          offenseList.offenses.forEach((item: any) => {
            if (item.data?.isIncident) {
              // This is an incident
              existingIncidents.push({
                id: item.id,
                incident: {
                  code:
                    item.data?.offense_code || item.offense_type || "UNKNOWN",
                  literal:
                    item.data?.offense_literal ||
                    item.offense_type ||
                    "Unknown Incident",
                  citation: item.data?.offense_citation || "",
                  statute: item.data?.offense_statute || "",
                  level: item.data?.offense_level || "",
                },
                data: item.data || {},
              });
            } else {
              // This is an offense
              existingOffenses.push({
                id: item.id,
                offense: {
                  code:
                    item.data?.offense_code || item.offense_type || "UNKNOWN",
                  literal:
                    item.data?.offense_literal ||
                    item.offense_type ||
                    "Unknown Offense",
                  citation: item.data?.offense_citation || "",
                  statute: item.data?.offense_statute || "",
                  level: item.data?.offense_level || "",
                },
                data: item.data || {},
              });
            }
          });

          setAddedOffenses(existingOffenses);
          setAddedIncidents(existingIncidents);
        }
      }
    }
  }, [reportSections]);

  const handleAddClick = (event: React.MouseEvent<HTMLElement>) => {
    if (readOnly) return;
    setAnchorEl(event.currentTarget);
  };

  const handleClose = () => {
    setAnchorEl(null);
  };

  const handleAddOffense = () => {
    handleClose();
    setIsOffenseModalOpen(true);
  };

  const handleAddIncident = () => {
    handleClose();

    // Get the initial incident type from the report's incident details section
    let initialIncidentType = "SITUATION_TYPE_OTHER"; // Default fallback

    if (reportSections?.sections) {
      const incidentDetailsSection = reportSections.sections.find(
        (section: any) => section.type === "SECTION_TYPE_INCIDENT_DETAILS"
      );

      // @ts-expect-error TODO: Fix type issue
      if (incidentDetailsSection?.incidentDetails?.initialType) {
        initialIncidentType =
          // @ts-expect-error TODO: Fix type issue
          incidentDetailsSection.incidentDetails.initialType;
      }
    }

    // Create incident directly without modal
    const incident: IncidentData = {
      code: initialIncidentType,
      literal: getIncidentLabel(initialIncidentType),
      citation: "",
      statute: "",
      level: "",
    };

    handleIncidentSelect(incident);
  };

  const handleOffenseSelect = (offense: OffenseData) => {
    // Check if offense is already added
    const isAlreadyAdded = addedOffenses.some(
      (addedOffense) => addedOffense.offense.code === offense.code
    );

    if (isAlreadyAdded) {
      console.log("Offense already added");
      return;
    }

    // Add new offense to the list
    const newOffense: AddedOffense = {
      id: `offense_${Date.now()}`,
      offense,
      data: {
        // Include NIBRS fields in data object
        offense_code: offense.code,
        offense_literal: offense.literal,
        offense_citation: offense.citation,
        offense_statute: offense.statute,
        offense_level: offense.level,
        // Default form values
        offenseWas: "completed",
        cargoTheftRelated: false,
        offenseSpecialCircumstance: "",
        justifiableHomicideCircumstance: "",
        biasMotivation: "",
        numberOfPremisesEntered: "",
        methodOfEntry: "",
        offenderSuspectedOfUsing: {
          alcohol: false,
          computerEquipment: false,
          drugsNarcotics: false,
        },
        attributes: {
          domesticAbuse: false,
          gangRelated: false,
          gamblingRelated: false,
        },
        weapons: [
          {
            id: "weapon_1",
            weaponType: "",
            isAutomatic: false,
          },
        ],
        criminalActivity: {
          buyingReceiving: false,
          cultivatingManufacturing: false,
          distributing: false,
          allocating: false,
          sharing: false,
          delivering: false,
          apportioning: false,
          dividing: false,
          dispensing: false,
          transferring: false,
        },
        gangInformation: "",
      },
    };

    setAddedOffenses((prev) => [...prev, newOffense]);
    setHasUnsavedChanges(true);

    // Immediately save the new offense
    if (reportId && offenseSectionId && offenseSection) {
      const updatedOffensesList = [...addedOffenses, newOffense];
      saveOffenseListToBackend(updatedOffensesList, addedIncidents);
    }

    // Trigger scroll to the newly added offense (leave 100px above)
    setTimeout(() => {
      const elem = offensesContainerRef.current
        ?.lastElementChild as HTMLElement | null;
      if (!elem) return;
      const getScrollParent = (node: HTMLElement | null): HTMLElement => {
        let p: HTMLElement | null = node;
        while (p && p !== document.body) {
          const style = getComputedStyle(p);
          const hasScrollableContent = p.scrollHeight > p.clientHeight;
          const overflowYScrollable = /(auto|scroll)/.test(style.overflowY);
          if (hasScrollableContent && overflowYScrollable) {
            return p;
          }
          p = p.parentElement;
        }
        return document.documentElement;
      };

      const scrollParent = getScrollParent(elem);
      const elemTop = elem.getBoundingClientRect().top + window.scrollY;
      const targetTop = Math.max(elemTop - 100, 0);

      if (scrollParent === document.documentElement) {
        window.scrollTo({ top: targetTop, behavior: "smooth" });
      } else {
        scrollParent.scrollTo({
          top:
            targetTop -
            scrollParent.getBoundingClientRect().top +
            scrollParent.scrollTop,
          behavior: "smooth",
        });
      }
    }, 100);
  };

  const handleIncidentSelect = (incident: IncidentData) => {
    // Allow multiple incidents of the same type - no duplicate check needed

    // Add new incident to the list
    const newIncident: AddedIncident = {
      id: `incident_${Date.now()}`,
      incident,
      data: {
        // Include incident fields in data object
        offense_code: incident.code,
        offense_literal: incident.literal,
        offense_citation: incident.citation,
        offense_statute: incident.statute,
        offense_level: incident.level,
        // Mark as incident
        isIncident: true,
        incidentType: incident.code,
      },
    };

    setAddedIncidents((prev) => [...prev, newIncident]);
    setHasUnsavedChanges(true);

    // Immediately save the new incident
    if (reportId && offenseSectionId && offenseSection) {
      const updatedIncidentsList = [...addedIncidents, newIncident];
      saveOffenseListToBackend(addedOffenses, updatedIncidentsList);
    }

    // Trigger scroll to the newly added incident (leave 100px above)
    setTimeout(() => {
      const elem = offensesContainerRef.current
        ?.lastElementChild as HTMLElement | null;
      if (!elem) return;
      const getScrollParent = (node: HTMLElement | null): HTMLElement => {
        let p: HTMLElement | null = node;
        while (p && p !== document.body) {
          const style = getComputedStyle(p);
          const hasScrollableContent = p.scrollHeight > p.clientHeight;
          const overflowYScrollable = /(auto|scroll)/.test(style.overflowY);
          if (hasScrollableContent && overflowYScrollable) {
            return p;
          }
          p = p.parentElement;
        }
        return document.documentElement;
      };

      const scrollParent = getScrollParent(elem);
      const elemTop = elem.getBoundingClientRect().top + window.scrollY;
      const targetTop = Math.max(elemTop - 100, 0);

      if (scrollParent === document.documentElement) {
        window.scrollTo({ top: targetTop, behavior: "smooth" });
      } else {
        scrollParent.scrollTo({
          top:
            targetTop -
            scrollParent.getBoundingClientRect().top +
            scrollParent.scrollTop,
          behavior: "smooth",
        });
      }
    }, 100);
  };

  // Helper function to save offense and incident list to backend
  const saveOffenseListToBackend = (
    offensesList: AddedOffense[],
    incidentsList: AddedIncident[] = addedIncidents
  ) => {
    setIsSaving(true);

    // Prepare the offense data for backend
    const offensesData = offensesList.map((addedOffense) => ({
      id: addedOffense.id,
      offense_type: addedOffense.offense.literal,
      data: addedOffense.data || {},
    }));

    // Prepare the incident data for backend
    const incidentsData = incidentsList.map((addedIncident) => ({
      id: addedIncident.id,
      offense_type: addedIncident.incident.literal,
      data: addedIncident.data || {},
    }));

    // Combine offenses and incidents
    const combinedData = [...offensesData, ...incidentsData];

    // Update the report section
    const updatedSection = {
      ...offenseSection,
      id: offenseSectionId,
      offenseList: {
        id: offenseSection?.offenseList?.id || "offense_content_01",
        offenses: combinedData,
      },
    };

    updateReportSectionMutation.mutate({
      // @ts-expect-error TODO: Fix type issue
      reportId,
      section: updatedSection,
    });
  };

  const handleSaveOffense = (offenseId: string, formData: any) => {
    if (!reportId || !offenseSectionId || readOnly) return;

    setIsSaving(true);
    setHasUnsavedChanges(false);

    // Create updated offenses list with the new form data
    const updatedOffensesList = addedOffenses.map((offense) =>
      offense.id === offenseId ? { ...offense, data: formData } : offense
    );

    // Update the local state
    setAddedOffenses(updatedOffensesList);

    // Use the helper function to save
    saveOffenseListToBackend(updatedOffensesList, addedIncidents);
  };

  const handleSaveIncident = (incidentId: string, formData: any) => {
    if (!reportId || !offenseSectionId || readOnly) return;

    setIsSaving(true);
    setHasUnsavedChanges(false);

    // Create updated incidents list with the new form data
    const updatedIncidentsList = addedIncidents.map((incident) =>
      incident.id === incidentId ? { ...incident, data: formData } : incident
    );

    // Update the local state
    setAddedIncidents(updatedIncidentsList);

    // Use the helper function to save
    saveOffenseListToBackend(addedOffenses, updatedIncidentsList);
  };

  const handleDeleteOffense = (offenseId: string) => {
    if (readOnly) return;

    setIsSaving(true);

    // Remove from local state
    const updatedOffenses = addedOffenses.filter(
      (offense) => offense.id !== offenseId
    );
    setAddedOffenses(updatedOffenses);

    // Update backend if we have a section
    if (reportId && offenseSectionId && offenseSection) {
      saveOffenseListToBackend(updatedOffenses, addedIncidents);
    } else {
      setIsSaving(false);
    }
  };

  const handleDeleteIncident = (incidentId: string) => {
    if (readOnly) return;

    setIsSaving(true);

    // Remove from local state
    const updatedIncidents = addedIncidents.filter(
      (incident) => incident.id !== incidentId
    );
    setAddedIncidents(updatedIncidents);

    // Update backend if we have a section
    if (reportId && offenseSectionId && offenseSection) {
      saveOffenseListToBackend(addedOffenses, updatedIncidents);
    } else {
      setIsSaving(false);
    }
  };

  // Handle save status changes from child components
  const handleOffenseFormSaveStatusChange = (status: {
    isSaving: boolean;
    hasUnsavedChanges: boolean;
    source: string;
  }) => {
    setIsSaving(status.isSaving);
    setHasUnsavedChanges(status.hasUnsavedChanges);
  };

  // Handle person addition from offense card

  const handleAddPersonFromOffense =
    (offenseId: string) =>
    (
      relationType:
        | "victim"
        | "offender"
        | "witness"
        | "suspect"
        | "involved_party"
    ) => {
      setActiveOffenseForPerson({ offenseId, relationType });

      // Set the active offense relation context in the parent
      if (onSetActiveOffenseRelation) {
        onSetActiveOffenseRelation({ offenseId, relationType });
      }

      if (onOpenSidePanel) {
        onOpenSidePanel("PERSON");
      }
    };

  // Handle vehicle addition from offense card
  const handleAddVehicleFromOffense = (offenseId: string) => () => {
    console.log(
      "handleAddVehicleFromOffense called with offenseId:",
      offenseId
    );
    // Set the vehicle offense context in the parent
    if (onSetActiveVehicleOffenseContext) {
      console.log("Calling onSetActiveVehicleOffenseContext");
      onSetActiveVehicleOffenseContext({ offenseId });
    } else {
      console.error("onSetActiveVehicleOffenseContext is not available");
    }

    if (onOpenSidePanel) {
      onOpenSidePanel("VEHICLE");
    }
  };

  // Handle property addition from offense card
  const handleAddPropertyFromOffense = (offenseId: string) => () => {
    console.log(
      "handleAddPropertyFromOffense called with offenseId:",
      offenseId
    );
    // Set the property offense context in the parent
    if (onSetActivePropertyOffenseContext) {
      console.log("Calling onSetActivePropertyOffenseContext");
      onSetActivePropertyOffenseContext({ offenseId });
    } else {
      console.error("onSetActivePropertyOffenseContext is not available");
    }

    if (onOpenSidePanel) {
      onOpenSidePanel("PROPERTY");
    }
  };

  // Handle organization addition from offense card
  const handleAddOrganizationFromOffense = (offenseId: string) => () => {
    console.log(
      "handleAddOrganizationFromOffense called with offenseId:",
      offenseId
    );
    // Set the organization offense context in the parent
    if (onSetActiveOrganizationOffenseContext) {
      console.log("Calling onSetActiveOrganizationOffenseContext");
      onSetActiveOrganizationOffenseContext({ offenseId });
    } else {
      console.error("onSetActiveOrganizationOffenseContext is not available");
    }

    if (onOpenSidePanel) {
      onOpenSidePanel("ORGANIZATION");
    }
  };

  // Handle victim organization addition from offense card
  const handleAddVictimOrganizationFromOffense = (offenseId: string) => () => {
    console.log(
      "handleAddVictimOrganizationFromOffense called with offenseId:",
      offenseId
    );
    // Set the victim organization offense context in the parent
    if (onSetActiveVictimOrganizationOffenseContext) {
      console.log("Calling onSetActiveVictimOrganizationOffenseContext");
      onSetActiveVictimOrganizationOffenseContext({ offenseId });
    } else {
      console.error(
        "onSetActiveVictimOrganizationOffenseContext is not available"
      );
    }

    if (onOpenSidePanel) {
      onOpenSidePanel("ORGANIZATION");
    }
  };
  // alec@ note to come back when thinking about organizations as victims
  const victimIds = useMemo(() => {
    if (!relations) return [] as string[];
    return relations
      .filter((rel: any) => rel.relationType === "RELATION_TYPE_OFFENSE_VICTIM")
      .map((rel: any) => {
        if (rel.objectA?.objectType === "entity") return rel.objectA.globalId;
        if (rel.objectB?.objectType === "entity") return rel.objectB.globalId;
        return null;
      })
      .filter(Boolean);
  }, [relations]);

  const offenderIds = useMemo(() => {
    if (!relations) return [] as string[];
    return relations
      .filter(
        (rel: any) => rel.relationType === "RELATION_TYPE_OFFENSE_OFFENDER"
      )
      .map((rel: any) => {
        if (rel.objectA?.objectType === "entity") return rel.objectA.globalId;
        if (rel.objectB?.objectType === "entity") return rel.objectB.globalId;
        return null;
      })
      .filter(Boolean);
  }, [relations]);

  const victimPeople = useMemo(() => {
    return (people || []).filter((p) => victimIds.includes(p.id));
  }, [people, victimIds]);

  const offenderPeople = useMemo(() => {
    return (people || []).filter((p) => offenderIds.includes(p.id));
  }, [people, offenderIds]);

  const victimOffenderPairs = useMemo(() => {
    const pairs: any[] = [];

    // Group victim & offender person IDs by offenseId
    const offenseMap: Record<
      string,
      { victims: string[]; offenders: string[] }
    > = {};

    (relations || []).forEach((rel: any) => {
      const isVictimRel = rel.relationType === "RELATION_TYPE_OFFENSE_VICTIM";
      const isOffenderRel =
        rel.relationType === "RELATION_TYPE_OFFENSE_OFFENDER";
      if (!isVictimRel && !isOffenderRel) return;

      // Determine offenseId and personId from relation
      let offenseId: string | null = null;
      let personId: string | null = null;

      if (rel.objectA?.objectType === "offense")
        offenseId = rel.objectA.reportScopedId;
      if (rel.objectB?.objectType === "offense")
        offenseId = rel.objectB.reportScopedId;

      if (rel.objectA?.objectType === "entity") personId = rel.objectA.globalId;
      if (rel.objectB?.objectType === "entity") personId = rel.objectB.globalId;

      if (!offenseId || !personId) return;

      if (!offenseMap[offenseId]) {
        offenseMap[offenseId] = { victims: [], offenders: [] };
      }

      if (isVictimRel) offenseMap[offenseId].victims.push(personId);
      if (isOffenderRel) offenseMap[offenseId].offenders.push(personId);
    });

    const existingVO = (relations || []).filter(
      (rel: any) =>
        rel.objectA?.objectType === "entity" &&
        rel.objectB?.objectType === "entity"
    );

    Object.entries(offenseMap).forEach(([offenseId, group]) => {
      group.victims.forEach((vId) => {
        group.offenders.forEach((oId) => {
          const victim = (people || []).find((p) => p.id === vId);
          const offender = (people || []).find((p) => p.id === oId);
          if (!victim || !offender) return;

          const existing = existingVO.find(
            (rel: any) =>
              (rel.objectA.globalId === vId && rel.objectB.globalId === oId) ||
              (rel.objectA.globalId === oId && rel.objectB.globalId === vId)
          );

          pairs.push({
            victim,
            offender,
            existingRelationId: existing?.id,
            relationType: existing?.relationType,
            existingRelation: existing || null,
            offenseId,
          });
        });
      });
    });

    return pairs;
  }, [people, relations]);

  return (
    <Box>
      {/* Top Divider */}
      <Divider sx={{ borderColor: colors.grey[300], mb: 3 }} />

      {/* Header */}
      <Box
        sx={{
          display: "flex",
          justifyContent: "space-between",
          alignItems: "center",
          py: 2,
        }}
      >
        <Typography style="h1" color={colors.grey[900]}>
          Events
        </Typography>

        {!readOnly && (
          <Button onClick={handleAddClick} label="Add" leftIcon={<AddIcon />} />
        )}
      </Box>

      {/* Added Offense and Incident Cards */}
      {(addedOffenses.length > 0 || addedIncidents.length > 0) && (
        <Box ref={offensesContainerRef} sx={{ mt: 3 }}>
          {/* Offense Cards */}
          {addedOffenses.map((addedOffense) => (
            <OffenseFormCard
              key={addedOffense.id}
              offense={addedOffense.offense}
              offenseId={addedOffense.id}
              onSave={handleSaveOffense}
              onDelete={handleDeleteOffense}
              onSaveStatusChange={handleOffenseFormSaveStatusChange}
              readOnly={readOnly}
              initialData={addedOffense.data}
              people={people}
              vehicles={vehicles}
              properties={properties}
              organizations={organizations}
              relations={relations}
              reportId={reportId}
              associatedCases={associatedCases}
              onAddPerson={handleAddPersonFromOffense(addedOffense.id)}
              onAddVehicle={handleAddVehicleFromOffense(addedOffense.id)}
              onAddProperty={handleAddPropertyFromOffense(addedOffense.id)}
              onAddOrganization={handleAddOrganizationFromOffense(
                addedOffense.id
              )}
              onAddVictimOrganization={handleAddVictimOrganizationFromOffense(
                addedOffense.id
              )}
              onRemovePersonFromOffense={(personId: string) => {
                if (onRemovePersonFromOffense) {
                  onRemovePersonFromOffense(personId, addedOffense.id);
                }
              }}
              onRemoveVehicleFromOffense={(vehicleId: string) => {
                if (onRemoveVehicleFromOffense) {
                  onRemoveVehicleFromOffense(vehicleId, addedOffense.id);
                }
              }}
              onRemovePropertyFromOffense={(propertyId: string) => {
                if (onRemovePropertyFromOffense) {
                  onRemovePropertyFromOffense(propertyId, addedOffense.id);
                }
              }}
              onRemoveOrganizationFromOffense={(organizationId: string) => {
                if (onRemoveOrganizationFromOffense) {
                  onRemoveOrganizationFromOffense(
                    organizationId,
                    addedOffense.id
                  );
                }
              }}
              onRemoveVictimOrganizationFromOffense={(
                organizationId: string
              ) => {
                if (onRemoveVictimOrganizationFromOffense) {
                  onRemoveVictimOrganizationFromOffense(
                    organizationId,
                    addedOffense.id
                  );
                }
              }}
              onRemovePersonFromReport={onRemovePersonFromReport}
              onRemoveVehicleFromReport={onRemoveVehicleFromReport}
              onRemovePropertyFromReport={onRemovePropertyFromReport}
              onRemoveOrganizationFromReport={onRemoveOrganizationFromReport}
              onOpenSidePanel={onOpenSidePanel}
              onQuickAddPersonToOffense={(
                personId: string,
                offenseId: string,
                relationType:
                  | "victim"
                  | "offender"
                  | "witness"
                  | "suspect"
                  | "involved_party"
              ) => {
                if (onAddPersonToOffense) {
                  onAddPersonToOffense(personId, offenseId, relationType);
                }
              }}
              onQuickAddVehicleToOffense={(
                vehicleId: string,
                offenseId: string
              ) => {
                if (onAddVehicleToOffense) {
                  onAddVehicleToOffense(vehicleId, offenseId);
                }
              }}
              onQuickAddPropertyToOffense={(
                propertyId: string,
                offenseId: string
              ) => {
                if (onAddPropertyToOffense) {
                  onAddPropertyToOffense(propertyId, offenseId);
                }
              }}
              onQuickAddOrganizationToOffense={(
                organizationId: string,
                offenseId: string
              ) => {
                if (onAddOrganizationToOffense) {
                  onAddOrganizationToOffense(organizationId, offenseId);
                }
              }}
              onQuickAddVictimOrganizationToOffense={(
                organizationId: string,
                offenseId: string
              ) => {
                if (onAddVictimOrganizationToOffense) {
                  onAddVictimOrganizationToOffense(organizationId, offenseId);
                }
              }}
              onEntityEdit={onEntityEdit}
              onAddVictimDetails={onAddVictimDetails}
              onAddVictimDetailsOrganization={onAddVictimDetailsOrganization}
            />
          ))}

          {/* Incident Cards */}
          {addedIncidents.map((addedIncident) => (
            <IncidentFormCard
              key={addedIncident.id}
              incident={addedIncident.incident}
              incidentId={addedIncident.id}
              onSave={handleSaveIncident}
              onDelete={handleDeleteIncident}
              onSaveStatusChange={handleOffenseFormSaveStatusChange}
              readOnly={readOnly}
              initialData={addedIncident.data}
              people={people}
              vehicles={vehicles}
              properties={properties}
              organizations={organizations}
              relations={relations}
              reportId={reportId}
              associatedCases={associatedCases}
              onAddPerson={handleAddPersonFromOffense(addedIncident.id)}
              onAddVehicle={handleAddVehicleFromOffense(addedIncident.id)}
              onAddProperty={handleAddPropertyFromOffense(addedIncident.id)}
              onAddOrganization={handleAddOrganizationFromOffense(
                addedIncident.id
              )}
              onRemovePersonFromOffense={(personId: string) => {
                if (onRemovePersonFromOffense) {
                  onRemovePersonFromOffense(personId, addedIncident.id);
                }
              }}
              onRemoveVehicleFromOffense={(vehicleId: string) => {
                if (onRemoveVehicleFromOffense) {
                  onRemoveVehicleFromOffense(vehicleId, addedIncident.id);
                }
              }}
              onRemovePropertyFromOffense={(propertyId: string) => {
                if (onRemovePropertyFromOffense) {
                  onRemovePropertyFromOffense(propertyId, addedIncident.id);
                }
              }}
              onRemoveOrganizationFromOffense={(organizationId: string) => {
                if (onRemoveOrganizationFromOffense) {
                  onRemoveOrganizationFromOffense(
                    organizationId,
                    addedIncident.id
                  );
                }
              }}
              onRemovePersonFromReport={onRemovePersonFromReport}
              onRemoveVehicleFromReport={onRemoveVehicleFromReport}
              onRemovePropertyFromReport={onRemovePropertyFromReport}
              onRemoveOrganizationFromReport={onRemoveOrganizationFromReport}
              onOpenSidePanel={onOpenSidePanel}
              onQuickAddPersonToOffense={(
                personId: string,
                incidentId: string,
                relationType:
                  | "victim"
                  | "offender"
                  | "witness"
                  | "suspect"
                  | "involved_party"
              ) => {
                if (onAddPersonToOffense) {
                  onAddPersonToOffense(personId, incidentId, relationType);
                }
              }}
              onQuickAddVehicleToOffense={(
                vehicleId: string,
                incidentId: string
              ) => {
                if (onAddVehicleToOffense) {
                  onAddVehicleToOffense(vehicleId, incidentId);
                }
              }}
              onQuickAddPropertyToOffense={(
                propertyId: string,
                incidentId: string
              ) => {
                if (onAddPropertyToOffense) {
                  onAddPropertyToOffense(propertyId, incidentId);
                }
              }}
              onQuickAddOrganizationToOffense={(
                organizationId: string,
                incidentId: string
              ) => {
                if (onAddOrganizationToOffense) {
                  onAddOrganizationToOffense(organizationId, incidentId);
                }
              }}
              onEntityEdit={onEntityEdit}
            />
          ))}
        </Box>
      )}

      {/* VictimOffender Relations Card */}
      {victimOffenderPairs.length > 0 && (
        <OffenseRelationsCard
          reportId={reportId || ""}
          victimOffenderPairs={victimOffenderPairs}
          readOnly={readOnly}
        />
      )}

      {/* Bottom Divider */}
      <Divider sx={{ borderColor: colors.grey[300], mt: 3 }} />

      {/* Add Menu */}
      <Menu
        anchorEl={anchorEl}
        open={open}
        onClose={handleClose}
        anchorOrigin={{
          vertical: "bottom",
          horizontal: "right",
        }}
        transformOrigin={{
          vertical: "top",
          horizontal: "right",
        }}
        sx={{
          mt: 0.25,
          "& .MuiPaper-root": {
            borderRadius: "8px",
            minWidth: "200px",
          },
          "& .MuiMenuItem-root": {
            py: 1.5,
          },
        }}
      >
        <MenuItem onClick={handleAddOffense}>
          <Typography style="body2" color={colors.grey[500]}>
            Add an Offense
          </Typography>
        </MenuItem>
        <MenuItem onClick={handleAddIncident}>
          <Typography style="body2" color={colors.grey[500]}>
            Add an Incident
          </Typography>
        </MenuItem>
      </Menu>

      {/* Offense Search Modal */}
      <OffenseSearchModal
        open={isOffenseModalOpen}
        onClose={() => setIsOffenseModalOpen(false)}
        onSelectOffense={handleOffenseSelect}
      />
    </Box>
  );
}
