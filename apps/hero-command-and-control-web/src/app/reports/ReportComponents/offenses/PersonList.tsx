import { But<PERSON> } from "@/design-system/components/Button";
import { Checkbox } from "@/design-system/components/Checkbox";
import { Label } from "@/design-system/components/Label";
import { Typography } from "@/design-system/components/Typography";
import { colors } from "@/design-system/tokens";
import AddIcon from "@mui/icons-material/Add";
import DeleteIcon from "@mui/icons-material/Delete";
import ExpandLessIcon from "@mui/icons-material/ExpandLess";
import ExpandMoreIcon from "@mui/icons-material/ExpandMore";
import MoreVertIcon from "@mui/icons-material/MoreVert";
import OpenInNewIcon from "@mui/icons-material/OpenInNew";
import {
  Box,
  Collapse,
  Dialog,
  DialogActions,
  DialogContent,
  IconButton,
  Menu,
  MenuItem,
} from "@mui/material";
import CircularProgress from "@mui/material/CircularProgress";
import React, { useEffect, useState } from "react";

export interface PersonData {
  id: string;
  name: string;
  sex: string;
  height: string;
  hair: string;
  weight: string;
  eye: string;
  dateOfBirth: string;
}

export interface OrganizationData {
  id: string;
  name: string;
  type: string;
  streetAddress: string;
  state: string;
  zIP: string;
}

interface PersonListProps {
  people: PersonData[];
  title: string;
  relationType: "victim" | "offender" | "other";
  offenseId: string;
  readOnly?: boolean;
  relations?: any[];
  availablePeopleForQuickAdd: PersonData[];
  reportId?: string;
  isIncident?: boolean; // New prop to indicate this is for an incident
  // Organization support for victims only
  organizations?: OrganizationData[];
  availableOrganizationsForQuickAdd?: OrganizationData[];
  onAddPerson: (
    relationType:
      | "victim"
      | "offender"
      | "witness"
      | "suspect"
      | "involved_party"
  ) => void;
  onAddOrganization?: () => void;
  onRemovePersonFromOffense?: (personId: string, offenseId: string) => void;
  onRemovePersonFromReport?: (personId: string, entityType: "person") => void;
  onRemoveOrganizationFromOffense?: (
    organizationId: string,
    offenseId: string
  ) => void;
  onRemoveVictimOrganizationFromOffense?: (
    organizationId: string,
    offenseId: string
  ) => void;
  onRemoveOrganizationFromReport?: (
    organizationId: string,
    entityType: "organization"
  ) => void;
  onQuickAddPerson: (
    personId: string,
    relationType:
      | "victim"
      | "offender"
      | "witness"
      | "suspect"
      | "involved_party"
  ) => void;
  onQuickAddOrganization?: (organizationId: string) => void;
  onEntityEdit?: (entityId: string, entityType: "person") => void;
  onEntityEditOrganization?: (
    entityId: string,
    entityType: "organization"
  ) => void;
  onAddVictimDetails?: (personId: string) => void;
  onAddVictimDetailsOrganization?: (organizationId: string) => void;
}

const PersonList: React.FC<PersonListProps> = ({
  people,
  title,
  relationType,
  offenseId,
  readOnly = false,
  relations = [],
  availablePeopleForQuickAdd,
  reportId,
  isIncident = false,
  // Organization props - only used for victims
  organizations = [],
  availableOrganizationsForQuickAdd = [],
  onAddPerson,
  onAddOrganization,
  onRemovePersonFromOffense,
  onRemovePersonFromReport,
  onRemoveOrganizationFromOffense,
  onRemoveVictimOrganizationFromOffense,
  onRemoveOrganizationFromReport,
  onQuickAddPerson,
  onQuickAddOrganization,
  onEntityEdit,
  onEntityEditOrganization,
  onAddVictimDetails,
  onAddVictimDetailsOrganization,
}) => {
  const [menuAnchorEl, setMenuAnchorEl] = useState<null | HTMLElement>(null);
  const [selectedPersonForMenu, setSelectedPersonForMenu] =
    useState<PersonData | null>(null);
  const [selectedOrganizationForMenu, setSelectedOrganizationForMenu] =
    useState<OrganizationData | null>(null);
  const [removeModalOpen, setRemoveModalOpen] = useState(false);
  const [removeFromReport, setRemoveFromReport] = useState(false);
  const [quickAddExpanded, setQuickAddExpanded] = useState(true);
  const [loadingIds, setLoadingIds] = useState<Set<string>>(new Set());
  const [addOtherMenuAnchor, setAddOtherMenuAnchor] =
    useState<null | HTMLElement>(null);
  const [addVictimMenuAnchor, setAddVictimMenuAnchor] =
    useState<null | HTMLElement>(null);
  const [quickAddMenuAnchor, setQuickAddMenuAnchor] =
    useState<null | HTMLElement>(null);
  const [selectedPersonForQuickAdd, setSelectedPersonForQuickAdd] = useState<
    string | null
  >(null);
  const [selectedOrganizationForQuickAdd, setSelectedOrganizationForQuickAdd] =
    useState<string | null>(null);
  const [menuEntityType, setMenuEntityType] = useState<
    "person" | "organization"
  >("person");

  // Helper function to check if victim has filled additional questions
  const getVictimReportRelation = (personId: string) => {
    if (!relations || !reportId) return null;

    return relations.find((rel: any) => {
      const isVictimReportRelation =
        rel.relationType === "RELATION_TYPE_VICTIM_REPORT";
      const isPersonInvolved =
        (rel.objectA?.objectType === "entity" &&
          rel.objectA?.globalId === personId) ||
        (rel.objectB?.objectType === "entity" &&
          rel.objectB?.globalId === personId);
      const isReportInvolved =
        (rel.objectA?.objectType === "report" &&
          rel.objectA?.reportScopedId === reportId) ||
        (rel.objectB?.objectType === "report" &&
          rel.objectB?.reportScopedId === reportId);

      return isVictimReportRelation && isPersonInvolved && isReportInvolved;
    });
  };

  // Helper function to check if organization victim has filled additional questions
  const getOrganizationVictimReportRelation = (organizationId: string) => {
    if (!relations || !reportId) return null;

    return relations.find((rel: any) => {
      const isVictimReportRelation =
        rel.relationType === "RELATION_TYPE_VICTIM_REPORT";
      const isOrganizationInvolved =
        (rel.objectA?.objectType === "entity" &&
          rel.objectA?.globalId === organizationId) ||
        (rel.objectB?.objectType === "entity" &&
          rel.objectB?.globalId === organizationId);
      const isReportInvolved =
        (rel.objectA?.objectType === "report" &&
          rel.objectA?.reportScopedId === reportId) ||
        (rel.objectB?.objectType === "report" &&
          rel.objectB?.reportScopedId === reportId);

      return (
        isVictimReportRelation && isOrganizationInvolved && isReportInvolved
      );
    });
  };

  // Helper function to get person role numbers (V/O/W/S/I) and RP tag for a person in the main list
  const getVictimOffenderNumbers = (personId: string): string[] => {
    if (!relations) return [];

    // Find ALL person relations (victim/offender/witness/suspect/involved_party) for this person
    const personRelations = relations.filter((rel: any) => {
      const isPersonRelation =
        rel.relationType === "RELATION_TYPE_OFFENSE_VICTIM" ||
        rel.relationType === "RELATION_TYPE_OFFENSE_OFFENDER" ||
        rel.relationType === "RELATION_TYPE_OFFENSE_WITNESS" ||
        rel.relationType === "RELATION_TYPE_OFFENSE_SUSPECT" ||
        rel.relationType === "RELATION_TYPE_OFFENSE_INVOLVED_PARTY";

      if (!isPersonRelation) return false;

      const isPersonInvolved =
        (rel.objectA?.objectType === "entity" &&
          rel.objectA?.globalId === personId) ||
        (rel.objectB?.objectType === "entity" &&
          rel.objectB?.globalId === personId);

      return isPersonInvolved && rel.metadata?.victimOffenderNumber;
    });

    // Get all victim/offender numbers and remove duplicates
    const numbers = personRelations
      .map((rel) => rel.metadata?.victimOffenderNumber)
      .filter(Boolean);

    // Remove duplicates and ensure no multiple tags of same type
    const uniqueNumbers = [];
    const seenTypes = new Set();

    for (const number of numbers) {
      const type = number.charAt(0); // 'V' or 'O'
      if (!seenTypes.has(type)) {
        uniqueNumbers.push(number);
        seenTypes.add(type);
      }
    }

    // Check for reporting party status from victim report relations
    const victimReportRelation = relations.find((rel: any) => {
      const isVictimReportRelation =
        rel.relationType === "RELATION_TYPE_VICTIM_REPORT";
      const isPersonInvolved =
        (rel.objectA?.objectType === "entity" &&
          rel.objectA?.globalId === personId) ||
        (rel.objectB?.objectType === "entity" &&
          rel.objectB?.globalId === personId);
      const isReportInvolved =
        (rel.objectA?.objectType === "report" &&
          rel.objectA?.reportScopedId === reportId) ||
        (rel.objectB?.objectType === "report" &&
          rel.objectB?.reportScopedId === reportId);

      return isVictimReportRelation && isPersonInvolved && isReportInvolved;
    });

    // Add RP tag if person is reporting party
    if (victimReportRelation?.metadata?.isReportingParty) {
      uniqueNumbers.push("RP");
    }

    return uniqueNumbers;
  };

  // Helper function to get person role numbers (V/O/W/S/I) and RP tag for quick add people (from case)
  const getQuickAddVictimOffenderNumbers = (personId: string): string[] => {
    if (!relations) return [];

    // Find ALL victim or offender relations for this person
    const personRelations = relations.filter((rel: any) => {
      const isPersonRelation =
        rel.relationType === "RELATION_TYPE_OFFENSE_VICTIM" ||
        rel.relationType === "RELATION_TYPE_OFFENSE_OFFENDER" ||
        rel.relationType === "RELATION_TYPE_OFFENSE_WITNESS" ||
        rel.relationType === "RELATION_TYPE_OFFENSE_SUSPECT" ||
        rel.relationType === "RELATION_TYPE_OFFENSE_INVOLVED_PARTY";

      if (!isPersonRelation) return false;

      const isPersonInvolved =
        (rel.objectA?.objectType === "entity" &&
          rel.objectA?.globalId === personId) ||
        (rel.objectB?.objectType === "entity" &&
          rel.objectB?.globalId === personId);

      return isPersonInvolved && rel.metadata?.victimOffenderNumber;
    });

    // Get all victim/offender numbers and remove duplicates
    const numbers = personRelations
      .map((rel) => rel.metadata?.victimOffenderNumber)
      .filter(Boolean);

    // Remove duplicates and ensure no multiple tags of same type (no V1 and V3)
    const uniqueNumbers = [];
    const seenTypes = new Set();

    for (const number of numbers) {
      const type = number.charAt(0); // 'V' or 'O'
      if (!seenTypes.has(type)) {
        uniqueNumbers.push(number);
        seenTypes.add(type);
      }
    }

    // Check for reporting party status from victim report relations
    const victimReportRelation = relations.find((rel: any) => {
      const isVictimReportRelation =
        rel.relationType === "RELATION_TYPE_VICTIM_REPORT";
      const isPersonInvolved =
        (rel.objectA?.objectType === "entity" &&
          rel.objectA?.globalId === personId) ||
        (rel.objectB?.objectType === "entity" &&
          rel.objectB?.globalId === personId);
      const isReportInvolved =
        (rel.objectA?.objectType === "report" &&
          rel.objectA?.reportScopedId === reportId) ||
        (rel.objectB?.objectType === "report" &&
          rel.objectB?.reportScopedId === reportId);

      return isVictimReportRelation && isPersonInvolved && isReportInvolved;
    });

    // Add RP tag if person is reporting party
    if (victimReportRelation?.metadata?.isReportingParty) {
      uniqueNumbers.push("RP");
    }

    return uniqueNumbers;
  };

  // Helper function to get color based on designation prefix
  const getLabelColor = (
    designation: string
  ): "vine" | "rose" | "purple" | "amber" | "grey" | "blue" => {
    if (designation === "RP") {
      return "blue"; // Reporting Party - blue
    }

    const prefix = designation.charAt(0);
    switch (prefix) {
      case "V":
        return "vine"; // Victim - green
      case "O":
        return "rose"; // Offender - red
      case "W":
        return "purple"; // Witness - purple
      case "S":
        return "amber"; // Suspect - amber
      case "I":
        return "grey"; // Involved Party - grey
      default:
        return "grey";
    }
  };

  const handleMenuClick = (
    event: React.MouseEvent<HTMLElement>,
    entity: PersonData | OrganizationData,
    entityType: "person" | "organization"
  ) => {
    event.stopPropagation();
    setMenuAnchorEl(event.currentTarget);
    setMenuEntityType(entityType);
    if (entityType === "person") {
      setSelectedPersonForMenu(entity as PersonData);
      setSelectedOrganizationForMenu(null);
    } else {
      setSelectedOrganizationForMenu(entity as OrganizationData);
      setSelectedPersonForMenu(null);
    }
  };

  const handleMenuClose = () => {
    setMenuAnchorEl(null);
    setSelectedPersonForMenu(null);
    setSelectedOrganizationForMenu(null);
    setMenuEntityType("person");
  };

  const handleOpenRecord = () => {
    const selectedEntity = selectedPersonForMenu || selectedOrganizationForMenu;
    if (selectedEntity) {
      window.open(`/entity?entityId=${selectedEntity.id}`, "_blank");
    }
    setMenuAnchorEl(null);
    setSelectedPersonForMenu(null);
    setSelectedOrganizationForMenu(null);
  };

  const handleRemoveClick = () => {
    setRemoveModalOpen(true);
    setMenuAnchorEl(null);
  };

  const handleRemoveCancel = () => {
    setRemoveModalOpen(false);
    setRemoveFromReport(false);
    setSelectedPersonForMenu(null);
    setSelectedOrganizationForMenu(null);
    setMenuEntityType("person");
  };

  const handleRemoveConfirm = () => {
    if (selectedPersonForMenu) {
      if (onRemovePersonFromOffense) {
        onRemovePersonFromOffense(selectedPersonForMenu.id, offenseId);
      }

      if (removeFromReport && onRemovePersonFromReport) {
        onRemovePersonFromReport(selectedPersonForMenu.id, "person");
      }
    } else if (selectedOrganizationForMenu) {
      // For victim organizations, use the victim-specific removal handler
      if (relationType === "victim" && onRemoveVictimOrganizationFromOffense) {
        onRemoveVictimOrganizationFromOffense(
          selectedOrganizationForMenu.id,
          offenseId
        );
      } else if (onRemoveOrganizationFromOffense) {
        // For general organizations, use the general removal handler
        onRemoveOrganizationFromOffense(
          selectedOrganizationForMenu.id,
          offenseId
        );
      }

      if (removeFromReport && onRemoveOrganizationFromReport) {
        onRemoveOrganizationFromReport(
          selectedOrganizationForMenu.id,
          "organization"
        );
      }
    }

    setRemoveModalOpen(false);
    setRemoveFromReport(false);
    setSelectedPersonForMenu(null);
    setSelectedOrganizationForMenu(null);
    setMenuEntityType("person");
  };

  const handleQuickAddClick = (
    entityId: string,
    entityType: "person" | "organization",
    event?: React.MouseEvent
  ) => {
    if (entityType === "organization") {
      if (onQuickAddOrganization) {
        onQuickAddOrganization(entityId);
      }
      return;
    }

    // Existing person quick add logic
    if (relationType === "other" && !isIncident) {
      // For "other" people in offenses, show a menu to select witness/suspect/involved_party
      setSelectedPersonForQuickAdd(entityId);
      setQuickAddMenuAnchor((event?.currentTarget as HTMLElement) || null);
    } else if (isIncident) {
      // For incidents, directly add as involved_party
      onQuickAddPerson(entityId, "involved_party");
    } else {
      onQuickAddPerson(entityId, relationType as "victim" | "offender");
    }
  };

  const handleAddEntity = (entityType?: "person" | "organization") => {
    if (entityType === "organization") {
      if (onAddOrganization) {
        onAddOrganization();
      }
      return;
    }

    if (entityType === "person") {
      if (relationType === "other" && !isIncident) {
        // For "other" type in offenses, show the menu to select specific type
        setAddOtherMenuAnchor(
          document.querySelector(
            `[data-testid="add-${relationType}-button"]`
          ) as HTMLElement
        );
      } else if (isIncident) {
        // For incidents, directly add as involved_party
        onAddPerson("involved_party");
      } else {
        onAddPerson(
          relationType as
            | "victim"
            | "offender"
            | "witness"
            | "suspect"
            | "involved_party"
        );
      }
      return;
    }

    // No entity type specified - this means we need to show a menu
    if (shouldShowOrganizations) {
      // For victims with organizations, show victim type menu
      setAddVictimMenuAnchor(
        document.querySelector(
          `[data-testid="add-${relationType}-button"]`
        ) as HTMLElement
      );
    } else if (relationType === "other" && !isIncident) {
      // For "other" type in offenses, show the menu to select specific type
      setAddOtherMenuAnchor(
        document.querySelector(
          `[data-testid="add-${relationType}-button"]`
        ) as HTMLElement
      );
    } else if (isIncident) {
      // For incidents, directly add as involved_party
      onAddPerson("involved_party");
    } else {
      onAddPerson(
        relationType as
          | "victim"
          | "offender"
          | "witness"
          | "suspect"
          | "involved_party"
      );
    }
  };

  // Clear loading state when entities are no longer in quick add lists
  useEffect(() => {
    setLoadingIds((prev) => {
      const newSet = new Set(
        [...prev].filter(
          (id) =>
            availablePeopleForQuickAdd.some((p) => p.id === id) ||
            availableOrganizationsForQuickAdd.some((o) => o.id === id)
        )
      );

      // Only update state if the set actually changed
      if (
        newSet.size !== prev.size ||
        ![...newSet].every((id) => prev.has(id))
      ) {
        return newSet;
      }
      return prev;
    });
  }, [availablePeopleForQuickAdd, availableOrganizationsForQuickAdd]);

  const handleAddOtherMenuClose = () => {
    setAddOtherMenuAnchor(null);
  };

  const handleAddOtherType = (
    type: "witness" | "suspect" | "involved_party"
  ) => {
    onAddPerson(type);
    handleAddOtherMenuClose();
  };

  const handleAddVictimMenuClose = () => {
    setAddVictimMenuAnchor(null);
  };

  const handleAddVictimType = (entityType: "person" | "organization") => {
    handleAddEntity(entityType);
    handleAddVictimMenuClose();
  };

  const handleQuickAddMenuClose = () => {
    setQuickAddMenuAnchor(null);
    setSelectedPersonForQuickAdd(null);
  };

  const handleQuickAddOtherType = (
    type: "witness" | "suspect" | "involved_party"
  ) => {
    if (selectedPersonForQuickAdd) {
      onQuickAddPerson(selectedPersonForQuickAdd, type);
    }
    handleQuickAddMenuClose();
  };

  // Show organizations for victims only
  const shouldShowOrganizations = relationType === "victim" && !isIncident;
  const allEntities = shouldShowOrganizations
    ? [...people, ...organizations]
    : people;
  const allQuickAddEntities = shouldShowOrganizations
    ? [...availablePeopleForQuickAdd, ...availableOrganizationsForQuickAdd]
    : availablePeopleForQuickAdd;

  return (
    <>
      <Box sx={{ my: 4 }}>
        <Box
          sx={{
            display: "flex",
            justifyContent: "space-between",
            alignItems: "center",
            mb: 3,
          }}
        >
          <Typography style="caps1" color={colors.grey[900]}>
            {title}
          </Typography>
        </Box>

        {allEntities.length > 0 && (
          <Box sx={{ display: "flex", flexDirection: "column", gap: 2 }}>
            {/* Render People */}
            {people.map((person) => {
              const victimOffenderNumbers = getVictimOffenderNumbers(person.id);

              return (
                <Box
                  key={person.id}
                  onClick={() => {
                    if (readOnly) return;
                    onEntityEdit && onEntityEdit(person.id, "person");
                  }}
                  sx={{
                    padding: 2,
                    border: `1px solid ${colors.grey[200]}`,
                    borderRadius: "8px",
                    backgroundColor: colors.grey[50],
                    display: "flex",
                    justifyContent: "space-between",
                    alignItems: "center",
                    cursor: readOnly ? "default" : "pointer",
                    "&:hover:not(:has(.button-area:hover))": {
                      backgroundColor: colors.grey[100],
                    },
                    "&:active:not(:has(.button-area:hover))": {
                      backgroundColor: colors.grey[200],
                    },
                  }}
                >
                  <Box sx={{ flex: 1 }}>
                    <Box
                      sx={{
                        mb: 1,
                        display: "flex",
                        alignItems: "center",
                        gap: 1,
                      }}
                    >
                      <Typography style="body3" color={colors.grey[900]}>
                        {person.name}
                      </Typography>
                      {(() => {
                        const numbers = getVictimOffenderNumbers(person.id);
                        if (numbers.length === 0) return null;
                        return numbers.map((number, index) => (
                          <Label
                            key={index}
                            label={number}
                            size="small"
                            color={getLabelColor(number)}
                            prominence={false}
                            pilled
                          />
                        ));
                      })()}
                    </Box>
                    <Box sx={{ display: "flex", gap: 1, flexWrap: "wrap" }}>
                      <Typography style="tag2" color={colors.grey[500]}>
                        Sex:
                      </Typography>
                      <Typography style="tag2" color={colors.grey[900]}>
                        {person.sex || "---"}
                      </Typography>
                      <Typography style="tag2" color={colors.grey[500]}>
                        , Height:
                      </Typography>
                      <Typography style="tag2" color={colors.grey[900]}>
                        {person.height || "---"}
                      </Typography>
                      <Typography style="tag2" color={colors.grey[500]}>
                        , Weight:
                      </Typography>
                      <Typography style="tag2" color={colors.grey[900]}>
                        {person.weight || "---"}
                      </Typography>
                      <Typography style="tag2" color={colors.grey[500]}>
                        , Hair:
                      </Typography>
                      <Typography style="tag2" color={colors.grey[900]}>
                        {person.hair || "---"}
                      </Typography>
                      <Typography style="tag2" color={colors.grey[500]}>
                        , Eyes:
                      </Typography>
                      <Typography style="tag2" color={colors.grey[900]}>
                        {person.eye || "---"}
                      </Typography>
                    </Box>
                  </Box>
                  {!readOnly && (
                    <Box
                      className="button-area"
                      sx={{ display: "flex", alignItems: "center", gap: 1 }}
                    >
                      {/* Victim Details button - show different variants based on whether relation exists */}
                      {relationType === "victim" && onAddVictimDetails && (
                        <Box
                          sx={{
                            "& button:hover": {
                              backgroundColor: getVictimReportRelation(
                                person.id
                              )
                                ? `${colors.grey[200]} !important`
                                : `${colors.blue[50]} !important`,
                            },
                            "& button:active": {
                              backgroundColor: getVictimReportRelation(
                                person.id
                              )
                                ? `${colors.grey[300]} !important`
                                : `${colors.blue[100]} !important`,
                            },
                          }}
                        >
                          <Button
                            label={
                              getVictimReportRelation(person.id)
                                ? "View Victim Details"
                                : "Add Victim Details"
                            }
                            size="small"
                            style={
                              getVictimReportRelation(person.id)
                                ? "ghost"
                                : "outline"
                            }
                            color={
                              getVictimReportRelation(person.id)
                                ? "grey"
                                : "blue"
                            }
                            onClick={(e) => {
                              e.stopPropagation();
                              onAddVictimDetails(person.id);
                            }}
                          />
                        </Box>
                      )}
                      <IconButton
                        size="small"
                        onClick={(e) => handleMenuClick(e, person, "person")}
                        sx={{
                          color: colors.grey[600],
                          "&:hover": {
                            bgcolor: colors.grey[200],
                            color: colors.grey[800],
                          },
                        }}
                      >
                        <MoreVertIcon fontSize="small" />
                      </IconButton>
                    </Box>
                  )}
                </Box>
              );
            })}

            {/* Render Organizations (only for victims) */}
            {shouldShowOrganizations &&
              organizations.map((organization) => {
                const victimOffenderNumbers = getVictimOffenderNumbers(
                  organization.id
                );

                return (
                  <Box
                    key={organization.id}
                    onClick={() => {
                      if (readOnly) return;
                      onEntityEditOrganization &&
                        onEntityEditOrganization(
                          organization.id,
                          "organization"
                        );
                    }}
                    sx={{
                      padding: 2,
                      border: `1px solid ${colors.grey[200]}`,
                      borderRadius: "8px",
                      backgroundColor: colors.grey[50],
                      display: "flex",
                      justifyContent: "space-between",
                      alignItems: "center",
                      cursor: readOnly ? "default" : "pointer",
                      "&:hover:not(:has(.button-area:hover))": {
                        backgroundColor: colors.grey[100],
                      },
                      "&:active:not(:has(.button-area:hover))": {
                        backgroundColor: colors.grey[200],
                      },
                    }}
                  >
                    <Box sx={{ flex: 1 }}>
                      <Box
                        sx={{
                          mb: 1,
                          display: "flex",
                          alignItems: "center",
                          gap: 1,
                        }}
                      >
                        <Typography style="body3" color={colors.grey[900]}>
                          {organization.name}
                        </Typography>
                        {(() => {
                          const numbers = getVictimOffenderNumbers(
                            organization.id
                          );
                          if (numbers.length === 0) return null;
                          return numbers.map((number, index) => (
                            <Label
                              key={index}
                              label={number}
                              size="small"
                              color={getLabelColor(number)}
                              prominence={false}
                              pilled
                            />
                          ));
                        })()}
                      </Box>
                      <Box sx={{ display: "flex", gap: 1, flexWrap: "wrap" }}>
                        <Typography style="tag2" color={colors.grey[500]}>
                          Type:
                        </Typography>
                        <Typography style="tag2" color={colors.grey[900]}>
                          {organization.type || "---"}
                        </Typography>
                        <Typography style="tag2" color={colors.grey[500]}>
                          , Address:
                        </Typography>
                        <Typography style="tag2" color={colors.grey[900]}>
                          {organization.streetAddress || "---"}
                        </Typography>
                        {organization.state && (
                          <>
                            <Typography style="tag2" color={colors.grey[500]}>
                              , State:
                            </Typography>
                            <Typography style="tag2" color={colors.grey[900]}>
                              {organization.state}
                            </Typography>
                          </>
                        )}
                      </Box>
                    </Box>
                    {!readOnly && (
                      <Box
                        className="button-area"
                        sx={{ display: "flex", alignItems: "center", gap: 1 }}
                      >
                        {/* Victim Details button for organizations - same as for people */}
                        {relationType === "victim" &&
                          onAddVictimDetailsOrganization && (
                            <Box
                              sx={{
                                "& button:hover": {
                                  backgroundColor:
                                    getOrganizationVictimReportRelation(
                                      organization.id
                                    )
                                      ? `${colors.grey[200]} !important`
                                      : `${colors.blue[50]} !important`,
                                },
                                "& button:active": {
                                  backgroundColor:
                                    getOrganizationVictimReportRelation(
                                      organization.id
                                    )
                                      ? `${colors.grey[300]} !important`
                                      : `${colors.blue[100]} !important`,
                                },
                              }}
                            >
                              <Button
                                label={
                                  getOrganizationVictimReportRelation(
                                    organization.id
                                  )
                                    ? "View Victim Details"
                                    : "Add Victim Details"
                                }
                                size="small"
                                style={
                                  getOrganizationVictimReportRelation(
                                    organization.id
                                  )
                                    ? "ghost"
                                    : "outline"
                                }
                                color={
                                  getOrganizationVictimReportRelation(
                                    organization.id
                                  )
                                    ? "grey"
                                    : "blue"
                                }
                                onClick={(e) => {
                                  e.stopPropagation();
                                  onAddVictimDetailsOrganization(
                                    organization.id
                                  );
                                }}
                              />
                            </Box>
                          )}
                        <IconButton
                          size="small"
                          onClick={(e) =>
                            handleMenuClick(e, organization, "organization")
                          }
                          sx={{
                            color: colors.grey[600],
                            "&:hover": {
                              bgcolor: colors.grey[200],
                              color: colors.grey[800],
                            },
                          }}
                        >
                          <MoreVertIcon fontSize="small" />
                        </IconButton>
                      </Box>
                    )}
                  </Box>
                );
              })}
          </Box>
        )}

        {/* Add button below the cards */}
        {!readOnly && (
          <Box sx={{ mt: allEntities.length > 0 ? 3 : 0 }}>
            <Button
              data-testid={`add-${relationType}-button`}
              onClick={() => handleAddEntity()}
              style="ghost"
              color="blue"
              size="small"
              leftIcon={<AddIcon />}
              label={isIncident ? "Add Person" : `Add ${title}`}
            />
          </Box>
        )}

        {/* Quick Add Section */}
        {!readOnly && allQuickAddEntities.length > 0 && (
          <Box sx={{ mt: 2 }}>
            <Box
              sx={{
                display: "flex",
                justifyContent: "space-between",
                alignItems: "center",
                cursor: "pointer",
                py: 1,
              }}
              onClick={() => setQuickAddExpanded(!quickAddExpanded)}
            >
              <Typography style="caps2" color={colors.grey[500]}>
                QUICK ADD
              </Typography>
              <IconButton size="small">
                {quickAddExpanded ? <ExpandLessIcon /> : <ExpandMoreIcon />}
              </IconButton>
            </Box>

            <Collapse in={quickAddExpanded}>
              <Box
                sx={{
                  display: "grid",
                  gridTemplateColumns: "repeat(3, 1fr)",
                  gap: 1.5,
                  pt: 1,
                }}
              >
                {/* Quick Add People */}
                {availablePeopleForQuickAdd.map((person) => {
                  const quickAddVictimOffenderNumbers =
                    getQuickAddVictimOffenderNumbers(person.id);

                  return (
                    <Box
                      key={person.id}
                      sx={{
                        px: 1.5,
                        border: `1px solid ${colors.grey[200]}`,
                        borderRadius: "8px",
                        backgroundColor: "white",
                        display: "flex",
                        alignItems: "center",
                        cursor: "pointer",
                        height: "68px",
                        boxShadow: "0px 0px 16px 0px rgba(0, 0, 0, 0.04)",
                        "&:hover": {
                          backgroundColor: colors.grey[50],
                        },
                      }}
                      onClick={(e) =>
                        handleQuickAddClick(person.id, "person", e)
                      }
                    >
                      <Box
                        sx={{ mr: 2, display: "flex", alignItems: "center" }}
                      >
                        {loadingIds.has(person.id) ? (
                          <CircularProgress
                            size={20}
                            sx={{ color: colors.grey[900] }}
                          />
                        ) : (
                          <AddIcon
                            sx={{ color: colors.grey[900], fontSize: 20 }}
                          />
                        )}
                      </Box>
                      <Box sx={{ flex: 1, minWidth: 0 }}>
                        <Box
                          sx={{
                            mb: 0.5,
                            display: "flex",
                            alignItems: "center",
                            gap: 1,
                          }}
                        >
                          <Typography style="body3" color={colors.grey[900]}>
                            {person.name}
                          </Typography>
                          {quickAddVictimOffenderNumbers.map(
                            (number, index) => (
                              <Label
                                key={`${person.id}-${number}`}
                                label={number}
                                size="small"
                                color={getLabelColor(number)}
                                prominence={false}
                                pilled
                              />
                            )
                          )}
                        </Box>
                        <Box
                          sx={{
                            overflow: "hidden",
                            textOverflow: "ellipsis",
                            display: "-webkit-box",
                            WebkitLineClamp: 2,
                            WebkitBoxOrient: "vertical",
                          }}
                        >
                          <Typography style="tag2" color={colors.grey[500]}>
                            {person.dateOfBirth || "---"}, {person.sex || "---"}
                          </Typography>
                        </Box>
                      </Box>
                    </Box>
                  );
                })}

                {/* Quick Add Organizations (only for victims) */}
                {shouldShowOrganizations &&
                  availableOrganizationsForQuickAdd.map((organization) => (
                    <Box
                      key={organization.id}
                      sx={{
                        px: 1.5,
                        border: `1px solid ${colors.grey[200]}`,
                        borderRadius: "8px",
                        backgroundColor: "white",
                        display: "flex",
                        alignItems: "center",
                        cursor: "pointer",
                        height: "68px",
                        boxShadow: "0px 0px 16px 0px rgba(0, 0, 0, 0.04)",
                        "&:hover": {
                          backgroundColor: colors.grey[50],
                        },
                      }}
                      onClick={() =>
                        handleQuickAddClick(organization.id, "organization")
                      }
                    >
                      <Box
                        sx={{ mr: 2, display: "flex", alignItems: "center" }}
                      >
                        {loadingIds.has(organization.id) ? (
                          <CircularProgress
                            size={20}
                            sx={{ color: colors.grey[900] }}
                          />
                        ) : (
                          <AddIcon
                            sx={{ color: colors.grey[900], fontSize: 20 }}
                          />
                        )}
                      </Box>
                      <Box sx={{ flex: 1, minWidth: 0 }}>
                        <Box
                          sx={{
                            mb: 0.5,
                            display: "flex",
                            alignItems: "center",
                            gap: 1,
                          }}
                        >
                          <Typography style="body3" color={colors.grey[900]}>
                            {organization.name}
                          </Typography>
                        </Box>
                        <Box
                          sx={{
                            overflow: "hidden",
                            textOverflow: "ellipsis",
                            display: "-webkit-box",
                            WebkitLineClamp: 2,
                            WebkitBoxOrient: "vertical",
                          }}
                        >
                          <Typography style="tag2" color={colors.grey[500]}>
                            {organization.type || "---"},{" "}
                            {organization.streetAddress || "---"}
                          </Typography>
                        </Box>
                      </Box>
                    </Box>
                  ))}
              </Box>
            </Collapse>
          </Box>
        )}
      </Box>

      {/* Menu for entity actions */}
      <Menu
        anchorEl={menuAnchorEl}
        open={Boolean(menuAnchorEl)}
        onClose={handleMenuClose}
        anchorOrigin={{
          vertical: "bottom",
          horizontal: "right",
        }}
        transformOrigin={{
          vertical: "top",
          horizontal: "right",
        }}
        sx={{
          mt: 0.25,
          "& .MuiPaper-root": {
            borderRadius: "8px",
            minWidth: "200px",
          },
          "& .MuiMenuItem-root": {
            py: 1.5,
            display: "flex",
            alignItems: "center",
            gap: 1.5,
          },
        }}
      >
        <MenuItem onClick={handleOpenRecord}>
          <OpenInNewIcon fontSize="small" sx={{ color: colors.grey[500] }} />
          <Typography style="body2" color={colors.grey[500]}>
            Open record in new tab
          </Typography>
        </MenuItem>
        <MenuItem onClick={handleRemoveClick}>
          <DeleteIcon fontSize="small" sx={{ color: colors.grey[500] }} />
          <Typography style="body2" color={colors.grey[500]}>
            Remove from offense
          </Typography>
        </MenuItem>
      </Menu>

      {/* Remove confirmation modal */}
      <Dialog
        open={removeModalOpen}
        onClose={handleRemoveCancel}
        maxWidth="sm"
        fullWidth
        PaperProps={{
          sx: {
            borderRadius: "12px",
            p: 1,
          },
        }}
      >
        <DialogContent sx={{ p: 3 }}>
          <Box sx={{ mb: 2 }}>
            <Typography style="h2" color={colors.grey[900]}>
              Delete{" "}
              {selectedPersonForMenu?.name ||
                selectedOrganizationForMenu?.name ||
                "Entity"}
            </Typography>
          </Box>
          <Box sx={{ mb: 3 }}>
            <Typography style="body3" color={colors.grey[700]}>
              {selectedPersonForMenu?.name ||
                selectedOrganizationForMenu?.name ||
                "Entity"}{" "}
              will be deleted from the offense
            </Typography>
          </Box>

          <Box sx={{ display: "flex", alignItems: "center", gap: 2 }}>
            <Checkbox
              checked={removeFromReport}
              onChange={(e) => setRemoveFromReport(e.target.checked)}
              label="Also delete from the report"
              size="small"
            />
          </Box>
        </DialogContent>

        <DialogActions sx={{ p: 3, pt: 0 }}>
          <Button
            label="Cancel"
            color="grey"
            prominence={false}
            onClick={handleRemoveCancel}
          />
          <Button
            label="Delete"
            color="blue"
            prominence={true}
            onClick={handleRemoveConfirm}
          />
        </DialogActions>
      </Dialog>

      {/* Add Other People Menu */}
      <Menu
        anchorEl={addOtherMenuAnchor}
        open={Boolean(addOtherMenuAnchor)}
        onClose={handleAddOtherMenuClose}
        anchorOrigin={{
          vertical: "bottom",
          horizontal: "left",
        }}
        transformOrigin={{
          vertical: "top",
          horizontal: "left",
        }}
        sx={{
          mt: 0.25,
          "& .MuiPaper-root": {
            borderRadius: "8px",
            minWidth: "200px",
          },
          "& .MuiMenuItem-root": {
            py: 1.5,
          },
        }}
      >
        <MenuItem onClick={() => handleAddOtherType("witness")}>
          <Box sx={{ display: "flex", alignItems: "center", gap: 1 }}>
            <Typography style="body2" color={colors.grey[900]}>
              Add Witness
            </Typography>
          </Box>
        </MenuItem>
        <MenuItem onClick={() => handleAddOtherType("suspect")}>
          <Box sx={{ display: "flex", alignItems: "center", gap: 1 }}>
            <Typography style="body2" color={colors.grey[900]}>
              Add Suspect
            </Typography>
          </Box>
        </MenuItem>
        <MenuItem onClick={() => handleAddOtherType("involved_party")}>
          <Box sx={{ display: "flex", alignItems: "center", gap: 1 }}>
            <Typography style="body2" color={colors.grey[900]}>
              Add Involved Party
            </Typography>
          </Box>
        </MenuItem>
      </Menu>

      {/* Add Victim Type Menu */}
      <Menu
        anchorEl={addVictimMenuAnchor}
        open={Boolean(addVictimMenuAnchor)}
        onClose={handleAddVictimMenuClose}
        anchorOrigin={{
          vertical: "bottom",
          horizontal: "left",
        }}
        transformOrigin={{
          vertical: "top",
          horizontal: "left",
        }}
        sx={{
          mt: 0.25,
          "& .MuiPaper-root": {
            borderRadius: "8px",
            minWidth: "200px",
          },
          "& .MuiMenuItem-root": {
            py: 1.5,
          },
        }}
      >
        <MenuItem onClick={() => handleAddVictimType("person")}>
          <Box sx={{ display: "flex", alignItems: "center", gap: 1 }}>
            <Typography style="body2" color={colors.grey[900]}>
              Add Person
            </Typography>
          </Box>
        </MenuItem>
        <MenuItem onClick={() => handleAddVictimType("organization")}>
          <Box sx={{ display: "flex", alignItems: "center", gap: 1 }}>
            <Typography style="body2" color={colors.grey[900]}>
              Add Organization
            </Typography>
          </Box>
        </MenuItem>
      </Menu>

      {/* Quick Add Menu for "other" people */}
      <Menu
        anchorEl={quickAddMenuAnchor}
        open={Boolean(quickAddMenuAnchor)}
        onClose={handleQuickAddMenuClose}
        anchorOrigin={{
          vertical: "bottom",
          horizontal: "left",
        }}
        transformOrigin={{
          vertical: "top",
          horizontal: "left",
        }}
        sx={{
          mt: 0.25,
          "& .MuiPaper-root": {
            borderRadius: "8px",
            minWidth: "200px",
          },
          "& .MuiMenuItem-root": {
            py: 1.5,
            display: "flex",
            alignItems: "center",
            gap: 1.5,
          },
        }}
      >
        <MenuItem onClick={() => handleQuickAddOtherType("witness")}>
          <Box sx={{ display: "flex", alignItems: "center", gap: 1 }}>
            <Typography style="body2" color={colors.grey[900]}>
              Add as Witness
            </Typography>
          </Box>
        </MenuItem>
        <MenuItem onClick={() => handleQuickAddOtherType("suspect")}>
          <Box sx={{ display: "flex", alignItems: "center", gap: 1 }}>
            <Typography style="body2" color={colors.grey[900]}>
              Add as Suspect
            </Typography>
          </Box>
        </MenuItem>
        <MenuItem onClick={() => handleQuickAddOtherType("involved_party")}>
          <Box sx={{ display: "flex", alignItems: "center", gap: 1 }}>
            <Typography style="body2" color={colors.grey[900]}>
              Add as Involved Party
            </Typography>
          </Box>
        </MenuItem>
      </Menu>
    </>
  );
};

export default PersonList;
