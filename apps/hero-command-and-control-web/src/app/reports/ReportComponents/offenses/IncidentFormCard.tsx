import { Checkbox } from "@/design-system/components/Checkbox";
import { Dropdown } from "@/design-system/components/Dropdown";
import { Typography } from "@/design-system/components/Typography";
import { colors } from "@/design-system/tokens";
import DeleteIcon from "@mui/icons-material/Delete";
import ExpandLessIcon from "@mui/icons-material/ExpandLess";
import ExpandMoreIcon from "@mui/icons-material/ExpandMore";
import { Box, Collapse, Divider, IconButton } from "@mui/material";
import React, { useEffect, useRef, useState } from "react";
import { useBatchGetLatestEntities } from "../../../apis/services/workflow/entity/hooks";
import { INCIDENT_TYPE_OPTIONS } from "./incidentTypes";

// Incident Involved options
const INCIDENT_INVOLVED_OPTIONS = [
  "Alcohol",
  "Drugs",
  "Physical Injury",
  "Mental Health",
  "Medical Transport",
  "Weapons",
];

// Import the extracted components
import OrganizationList, { OrganizationData } from "./OrganizationList";
import PersonList, { PersonData } from "./PersonList";
import PropertyList, { PropertyData } from "./PropertyList";
import VehicleList, { VehicleData } from "./VehicleList";

interface IncidentData {
  code: string;
  literal: string;
  citation: string;
  statute: string;
  level: string;
}

interface IncidentFormData {
  incidentType: string;
  isIncident: boolean; // Flag to distinguish from offenses
  incidentInvolved: string[]; // Multi-select for incident involved options
}

interface IncidentFormCardProps {
  incident: IncidentData;
  incidentId: string;
  onSave: (incidentId: string, data: IncidentFormData) => void;
  onDelete: (incidentId: string) => void;
  onSaveStatusChange?: (status: {
    isSaving: boolean;
    hasUnsavedChanges: boolean;
    source: string;
  }) => void;
  readOnly?: boolean;
  initialData?: Partial<IncidentFormData>;
  // Entity management props
  people?: PersonData[];
  vehicles?: VehicleData[];
  properties?: PropertyData[];
  organizations?: OrganizationData[];
  relations?: any[];
  reportId?: string;
  associatedCases?: any[];
  onAddPerson?: (
    relationType:
      | "victim"
      | "offender"
      | "witness"
      | "suspect"
      | "involved_party"
  ) => void;
  onAddVehicle?: () => void;
  onAddProperty?: () => void;
  onAddOrganization?: () => void;
  onRemovePersonFromOffense?: (personId: string, incidentId: string) => void;
  onRemoveVehicleFromOffense?: (vehicleId: string, incidentId: string) => void;
  onRemovePropertyFromOffense?: (
    propertyId: string,
    incidentId: string
  ) => void;
  onRemoveOrganizationFromOffense?: (
    organizationId: string,
    incidentId: string
  ) => void;
  onRemovePersonFromReport?: (personId: string, entityType: "person") => void;
  onRemoveVehicleFromReport?: (
    vehicleId: string,
    entityType: "vehicle"
  ) => void;
  onRemovePropertyFromReport?: (
    propertyId: string,
    entityType: "property"
  ) => void;
  onRemoveOrganizationFromReport?: (
    organizationId: string,
    entityType: "organization"
  ) => void;
  onOpenSidePanel?: (
    panelType: "PERSON" | "VEHICLE" | "PROPERTY" | "ORGANIZATION"
  ) => void;
  onQuickAddPersonToOffense?: (
    personId: string,
    incidentId: string,
    relationType:
      | "victim"
      | "offender"
      | "witness"
      | "suspect"
      | "involved_party"
  ) => void;
  onQuickAddVehicleToOffense?: (vehicleId: string, incidentId: string) => void;
  onQuickAddPropertyToOffense?: (
    propertyId: string,
    incidentId: string
  ) => void;
  onQuickAddOrganizationToOffense?: (
    organizationId: string,
    incidentId: string
  ) => void;
  onEntityEdit?: (
    entityId: string,
    entityType: "person" | "vehicle" | "property" | "organization"
  ) => void;
  onAddVictimDetails?: (personId: string) => void;
}

// Helper function to convert text to title case
const toTitleCase = (str: string): string => {
  return str
    .toLowerCase()
    .split(" ")
    .map((word) => word.charAt(0).toUpperCase() + word.slice(1))
    .join(" ");
};

const IncidentFormCard: React.FC<IncidentFormCardProps> = ({
  incident,
  incidentId,
  onSave,
  onDelete,
  onSaveStatusChange,
  readOnly = false,
  initialData,
  people,
  vehicles,
  properties,
  organizations,
  relations,
  reportId,
  associatedCases,
  onAddPerson,
  onAddVehicle,
  onAddProperty,
  onAddOrganization,
  onRemovePersonFromOffense,
  onRemoveVehicleFromOffense,
  onRemovePropertyFromOffense,
  onRemoveOrganizationFromOffense,
  onRemovePersonFromReport,
  onRemoveVehicleFromReport,
  onRemovePropertyFromReport,
  onRemoveOrganizationFromReport,
  onOpenSidePanel,
  onQuickAddPersonToOffense,
  onQuickAddVehicleToOffense,
  onQuickAddPropertyToOffense,
  onQuickAddOrganizationToOffense,
  onEntityEdit,
  onAddVictimDetails,
}) => {
  const [isExpanded, setIsExpanded] = useState(true);
  const [hasUnsavedChanges, setHasUnsavedChanges] = useState(false);
  const [isSaving, setIsSaving] = useState(false);

  // State for case entities (for quick add)
  const [caseEntityIds, setCaseEntityIds] = useState<string[]>([]);
  const [caseEntities, setCaseEntities] = useState<{
    people: PersonData[];
    vehicles: VehicleData[];
    properties: PropertyData[];
    organizations: OrganizationData[];
  }>({
    people: [],
    vehicles: [],
    properties: [],
    organizations: [],
  });

  const [formData, setFormData] = useState<IncidentFormData>({
    incidentType: "",
    isIncident: true,
    incidentInvolved: [],
    ...initialData,
  });

  const currentFormDataRef = useRef(formData);

  // Update refs when state changes
  useEffect(() => {
    currentFormDataRef.current = formData;
  }, [formData]);

  // Extract entity IDs from associated cases
  useEffect(() => {
    if (associatedCases && associatedCases.length > 0) {
      const firstCase = associatedCases[0];
      if (firstCase?.entityRefs) {
        const entityIds = firstCase.entityRefs.map((ref: any) => ref.id);
        setCaseEntityIds(entityIds);
      } else {
        setCaseEntityIds([]);
      }
    } else {
      setCaseEntityIds([]);
    }
  }, [associatedCases]);

  // Fetch case entities using batch get
  const { data: batchCaseEntitiesData } = useBatchGetLatestEntities(
    caseEntityIds,
    {
      enabled: caseEntityIds.length > 0,
      staleTime: 30 * 1000, // 30 seconds instead of 5 minutes for more responsive updates
      refetchOnWindowFocus: true,
      refetchOnMount: true,
      queryKey: ["caseEntities", incidentId, caseEntityIds],
      placeholderData: (previousData) => previousData,
    }
  );

  // Process batch fetched case entities
  useEffect(() => {
    if (batchCaseEntitiesData?.entities) {
      const fetchedEntities = batchCaseEntitiesData.entities;
      const peopleEntities: any[] = [];
      const vehicleEntities: any[] = [];
      const propertyEntities: any[] = [];
      const organizationEntities: any[] = [];

      fetchedEntities.forEach((entity) => {
        // @ts-expect-error TODO: Fix type issue
        switch (entity.entityType as string) {
          case "ENTITY_TYPE_PERSON":
            peopleEntities.push(entity);
            break;
          case "ENTITY_TYPE_VEHICLE":
            vehicleEntities.push(entity);
            break;
          case "ENTITY_TYPE_PROPERTY":
            propertyEntities.push(entity);
            break;
          case "ENTITY_TYPE_ORGANIZATION":
            organizationEntities.push(entity);
            break;
        }
      });

      // Convert entities to display format
      const convertedPeople = peopleEntities.map((entity) => {
        try {
          const data =
            typeof entity.data === "string"
              ? JSON.parse(entity.data)
              : entity.data;
          const classificationSection = data?.classificationSection;
          const descriptorsSection = data?.descriptorsSection;

          const firstName = classificationSection?.firstName || "";
          const lastName = classificationSection?.lastName || "";
          const middleName = classificationSection?.middleName || "";
          const name =
            `${firstName} ${middleName} ${lastName}`
              .trim()
              .replace(/\s+/g, " ") || "---";

          return {
            id: entity.id,
            name,
            sex: classificationSection?.sex || "---",
            height: descriptorsSection?.height || "---",
            hair: descriptorsSection?.hairColor || "---",
            weight: descriptorsSection?.weight || "---",
            eye: descriptorsSection?.eyeColor || "---",
            dateOfBirth: classificationSection?.dateOfBirth || "---",
          };
        } catch (error) {
          console.error("Error parsing person entity data:", error);
          return {
            id: entity.id,
            name: "---",
            sex: "---",
            height: "---",
            hair: "---",
            weight: "---",
            eye: "---",
            dateOfBirth: "---",
          };
        }
      });

      const convertedVehicles = vehicleEntities.map((entity) => {
        try {
          const data =
            typeof entity.data === "string"
              ? JSON.parse(entity.data)
              : entity.data;
          const vehicleSection = data?.vehicleInformationSection;

          return {
            id: entity.id,
            vIN: vehicleSection?.vIN || "---",
            year: vehicleSection?.year || "---",
            make: vehicleSection?.make || "---",
            model: vehicleSection?.model || "---",
            color: vehicleSection?.color || "---",
            ownerIfApplicable: vehicleSection?.ownerIfApplicable || "---",
          };
        } catch (error) {
          console.error("Error parsing vehicle entity data:", error);
          return {
            id: entity.id,
            vIN: "---",
            year: "---",
            make: "---",
            model: "---",
            color: "---",
            ownerIfApplicable: "---",
          };
        }
      });

      const convertedProperties = propertyEntities.map((entity) => {
        try {
          const data =
            typeof entity.data === "string"
              ? JSON.parse(entity.data)
              : entity.data;
          const propertySection = data?.propertyInformationSection;

          return {
            id: entity.id,
            propertyType: propertySection?.propertyType || "---",
            serialNumber: propertySection?.serialNumber || "---",
            category: propertySection?.category || "---",
            collectedValue: propertySection?.collectedValue || "---",
            makeModelBrand: propertySection?.makeModelBrand || "---",
            description: propertySection?.description || "---",
          };
        } catch (error) {
          console.error("Error parsing property entity data:", error);
          return {
            id: entity.id,
            propertyType: "---",
            serialNumber: "---",
            category: "---",
            collectedValue: "---",
            makeModelBrand: "---",
            description: "---",
          };
        }
      });

      const convertedOrganizations = organizationEntities.map((entity) => {
        try {
          const data =
            typeof entity.data === "string"
              ? JSON.parse(entity.data)
              : entity.data;
          const organizationSection = data?.organizationInformationSection;

          return {
            id: entity.id,
            name: organizationSection?.name || "---",
            type: organizationSection?.type || "---",
            streetAddress: organizationSection?.streetAddress || "---",
            state: organizationSection?.state || "---",
            zIP: organizationSection?.zIP || "---",
          };
        } catch (error) {
          console.error("Error parsing organization entity data:", error);
          return {
            id: entity.id,
            name: "---",
            type: "---",
            streetAddress: "---",
            state: "---",
            zIP: "---",
          };
        }
      });

      setCaseEntities({
        people: convertedPeople,
        vehicles: convertedVehicles,
        properties: convertedProperties,
        organizations: convertedOrganizations,
      });
    } else {
      setCaseEntities({
        people: [],
        vehicles: [],
        properties: [],
        organizations: [],
      });
    }
  }, [batchCaseEntitiesData]);

  // Use a ref to track previous status like IncidentDetailsCard
  const lastStatusRef = useRef<{
    isSaving: boolean;
    hasUnsavedChanges: boolean;
  } | null>(null);

  // Notify parent on save/dirty changes like IncidentDetailsCard
  useEffect(() => {
    if (!onSaveStatusChange) return;

    const currentStatus = { isSaving, hasUnsavedChanges };
    const lastStatus = lastStatusRef.current;
    const changed =
      !lastStatus ||
      lastStatus.isSaving !== currentStatus.isSaving ||
      lastStatus.hasUnsavedChanges !== currentStatus.hasUnsavedChanges;

    if (changed) {
      lastStatusRef.current = { ...currentStatus };
      onSaveStatusChange({
        ...currentStatus,
        source: `incident_${incidentId}`,
      });
    }
  }, [isSaving, hasUnsavedChanges, onSaveStatusChange, incidentId]);

  // Save incident immediately when created
  useEffect(() => {
    if (!initialData) {
      // This is a new incident, save immediately
      saveIncidentData();
    }
  }, []); // Only run on mount

  const saveIncidentData = () => {
    if (isSaving || readOnly) return;

    setIsSaving(true);
    setHasUnsavedChanges(false);

    const currentData = currentFormDataRef.current;
    onSave(incidentId, currentData);

    // Simulate save completion
    setTimeout(() => {
      setIsSaving(false);
    }, 300);
  };

  const handleBlur = () => {
    if (hasUnsavedChanges && !readOnly) {
      saveIncidentData();
    }
  };

  const handleDropdownChange = (field: string) => (value: string | null) => {
    if (value !== null) {
      setFormData((prev) => {
        const updated = { ...prev, [field]: value };
        currentFormDataRef.current = updated;
        return updated;
      });
      setHasUnsavedChanges(true);
      // Save immediately on dropdown selection
      setTimeout(() => saveIncidentData(), 0);
    }
  };

  const handleIncidentInvolvedChange =
    (option: string) => (event: React.ChangeEvent<HTMLInputElement>) => {
      setFormData((prev) => {
        const currentInvolved = prev.incidentInvolved || [];
        let updated: IncidentFormData;

        if (event.target.checked) {
          // Add option if checked
          updated = {
            ...prev,
            incidentInvolved: [...currentInvolved, option],
          };
        } else {
          // Remove option if unchecked
          updated = {
            ...prev,
            incidentInvolved: currentInvolved.filter((item) => item !== option),
          };
        }

        currentFormDataRef.current = updated;
        return updated;
      });
      setHasUnsavedChanges(true);
      // Save immediately on checkbox change
      setTimeout(() => saveIncidentData(), 0);
    };

  // Helper function to get people related to this incident (simplified - no roles)
  const getRelatedPeople = (): PersonData[] => {
    if (!relations || !people) return [];

    // Use new incident-person relation type
    const targetRelationType = "RELATION_TYPE_INCIDENT_PERSON";

    const relatedPersonIds = relations
      .filter((rel: any) => {
        if (rel.relationType !== targetRelationType) return false;

        const isThisIncident =
          (rel.objectA?.objectType === "offense" &&
            rel.objectA?.reportScopedId === incidentId) ||
          (rel.objectB?.objectType === "offense" &&
            rel.objectB?.reportScopedId === incidentId);

        return isThisIncident;
      })
      .map((rel: any) => {
        if (rel.objectA?.objectType === "entity") return rel.objectA.globalId;
        if (rel.objectB?.objectType === "entity") return rel.objectB.globalId;
        return null;
      })
      .filter(Boolean);

    return people.filter((person) => relatedPersonIds.includes(person.id));
  };

  // Helper function to get vehicles related to this incident
  const getRelatedVehicles = (): VehicleData[] => {
    if (!vehicles || !relations) return [];

    const relationTypeKey = "RELATION_TYPE_INCIDENT_VEHICLE";

    // Find relations where this incident is involved and relation type matches
    const relevantRelations = relations.filter((relation: any) => {
      const isIncidentInvolved =
        (relation.objectA?.objectType === "offense" &&
          relation.objectA?.reportScopedId === incidentId) ||
        (relation.objectB?.objectType === "offense" &&
          relation.objectB?.reportScopedId === incidentId);

      return isIncidentInvolved && relation.relationType === relationTypeKey;
    });

    // Extract vehicle IDs from relations
    const vehicleIds = relevantRelations
      .map((relation: any) => {
        // If object A is the incident, then object B is the vehicle
        if (relation.objectA?.objectType === "offense") {
          return relation.objectB?.globalId;
        }
        // If object B is the incident, then object A is the vehicle
        return relation.objectA?.globalId;
      })
      .filter(Boolean);

    // Return vehicles that match these IDs
    return vehicles.filter((vehicle) => vehicleIds.includes(vehicle.id));
  };

  // Helper function to get properties related to this incident
  const getRelatedProperties = (): PropertyData[] => {
    if (!properties || !relations) return [];

    const relationTypeKey = "RELATION_TYPE_INCIDENT_PROPERTY";

    // Find relations where this incident is involved and relation type matches
    const relevantRelations = relations.filter((relation: any) => {
      const isIncidentInvolved =
        (relation.objectA?.objectType === "offense" &&
          relation.objectA?.reportScopedId === incidentId) ||
        (relation.objectB?.objectType === "offense" &&
          relation.objectB?.reportScopedId === incidentId);

      return isIncidentInvolved && relation.relationType === relationTypeKey;
    });

    // Extract property IDs from relations
    const propertyIds = relevantRelations
      .map((relation: any) => {
        // If object A is the incident, then object B is the property
        if (relation.objectA?.objectType === "offense") {
          return relation.objectB?.globalId;
        }
        // If object B is the incident, then object A is the property
        return relation.objectA?.globalId;
      })
      .filter(Boolean);

    // Return properties that match these IDs
    return properties.filter((property) => propertyIds.includes(property.id));
  };

  // Helper function to get organizations related to this incident
  const getRelatedOrganizations = (): OrganizationData[] => {
    if (!organizations || !relations) return [];

    const relationTypeKey = "RELATION_TYPE_INCIDENT_ORGANIZATION";

    // Find relations where this incident is involved and relation type matches
    const relevantRelations = relations.filter((relation: any) => {
      const isIncidentInvolved =
        (relation.objectA?.objectType === "offense" &&
          relation.objectA?.reportScopedId === incidentId) ||
        (relation.objectB?.objectType === "offense" &&
          relation.objectB?.reportScopedId === incidentId);

      return isIncidentInvolved && relation.relationType === relationTypeKey;
    });

    // Extract organization IDs from relations
    const organizationIds = relevantRelations
      .map((relation: any) => {
        // If object A is the incident, then object B is the organization
        if (relation.objectA?.objectType === "offense") {
          return relation.objectB?.globalId;
        }
        // If object B is the incident, then object A is the organization
        return relation.objectA?.globalId;
      })
      .filter(Boolean);

    // Return organizations that match these IDs
    return organizations.filter((organization) =>
      organizationIds.includes(organization.id)
    );
  };

  // Get all people related to this incident (simplified)
  const uniquePeople = getRelatedPeople();

  const relatedVehicles = getRelatedVehicles();
  const relatedProperties = getRelatedProperties();
  const relatedOrganizations = getRelatedOrganizations();

  // Handler for adding entities to this incident (simplified for incidents)
  const handleAddPerson = () => {
    if (onAddPerson) {
      // For incidents, we just use "involved_party" as a generic relation type
      onAddPerson("involved_party");
    }
  };

  const handleAddVehicle = () => {
    if (onAddVehicle) {
      onAddVehicle();
    }
  };

  const handleAddProperty = () => {
    if (onAddProperty) {
      onAddProperty();
    }
  };

  const handleAddOrganization = () => {
    if (onAddOrganization) {
      onAddOrganization();
    }
  };

  // Quick add handlers - these will directly add entities from the case to the incident
  const handleQuickAddPerson = (personId: string) => {
    if (onQuickAddPersonToOffense) {
      // For incidents, we use "involved_party" as a generic relation type
      onQuickAddPersonToOffense(personId, incidentId, "involved_party");
    }
  };

  const handleQuickAddVehicle = (vehicleId: string) => {
    if (onQuickAddVehicleToOffense) {
      onQuickAddVehicleToOffense(vehicleId, incidentId);
    }
  };

  const handleQuickAddProperty = (propertyId: string) => {
    if (onQuickAddPropertyToOffense) {
      onQuickAddPropertyToOffense(propertyId, incidentId);
    }
  };

  const handleQuickAddOrganization = (organizationId: string) => {
    if (onQuickAddOrganizationToOffense) {
      onQuickAddOrganizationToOffense(organizationId, incidentId);
    }
  };

  // Helper function to get available entities for quick add (entities in case but not in this incident)
  const getAvailableEntitiesForQuickAdd = (
    entityType: "person" | "vehicle" | "property" | "organization"
  ): any[] => {
    let caseEntitiesForType: any[] = [];
    let currentRelatedEntities: any[] = [];

    if (entityType === "person") {
      caseEntitiesForType = caseEntities.people;
      currentRelatedEntities = uniquePeople;
    } else if (entityType === "vehicle") {
      caseEntitiesForType = caseEntities.vehicles;
      currentRelatedEntities = getRelatedVehicles();
    } else if (entityType === "property") {
      caseEntitiesForType = caseEntities.properties;
      currentRelatedEntities = getRelatedProperties();
    } else if (entityType === "organization") {
      caseEntitiesForType = caseEntities.organizations;
      currentRelatedEntities = getRelatedOrganizations();
    }

    // Return case entities that are not already related to this incident
    return caseEntitiesForType.filter(
      (entity) =>
        !currentRelatedEntities.some((related) => related.id === entity.id)
    );
  };

  // Get the incident type label for display
  const getIncidentTypeLabel = () => {
    const typeOption = INCIDENT_TYPE_OPTIONS.find(
      (option) => option.value === formData.incidentType
    );
    return typeOption ? typeOption.label : "Unknown Incident";
  };

  return (
    <Box
      sx={{
        display: "flex",
        width: "100%",
        flexDirection: "column",
        alignItems: "flex-start",
        borderRadius: "12px",
        border: `1px solid ${colors.grey[200]}`,
        background: "#FFF",
        overflow: "hidden",
        boxShadow: "0px 0px 16px 0px rgba(0, 0, 0, 0.04)",
        mb: 3,
      }}
    >
      {/* Header */}
      <Box
        sx={{
          display: "flex",
          width: "100%",
          justifyContent: "space-between",
          alignItems: "center",
          padding: "16px 24px",
          borderBottom: `1px solid ${colors.grey[200]}`,
          backgroundColor: colors.grey[50],
        }}
      >
        <Box sx={{ display: "flex", alignItems: "center", gap: 2 }}>
          <Typography style="h3" color={colors.grey[900]}>
            {getIncidentTypeLabel()}
          </Typography>
        </Box>

        <Box sx={{ display: "flex", alignItems: "center", gap: 1 }}>
          {!readOnly && (
            <IconButton
              onClick={() => onDelete(incidentId)}
              size="small"
              sx={{
                color: colors.grey[600],
                "&:hover": {
                  bgcolor: colors.grey[100],
                },
              }}
            >
              <DeleteIcon />
            </IconButton>
          )}
          <IconButton
            onClick={() => setIsExpanded(!isExpanded)}
            size="small"
            sx={{
              color: colors.grey[600],
              "&:hover": {
                bgcolor: colors.grey[100],
              },
            }}
          >
            {isExpanded ? <ExpandLessIcon /> : <ExpandMoreIcon />}
          </IconButton>
        </Box>
      </Box>

      {/* Form Content */}
      <Collapse in={isExpanded} sx={{ width: "100%" }}>
        <Box sx={{ padding: "24px" }}>
          {/* Incident Type Section */}
          <Box sx={{ mb: 4 }}>
            <Box sx={{ mb: 3 }}>
              <Typography style="caps1" color={colors.grey[700]}>
                Incident Details
              </Typography>
            </Box>

            <Box sx={{ display: "flex", gap: 2, mb: 3 }}>
              <Box sx={{ width: "50%" }}>
                <Dropdown
                  title="Incident Type"
                  placeholder="Select incident type"
                  options={INCIDENT_TYPE_OPTIONS}
                  value={formData.incidentType}
                  onChange={handleDropdownChange("incidentType")}
                  readOnly={readOnly}
                  enableSearch
                />
              </Box>
            </Box>

            {/* Incident Involved Section */}
            <Box sx={{ mb: 3 }}>
              <Box sx={{ mb: 1.5 }}>
                <Typography style="body4" color={colors.grey[500]}>
                  Incident Involved
                </Typography>
              </Box>
              <Box sx={{ display: "flex", flexWrap: "wrap", gap: 2 }}>
                {INCIDENT_INVOLVED_OPTIONS.map((option) => (
                  <Checkbox
                    key={option}
                    label={option}
                    checked={
                      formData.incidentInvolved?.includes(option) || false
                    }
                    onChange={handleIncidentInvolvedChange(option)}
                    disabled={readOnly}
                    size="medium"
                  />
                ))}
              </Box>
            </Box>
          </Box>

          <Divider sx={{ mx: -3 }} />

          {/* People Section */}
          {people && relations && (
            <Box>
              <PersonList
                people={uniquePeople}
                title="People"
                relationType="other"
                offenseId={incidentId}
                readOnly={readOnly}
                relations={relations}
                reportId={reportId}
                isIncident={true}
                availablePeopleForQuickAdd={getAvailableEntitiesForQuickAdd(
                  "person"
                )}
                onAddPerson={() => handleAddPerson()}
                onRemovePersonFromOffense={onRemovePersonFromOffense}
                onRemovePersonFromReport={onRemovePersonFromReport}
                onQuickAddPerson={(personId: string) =>
                  handleQuickAddPerson(personId)
                }
                onEntityEdit={onEntityEdit}
              />
            </Box>
          )}

          {/* Vehicles Section */}
          {vehicles && relations && (
            <>
              <Divider sx={{ mx: -3 }} />
              <VehicleList
                vehicles={relatedVehicles}
                title="Vehicles"
                offenseId={incidentId}
                readOnly={readOnly}
                availableVehiclesForQuickAdd={getAvailableEntitiesForQuickAdd(
                  "vehicle"
                )}
                onAddVehicle={handleAddVehicle}
                onRemoveVehicleFromOffense={onRemoveVehicleFromOffense}
                onRemoveVehicleFromReport={onRemoveVehicleFromReport}
                onQuickAddVehicle={handleQuickAddVehicle}
                onEntityEdit={onEntityEdit}
              />
            </>
          )}

          {/* Properties Section */}
          {properties && relations && (
            <>
              <Divider sx={{ mx: -3 }} />
              <PropertyList
                properties={relatedProperties}
                title="Properties"
                offenseId={incidentId}
                readOnly={readOnly}
                availablePropertiesForQuickAdd={getAvailableEntitiesForQuickAdd(
                  "property"
                )}
                onAddProperty={handleAddProperty}
                onRemovePropertyFromOffense={onRemovePropertyFromOffense}
                onRemovePropertyFromReport={onRemovePropertyFromReport}
                onQuickAddProperty={handleQuickAddProperty}
                onEntityEdit={onEntityEdit}
              />
            </>
          )}

          {/* Organizations Section */}
          {organizations && relations && (
            <>
              <Divider sx={{ mx: -3 }} />
              <OrganizationList
                organizations={relatedOrganizations}
                title="Organizations"
                offenseId={incidentId}
                readOnly={readOnly}
                availableOrganizationsForQuickAdd={getAvailableEntitiesForQuickAdd(
                  "organization"
                )}
                onAddOrganization={handleAddOrganization}
                onRemoveOrganizationFromOffense={
                  onRemoveOrganizationFromOffense
                }
                onRemoveOrganizationFromReport={onRemoveOrganizationFromReport}
                onQuickAddOrganization={handleQuickAddOrganization}
                onEntityEdit={onEntityEdit}
              />
            </>
          )}
        </Box>
      </Collapse>
    </Box>
  );
};

export default IncidentFormCard;
