"use client";

import React from "react";
import { Box } from "@mui/material";
import { Typography } from "@/design-system/components/Typography";
import { Report } from "proto/hero/reports/v2/reports_pb";
import { useListOrdersForSituation } from "@/app/apis/services/workflow/orders/hooks";
import { ListOrdersForSituationRequest } from "proto/hero/orders/v2/orders_pb";
import { colors } from "@/design-system/tokens/colors";

interface NotesTabProps {
  report: Report | undefined;
  currentUserAssetId?: string;
}

export default function NotesTab({ report, currentUserAssetId }: NotesTabProps) {
  // Fetch orders for this situation
  const { data: ordersData } = useListOrdersForSituation({
    situationId: report?.situationId || "",
    pageSize: 50,
    pageToken: "",
  } as ListOrdersForSituationRequest, {
    enabled: !!report?.situationId,
  });

  // Find the current user's assist member order
  const userOrder = React.useMemo(() => {
    if (!ordersData?.orders || !currentUserAssetId) return null;

    return ordersData.orders.find(
      (order) =>
        order.assetId === currentUserAssetId &&
        // @ts-expect-error order.type is not properly typed in the proto definition yet
        order.type === "ORDER_TYPE_ASSIST_MEMBER"
    );
  }, [ordersData, currentUserAssetId]);

  const notes = userOrder?.notes || "";

  if (!notes.trim()) {
    return (
      <Box sx={{ p: 2, textAlign: "center" }}>
        <Typography style="body4" color={colors.grey[500]}>
          No notes available
        </Typography>
      </Box>
    );
  }

  return (
    <Box sx={{ p: 2 }}>
      <Typography 
        style="body3"
        color={colors.grey[900]}
        lineHeight="20px"
      >
        {notes}
      </Typography>
    </Box>
  );
} 