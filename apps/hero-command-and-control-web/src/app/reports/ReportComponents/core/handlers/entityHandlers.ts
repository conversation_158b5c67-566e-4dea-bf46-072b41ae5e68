import { PanelType } from "../types";

import { PANEL_CLOSE_TIMEOUT_MS } from "../constants";

import { create } from "@bufbuild/protobuf";
import { AddEntityRefToCaseRequestSchema } from "proto/hero/cases/v1/cases_pb";
import {
  GetLatestEntityRequest,
  Reference,
} from "proto/hero/entity/v1/entity_pb";
import { getLatestEntity as getLatestEntityEndpoint } from "../../../../apis/services/workflow/entity/endpoints";
import { updateEntityListSection } from "../utils/sectionUtils";

interface EntityHandlersProps {
  // State
  people: any[];
  vehicles: any[];
  properties: any[];
  organizations: any[];
  setPeople: (people: any[]) => void;
  setVehicles: (vehicles: any[]) => void;
  setProperties: (properties: any[]) => void;
  setOrganizations: (organizations: any[]) => void;
  setMediaFiles: (files: any[]) => void;
  setSidePanelOpen: (open: boolean) => void;
  setActivePanelType: (type: PanelType | null) => void;
  setEditingEntityId: (id: string | null) => void;
  setEditingEntityFormData: (data: any) => void;
  setSelectedRowId: (id: string | null) => void;
  setActiveOffenseRelation: (relation: any) => void;
  setNotification: (notification: any) => void;
  activeOffenseRelation: any;
  activeVehicleOffenseContext: any;
  activePropertyOffenseContext: any;
  activeOrganizationOffenseContext: any;
  activeVictimOrganizationOffenseContext: any;
  setActiveVehicleOffenseContext: (context: any) => void;
  setActivePropertyOffenseContext: (context: any) => void;
  setActiveOrganizationOffenseContext: (context: any) => void;
  setActiveVictimOrganizationOffenseContext: (context: any) => void;

  // IDs and refs
  reportId: string | null;
  peopleListSectionId: string | null;
  vehicleListSectionId: string | null;
  propertyListSectionId: string | null;
  organizationListSectionId: string | null;
  reportSections: any;
  associatedCases: any[];

  // API mutations
  updateReportSectionMutation: any;
  addEntityRefToCaseMutation: any;

  // Other handlers

  //Alec@ note to come back here when needing to handle Victim Organizations -- add relationType as prop
  handleAddPersonToOffense: (
    personId: string,
    offenseId: string,
    relationType: any,
    entity?: any
  ) => void;
  handleAddVehicleToOffense: (
    vehicleId: string,
    offenseId: string,
    entity?: any
  ) => void;
  handleAddPropertyToOffense: (
    propertyId: string,
    offenseId: string,
    entity?: any
  ) => void;
  handleAddOrganizationToOffense: (
    organizationId: string,
    offenseId: string,
    relationType?: "victim" | "general",
    entity?: any
  ) => void;
}

export const createEntityHandlers = ({
  people,
  vehicles,
  properties,
  organizations,
  setPeople,
  setVehicles,
  setProperties,
  setOrganizations,
  setMediaFiles,
  setSidePanelOpen,
  setActivePanelType,
  setEditingEntityId,
  setEditingEntityFormData,
  setSelectedRowId,
  setActiveOffenseRelation,
  setNotification,
  activeOffenseRelation,
  activeVehicleOffenseContext,
  activePropertyOffenseContext,
  activeOrganizationOffenseContext,
  activeVictimOrganizationOffenseContext,
  setActiveVehicleOffenseContext,
  setActivePropertyOffenseContext,
  setActiveOrganizationOffenseContext,
  setActiveVictimOrganizationOffenseContext,
  reportId,
  peopleListSectionId,
  vehicleListSectionId,
  propertyListSectionId,
  organizationListSectionId,
  reportSections,
  associatedCases,
  updateReportSectionMutation,
  addEntityRefToCaseMutation,
  handleAddPersonToOffense,
  handleAddVehicleToOffense,
  handleAddPropertyToOffense,
  handleAddOrganizationToOffense,
}: EntityHandlersProps) => {
  const handleEntityDelete = (
    entityId: string,
    entityType: "person" | "property" | "vehicle" | "organization"
  ) => {
    let updatedEntities: any[] = [];
    let sectionId: string | null = null;
    let stateSetter: ((value: any[]) => void) | null = null;
    let title: string = "";

    switch (entityType) {
      case "person":
        updatedEntities = people.filter((entity) => entity.id !== entityId);
        sectionId = peopleListSectionId;
        stateSetter = setPeople;
        title = "People";
        break;
      case "property":
        updatedEntities = properties.filter((entity) => entity.id !== entityId);
        sectionId = propertyListSectionId;
        stateSetter = setProperties;
        title = "Properties";
        break;
      case "vehicle":
        updatedEntities = vehicles.filter((entity) => entity.id !== entityId);
        sectionId = vehicleListSectionId;
        stateSetter = setVehicles;
        title = "Vehicles";
        break;
      case "organization":
        updatedEntities = organizations.filter(
          (entity) => entity.id !== entityId
        );
        sectionId = organizationListSectionId;
        stateSetter = setOrganizations;
        title = "Organizations";
        break;
    }

    // Optimistic UI update: Remove from local state
    if (stateSetter) {
      stateSetter(updatedEntities);
    }

    // Optimistic UI update: Remove reference from report section
    if (sectionId && reportId) {
      updateEntityListSection(
        title,
        null,
        sectionId,
        reportId,
        reportSections,
        updateReportSectionMutation,
        { people, vehicles, properties, organizations },
        updatedEntities
      );
    }
  };

  const handleEntityEdit = (
    entityId: string,
    entityType: "person" | "property" | "vehicle" | "organization"
  ) => {
    let panelType: PanelType | null = null;

    switch (entityType) {
      case "person":
        panelType = PanelType.PERSON;
        break;
      case "property":
        panelType = PanelType.PROPERTY;
        break;
      case "vehicle":
        panelType = PanelType.VEHICLE;
        break;
      case "organization":
        panelType = PanelType.ORGANIZATION;
        break;
    }

    if (panelType) {
      setActivePanelType(panelType);
      setEditingEntityId(entityId);
      setSidePanelOpen(true);
    }
  };

  const handleEntitySelect = (
    entityRef: Reference,
    entityType: "person" | "property" | "vehicle" | "organization"
  ) => {
    const getLatestEntity = async () => {
      try {
        // Check if entity is already in the report
        let isAlreadyInReport = false;
        let existingEntity = null;

        switch (entityType) {
          case "person":
            existingEntity = people.find((p) => p.id === entityRef.id);
            isAlreadyInReport = !!existingEntity;
            break;
          case "vehicle":
            existingEntity = vehicles.find((v) => v.id === entityRef.id);
            isAlreadyInReport = !!existingEntity;
            break;
          case "property":
            existingEntity = properties.find((p) => p.id === entityRef.id);
            isAlreadyInReport = !!existingEntity;
            break;
          case "organization":
            existingEntity = organizations.find((p) => p.id === entityRef.id);
            isAlreadyInReport = !!existingEntity;
            break;
        }

        // If entity is already in report, use existing entity for offense relations
        if (isAlreadyInReport && existingEntity) {
          // Handle offense relations for existing entities
          if (activeOffenseRelation && entityType === "person") {
            handleAddPersonToOffense(
              existingEntity.id,
              activeOffenseRelation.offenseId,
              activeOffenseRelation.relationType,
              existingEntity
            );
            // Clear the active offense relation
            setActiveOffenseRelation(null);
          }

          // Handle vehicle offense relations for existing entities
          if (activeVehicleOffenseContext && entityType === "vehicle") {
            console.log(
              "Creating vehicle offense relation for existing entity:",
              existingEntity.id,
              activeVehicleOffenseContext.offenseId
            );
            handleAddVehicleToOffense(
              existingEntity.id,
              activeVehicleOffenseContext.offenseId,
              existingEntity
            );
            // Clear the active vehicle offense context
            setActiveVehicleOffenseContext(null);
          }

          // Handle property offense relations for existing entities
          if (activePropertyOffenseContext && entityType === "property") {
            console.log(
              "Creating property offense relation for existing entity:",
              existingEntity.id,
              activePropertyOffenseContext.offenseId
            );
            handleAddPropertyToOffense(
              existingEntity.id,
              activePropertyOffenseContext.offenseId,
              existingEntity
            );
            // Clear the active property offense context
            setActivePropertyOffenseContext(null);
          }

          // Handle organization offense relations for existing entities
          if (
            (activeOrganizationOffenseContext ||
              activeVictimOrganizationOffenseContext) &&
            entityType === "organization"
          ) {
            if (activeVictimOrganizationOffenseContext) {
              console.log(
                "Creating victim organization offense relation for existing entity:",
                existingEntity.id,
                activeVictimOrganizationOffenseContext.offenseId
              );
              handleAddOrganizationToOffense(
                existingEntity.id,
                activeVictimOrganizationOffenseContext.offenseId,
                "victim",
                existingEntity
              );
              // Clear the active victim organization offense context
              setActiveVictimOrganizationOffenseContext(null);
            } else if (activeOrganizationOffenseContext) {
              console.log(
                "Creating organization offense relation for existing entity:",
                existingEntity.id,
                activeOrganizationOffenseContext.offenseId
              );
              handleAddOrganizationToOffense(
                existingEntity.id,
                activeOrganizationOffenseContext.offenseId,
                "general",
                existingEntity
              );
              // Clear the active organization offense context
              setActiveOrganizationOffenseContext(null);
            }
          }

          return; // Don't add to report again
        }

        // Entity is not in report, proceed with normal flow
        // Fetch the entity data
        const entity = await getLatestEntityEndpoint({
          id: entityRef.id,
        } as GetLatestEntityRequest);

        if (entity) {
          let updatedEntities = [];
          let sectionId: string | null = null;
          let title = "";

          switch (entityType) {
            case "person":
              updatedEntities = [...people, entity];
              setPeople(updatedEntities);
              sectionId = peopleListSectionId;
              title = "People";

              // If this person was selected from an offense context, create the relation
              if (activeOffenseRelation && entityType === "person") {
                handleAddPersonToOffense(
                  entity.id,
                  activeOffenseRelation.offenseId,
                  activeOffenseRelation.relationType,
                  entity
                );
                // Clear the active offense relation
                setActiveOffenseRelation(null);
              }
              break;
            case "vehicle":
              updatedEntities = [...vehicles, entity];
              setVehicles(updatedEntities);
              sectionId = vehicleListSectionId;
              title = "Vehicles";

              // If this vehicle was selected from an offense context, create the relation
              if (activeVehicleOffenseContext) {
                console.log(
                  "Creating vehicle offense relation:",
                  entity.id,
                  activeVehicleOffenseContext.offenseId
                );
                handleAddVehicleToOffense(
                  entity.id,
                  activeVehicleOffenseContext.offenseId,
                  entity
                );
                // Clear the active vehicle offense context
                setActiveVehicleOffenseContext(null);
              } else {
                console.log(
                  "No active vehicle offense context for vehicle:",
                  entity.id
                );
              }
              break;
            case "property":
              updatedEntities = [...properties, entity];
              setProperties(updatedEntities);
              sectionId = propertyListSectionId;
              title = "Properties";

              // If this property was selected from an offense context, create the relation
              if (activePropertyOffenseContext) {
                console.log(
                  "Creating property offense relation:",
                  entity.id,
                  activePropertyOffenseContext.offenseId
                );
                handleAddPropertyToOffense(
                  entity.id,
                  activePropertyOffenseContext.offenseId,
                  entity
                );
                // Clear the active property offense context
                setActivePropertyOffenseContext(null);
              } else {
                console.log(
                  "No active property offense context for property:",
                  entity.id
                );
              }
              break;
            case "organization":
              updatedEntities = [...organizations, entity];
              setOrganizations(updatedEntities);
              sectionId = organizationListSectionId;
              title = "Organizations";

              // If this organization was selected from an offense context, create the relation
              if (activeVictimOrganizationOffenseContext) {
                console.log(
                  "Creating victim organization offense relation:",
                  entity.id,
                  activeVictimOrganizationOffenseContext.offenseId
                );
                handleAddOrganizationToOffense(
                  entity.id,
                  activeVictimOrganizationOffenseContext.offenseId,
                  "victim",
                  entity
                );
                // Clear the active victim organization offense context
                setActiveVictimOrganizationOffenseContext(null);
              } else if (activeOrganizationOffenseContext) {
                console.log(
                  "Creating organization offense relation:",
                  entity.id,
                  activeOrganizationOffenseContext.offenseId
                );
                handleAddOrganizationToOffense(
                  entity.id,
                  activeOrganizationOffenseContext.offenseId,
                  "general",
                  entity
                );
                // Clear the active organization offense context
                setActiveOrganizationOffenseContext(null);
              } else {
                console.log(
                  "No active organization offense context for organization:",
                  entity.id
                );
              }
              break;
          }

          // Update the report section
          if (sectionId && reportId) {
            updateEntityListSection(
              title,
              entity,
              sectionId,
              reportId,
              reportSections,
              updateReportSectionMutation,
              { people, vehicles, properties, organizations },
              updatedEntities
            );

            // Add entity reference to all associated cases
            if (associatedCases?.length > 0) {
              associatedCases.forEach((caseItem) => {
                // Check if entity is already in this case to prevent duplicates
                const isAlreadyInCase = caseItem.entityRefs?.some(
                  (ref: any) => ref.id === entityRef.id
                );

                if (!isAlreadyInCase) {
                  const request = create(AddEntityRefToCaseRequestSchema, {
                    caseId: caseItem.id,
                    entityRef,
                  });
                  addEntityRefToCaseMutation.mutate(request);
                } else {
                  console.log(
                    "Entity already in case, skipping:",
                    entityRef.id
                  );
                }
              });
            }
          }
        }
      } catch (error) {
        console.error("Error fetching entity:", error);
        setNotification({
          open: true,
          message: `Error adding entity to report: ${error}`,
          severity: "error",
        });
      }
    };

    getLatestEntity();
  };

  const handleRemovePersonFromReport = (
    personId: string,
    entityType: "person"
  ) => {
    if (!reportId) return;

    // Remove from local state
    const updatedPeople = people.filter((person) => person.id !== personId);
    setPeople(updatedPeople);

    // Update the report section to remove the entity reference
    if (peopleListSectionId) {
      updateEntityListSection(
        "People",
        null,
        peopleListSectionId,
        reportId,
        reportSections,
        updateReportSectionMutation,
        { people, vehicles, properties, organizations },
        updatedPeople
      );
    }
  };

  const handleRemoveVehicleFromReport = (
    vehicleId: string,
    entityType: "vehicle"
  ) => {
    if (!reportId) return;

    // Remove from local state
    const updatedVehicles = vehicles.filter(
      (vehicle) => vehicle.id !== vehicleId
    );
    setVehicles(updatedVehicles);

    // Update the report section to remove the entity reference
    if (vehicleListSectionId) {
      updateEntityListSection(
        "Vehicles",
        null,
        vehicleListSectionId,
        reportId,
        reportSections,
        updateReportSectionMutation,
        { people, vehicles, properties, organizations },
        updatedVehicles
      );
    }
  };

  const handleRemovePropertyFromReport = (
    propertyId: string,
    entityType: "property"
  ) => {
    if (!reportId) return;

    // Remove from local state
    const updatedProperties = properties.filter(
      (property) => property.id !== propertyId
    );
    setProperties(updatedProperties);

    // Update the report section to remove the entity reference
    if (propertyListSectionId) {
      updateEntityListSection(
        "Properties",
        null,
        propertyListSectionId,
        reportId,
        reportSections,
        updateReportSectionMutation,
        { people, vehicles, properties, organizations },
        updatedProperties
      );
    }
  };

  const handleRemoveOrganizationFromReport = (
    organizationId: string,
    entityType: "organization"
  ) => {
    if (!reportId) return;

    // Remove from local state
    const updatedOrganizations = organizations.filter(
      (organization) => organization.id !== organizationId
    );
    setOrganizations(updatedOrganizations);

    // Update the report section to remove the entity reference
    if (organizationListSectionId) {
      updateEntityListSection(
        "Organizations",
        null,
        organizationListSectionId,
        reportId,
        reportSections,
        updateReportSectionMutation,
        { people, vehicles, properties, organizations },
        updatedOrganizations
      );
    }
  };

  const handleCloseSidePanel = (
    force: boolean = false,
    isSaveLoading: boolean,
    isUpdateLoading: boolean,
    isSaveAndAddAnotherLoading: boolean
  ) => {
    if (
      !force &&
      (isSaveLoading || isUpdateLoading || isSaveAndAddAnotherLoading)
    ) {
      return;
    }

    setSidePanelOpen(false);
    setTimeout(() => {
      setActivePanelType(null);
      setEditingEntityId(null);
      setEditingEntityFormData(null);
      setSelectedRowId(null);
      // Clear active offense relation when panel closes
      setActiveOffenseRelation(null);
      // Don't clear vehicle and property offense or org contexts here - they need to persist until entity creation
      console.log(
        "Panel closed - keeping vehicle/property/organization offense contexts active"
      );
    }, PANEL_CLOSE_TIMEOUT_MS);
  };

  const handleOpenSidePanel = (panelType: PanelType, readOnly: boolean) => {
    if (readOnly) return; // Don't open panel in read-only mode

    setActivePanelType(panelType);
    setEditingEntityId(null);
    setEditingEntityFormData(null);
    setSidePanelOpen(true);
  };

  return {
    handleEntityDelete,
    handleEntityEdit,
    handleEntitySelect,
    handleRemovePersonFromReport,
    handleRemoveVehicleFromReport,
    handleRemovePropertyFromReport,
    handleRemoveOrganizationFromReport,
    handleCloseSidePanel,
    handleOpenSidePanel,
  };
};
