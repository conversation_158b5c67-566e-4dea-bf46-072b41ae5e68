import { create } from "@bufbuild/protobuf";
import { GetLatestEntityRequest } from "proto/hero/entity/v1/entity_pb";
import {
  CreateRelationRequestSchema,
  DeleteRelationRequestSchema,
  ObjectReferenceSchema,
  RelationSchema,
} from "proto/hero/reports/v2/reports_pb";
import { getLatestEntity as getLatestEntityEndpoint } from "../../../../apis/services/workflow/entity/endpoints";
import { updateEntityListSection } from "../utils/sectionUtils";
import {
  entitiesToOrganizationData,
  entitiesToPersonData,
  entitiesToPropertyData,
  entitiesToVehicleData,
} from "../utils/utils";

interface RelationHandlersProps {
  // State
  reportId: string | null;
  report: any;
  people: any[];
  vehicles: any[];
  properties: any[];
  organizations: any[];
  setPeople: (people: any[]) => void;
  setVehicles: (vehicles: any[]) => void;
  setProperties: (properties: any[]) => void;
  setOrganizations: (properties: any[]) => void;
  setNotification: (notification: any) => void;
  associatedCases: any[];

  // Section IDs
  peopleListSectionId: string | null;
  vehicleListSectionId: string | null;
  propertyListSectionId: string | null;
  organizationListSectionId: string | null;
  reportSections: any;

  // API mutations
  createRelationMutation: any;
  deleteRelationMutation: any;
  updateRelationMutation?: any;
  updateReportSectionMutation: any;
}

// Helper function to get the next victim/offender number for case-level numbering
// alec@ note to come back and check this when adding in organizations as victims
const getNextVictimOffenderNumber = (
  relations: any[],
  entityId: string,
  relationType:
    | "victim"
    | "offender"
    | "witness"
    | "suspect"
    | "involved_party",
  associatedCases?: any[]
): string => {
  const existingRelation = relations.find((rel) => {
    const relationTypeMapping = {
      victim: "RELATION_TYPE_OFFENSE_VICTIM",
      offender: "RELATION_TYPE_OFFENSE_OFFENDER",
      witness: "RELATION_TYPE_OFFENSE_WITNESS",
      suspect: "RELATION_TYPE_OFFENSE_SUSPECT",
      involved_party: "RELATION_TYPE_OFFENSE_INVOLVED_PARTY",
    };

    const targetRelationType = relationTypeMapping[relationType];
    const isRelevantRelation = rel.relationType === targetRelationType;

    if (!isRelevantRelation) return false;

    const entityInvolved =
      (rel.objectA?.objectType === "entity" &&
        rel.objectA?.globalId === entityId) ||
      (rel.objectB?.objectType === "entity" &&
        rel.objectB?.globalId === entityId);

    return entityInvolved && rel.metadata?.victimOffenderNumber;
  });

  if (existingRelation) {
    return existingRelation.metadata.victimOffenderNumber;
  }

  // If not found in current report relations, check case entities for existing numbers
  // Look through associated case entities to see if this entity already has a number
  if (associatedCases && associatedCases.length > 0) {
    const firstCase = associatedCases[0];
    if (firstCase?.entityRefs) {
      const existingEntityRef = firstCase.entityRefs.find(
        (ref: any) => ref.id === entityId
      );
      if (existingEntityRef?.metadata?.victimOffenderNumber) {
        return existingEntityRef.metadata.victimOffenderNumber;
      }
    }
  }

  // Find the highest existing number for this type
  const prefixMapping = {
    victim: "V",
    offender: "O",
    witness: "W",
    suspect: "S",
    involved_party: "I",
  };

  const prefix = prefixMapping[relationType];

  // Get numbers from current report relations
  const relationTypeMapping = {
    victim: "RELATION_TYPE_OFFENSE_VICTIM",
    offender: "RELATION_TYPE_OFFENSE_OFFENDER",
    witness: "RELATION_TYPE_OFFENSE_WITNESS",
    suspect: "RELATION_TYPE_OFFENSE_SUSPECT",
    involved_party: "RELATION_TYPE_OFFENSE_INVOLVED_PARTY",
  };

  const targetRelationType = relationTypeMapping[relationType];

  const reportNumbers = relations
    .filter((rel) => {
      const isRelevantRelation = rel.relationType === targetRelationType;
      return (
        isRelevantRelation &&
        rel.metadata?.victimOffenderNumber?.startsWith(prefix)
      );
    })
    .map((rel) => {
      //alec@ note that we need to change logic here if we make a 2 character tag
      const numberStr = rel.metadata.victimOffenderNumber.slice(1); // Remove prefix
      return parseInt(numberStr, 10);
    })
    .filter((num) => !isNaN(num));

  // Get numbers from case entities (if available)
  const caseNumbers: number[] = [];
  if (associatedCases && associatedCases.length > 0) {
    const firstCase = associatedCases[0];
    if (firstCase?.entityRefs) {
      firstCase.entityRefs.forEach((ref: any) => {
        if (ref.metadata?.victimOffenderNumber?.startsWith(prefix)) {
          const numberStr = ref.metadata.victimOffenderNumber.slice(1);
          const num = parseInt(numberStr, 10);
          if (!isNaN(num)) {
            caseNumbers.push(num);
          }
        }
      });
    }
  }

  const allNumbers = [...reportNumbers, ...caseNumbers];
  const highestNumber = allNumbers.length > 0 ? Math.max(...allNumbers) : 0;
  return `${prefix}${highestNumber + 1}`;
};

export const createRelationHandlers = ({
  reportId,
  report,
  people,
  vehicles,
  properties,
  organizations,
  setPeople,
  setVehicles,
  setProperties,
  setOrganizations,
  setNotification,
  associatedCases,
  peopleListSectionId,
  vehicleListSectionId,
  propertyListSectionId,
  organizationListSectionId,
  reportSections,
  createRelationMutation,
  deleteRelationMutation,
  updateRelationMutation,
  updateReportSectionMutation,
}: RelationHandlersProps) => {
  // Helper function to check if an offense ID is actually an incident
  const isIncident = (offenseId: string): boolean => {
    // Check if this is a local temporary incident ID
    if (offenseId.startsWith("incident_")) {
      return true;
    }

    if (!reportSections?.sections) {
      return false;
    }

    const offenseSection = reportSections.sections.find(
      (section: any) => section.type === "SECTION_TYPE_OFFENSE"
    );

    if (!offenseSection?.offenseList?.offenses) {
      return false;
    }

    const item = offenseSection.offenseList.offenses.find(
      (offense: any) => offense.id === offenseId
    );

    return item?.data?.isIncident === true;
  };

  const handleAddPersonToOffense = async (
    personId: string,
    offenseId: string,
    relationType:
      | "victim"
      | "offender"
      | "witness"
      | "suspect"
      | "involved_party",
    entity?: any
  ) => {
    if (!reportId) return;

    // Check if this is an incident
    const isIncidentRelation = isIncident(offenseId);

    // Create the relation type string
    let relationTypeString: string;
    if (isIncidentRelation) {
      // For incidents, always use the generic incident-person relation
      relationTypeString = "RELATION_TYPE_INCIDENT_PERSON";
    } else {
      // For offenses, use specific relation types
      switch (relationType) {
        case "victim":
          relationTypeString = "RELATION_TYPE_OFFENSE_VICTIM";
          break;
        case "offender":
          relationTypeString = "RELATION_TYPE_OFFENSE_OFFENDER";
          break;
        case "witness":
          relationTypeString = "RELATION_TYPE_OFFENSE_WITNESS";
          break;
        case "suspect":
          relationTypeString = "RELATION_TYPE_OFFENSE_SUSPECT";
          break;
        case "involved_party":
          relationTypeString = "RELATION_TYPE_OFFENSE_INVOLVED_PARTY";
          break;
        default:
          return;
      }
    }

    let personEntity = entity;

    if (!personEntity) {
      // Try to find in local state first
      personEntity = people.find((p) => p.id === personId);
    }

    // If still not found, fetch from backend
    if (!personEntity) {
      try {
        personEntity = await getLatestEntityEndpoint({
          id: personId,
        } as GetLatestEntityRequest);

        if (personEntity) {
          // Add to local state
          const updatedPeople = [...people, personEntity];
          setPeople(updatedPeople);

          // Update report section so the entity appears in People list
          if (peopleListSectionId) {
            updateEntityListSection(
              "People",
              personEntity,
              peopleListSectionId,
              reportId,
              reportSections,
              updateReportSectionMutation,
              { people, vehicles, properties, organizations },
              updatedPeople
            );
          }
        }
      } catch (error) {
        console.error("Error fetching person for relation:", error);
        return;
      }
    }

    const personData = personEntity
      ? entitiesToPersonData([personEntity])[0]
      : null;

    if (!personData) {
      console.error("Person data not found for relation creation");
      return;
    }

    // Get the numbered label for offense person types only (not for incidents)
    const metadata: any = {};
    if (!isIncidentRelation) {
      const numberedLabel = getNextVictimOffenderNumber(
        report?.relations || [],
        personId,
        relationType,
        associatedCases
      );
      metadata.victimOffenderNumber = numberedLabel;
    }

    // Create object references
    const offenseObjectRef = create(ObjectReferenceSchema, {
      objectType: "offense",
      reportScopedId: offenseId,
      displayName: `Offense ${offenseId}`,
    });

    const personObjectRef = create(ObjectReferenceSchema, {
      objectType: "entity",
      globalId: personId,
      displayName: personData.name,
    });

    // Create relation with metadata
    const relation = create(RelationSchema, {
      reportId: reportId,
      objectA: offenseObjectRef,
      objectB: personObjectRef,
      relationType: relationTypeString,
      description: isIncidentRelation
        ? `${personData.name} is involved in this incident`
        : `${personData.name} is a ${relationType} in this offense`,
      metadata: metadata,
    });

    // Create the request
    const createRelationRequest = create(CreateRelationRequestSchema, {
      reportId: reportId,
      relation: relation,
    });

    createRelationMutation.mutate(createRelationRequest);
  };

  const handleAddVehicleToOffense = async (
    vehicleId: string,
    offenseId: string,
    entity?: any
  ) => {
    console.log("handleAddVehicleToOffense called:", {
      vehicleId,
      offenseId,
      reportId,
    });
    if (!reportId) return;

    let vehicleEntity = entity;
    if (!vehicleEntity) {
      vehicleEntity = vehicles.find((v) => v.id === vehicleId);
    }

    if (!vehicleEntity) {
      try {
        vehicleEntity = await getLatestEntityEndpoint({
          id: vehicleId,
        } as GetLatestEntityRequest);
        if (vehicleEntity) {
          const updatedVehicles = [...vehicles, vehicleEntity];
          setVehicles(updatedVehicles);

          if (vehicleListSectionId) {
            updateEntityListSection(
              "Vehicles",
              vehicleEntity,
              vehicleListSectionId,
              reportId,
              reportSections,
              updateReportSectionMutation,
              { people, vehicles, properties, organizations },
              updatedVehicles
            );
          }
        }
      } catch (error) {
        console.error("Error fetching vehicle for relation:", error);
        return;
      }
    }

    const vehicleData = vehicleEntity
      ? entitiesToVehicleData([vehicleEntity])[0]
      : null;

    if (!vehicleData) {
      console.error("Vehicle data not found for relation creation");
      return;
    }

    console.log("Creating vehicle relation with data:", vehicleData);

    // Check if this is an incident
    const isIncidentRelation = isIncident(offenseId);

    // Create object references
    const offenseObjectRef = create(ObjectReferenceSchema, {
      objectType: "offense",
      reportScopedId: offenseId,
      displayName: isIncidentRelation
        ? `Incident ${offenseId}`
        : `Offense ${offenseId}`,
    });

    const vehicleObjectRef = create(ObjectReferenceSchema, {
      objectType: "entity",
      globalId: vehicleId,
      displayName: `${vehicleData.make} ${vehicleData.model}`,
    });

    // Create relation
    const relation = create(RelationSchema, {
      reportId: reportId,
      objectA: offenseObjectRef,
      objectB: vehicleObjectRef,
      relationType: isIncidentRelation
        ? "RELATION_TYPE_INCIDENT_VEHICLE"
        : "RELATION_TYPE_OFFENSE_VEHICLE",
      description: isIncidentRelation
        ? `${vehicleData.make} ${vehicleData.model} is involved in this incident`
        : `${vehicleData.make} ${vehicleData.model} is involved in this offense`,
    });

    // Create the request
    const createRelationRequest = create(CreateRelationRequestSchema, {
      reportId: reportId,
      relation: relation,
    });

    console.log("Submitting vehicle relation request:", createRelationRequest);
    createRelationMutation.mutate(createRelationRequest);
  };

  const handleAddPropertyToOffense = async (
    propertyId: string,
    offenseId: string,
    entity?: any
  ) => {
    console.log("handleAddPropertyToOffense called:", {
      propertyId,
      offenseId,
      reportId,
    });
    if (!reportId) return;

    let propertyEntity = entity;
    if (!propertyEntity) {
      propertyEntity = properties.find((p) => p.id === propertyId);
    }

    if (!propertyEntity) {
      try {
        propertyEntity = await getLatestEntityEndpoint({
          id: propertyId,
        } as GetLatestEntityRequest);
        if (propertyEntity) {
          const updatedProperties = [...properties, propertyEntity];
          setProperties(updatedProperties);

          if (propertyListSectionId) {
            updateEntityListSection(
              "Properties",
              propertyEntity,
              propertyListSectionId,
              reportId,
              reportSections,
              updateReportSectionMutation,
              { people, vehicles, properties, organizations },
              updatedProperties
            );
          }
        }
      } catch (error) {
        console.error("Error fetching property for relation:", error);
        return;
      }
    }

    const propertyData = propertyEntity
      ? entitiesToPropertyData([propertyEntity])[0]
      : null;

    if (!propertyData) {
      console.error("Property data not found for relation creation");
      return;
    }

    console.log("Creating property relation with data:", propertyData);

    // Check if this is an incident
    const isIncidentRelation = isIncident(offenseId);

    // Create object references
    const offenseObjectRef = create(ObjectReferenceSchema, {
      objectType: "offense",
      reportScopedId: offenseId,
      displayName: isIncidentRelation
        ? `Incident ${offenseId}`
        : `Offense ${offenseId}`,
    });

    const propertyObjectRef = create(ObjectReferenceSchema, {
      objectType: "entity",
      globalId: propertyId,
      displayName: propertyData.category,
    });

    // Create relation
    const relation = create(RelationSchema, {
      reportId: reportId,
      objectA: offenseObjectRef,
      objectB: propertyObjectRef,
      relationType: isIncidentRelation
        ? "RELATION_TYPE_INCIDENT_PROPERTY"
        : "RELATION_TYPE_OFFENSE_PROPERTY",
      description: isIncidentRelation
        ? `${propertyData.category} is involved in this incident`
        : `${propertyData.category} is involved in this offense`,
    });

    // Create the request
    const createRelationRequest = create(CreateRelationRequestSchema, {
      reportId: reportId,
      relation: relation,
    });

    console.log("Submitting property relation request:", createRelationRequest);
    createRelationMutation.mutate(createRelationRequest);
  };

  const handleAddOrganizationToOffense = async (
    organizationId: string,
    offenseId: string,
    relationType: "victim" | "general" = "general",
    entity?: any
  ) => {
    console.log("handleAddOrganizationToOffense called:", {
      organizationId,
      offenseId,
      relationType,
      reportId,
    });
    if (!reportId) return;

    let organizationEntity = entity;
    if (!organizationEntity) {
      organizationEntity = organizations.find((p) => p.id === organizationId);
    }

    if (!organizationEntity) {
      try {
        organizationEntity = await getLatestEntityEndpoint({
          id: organizationId,
        } as GetLatestEntityRequest);
        if (organizationEntity) {
          const updatedOrganizations = [...organizations, organizationEntity];
          setOrganizations(updatedOrganizations);

          if (organizationListSectionId) {
            updateEntityListSection(
              "Organizations",
              organizationEntity,
              organizationListSectionId,
              reportId,
              reportSections,
              updateReportSectionMutation,
              { people, vehicles, properties, organizations },
              updatedOrganizations
            );
          }
        }
      } catch (error) {
        console.error("Error fetching organization for relation:", error);
        return;
      }
    }

    const organizationData = organizationEntity
      ? entitiesToOrganizationData([organizationEntity])[0]
      : null;

    if (!organizationData) {
      console.error("Organization data not found for relation creation");
      return;
    }

    console.log("Creating organization relation with data:", organizationData);

    // Check if this is an incident
    const isIncidentRelation = isIncident(offenseId);

    // Determine relation type based on context
    let relationTypeString: string;
    if (isIncidentRelation) {
      relationTypeString = "RELATION_TYPE_INCIDENT_ORGANIZATION";
    } else {
      // Use specific relation type for victims
      relationTypeString =
        relationType === "victim"
          ? "RELATION_TYPE_OFFENSE_VICTIM"
          : "RELATION_TYPE_OFFENSE_ORGANIZATION";
    }

    // Get victim number if this is a victim organization
    const metadata: any = {};
    if (relationType === "victim" && !isIncidentRelation) {
      const victimNumber = getNextVictimOffenderNumber(
        report?.relations || [],
        organizationId,
        "victim",
        associatedCases
      );
      metadata.victimOffenderNumber = victimNumber;
    }

    // Create object references
    const offenseObjectRef = create(ObjectReferenceSchema, {
      objectType: "offense",
      reportScopedId: offenseId,
      displayName: isIncidentRelation
        ? `Incident ${offenseId}`
        : `Offense ${offenseId}`,
    });

    const organizationObjectRef = create(ObjectReferenceSchema, {
      objectType: "entity",
      globalId: organizationId,
      displayName: organizationData.name,
    });

    // Create relation
    const relation = create(RelationSchema, {
      reportId: reportId,
      objectA: offenseObjectRef,
      objectB: organizationObjectRef,
      relationType: relationTypeString,
      description: isIncidentRelation
        ? `${organizationData.name} is involved in this incident`
        : relationType === "victim"
        ? `${organizationData.name} is a victim in this offense`
        : `${organizationData.name} is involved in this offense`,
      metadata: metadata,
    });

    // Create the request
    const createRelationRequest = create(CreateRelationRequestSchema, {
      reportId: reportId,
      relation: relation,
    });

    console.log(
      "Submitting organization relation request:",
      createRelationRequest
    );
    createRelationMutation.mutate(createRelationRequest);
  };

  // alec@ note to come back here when orgs can be victims

  const handleCreateVictimEntityRelation = async (
    personId: string,
    victimQuestionsData: any,
    entity?: any
  ) => {
    if (!reportId) return;

    let personEntity = entity;

    if (!personEntity) {
      // Try to find in local state first
      personEntity = people.find((p) => p.id === personId);
    }

    // If still not found, fetch from backend
    if (!personEntity) {
      try {
        personEntity = await getLatestEntityEndpoint({
          id: personId,
        } as GetLatestEntityRequest);

        if (personEntity) {
          // Add to local state
          const updatedPeople = [...people, personEntity];
          setPeople(updatedPeople);

          // Update report section so the entity appears in People list
          if (peopleListSectionId) {
            updateEntityListSection(
              "People",
              personEntity,
              peopleListSectionId,
              reportId,
              reportSections,
              updateReportSectionMutation,
              { people, vehicles, properties, organizations },
              updatedPeople
            );
          }
        }
      } catch (error) {
        console.error("Error fetching person for victim relation:", error);
        return;
      }
    }

    const personData = personEntity
      ? entitiesToPersonData([personEntity])[0]
      : null;

    if (!personData) {
      console.error("Person data not found for victim relation creation");
      return;
    }

    // Create object references
    const reportObjectRef = create(ObjectReferenceSchema, {
      objectType: "report",
      reportScopedId: reportId,
      displayName: `Report ${reportId}`,
    });

    const personObjectRef = create(ObjectReferenceSchema, {
      objectType: "entity",
      globalId: personId,
      displayName: personData.name,
    });

    // Create relation with victim questions metadata
    const relation = create(RelationSchema, {
      reportId: reportId,
      objectA: reportObjectRef,
      objectB: personObjectRef,
      relationType: "RELATION_TYPE_VICTIM_ENTITY",
      metadata: victimQuestionsData,
    });

    // Create the request
    const createRelationRequest = create(CreateRelationRequestSchema, {
      reportId: reportId,
      relation: relation,
    });

    createRelationMutation.mutate(createRelationRequest);
  };

  const handleRemovePersonFromOffense = (
    personId: string,
    offenseId: string
  ) => {
    if (!reportId || !report?.relations) {
      return;
    }

    // Find the relation to delete
    const relationToDelete = report.relations.find((relation: any) => {
      const isOffenseInvolved =
        (relation.objectA?.objectType === "offense" &&
          relation.objectA?.reportScopedId === offenseId) ||
        (relation.objectB?.objectType === "offense" &&
          relation.objectB?.reportScopedId === offenseId);

      const isPersonInvolved =
        (relation.objectA?.objectType === "entity" &&
          relation.objectA?.globalId === personId) ||
        (relation.objectB?.objectType === "entity" &&
          relation.objectB?.globalId === personId);

      return isOffenseInvolved && isPersonInvolved;
    });

    if (!relationToDelete) {
      console.error("Person relation not found for deletion");
      return;
    }

    // Create delete request
    const deleteRelationRequest = create(DeleteRelationRequestSchema, {
      reportId: reportId,
      relationId: relationToDelete.id,
    });

    deleteRelationMutation.mutate(deleteRelationRequest);
  };

  const handleRemoveVehicleFromOffense = (
    vehicleId: string,
    offenseId: string
  ) => {
    if (!reportId || !report?.relations) {
      return;
    }

    // Find the relation to delete
    const relationToDelete = report.relations.find((relation: any) => {
      const isOffenseInvolved =
        (relation.objectA?.objectType === "offense" &&
          relation.objectA?.reportScopedId === offenseId) ||
        (relation.objectB?.objectType === "offense" &&
          relation.objectB?.reportScopedId === offenseId);

      const isVehicleInvolved =
        (relation.objectA?.objectType === "entity" &&
          relation.objectA?.globalId === vehicleId) ||
        (relation.objectB?.objectType === "entity" &&
          relation.objectB?.globalId === vehicleId);

      return (
        isOffenseInvolved &&
        isVehicleInvolved &&
        relation.relationType === "RELATION_TYPE_OFFENSE_VEHICLE"
      );
    });

    if (!relationToDelete) {
      console.error("Vehicle relation not found for deletion");
      return;
    }

    // Create delete request
    const deleteRelationRequest = create(DeleteRelationRequestSchema, {
      reportId: reportId,
      relationId: relationToDelete.id,
    });

    deleteRelationMutation.mutate(deleteRelationRequest);
  };

  const handleRemovePropertyFromOffense = (
    propertyId: string,
    offenseId: string
  ) => {
    if (!reportId || !report?.relations) {
      return;
    }

    // Find the relation to delete
    const relationToDelete = report.relations.find((relation: any) => {
      const isOffenseInvolved =
        (relation.objectA?.objectType === "offense" &&
          relation.objectA?.reportScopedId === offenseId) ||
        (relation.objectB?.objectType === "offense" &&
          relation.objectB?.reportScopedId === offenseId);

      const isPropertyInvolved =
        (relation.objectA?.objectType === "entity" &&
          relation.objectA?.globalId === propertyId) ||
        (relation.objectB?.objectType === "entity" &&
          relation.objectB?.globalId === propertyId);

      return (
        isOffenseInvolved &&
        isPropertyInvolved &&
        relation.relationType === "RELATION_TYPE_OFFENSE_PROPERTY"
      );
    });

    if (!relationToDelete) {
      console.error("Property relation not found for deletion");
      return;
    }

    // Create delete request
    const deleteRelationRequest = create(DeleteRelationRequestSchema, {
      reportId: reportId,
      relationId: relationToDelete.id,
    });

    deleteRelationMutation.mutate(deleteRelationRequest);
  };

  const handleRemoveOrganizationFromOffense = (
    organizationId: string,
    offenseId: string
  ) => {
    if (!reportId || !report?.relations) {
      return;
    }

    // Find the relation to delete
    const relationToDelete = report.relations.find((relation: any) => {
      const isOffenseInvolved =
        (relation.objectA?.objectType === "offense" &&
          relation.objectA?.reportScopedId === offenseId) ||
        (relation.objectB?.objectType === "offense" &&
          relation.objectB?.reportScopedId === offenseId);

      const isOrganizationInvolved =
        (relation.objectA?.objectType === "entity" &&
          relation.objectA?.globalId === organizationId) ||
        (relation.objectB?.objectType === "entity" &&
          relation.objectB?.globalId === organizationId);

      return (
        isOffenseInvolved &&
        isOrganizationInvolved &&
        relation.relationType === "RELATION_TYPE_OFFENSE_ORGANIZATION"
      );
    });

    if (!relationToDelete) {
      console.error("Organization relation not found for deletion");
      return;
    }

    // Create delete request
    const deleteRelationRequest = create(DeleteRelationRequestSchema, {
      reportId: reportId,
      relationId: relationToDelete.id,
    });

    deleteRelationMutation.mutate(deleteRelationRequest);
  };

  const handleRemoveVictimOrganizationFromOffense = (
    organizationId: string,
    offenseId: string
  ) => {
    if (!reportId || !report?.relations) {
      return;
    }

    // Find the victim organization relation to delete
    const relationToDelete = report.relations.find((relation: any) => {
      const isOffenseInvolved =
        (relation.objectA?.objectType === "offense" &&
          relation.objectA?.reportScopedId === offenseId) ||
        (relation.objectB?.objectType === "offense" &&
          relation.objectB?.reportScopedId === offenseId);

      const isOrganizationInvolved =
        (relation.objectA?.objectType === "entity" &&
          relation.objectA?.globalId === organizationId) ||
        (relation.objectB?.objectType === "entity" &&
          relation.objectB?.globalId === organizationId);

      return (
        isOffenseInvolved &&
        isOrganizationInvolved &&
        relation.relationType === "RELATION_TYPE_OFFENSE_VICTIM"
      );
    });

    if (!relationToDelete) {
      console.error("Victim organization relation not found for deletion");
      return;
    }

    // Create delete request
    const deleteRelationRequest = create(DeleteRelationRequestSchema, {
      reportId: reportId,
      relationId: relationToDelete.id,
    });

    deleteRelationMutation.mutate(deleteRelationRequest);
  };

  const handleSetActiveOffenseRelation = (
    context: {
      offenseId: string;
      relationType:
        | "victim"
        | "offender"
        | "witness"
        | "suspect"
        | "involved_party";
    } | null
  ) => {
    // This is now handled in the parent component
    // We're returning this function for compatibility
    return context;
  };

  const handleCreateVictimReportRelation = async (
    entityId: string,
    reportId: string,
    entity?: any,
    additionalVictimData?: any
  ) => {
    if (!reportId) return;

    // Get the entity name - handle both people and organizations
    let entityName = "Unknown Entity";
    if (entity) {
      if (entity.name) {
        // For organizations or entities with direct name field
        entityName = entity.name;
      } else if (entity.firstName || entity.lastName) {
        // For people with firstName/lastName structure
        entityName = `${entity.firstName || ""} ${
          entity.lastName || ""
        }`.trim();
      }
    }

    const reportObjectRef = create(ObjectReferenceSchema, {
      objectType: "report",
      reportScopedId: reportId,
      displayName: `Report ${reportId}`,
    });

    const entityObjectRef = create(ObjectReferenceSchema, {
      objectType: "entity",
      globalId: entityId,
      displayName: entityName,
    });

    // Create the relation
    const relation = create(RelationSchema, {
      reportId: reportId,
      objectA: reportObjectRef,
      objectB: entityObjectRef,
      relationType: "RELATION_TYPE_VICTIM_REPORT",
      metadata: additionalVictimData,
    });

    const createRelationRequest = create(CreateRelationRequestSchema, {
      reportId: reportId,
      relation: relation,
    });

    createRelationMutation.mutate(createRelationRequest);
  };

  const handleUpdateVictimReportRelation = async (
    relationId: string,
    additionalVictimData: any
  ) => {
    // This function is not implemented here because it's handled directly
    // in the ReportingPage component using the updateRelationMutation
    // We include it in the return for interface compatibility
    console.log(
      "handleUpdateVictimReportRelation called with:",
      relationId,
      additionalVictimData
    );
  };

  const handleSetActiveVehicleOffenseContext = (
    context: { offenseId: string } | null
  ) => {
    // This is now handled in the parent component
    return context;
  };

  const handleSetActivePropertyOffenseContext = (
    context: { offenseId: string } | null
  ) => {
    // This is now handled in the parent component
    return context;
  };

  const handleAddPersonToArrest = async (
    personId: string,
    arrestId: string,
    entity?: any
  ) => {
    if (!reportId) return;

    let personEntity = entity;

    if (!personEntity) {
      // Try to find in local state first
      personEntity = people.find((p) => p.id === personId);
    }

    // If still not found, fetch from backend
    if (!personEntity) {
      try {
        personEntity = await getLatestEntityEndpoint({
          id: personId,
        } as GetLatestEntityRequest);

        if (personEntity) {
          // Add to local state
          const updatedPeople = [...people, personEntity];
          setPeople(updatedPeople);

          // Update report section so the entity appears in People list
          if (peopleListSectionId) {
            updateEntityListSection(
              "People",
              personEntity,
              peopleListSectionId,
              reportId,
              reportSections,
              updateReportSectionMutation,
              { people, vehicles, properties, organizations },
              updatedPeople
            );
          }
        }
      } catch (error) {
        console.error("Error fetching person for arrest relation:", error);
        return;
      }
    }

    const personData = personEntity
      ? entitiesToPersonData([personEntity])[0]
      : null;

    if (!personData) {
      console.error("Person data not found for arrest relation creation");
      return;
    }

    // Create object references
    const arrestObjectRef = create(ObjectReferenceSchema, {
      objectType: "arrest",
      reportScopedId: arrestId,
      displayName: `Arrest ${arrestId}`,
    });

    const personObjectRef = create(ObjectReferenceSchema, {
      objectType: "entity",
      globalId: personId,
      displayName: personData.name,
    });

    // Create relation
    const relation = create(RelationSchema, {
      reportId: reportId,
      objectA: arrestObjectRef,
      objectB: personObjectRef,
      relationType: "RELATION_TYPE_ARREST_ARRESTEE",
      description: `${personData.name} is an arrestee in this arrest`,
    });

    // Create the request
    const createRelationRequest = create(CreateRelationRequestSchema, {
      reportId: reportId,
      relation: relation,
    });

    createRelationMutation.mutate(createRelationRequest);
  };

  const handleRemovePersonFromArrest = (personId: string, arrestId: string) => {
    if (!reportId || !report?.relations) {
      return;
    }

    // Find the relation to delete
    const relationToDelete = report.relations.find((relation: any) => {
      const isArrestInvolved =
        (relation.objectA?.objectType === "arrest" &&
          relation.objectA?.reportScopedId === arrestId) ||
        (relation.objectB?.objectType === "arrest" &&
          relation.objectB?.reportScopedId === arrestId);

      const isPersonInvolved =
        (relation.objectA?.objectType === "entity" &&
          relation.objectA?.globalId === personId) ||
        (relation.objectB?.objectType === "entity" &&
          relation.objectB?.globalId === personId);

      return (
        isArrestInvolved &&
        isPersonInvolved &&
        relation.relationType === "RELATION_TYPE_ARREST_ARRESTEE"
      );
    });

    if (!relationToDelete) {
      console.error("Arrest person relation not found for deletion");
      return;
    }

    // Create delete request
    const deleteRelationRequest = create(DeleteRelationRequestSchema, {
      reportId: reportId,
      relationId: relationToDelete.id,
    });

    deleteRelationMutation.mutate(deleteRelationRequest);
  };

  const handleSetActiveOrganizationOffenseContext = (
    context: { offenseId: string } | null
  ) => {
    // This is now handled in the parent component
    return context;
  };

  // Specific handler for adding victim organizations
  const handleAddVictimOrganizationToOffense = async (
    organizationId: string,
    offenseId: string,
    entity?: any
  ) => {
    return handleAddOrganizationToOffense(
      organizationId,
      offenseId,
      "victim",
      entity
    );
  };

  return {
    handleAddPersonToOffense,
    handleAddVehicleToOffense,
    handleAddPropertyToOffense,
    handleAddOrganizationToOffense,
    handleAddVictimOrganizationToOffense,
    handleCreateVictimEntityRelation,
    handleRemovePersonFromOffense,
    handleRemoveVehicleFromOffense,
    handleRemovePropertyFromOffense,
    handleRemoveOrganizationFromOffense,
    handleRemoveVictimOrganizationFromOffense,
    handleSetActiveOffenseRelation,
    handleCreateVictimReportRelation,
    handleUpdateVictimReportRelation,
    handleSetActiveVehicleOffenseContext,
    handleSetActivePropertyOffenseContext,
    handleAddPersonToArrest,
    handleRemovePersonFromArrest,
  };
};
