import { useMemo } from "react";
import { PanelType, getPanelTitle } from "../types";
import { getSchemaForPanelType } from "../utils/schemaUtils";
import { scrollToSection as scrollToSectionUtil } from "../utils/scrollUtils";
import { navItems } from "../utils/utils";

// Helper to determine if comments should be initially expanded
export const useShouldExpandComments = (orderForReport: any) => {
  return useMemo(() => {
    return orderForReport?.type === "ORDER_TYPE_REVISE_REPORT";
  }, [orderForReport]);
};

// Helper to get review round ID
export const useReviewRoundId = (orderForReport: any) => {
  return useMemo(() => {
    if (
      orderForReport?.type === "ORDER_TYPE_REVIEW_REPORT" &&
      orderForReport?.status !== "ORDER_STATUS_COMPLETED"
    ) {
      return orderForReport.reviewRoundId || "";
    }
    return "";
  }, [orderForReport]);
};

// Helper to check if this is a revise order
export const useIsReviseOrder = (orderForReport: any) => {
  return useMemo(() => {
    return (
      orderForReport?.type === "ORDER_TYPE_REVISE_REPORT" &&
      orderForReport?.status !== "ORDER_STATUS_COMPLETED"
    );
  }, [orderForReport]);
};

// Helper to find rejected review round
export const useRejectedReviewRound = (reviewRoundsData: any) => {
  return useMemo(() => {
    return reviewRoundsData?.reviewRounds?.find(
      // @ts-expect-error TODO: Fix type issue
      (round) => round.status === "REVIEW_STATUS_CHANGES_REQUESTED"
    );
  }, [reviewRoundsData]);
};

// Helper to determine read-only mode
export const useReadOnly = (orderForReport: any, report: any) => {
  return useMemo(() => {
    // For supplemental reports, only check report status
    if (String(report?.reportType) === "REPORT_TYPE_INCIDENT_SUPPLEMENTAL") {
      return String(report?.status) === "REPORT_STATUS_APPROVED";
    }

    // For other reports, use the original logic
    return (
      !orderForReport || orderForReport?.status === "ORDER_STATUS_COMPLETED"
    );
  }, [orderForReport, report?.reportType, report?.status]);
};

// Helper to create available navigation items
export const useAvailableNavItems = (
  incidentDetailsSectionId: string | null,
  peopleListSectionId: string | null,
  vehicleListSectionId: string | null,
  propertyListSectionId: string | null,
  organizationListSectionId: string | null,
  offenseListSectionId: string | null,
  arrestListSectionId: string | null,
  narrativeSectionId: string | null,
  mediaSectionId: string | null
) => {
  return useMemo(() => {
    const sectionIdMapping = {
      "incident-details": incidentDetailsSectionId,
      people: peopleListSectionId,
      vehicles: vehicleListSectionId,
      property: propertyListSectionId,
      organizations: organizationListSectionId,
      offenses: offenseListSectionId,
      arrests: arrestListSectionId,
      narrative: narrativeSectionId,
      media: mediaSectionId,
    };

    console.log("sectionId Mapping is: ", sectionIdMapping);

    return navItems.filter(
      (item) => sectionIdMapping[item.id as keyof typeof sectionIdMapping]
    );
  }, [
    incidentDetailsSectionId,
    peopleListSectionId,
    vehicleListSectionId,
    propertyListSectionId,
    organizationListSectionId,
    offenseListSectionId,
    arrestListSectionId,
    narrativeSectionId,
    mediaSectionId,
  ]);
};

// Helper to get current schema
export const useGetCurrentSchema = (
  activePanelType: PanelType | null,
  editingEntityId: string | null,
  editingEntity: any,
  specificSchema: any,
  personSchemas: any,
  propertySchemas: any,
  vehicleSchemas: any,
  organizationSchemas: any
) => {
  return () => {
    return getSchemaForPanelType(
      activePanelType,
      editingEntityId,
      editingEntity,
      specificSchema,
      personSchemas,
      propertySchemas,
      vehicleSchemas,
      organizationSchemas
    );
  };
};

// Helper to get contextual panel title
export const useGetContextualPanelTitle = (
  activePanelType: PanelType | null,
  activeOffenseRelation: any,
  activeVehicleOffenseContext: any,
  activePropertyOffenseContext: any,
  activeOrganizationOffenseContext: any,
  editingEntityId: string | null
) => {
  return () => {
    // Check if we have offense context for person entities
    if (
      activePanelType === PanelType.PERSON &&
      activeOffenseRelation &&
      !editingEntityId
    ) {
      return getPanelTitle(
        activePanelType,
        { relationType: activeOffenseRelation.relationType },
        !!editingEntityId
      );
    }

    // Check if we have offense context for vehicle entities
    if (
      activePanelType === PanelType.VEHICLE &&
      activeVehicleOffenseContext &&
      !editingEntityId
    ) {
      return getPanelTitle(activePanelType, {}, !!editingEntityId);
    }

    // Check if we have offense context for property entities
    if (
      activePanelType === PanelType.PROPERTY &&
      activePropertyOffenseContext &&
      !editingEntityId
    ) {
      return getPanelTitle(activePanelType, {}, !!editingEntityId);
    }

    // Check if we have offense context for organization entities
    if (
      activePanelType === PanelType.ORGANIZATION &&
      activeOrganizationOffenseContext &&
      !editingEntityId
    ) {
      return getPanelTitle(activePanelType, {}, !!editingEntityId);
    }

    // Default titles without offense context or in edit mode
    return getPanelTitle(activePanelType, undefined, !!editingEntityId);
  };
};

// Scroll to section helper
export const createScrollToSection = (
  scrollContainerRef: React.RefObject<HTMLDivElement | null>
) => {
  return (id: string) => {
    scrollToSectionUtil(id, scrollContainerRef.current);
  };
};

// Go back helper
export const createGoBack = (router: any) => {
  return () => {
    const currentPath = window.location.pathname;
    router.replace(currentPath);
  };
};
