"use client";

import { useBreadcrumbs } from "@/app/contexts/Breadcrumb/BreadcrumbContext";
import { useOrders } from "@/app/contexts/OrderContext";
import { useBreadcrumbHeader } from "@/app/hooks/useBreadcrumbHeader";
import { useRecentlyViewedTracker } from "@/app/hooks/useRecentlyViewedTracker";
import { Button } from "@/design-system/components/Button";
import { Header, SaveStatus } from "@/design-system/components/Header";
import { TextInput } from "@/design-system/components/TextInput";
import { Typography as TypographyDS } from "@/design-system/components/Typography";
import { colors } from "@/design-system/tokens";
import {
  Comment as CommentIcon,
  History as HistoryIcon,
} from "@mui/icons-material";
import {
  Box,
  FormControlLabel,
  Modal,
  Radio,
  RadioGroup
} from "@mui/material";
import { useQueryClient } from "@tanstack/react-query";
import { useRouter } from "next/navigation";
import { Order } from "proto/hero/orders/v2/orders_pb";
import {
  ApproveReviewRoundRequest,
  Report,
  ReportSection,
  ReportStatus,
  RequestChangesRequest,
  SubmitForReviewRequest,
} from "proto/hero/reports/v2/reports_pb";
import React, { useEffect, useState } from "react";
import {
  useApproveReviewRound,
  useRequestChanges,
  useSubmitForReview,
  useUpdateReportStatus,
} from "../../../apis/services/workflow/reports/v2/hooks";
import { useDispatcher } from "../../../contexts/User/DispatcherContext";
import { getIncidentLabel } from "../../../utils/utils";
import { ReassignReportPopup } from "../common/ReassignReportPopup";

interface ReportingHeaderProps {
  report: Report | undefined;
  currentOrder?: Order;
  sections?: ReportSection[];
  onClose: () => void;
  onCommentClick?: (e: React.MouseEvent<HTMLElement>) => void;
  isCommentOpen: boolean;
  saveStatuses?: SaveStatus[];
  orderType?: string;
  reviewRoundId?: string;
}

// Helper function to get report type
function getReportType(sections: ReportSection[]) {
  const incidentDetails = sections.find(
    // @ts-expect-error TODO: Fix type issue
    (s) => s.type === "SECTION_TYPE_INCIDENT_DETAILS"
  ) as any;

  if (!incidentDetails?.incidentDetails) return "Unspecified";
  const { finalType, initialType } = incidentDetails.incidentDetails;
  return getIncidentLabel(String(finalType || initialType)) || "Unspecified";
}

export default function ReportHeader({
  report,
  currentOrder,
  sections,
  onClose,
  onCommentClick,
  isCommentOpen,
  saveStatuses = [],
  orderType = "",
  reviewRoundId = "",
}: ReportingHeaderProps) {
  const router = useRouter();
  const { ordersData, isLoading, error, acknowledgeOrder } = useOrders();
  const { asset: dispatcherAsset } = useDispatcher();
  const queryClient = useQueryClient();
  const effectiveReportDate = report?.createdAt
    ? new Date(report.createdAt)
    : new Date();
  const reportedDate = effectiveReportDate
    .toISOString()
    .split("T")[0]
    .replace(/-/g, "/");

  const [isSubmitting, setIsSubmitting] = useState(false);
  const [reviewModalOpen, setReviewModalOpen] = useState(false);
  const [reviewAction, setReviewAction] = useState<"approve" | "sendToAuthor">(
    "approve"
  );
  const [reviewComment, setReviewComment] = useState("");
  const isReviewOrder = orderType === "ORDER_TYPE_REVIEW_REPORT";
  const reportId = report?.id?.replace(/[^0-9]/g, "")?.slice(0, 3) || "";

  const reportType = getReportType(sections || report?.sections || []);

  // Check if this is a supplemental report
  const isSupplementalReport = String(report?.reportType) === "REPORT_TYPE_INCIDENT_SUPPLEMENTAL";

  // Check if order is completed or non-existent
  const isOrderCompletedOrNonExistent = !currentOrder ||
    // @ts-expect-error TODO: Fix type issue
    currentOrder.status === "ORDER_STATUS_COMPLETED";

  // For supplemental reports, check if report is already approved
  const isSupplementalReportCompleted = isSupplementalReport &&
    String(report?.status) === "REPORT_STATUS_APPROVED";

  // Check if report is in a final state (completed)
  const isReportFinal = String(report?.status) === "REPORT_STATUS_APPROVED" ||
    String(report?.status) === "REPORT_STATUS_REJECTED" ||
    String(report?.status) === "REPORT_STATUS_CANCELLED";

  // Determine if submit button should be shown
  const shouldShowSubmitButton = !isReportFinal &&
    !isSupplementalReportCompleted &&
    (!isOrderCompletedOrNonExistent || isSupplementalReport);

  // Reassign popup state
  const [reassignPopupOpen, setReassignPopupOpen] = useState(false);

  const { breadcrumbs } = useBreadcrumbHeader({
    id: `report-${report?.id}`,
    label: `Report ${reportId}`,
    path: `/reports?reportId=${report?.id}`,
  });

  useRecentlyViewedTracker({
    id: `report-${report?.id}`,
    title: `REP ${reportId}`,
    subtitle: `${reportType} Report`,
    path: `/reports?reportId=${report?.id}`,
  });

  const { clearBreadcrumbs } = useBreadcrumbs();

  useEffect(() => {
    if (saveStatuses.some(s => s.isSaving)) {
      if (
        // @ts-expect-error TODO: Fix type issue
        currentOrder?.status === "ORDER_STATUS_CREATED" ||
        // @ts-expect-error TODO: Fix type issue
        currentOrder?.status === "ORDER_STATUS_ASSIGNED"
      ) {
        acknowledgeOrder(currentOrder?.id || "");
      }
    }
  }, [saveStatuses]);

  const handleReassignReport = () => {
    setReassignPopupOpen(true);
  };

  const handleReassignPopupClose = () => {
    setReassignPopupOpen(false);
  };

  const handleReassignSuccess = () => {
    // Navigate to reports page after successful reassignment
    console.log("Report reassigned successfully");
    onClose();
    handleNavigateToHomepage();
  };

  const handleSubmit = () => {
    if (!report?.id || isSubmitting) return;

    if (isSupplementalReport) {
      // For supplemental reports, update status to APPROVED
      setIsSubmitting(true);
      updateReportStatusMutation.mutate({
        id: report.id,
        status: ReportStatus.APPROVED,
      });
    } else if (isReviewOrder) {
      // For review order, open the modal
      setReviewModalOpen(true);
    } else {
      // For write or revise order, submit for review
      setIsSubmitting(true);
      submitForReviewMutation.mutate({
        reportId: report.id,
        note: "",
      } as SubmitForReviewRequest);
    }
  };

  const handleSubmitReview = () => {
    if (!report?.id || !reviewRoundId || isSubmitting) return;

    setIsSubmitting(true);

    if (reviewAction === "approve") {
      approveReviewRoundMutation.mutate({
        reviewRoundId,
        note: reviewComment,
      } as ApproveReviewRoundRequest);
    } else {
      // Send to author
      requestChangesMutation.mutate({
        reviewRoundId,
        note: reviewComment,
        sendToLevel: 0, // Send back to author
        sendToAssetId: "",
      } as RequestChangesRequest);
    }

    // Close modal
    setReviewModalOpen(false);
  };

  const handleNavigateToHomepage = () => {
    clearBreadcrumbs();
    router.push("/reports");
  };

  const handleCloseReviewModal = () => {
    setReviewModalOpen(false);
  };

  const renderReviewModal = () => {
    return (
      <Modal open={reviewModalOpen} onClose={handleCloseReviewModal}>
        <Box
          sx={{
            position: "absolute",
            top: "50%",
            left: "50%",
            transform: "translate(-50%, -50%)",
            width: 500,
            bgcolor: "background.paper",
            boxShadow: 24,
            p: 4,
            borderRadius: 3,
          }}
        >
          <Box sx={{ mb: 2 }}>
            <TypographyDS style="h2" color={colors.grey[900]}>
              Finish review
            </TypographyDS>
          </Box>

          <RadioGroup
            value={reviewAction}
            onChange={(e) =>
              setReviewAction(e.target.value as "approve" | "sendToAuthor")
            }
          >
            <FormControlLabel
              value="approve"
              control={
                <Radio sx={{ "&.Mui-checked": { color: colors.blue[600] } }} />
              }
              label="Approve"
              sx={{ color: colors.grey[900], fontFamily: "Roboto" }}
            />
            <FormControlLabel
              value="sendToAuthor"
              control={
                <Radio sx={{ "&.Mui-checked": { color: colors.blue[600] } }} />
              }
              label="Send to author"
              sx={{ color: colors.grey[900], fontFamily: "Roboto" }}
            />
          </RadioGroup>

          <Box sx={{ mt: 3 }}>
            <TextInput
              title="Add comments"
              placeholder="Add comments"
              type="multiline"
              numOfLines={4}
              value={reviewComment}
              onChange={(e: React.ChangeEvent<HTMLInputElement>) =>
                setReviewComment(e.target.value)
              }
            />
          </Box>
          <Box
            sx={{ display: "flex", justifyContent: "flex-end", mt: 3, gap: 2 }}
          >
            <Button
              label="Cancel"
              color="grey"
              prominence={false}
              onClick={handleCloseReviewModal}
            />
            <Button
              label="Submit"
              onClick={handleSubmitReview}
              isLoading={isSubmitting}
            />
          </Box>
        </Box>
      </Modal>
    );
  };

  // Submit for review mutation
  const submitForReviewMutation = useSubmitForReview({
    onSuccess: () => {
      // Update orders cache to immediately remove this order
      if (report?.id) {
        queryClient.setQueriesData(
          { queryKey: ["ordersForAsset"] },
          (oldData: any) => {
            if (!oldData?.orders) return oldData;
            return {
              ...oldData,
              orders: oldData.orders.filter(
                (order: any) => order.reportId !== report.id
              ),
            };
          }
        );
      }
      setIsSubmitting(false);
      onClose();
      handleNavigateToHomepage();
    },
    onError: (error) => {
      setIsSubmitting(false);
      console.error("Error submitting report for review:", error);
    },
  });

  // Approve review round mutation
  const approveReviewRoundMutation = useApproveReviewRound({
    onSuccess: () => {
      // Update orders cache to immediately remove this order
      if (report?.id) {
        queryClient.setQueriesData(
          { queryKey: ["ordersForAsset"] },
          (oldData: any) => {
            if (!oldData?.orders) return oldData;
            return {
              ...oldData,
              orders: oldData.orders.filter(
                (order: any) => order.reportId !== report.id
              ),
            };
          }
        );
      }
      setIsSubmitting(false);
      onClose();
      handleNavigateToHomepage();
    },
    onError: (error) => {
      setIsSubmitting(false);
      console.error("Error approving review:", error);
    },
  });

  // Request changes mutation
  const requestChangesMutation = useRequestChanges({
    onSuccess: () => {
      // Update orders cache to immediately remove this order
      if (report?.id) {
        queryClient.setQueriesData(
          { queryKey: ["ordersForAsset"] },
          (oldData: any) => {
            if (!oldData?.orders) return oldData;
            return {
              ...oldData,
              orders: oldData.orders.filter(
                (order: any) => order.reportId !== report.id
              ),
            };
          }
        );
      }
      setIsSubmitting(false);
      onClose();
      handleNavigateToHomepage();
    },
    onError: (error) => {
      setIsSubmitting(false);
      console.error("Error requesting changes:", error);
    },
  });

  // Update report status mutation
  const updateReportStatusMutation = useUpdateReportStatus({
    onSuccess: () => {
      setIsSubmitting(false);
      onClose();
      handleNavigateToHomepage();
    },
    onError: (error) => {
      setIsSubmitting(false);
      console.error("Error updating report status:", error);
    },
  });

  return (
    <>
      <Header
        breadcrumbs={breadcrumbs}
        title={`REPORT ${reportId}`}
        metadata={[
          {
            label: "Report Created",
            value: report?.createdAt ? reportedDate : "N/A"
          },
          {
            label: "Reference ID",
            value: "0315251038"
          },
          {
            label: "Type",
            value: reportType
          }
        ]}
        saveStatuses={saveStatuses}
        actions={[
          {
            label: "",
            leftIcon: <HistoryIcon sx={{ fontSize: "20px" }} />,
            color: "grey",
            prominence: false
          },
          {
            label: "",
            leftIcon: <CommentIcon sx={{ fontSize: "18px" }} />,
            color: "grey",
            prominence: isCommentOpen,
            onClick: onCommentClick
          },
          // Show reassign button unless report is completed
          ...(isReportFinal ? [] : [
            {
              label: "Reassign",
              leftIcon: (
                <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 20 21" fill="none">
                  <path d="M8.40397 10.0832C10.2449 10.0832 11.7373 8.59079 11.7373 6.74984C11.7373 4.90889 10.2449 3.4165 8.40397 3.4165C6.56302 3.4165 5.07064 4.90889 5.07064 6.74984C5.07064 8.59079 6.56302 10.0832 8.40397 10.0832Z" fill="#364153" />
                  <path d="M8.96231 10.9332C8.77897 10.9248 8.59564 10.9165 8.40397 10.9165C6.38731 10.9165 4.50397 11.4748 2.89564 12.4332C2.1623 12.8665 1.7373 13.6832 1.7373 14.5415V16.7498H9.45397C8.79564 15.8082 8.40397 14.6582 8.40397 13.4165C8.40397 12.5248 8.61231 11.6915 8.96231 10.9332Z" fill="#364153" />
                  <path d="M17.3623 13.4165C17.3623 13.2332 17.3373 13.0665 17.3123 12.8915L18.2623 12.0498L17.429 10.6082L16.2206 11.0165C15.954 10.7915 15.654 10.6165 15.3206 10.4915L15.0706 9.24984H13.404L13.154 10.4915C12.8206 10.6165 12.5206 10.7915 12.254 11.0165L11.0456 10.6082L10.2123 12.0498L11.1623 12.8915C11.1373 13.0665 11.1123 13.2332 11.1123 13.4165C11.1123 13.5998 11.1373 13.7665 11.1623 13.9415L10.2123 14.7832L11.0456 16.2248L12.254 15.8165C12.5206 16.0415 12.8206 16.2165 13.154 16.3415L13.404 17.5832H15.0706L15.3206 16.3415C15.654 16.2165 15.954 16.0415 16.2206 15.8165L17.429 16.2248L18.2623 14.7832L17.3123 13.9415C17.3373 13.7665 17.3623 13.5998 17.3623 13.4165ZM14.2373 15.0832C13.3206 15.0832 12.5706 14.3332 12.5706 13.4165C12.5706 12.4998 13.3206 11.7498 14.2373 11.7498C15.154 11.7498 15.904 12.4998 15.904 13.4165C15.904 14.3332 15.154 15.0832 14.2373 15.0832Z" fill="#364153" />
                </svg>
              ),
              color: "grey" as const,
              prominence: false,
              onClick: handleReassignReport
            }
          ]),
          // Show submit button based on proper conditions
          ...(shouldShowSubmitButton ? [
            {
              label: isSupplementalReport ? "Complete" : isReviewOrder ? "Finish Review" : "Submit",
              onClick: handleSubmit,
              isLoading: isSubmitting,
            }
          ] : [])
        ]}
      />

      {/* Review Modal */}
      {renderReviewModal()}

      {/* Reassign Report Popup */}
      <ReassignReportPopup
        open={reassignPopupOpen}
        onClose={handleReassignPopupClose}
        order={currentOrder}
        reportId={report?.id}
        onSuccess={handleReassignSuccess}
      />
    </>
  );
}