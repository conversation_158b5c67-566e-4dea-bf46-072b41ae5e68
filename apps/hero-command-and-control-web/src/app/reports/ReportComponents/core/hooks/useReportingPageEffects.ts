import { useQueryClient } from "@tanstack/react-query";
import { throttle } from "lodash";
import { useCallback, useEffect } from "react";
import { REPORT_REVIEW_ROUNDS_QUERY_KEY } from "../../../../apis/services/workflow/reports/v2/hooks";
import { ENTITY_TYPES, SCROLL_THROTTLE_MS } from "../constants";
import { findActiveSection } from "../utils/scrollUtils";
import { processReportSections } from "../utils/sectionUtils";
import { formatComments } from "../utils/utils";

interface UseReportingPageEffectsProps {
  // State
  availableNavItems: any[];
  activeSection: string;
  setActiveSection: (section: string) => void;
  scrollContainerRef: React.RefObject<HTMLDivElement | null>;
  setEditingEntityFormData: (data: any) => void;
  setEntityIdsToFetch: (ids: string[]) => void;
  setPeople: (people: any[]) => void;
  setVehicles: (vehicles: any[]) => void;
  setProperties: (properties: any[]) => void;
  setOrganizations: (properties: any[]) => void;
  setSaveStatuses: React.Dispatch<React.SetStateAction<any[]>>;
  setAssociatedCases: (cases: any[]) => void;

  // Comment setters
  setIncidentComments: (comments: any[]) => void;
  setNarrativeComments: (comments: any[]) => void;
  setPeopleComments: (comments: any[]) => void;
  setVehicleComments: (comments: any[]) => void;
  setPropertyComments: (comments: any[]) => void;
  setOrganizationComments: (comments: any[]) => void;
  setMediaComments: (comments: any[]) => void;
  setGlobalComments: (comments: any[]) => void;

  // Section ID setters
  setPeopleListSectionId: (id: string | null) => void;
  setVehicleListSectionId: (id: string | null) => void;
  setPropertyListSectionId: (id: string | null) => void;
  setOrganizationListSectionId: (id: string | null) => void;
  setOffenseListSectionId: (id: string | null) => void;
  setArrestListSectionId: (id: string | null) => void;
  setNarrativeSectionId: (id: string | null) => void;
  setIncidentDetailsSectionId: (id: string | null) => void;
  setMediaSectionId: (id: string | null) => void;
  setSectionIdToType: (mapping: Record<string, string>) => void;

  // API data
  editingEntity: any;
  reportSections: any;
  batchEntitiesData: any;
  incidentCommentData: any;
  narrativeCommentData: any;
  peopleCommentData: any;
  vehicleCommentData: any;
  propertyCommentData: any;
  organizationCommentData: any;
  mediaCommentData: any;
  globalCommentData: any;
  casesData: any;
  mediaSectionId: string | null;

  // Props
  reportId: string | null;
  isReviseOrder: boolean;

  // Handlers
  handleSaveStatusChange: (status: any) => void;
}

export const useReportingPageEffects = ({
  availableNavItems,
  activeSection,
  setActiveSection,
  scrollContainerRef,
  setEditingEntityFormData,
  setEntityIdsToFetch,
  setPeople,
  setVehicles,
  setProperties,
  setOrganizations,
  setSaveStatuses,
  setAssociatedCases,
  setIncidentComments,
  setNarrativeComments,
  setPeopleComments,
  setVehicleComments,
  setPropertyComments,
  setOrganizationComments,
  setMediaComments,
  setGlobalComments,
  setPeopleListSectionId,
  setVehicleListSectionId,
  setPropertyListSectionId,
  setOrganizationListSectionId,
  setOffenseListSectionId,
  setArrestListSectionId,
  setNarrativeSectionId,
  setIncidentDetailsSectionId,
  setMediaSectionId,
  setSectionIdToType,
  editingEntity,
  reportSections,
  batchEntitiesData,
  incidentCommentData,
  narrativeCommentData,
  peopleCommentData,
  vehicleCommentData,
  propertyCommentData,
  organizationCommentData,
  mediaCommentData,
  globalCommentData,
  casesData,
  mediaSectionId,
  reportId,
  isReviseOrder,
  handleSaveStatusChange,
}: UseReportingPageEffectsProps) => {
  const queryClient = useQueryClient();

  // Update active section based on available sections
  useEffect(() => {
    if (availableNavItems.length > 0) {
      // Only update if current activeSection is not in available items or is empty
      if (
        !activeSection ||
        !availableNavItems.some((item) => item.id === activeSection)
      ) {
        setActiveSection(availableNavItems[0].id);
      }
    } else {
      // Clear active section if no items are available
      setActiveSection("");
    }
  }, [availableNavItems, activeSection, setActiveSection]);

  // Process entity data when it's loaded
  useEffect(() => {
    if (editingEntity && editingEntity.data) {
      const formattedData: any = {};
      if (typeof editingEntity.data === "object") {
        Object.keys(editingEntity.data).forEach((sectionKey) => {
          formattedData[sectionKey] = editingEntity.data?.[sectionKey] || {};
        });
      }
      setEditingEntityFormData(formattedData);
    }
  }, [editingEntity, setEditingEntityFormData]);

  // Process report sections
  useEffect(() => {
    if (reportSections) {
      const setSectionCallbacks = {
        setPeopleListSectionId,
        setVehicleListSectionId,
        setPropertyListSectionId,
        setOrganizationListSectionId,
        setOffenseListSectionId,
        setArrestListSectionId,
        setNarrativeSectionId,
        setIncidentDetailsSectionId,
        setMediaSectionId,
        setSectionIdToType,
      };

      const { entityIds } = processReportSections(
        reportSections.sections,
        setSectionCallbacks
      );
      setEntityIdsToFetch(entityIds);

      // Clear entity arrays if no entities to fetch
      if (entityIds.length === 0) {
        setPeople([]);
        setVehicles([]);
        setProperties([]);
        setOrganizations([]);
      }
    }
  }, [
    reportSections,
    setPeopleListSectionId,
    setVehicleListSectionId,
    setPropertyListSectionId,
    setOrganizationListSectionId,
    setOffenseListSectionId,
    setArrestListSectionId,
    setNarrativeSectionId,
    setIncidentDetailsSectionId,
    setMediaSectionId,
    setSectionIdToType,
    setEntityIdsToFetch,
    setPeople,
    setVehicles,
    setProperties,
    setOrganizations,
  ]);

  // Process batch fetched entities
  useEffect(() => {
    if (batchEntitiesData?.entities) {
      const fetchedEntities = batchEntitiesData.entities;
      const peopleEntities: any[] = [];
      const vehicleEntities: any[] = [];
      const propertyEntities: any[] = [];
      const organizationEntities: any[] = [];

      fetchedEntities.forEach((entity: any) => {
        switch (entity.entityType as string) {
          case ENTITY_TYPES.PERSON:
            peopleEntities.push(entity);
            break;
          case ENTITY_TYPES.VEHICLE:
            vehicleEntities.push(entity);
            break;
          case ENTITY_TYPES.PROPERTY:
            propertyEntities.push(entity);
            break;
          case ENTITY_TYPES.ORGANIZATION:
            organizationEntities.push(entity);
            break;
        }
      });

      setPeople(peopleEntities);
      setVehicles(vehicleEntities);
      setProperties(propertyEntities);
      setOrganizations(organizationEntities);
    }
  }, [
    batchEntitiesData,
    setPeople,
    setVehicles,
    setProperties,
    setOrganizations,
  ]);

  // Update comment states when comment data loads
  useEffect(() => {
    setIncidentComments(formatComments(incidentCommentData?.comments));
  }, [incidentCommentData, setIncidentComments]);

  useEffect(() => {
    setNarrativeComments(formatComments(narrativeCommentData?.comments));
  }, [narrativeCommentData, setNarrativeComments]);

  useEffect(() => {
    setPeopleComments(formatComments(peopleCommentData?.comments));
  }, [peopleCommentData, setPeopleComments]);

  useEffect(() => {
    setVehicleComments(formatComments(vehicleCommentData?.comments));
  }, [vehicleCommentData, setVehicleComments]);

  useEffect(() => {
    setPropertyComments(formatComments(propertyCommentData?.comments));
  }, [propertyCommentData, setPropertyComments]);

  useEffect(() => {
    setOrganizationComments(formatComments(organizationCommentData?.comments));
  }, [organizationCommentData, setOrganizationComments]);

  // Ensure media comments only processes comments associated with the media section
  useEffect(() => {
    // Filter comments based on mediaSectionId before formatting
    const filteredMediaComments = mediaCommentData?.comments?.filter(
      (comment: any) => comment.sectionId === mediaSectionId
    );
    setMediaComments(formatComments(filteredMediaComments));
  }, [mediaCommentData, mediaSectionId, setMediaComments]);

  useEffect(() => {
    // Global comments don't have sectionId, format directly
    setGlobalComments(formatComments(globalCommentData?.comments));
  }, [globalCommentData, setGlobalComments]);

  // Store associated cases in state when data is loaded
  useEffect(() => {
    if (casesData?.cases) {
      setAssociatedCases(casesData.cases);
    }
  }, [casesData, setAssociatedCases]);

  // Refetch review rounds data when reportId or isReviseOrder changes
  useEffect(() => {
    if (reportId && isReviseOrder) {
      queryClient.refetchQueries({
        queryKey: [REPORT_REVIEW_ROUNDS_QUERY_KEY, reportId],
        exact: false,
      });
    }
  }, [reportId, isReviseOrder, queryClient]);

  // Scroll handling with throttling
  const handleScroll = useCallback(() => {
    if (!scrollContainerRef.current || availableNavItems.length === 0) return;

    const { activeSection: newActiveSection } = findActiveSection(
      scrollContainerRef.current,
      availableNavItems
    );

    if (newActiveSection) {
      setActiveSection(newActiveSection);
    }
  }, [availableNavItems, scrollContainerRef, setActiveSection]);

  useEffect(() => {
    const container = scrollContainerRef.current;
    if (!container || availableNavItems.length === 0) return;

    const onScroll = throttle(handleScroll, SCROLL_THROTTLE_MS);
    container.addEventListener("scroll", onScroll);
    handleScroll(); // Initial call to set active section

    return () => container.removeEventListener("scroll", onScroll);
  }, [handleScroll, availableNavItems.length]);

  // Handler for save status changes from child components
  const internalHandleSaveStatusChange = useCallback(
    (status: any) => {
      setSaveStatuses((prev: any[]) => {
        const existingIndex = prev.findIndex((s) => s.source === status.source);

        if (existingIndex >= 0) {
          const newStatuses = [...prev];
          newStatuses[existingIndex] = status;
          return newStatuses;
        } else {
          return [...prev, status];
        }
      });
    },
    [setSaveStatuses]
  );

  // Return the internal handler if no external handler is provided
  return {
    handleSaveStatusChange:
      handleSaveStatusChange || internalHandleSaveStatusChange,
  };
};
