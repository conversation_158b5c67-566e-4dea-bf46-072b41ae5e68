import { Comment as ProtoComment } from "proto/hero/reports/v2/reports_pb";
import {
  OrganizationData,
  PersonData,
  PropertyData,
  VehicleData,
} from "../../entities/EntityCard";
import { LocalComment } from "../types";

// Helper function to format comments
export const formatComments = (
  apiComments: ProtoComment[] | undefined
): LocalComment[] => {
  if (!apiComments) return [];
  return apiComments.map((comment) => ({
    id: comment.id,
    author: comment.authorAssetId,
    displayName: comment.displayName,
    date: new Date(comment.createdAt).toLocaleString(),
    text: comment.text,
    resolved: comment.resolved || false,
    sectionId: comment.sectionId,
    reportId: comment.reportId,
  }));
};

// Navigation items for sidebar
export const navItems = [
  { id: "incident-details", label: "Incident Details" },
  { id: "offenses", label: "Events" },
  { id: "arrests", label: "Arrests" },
  { id: "people", label: "People" },
  { id: "vehicles", label: "Vehicles" },
  { id: "property", label: "Property" },
  { id: "organizations", label: "Organization" },
  { id: "narrative", label: "Narrative" },
  { id: "media", label: "Media" },
];

// Get entity display name based on entity type
export const getEntityDisplayName = (entity: any): string => {
  if (!entity || !entity.data) return "Unknown Entity";

  switch (entity.entityType) {
    case "ENTITY_TYPE_PERSON": {
      const firstName = entity.data.classificationSection?.firstName || "";
      const lastName = entity.data.classificationSection?.lastName || "";
      return `${firstName} ${lastName}`.trim() || "Unnamed Person";
    }

    case "ENTITY_TYPE_VEHICLE": {
      const year = entity.data.vehicleInformationSection?.year || "";
      const make = entity.data.vehicleInformationSection?.make || "";
      const model = entity.data.vehicleInformationSection?.model || "";
      return `${year} ${make} ${model}`.trim() || "Unknown Vehicle";
    }

    case "ENTITY_TYPE_PROPERTY": {
      const category = entity.data.propertyInformationSection?.category || "";
      const desc = entity.data.propertyInformationSection?.description || "";
      return `${category} - ${desc}`.trim() || "Unknown Property";
    }

    case "ENTITY_TYPE_ORGANIZATION": {
      const name = entity.data.organizationInformationSection?.name || "";
      const type = entity.data.organizationInformationSection?.type || "";
      return `${name} - ${type}`.trim() || "Unknown Organization";
    }
    default:
      return "Unknown Entity";
  }
};

// Convert entities for card display
export const entitiesToPersonData = (entities: any[]): PersonData[] =>
  entities.map((entity) => {
    const d = entity.data || {};
    return {
      id: entity.id,
      name: `${d.classificationSection?.firstName || ""} ${
        d.classificationSection?.lastName || ""
      }`.trim(),
      sex: d.classificationSection?.sex || "",
      height: d.descriptorsSection?.height || "",
      hair: d.descriptorsSection?.hairColor || "",
      weight: d.descriptorsSection?.weight || "",
      eye: d.descriptorsSection?.eyeColor || "",
      dateOfBirth: d.classificationSection?.dateOfBirth || "",
    };
  });

export const entitiesToPropertyData = (entities: any[]): PropertyData[] =>
  entities.map((entity) => {
    const d = entity.data || {};
    return {
      id: entity.id,
      propertyType: d.propertyInformationSection?.propertyType || "",
      serialNumber: d.propertyInformationSection?.serialNumber || "",
      category: d.propertyInformationSection?.category || "",
      collectedValue: d.propertyInformationSection?.collectedValue || "",
      makeModelBrand: d.propertyInformationSection?.makeModelBrand || "",
      description: d.propertyInformationSection?.description || "",
    };
  });

export const entitiesToVehicleData = (entities: any[]): VehicleData[] =>
  entities.map((entity) => {
    const d = entity.data || {};
    return {
      id: entity.id,
      vIN: d.vehicleInformationSection?.vIN || "",
      year: d.vehicleInformationSection?.year || "",
      make: d.vehicleInformationSection?.make || "",
      model: d.vehicleInformationSection?.model || "",
      color: d.vehicleInformationSection?.color || "",
      ownerIfApplicable: d.vehicleInformationSection?.ownerIfApplicable || "",
    };
  });

export const entitiesToOrganizationData = (
  entities: any[]
): OrganizationData[] =>
  entities.map((entity) => {
    const d = entity.data || {};
    return {
      id: entity.id,
      name: d.organizationInformationSection?.name || "",
      type: d.organizationInformationSection?.type || "",
      streetAddress: d.organizationInformationSection?.streetAddress || "",
      state: d.organizationInformationSection?.state || "",
      zIP: d.organizationInformationSection?.zIP || "",
    };
  });
