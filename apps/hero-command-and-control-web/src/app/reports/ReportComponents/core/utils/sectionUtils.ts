import { ReportSection } from "proto/hero/reports/v2/reports_pb";
import { getEntityDisplayName } from "./utils";

// Process report sections to extract section IDs and entity references
export const processReportSections = (
  sections: ReportSection[] | undefined,
  setSectionCallbacks: {
    setPeopleListSectionId: (id: string | null) => void;
    setVehicleListSectionId: (id: string | null) => void;
    setPropertyListSectionId: (id: string | null) => void;
    setOrganizationListSectionId: (id: string | null) => void;
    setOffenseListSectionId: (id: string | null) => void;
    setArrestListSectionId: (id: string | null) => void;
    setNarrativeSectionId: (id: string | null) => void;
    setIncidentDetailsSectionId: (id: string | null) => void;
    setMediaSectionId: (id: string | null) => void;
    setSectionIdToType: (mapping: Record<string, string>) => void;
  }
) => {
  if (!sections) return { entityIds: [] };

  const allEntityIds: string[] = [];
  // Create mapping for section IDs to section types
  const sectionMapping: Record<string, string> = {};

  sections.forEach((section) => {
    // @ts-expect-error TODO: Fix type issue
    const sectionTypeStr = section.type as string;
    let entityRefs: Array<{ id: string }> | undefined;

    // Store section ID to type mapping
    sectionMapping[section.id] = sectionTypeStr;

    switch (sectionTypeStr) {
      case "SECTION_TYPE_ENTITY_LIST_PEOPLE":
        setSectionCallbacks.setPeopleListSectionId(section.id);
        // @ts-expect-error TODO: Fix type issue
        entityRefs = section.entityList?.entityRefs;
        break;
      case "SECTION_TYPE_ENTITY_LIST_VEHICLE":
        setSectionCallbacks.setVehicleListSectionId(section.id);
        // @ts-expect-error TODO: Fix type issue
        entityRefs = section.entityList?.entityRefs;
        break;
      case "SECTION_TYPE_ENTITY_LIST_PROPERTIES":
        setSectionCallbacks.setPropertyListSectionId(section.id);
        // @ts-expect-error TODO: Fix type issue
        entityRefs = section.entityList?.entityRefs;
        break;
      case "SECTION_TYPE_ENTITY_LIST_ORGANIZATIONS":
        setSectionCallbacks.setOrganizationListSectionId(section.id);
        // @ts-expect-error TODO: Fix type issue
        entityRefs = section.entityList?.entityRefs;
        break;
      case "SECTION_TYPE_OFFENSE":
        setSectionCallbacks.setOffenseListSectionId(section.id);
        break;
      case "SECTION_TYPE_ARREST":
        setSectionCallbacks.setArrestListSectionId(section.id);
        break;
      case "SECTION_TYPE_NARRATIVE":
        setSectionCallbacks.setNarrativeSectionId(section.id);
        break;
      case "SECTION_TYPE_INCIDENT_DETAILS":
        setSectionCallbacks.setIncidentDetailsSectionId(section.id);
        break;
      case "SECTION_TYPE_MEDIA":
        setSectionCallbacks.setMediaSectionId(section.id);
        break;
    }

    // Collect entity IDs from entity list sections
    if (entityRefs && entityRefs.length > 0) {
      const ids = entityRefs.map((ref) => ref.id);
      allEntityIds.push(...ids);
    }
  });

  // Update section ID to type mapping
  setSectionCallbacks.setSectionIdToType(sectionMapping);

  // Return the unique entity IDs to fetch
  return {
    entityIds: [...new Set(allEntityIds)],
  };
};

// Helper: Update entity display name in section
export const updateEntityDisplayNameInSection = (
  entity: any,
  sectionId: string,
  reportId: string,
  reportSections: any,
  updateReportSectionMutation: any
) => {
  if (!reportId) return;
  const section = reportSections?.sections?.find(
    (s: any) => s.id === sectionId
  );
  const sectionTypeStr = section?.type as string;
  const isEntityListSection =
    sectionTypeStr === "SECTION_TYPE_ENTITY_LIST_PEOPLE" ||
    sectionTypeStr === "SECTION_TYPE_ENTITY_LIST_VEHICLE" ||
    sectionTypeStr === "SECTION_TYPE_ENTITY_LIST_PROPERTIES" ||
    sectionTypeStr === "SECTION_TYPE_ENTITY_LIST_ORGANIZATIONS";

  if (!section || !isEntityListSection) return;

  // Create a formatted display name for the entity
  const displayName = getEntityDisplayName(entity);

  const existingRefs = section.entityList?.entityRefs;
  const entityRefs = Array.isArray(existingRefs) ? [...existingRefs] : [];

  const existingRefIndex = entityRefs.findIndex((ref) => ref.id === entity.id);

  if (existingRefIndex >= 0) {
    // Update existing reference with updated display name
    entityRefs[existingRefIndex] = {
      ...entityRefs[existingRefIndex],
      displayName,
    };

    // Create the update request
    const updateRequest = {
      reportId,
      section: {
        ...section,
        entityList: {
          ...section.entityList,
          entityRefs,
        },
      },
    };
    updateReportSectionMutation.mutate(updateRequest);
  }
};

// Helper: Create or update entity list section in report
export const updateEntityListSection = (
  title: string,
  entity: any,
  existingSectionId: string | null,
  reportId: string,
  reportSections: any,
  updateReportSectionMutation: any,
  entities: {
    people: any[];
    vehicles: any[];
    properties: any[];
    organizations: any[];
  },
  allEntitiesOverride?: any[]
) => {
  if (!reportId) return;
  if (existingSectionId) {
    // Update existing section
    const section = reportSections?.sections?.find(
      (s: any) => s.id === existingSectionId
    );

    const sectionTypeStr = section?.type as string;
    const isValidSectionType =
      sectionTypeStr === "SECTION_TYPE_ENTITY_LIST_PEOPLE" ||
      sectionTypeStr === "SECTION_TYPE_ENTITY_LIST_VEHICLE" ||
      sectionTypeStr === "SECTION_TYPE_ENTITY_LIST_PROPERTIES" ||
      sectionTypeStr === "SECTION_TYPE_ENTITY_LIST_ORGANIZATIONS";

    if (section && isValidSectionType) {
      // Get all entities of this type from our local state or use the provided override
      let allEntities;

      // Define the type for entityRefs
      let entityRefs: Array<{
        id: string;
        type: string;
        version: number;
        displayName: string;
      }> = [];
      // Use override if provided, otherwise use the current state
      if (allEntitiesOverride) {
        allEntities = allEntitiesOverride;
      } else {
        // Build complete entity references based on our local state arrays
        switch (sectionTypeStr) {
          case "SECTION_TYPE_ENTITY_LIST_PEOPLE":
            allEntities = entities.people;
            break;
          case "SECTION_TYPE_ENTITY_LIST_VEHICLE":
            allEntities = entities.vehicles;
            break;
          case "SECTION_TYPE_ENTITY_LIST_PROPERTIES":
            allEntities = entities.properties;
            break;
          case "SECTION_TYPE_ENTITY_LIST_ORGANIZATIONS":
            allEntities = entities.organizations;
            break;
        }
      }

      // Create entity references for all entities in our local state
      if (allEntities && allEntities.length > 0) {
        entityRefs = allEntities.map((localEntity) => {
          const localDisplayName = getEntityDisplayName(localEntity);
          const localEntityType = localEntity.entityType.replace(
            "ENTITY_TYPE_",
            ""
          );

          return {
            id: localEntity.id,
            type: localEntityType,
            version: localEntity.version || 1,
            displayName: localDisplayName,
          };
        });
      }

      // Create update request using the actual API format
      const updateRequest = {
        reportId,
        section: {
          ...section,
          entityList: {
            ...section.entityList,
            entityRefs: entityRefs,
          },
        },
      };

      // Submit the update
      updateReportSectionMutation.mutate(updateRequest);
    }
  }
};
