// Panel types enum
export enum PanelType {
  PERSON = "PERSON",
  VEHICLE = "VEHICLE",
  PROPERTY = "PROPERTY",
  ORGANIZATION = "ORGANIZATION",
  MEDIA = "MEDIA",
}

// Helper function to get panel title based on type
export const getPanelTitle = (
  type: PanelType | null,
  offenseContext?: {
    relationType?:
      | "victim"
      | "offender"
      | "witness"
      | "suspect"
      | "involved_party";
    isIncident?: boolean;
  } | null,
  isEditing?: boolean
): string => {
  if (!type) return "";

  // In edit mode, don't show offense context
  if (isEditing) {
    switch (type) {
      case PanelType.PERSON:
        return "Edit Person";
      case PanelType.VEHICLE:
        return "Edit Vehicle";
      case PanelType.PROPERTY:
        return "Edit Property";
      case PanelType.ORGANIZATION:
        return "Edit Organization";
      case PanelType.MEDIA:
        return "Edit Media";
      default:
        return "";
    }
  }

  switch (type) {
    case PanelType.PERSON:
      if (offenseContext?.relationType) {
        // For incidents, always show "Add Person" regardless of relation type
        if (offenseContext.isIncident) {
          return "Add Person";
        }
        
        switch (offenseContext.relationType) {
          case "victim":
            return "Add Victim";
          case "offender":
            return "Add Offender";
          case "witness":
            return "Add Witness";
          case "suspect":
            return "Add Suspect";
          case "involved_party":
            return "Add Involved Party";
          default:
            return "Add Person";
        }
      }
      return "Add Person";
    case PanelType.VEHICLE:
      if (offenseContext) {
        return "Add Vehicle to Offense";
      }
      return "Add Vehicle";
    case PanelType.PROPERTY:
      if (offenseContext) {
        return "Add Property to Offense";
      }
      return "Add Property";
    case PanelType.ORGANIZATION:
      if (offenseContext) {
        return "Add Organization to Offense";
      }
      return "Add Organization";
    case PanelType.MEDIA:
      return "Add Media";
    default:
      return "";
  }
};

export type CommentSectionType =
  | "incident"
  | "people"
  | "vehicle"
  | "property"
  | "organization"
  | "narrative"
  | "media"
  | "global";

// Comment interface for local state management
export interface LocalComment {
  id: string;
  author: string;
  displayName?: string;
  date: string;
  text: string;
  resolved: boolean;
  sectionId?: string;
  reportId: string;
}

// Interface for tracking save status of components
export interface SaveStatus {
  isSaving: boolean;
  hasUnsavedChanges: boolean;
  source: string;
}

// Navigation item interface
export interface NavItem {
  id: string;
  label: string;
}

// Interface for section IDs
export interface SectionIds {
  peopleListSectionId: string | null;
  vehicleListSectionId: string | null;
  propertyListSectionId: string | null;
  organizationListSectionId: string | null;
  narrativeSectionId: string | null;
  incidentDetailsSectionId: string | null;
  mediaSectionId: string | null;
}

// Interface for comment setters
export interface CommentSetters {
  setIncidentComments: React.Dispatch<React.SetStateAction<LocalComment[]>>;
  setNarrativeComments: React.Dispatch<React.SetStateAction<LocalComment[]>>;
  setPeopleComments: React.Dispatch<React.SetStateAction<LocalComment[]>>;
  setVehicleComments: React.Dispatch<React.SetStateAction<LocalComment[]>>;
  setPropertyComments: React.Dispatch<React.SetStateAction<LocalComment[]>>;
  setOrganizationComments: React.Dispatch<React.SetStateAction<LocalComment[]>>;
  setMediaComments: React.Dispatch<React.SetStateAction<LocalComment[]>>;
  setGlobalComments: React.Dispatch<React.SetStateAction<LocalComment[]>>;
}

// Interface for notification state
export interface NotificationState {
  open: boolean;
  message: string;
  severity: "success" | "error";
}

// Interface for entities state
export interface EntitiesState {
  people: any[];
  vehicles: any[];
  properties: any[];
  organizations: any[];
  mediaFiles: any[];
}
