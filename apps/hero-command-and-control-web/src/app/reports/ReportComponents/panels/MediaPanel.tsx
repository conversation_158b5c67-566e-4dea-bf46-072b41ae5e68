/**
 * @fileoverview MediaPanel Component - Advanced file upload and staging interface
 * 
 * This component provides a comprehensive file upload interface for media attachments in reports.
 * It handles the complete file upload workflow from initial file selection to final submission,
 * including staging, preview, metadata editing, and protobuf integration.
 * 
 * Core Functionality:
 * - Multi-file upload with drag-and-drop support
 * - File staging and preview system
 * - Real-time upload progress tracking
 * - File metadata editing (title, description, category)
 * - Type-specific file previews and icons
 * - Error handling and retry mechanisms
 * - Integration with filerepository service
 * - Protobuf data structure management
 * 
 * Technical Architecture:
 * - Uses React hooks for complex state management
 * - Integrates with React Query for API operations
 * - Implements protobuf schemas for data consistency
 * - Provides optimistic UI updates
 * - Handles concurrent file uploads
 * - Manages memory cleanup for object URLs
 * 
 * File Upload Workflow:
 * 1. User selects or drops files → Files staged with preview generation
 * 2. User edits metadata (title, category, description)
 * 3. User clicks Upload → Files uploaded to repository concurrently
 * 4. Success → FileReference objects created with protobuf
 * 5. MediaContent object created and submitted to parent
 * 
 * Supported File Types:
 * - Images (preview with thumbnails)
 * - Videos (preview with video player)
 * - PDFs (icon with filename)
 * - Audio files (icon with filename)
 * - Generic documents (fallback icon)
 * 
 * @component
 * @example
 * // Basic usage in report creation
 * <MediaPanel
 *   onSubmit={(values) => handleMediaSubmission(values)}
 *   onCancel={() => closeSidePanel()}
 * />
 * 
 * // Read-only mode for viewing
 * <MediaPanel
 *   onSubmit={() => {}}
 *   onCancel={() => {}}
 *   readOnly={true}
 * />
 * 
 * // With loading states
 * <MediaPanel
 *   onSubmit={handleSubmit}
 *   onCancel={handleCancel}
 *   isSaveLoading={isUploading}
 * />
 */

import { Button } from "@/design-system/components/Button";
import { Typography } from "@/design-system/components/Typography";
import { colors } from "@/design-system/tokens";
import { create } from "@bufbuild/protobuf";
import {
  Box
} from "@mui/material";
import {
  FileReferenceSchema,
  MediaContentSchema
} from "proto/hero/reports/v2/reports_pb";
import React, { useRef, useState } from "react";
import {
  FaFileAlt,
  FaFileAudio,
  FaFilePdf,
  FaFileVideo
} from "react-icons/fa";
import { useFileUpload } from "../../../apis/services/filerepository/hooks";
import {
  FileUploadDropZone,
  StagedFilesList,
  UploadProgressDashboard,
  type StagedFileReference
} from "./uiComponents/MediaPanel";



/**
 * Props interface for the MediaPanel component
 * 
 * @interface MediaPanelProps
 */
interface MediaPanelProps {
  /** Callback function called when form is submitted with media content */
  onSubmit: (values: any) => void;

  /** Callback function called when user cancels the operation */
  onCancel: () => void;

  /** Whether the panel is in read-only mode (disables all editing) */
  readOnly?: boolean;

  /** Whether the save operation is currently in progress */
  isSaveLoading?: boolean;
}







/**
 * MediaPanel Component
 * 
 * A sophisticated file upload interface that handles the complete media attachment
 * workflow for reports. This component manages complex state for file staging,
 * upload progress, metadata editing, and error handling.
 * 
 * State Management:
 * - stagedFiles: Array of files being prepared for upload with metadata
 * - isDragging: Visual feedback for drag-and-drop operations
 * - validationError: Current validation error message for user feedback
 * 
 * Enhanced upload progress tracking state
 * - overallProgress: Overall upload progress percentage (0-100)
 * - uploadingSummary: Detailed upload progress information
 * 
 * The component uses protobuf schemas to ensure data consistency with the backend
 * and provides real-time feedback for all upload operations.
 * 
 * @param props - The props object containing callbacks and configuration
 * @returns A rendered MediaPanel component with full upload functionality
 */
export const MediaPanel: React.FC<MediaPanelProps> = ({
  onSubmit,
  onCancel,
  readOnly = false,
  isSaveLoading = false,
}) => {
  // Refs for DOM manipulation
  const fileInputRef = useRef<HTMLInputElement>(null);

  // Refs to track active intervals for cleanup
  const activeIntervalsRef = useRef<Set<NodeJS.Timeout>>(new Set());

  // Ref to track object URLs for cleanup
  const objectUrlsRef = useRef<Set<string>>(new Set());

  // State management for file upload workflow
  const [stagedFiles, setStagedFiles] = useState<StagedFileReference[]>([]);
  const [isDragging, setIsDragging] = useState(false);
  const [validationError, setValidationError] = useState<string | null>(null);

  // Enhanced upload progress tracking state
  const [overallProgress, setOverallProgress] = useState<number>(0);
  const [uploadingSummary, setUploadingSummary] = useState<{
    totalFiles: number;
    completedFiles: number;
    failedFiles: number;
    totalBytes: number;
    uploadedBytes: number;
    averageSpeed: number;
    estimatedTimeRemaining: number;
    isComplete?: boolean;
  } | null>(null);

  // File upload hook from filerepository service
  const { uploadFile, isLoading: isFileUploading } = useFileUpload();

  // Debug logging for development and troubleshooting
  console.log("MediaPanel render:", {
    readOnly,
    isSaveLoading,
    stagedFilesCount: stagedFiles.length,
    stagedFiles: stagedFiles.map(fileData => ({
      id: fileData.id,
      displayName: fileData.displayName,
      fileId: fileData.fileId,
      isUploading: fileData.isUploading
    }))
  });

  /**
   * Determines the file category based on file type
   * 
   * Currently returns empty string to allow user specification.
   * Users can input categories like "Evidence Photo", "Crime Scene", etc.
   * 
   * @param file - The File object to categorize
   * @returns Empty string for user input
   */
  const getFileCategory = (file: File): string => {
    // Return empty string - category is now user input
    // User can specify things like "Evidence Photo", "Crime Scene", "Witness Statement", etc.
    return "";
  };

  /**
   * Creates preview URLs for supported file types
   * 
   * This function generates Object URLs for files that can be previewed
   * directly in the browser (images, videos, PDFs). For other file types,
   * it returns an empty string and the UI will show appropriate icons.
   * 
   * IMPORTANT: Object URLs are tracked for proper cleanup to prevent memory leaks.
   * 
   * @param file - The File object to create a preview for
   * @returns Object URL string or empty string for unsupported types
   */
  const createPreview = (file: File): string => {
    const fileType = file.type;
    // For images, videos and PDFs, we can create object URLs
    if (fileType.startsWith("image/") || fileType.startsWith("video/") || fileType === "application/pdf") {
      const objectUrl = URL.createObjectURL(file);
      // Track the object URL for cleanup
      objectUrlsRef.current.add(objectUrl);
      return objectUrl;
    }
    // For other file types, return empty string as we'll use icons instead
    return "";
  };

  /**
   * Cleans up object URLs for all staged files
   * 
   * This function revokes all object URLs currently tracked to prevent memory leaks.
   * It should be called when clearing all files or when the component unmounts.
   */
  const cleanupAllObjectUrls = () => {
    stagedFiles.forEach(file => {
      if (file.preview && objectUrlsRef.current.has(file.preview)) {
        URL.revokeObjectURL(file.preview);
        objectUrlsRef.current.delete(file.preview);
      }
    });
  };

  /**
   * Handles form submission and file upload process
   * 
   * This is the main submission handler that orchestrates the entire upload
   * workflow. It performs validation, handles concurrent uploads, manages
   * state updates, and creates the final protobuf structures for submission.
   * 
   * Process Flow:
   * 1. Categorize files by their current state
   * 2. Validate that all uploads are complete
   * 3. Upload any remaining staged files
   * 4. Create FileReference objects with protobuf
   * 5. Package everything into MediaContent object
   * 6. Submit to parent component
   */
  const handleSubmit = async () => {
    console.log("MediaPanel handleSubmit called");
    console.log("stagedFiles:", stagedFiles);

    // Categorize files by their current state for processing
    const filesToUpload = stagedFiles.filter(fileData => fileData.file && !fileData.fileId && !fileData.isUploading && !fileData.uploadError);
    const alreadyUploadedFiles = stagedFiles.filter(fileData => fileData.fileId && !fileData.isUploading && !fileData.uploadError);
    const pendingUploads = stagedFiles.filter(fileData => fileData.isUploading);
    const failedUploads = stagedFiles.filter(fileData => fileData.uploadError);

    console.log(`Media files status: ${filesToUpload.length} to upload, ${alreadyUploadedFiles.length} already uploaded, ${pendingUploads.length} pending, ${failedUploads.length} failed`);

    // Validation checks before proceeding
    if (pendingUploads.length > 0) {
      setValidationError("Please wait for all files to finish uploading.");
      return;
    }

    if (failedUploads.length > 0) {
      setValidationError("Some files failed to upload. Please remove them and try again.");
      return;
    }

    // Process uploads if needed
    let currentFiles = stagedFiles;

    // Only upload files that need to be uploaded
    if (filesToUpload.length > 0) {
      setValidationError(null);
      console.log(`Uploading ${filesToUpload.length} new files (${alreadyUploadedFiles.length} already uploaded)`);
      currentFiles = await handleUploadAllFiles();

      // After upload, check for failures using the returned files
      const updatedFailedUploads = currentFiles.filter(fileData => fileData.uploadError);
      if (updatedFailedUploads.length > 0) {
        setValidationError("Some files failed to upload. Please remove them and try again.");
        return;
      }
    } else if (alreadyUploadedFiles.length > 0) {
      console.log(`Using ${alreadyUploadedFiles.length} already uploaded files, no new uploads needed`);
    }

    // Final validation checks
    if (currentFiles.length === 0) {
      setValidationError("Please add at least one file.");
      return;
    }

    const successfulUploads = currentFiles.filter(fileData => fileData.fileId && !fileData.isUploading && !fileData.uploadError);

    // Final check - ensure we have successful uploads after the upload process
    if (successfulUploads.length === 0) {
      setValidationError("No files were successfully uploaded. Please try again.");
      return;
    }

    // Create FileReference objects for successfully uploaded files using protobuf
    // IMPORTANT: Backend generates FileReference IDs automatically, so we leave id empty
    const fileRefs = successfulUploads.map((file, index) => {
      // Use displayName if provided, otherwise fallback to original filename
      const finalDisplayName = file.displayName ||
        (typeof file.metadata?.originalFilename === 'string' ? file.metadata.originalFilename : '') ||
        `File ${index + 1}`;

      // Use caption if provided, otherwise leave empty (backend expects this)
      const finalCaption = file.caption || "";

      return create(FileReferenceSchema, {
        id: "", // Backend will generate this automatically
        fileId: file.fileId,
        caption: finalCaption,
        displayName: finalDisplayName,
        displayOrder: index,
        fileCategory: file.fileCategory,
        metadata: file.metadata || {},
      });
    });

    // Create MediaContent object using protobuf schema
    const mediaContent = create(MediaContentSchema, {
      id: "", // Will be generated by backend
      title: "Report Media",
      fileRefs: fileRefs,
      metadata: {
        uploadSource: "report-media-panel",
        totalFiles: fileRefs.length,
      },
    });

    // Clear validation error and submit
    setValidationError(null);

    // Wait for success animation to show (isComplete should already be true from updateOverallProgress)
    await new Promise(resolve => setTimeout(resolve, 1000));

    // Submit the media content (this will close the panel)
    onSubmit({ mediaContent });
  };

  /**
   * Handles file input selection
   * 
   * This function processes files selected through the file input element,
   * creating staged file references with protobuf schemas and preparing
   * them for preview and metadata editing.
   * 
   * @param event - The change event from the file input
   */
  const handleFileUpload = async (event: React.ChangeEvent<HTMLInputElement>) => {
    if (event.target.files) {
      const files = Array.from(event.target.files);

      for (const file of files) {
        // Create staged file reference with protobuf and UI properties
        const tempId = Date.now().toString() + Math.random().toString(36).substring(2, 9);
        const baseFileRef = create(FileReferenceSchema, {
          id: tempId,
          fileId: "", // Empty until uploaded
          caption: "",
          displayName: "",
          displayOrder: 0,
          fileCategory: getFileCategory(file), // Empty string - user will specify
          metadata: {
            originalFilename: file.name,
            fileSize: file.size,
            fileType: file.type,
          },
        });

        // Create staged file by combining protobuf object with UI properties
        const stagedFile: StagedFileReference = {
          ...baseFileRef,
          file,
          preview: createPreview(file),
          isUploading: false,
        };

        // Add to staged files for user editing
        setStagedFiles(prev => [...prev, stagedFile]);
      }
    }
  };

  /**
   * Drag and drop event handlers
   * 
   * These functions manage the drag-and-drop interface, providing visual
   * feedback and handling file drops. They maintain the isDragging state
   * for UI feedback and process dropped files similar to file input.
   */

  /** Handles drag enter events for visual feedback */
  const handleDragEnter = (event: React.DragEvent<HTMLDivElement>) => {
    event.preventDefault();
    event.stopPropagation();
    setIsDragging(true);
  };

  /** Handles drag over events to allow dropping */
  const handleDragOver = (event: React.DragEvent<HTMLDivElement>) => {
    event.preventDefault();
    event.stopPropagation();
    setIsDragging(true);
  };

  /** Handles drag leave events to remove visual feedback */
  const handleDragLeave = (event: React.DragEvent<HTMLDivElement>) => {
    event.preventDefault();
    event.stopPropagation();
    setIsDragging(false);
  };

  /**
   * Handles file drop events
   * 
   * Processes files dropped onto the drop zone, creating staged file
   * references similar to the file input handler.
   * 
   * @param event - The drop event containing the files
   */
  const handleDrop = async (event: React.DragEvent<HTMLDivElement>) => {
    event.preventDefault();
    event.stopPropagation();
    setIsDragging(false);

    if (event.dataTransfer.files) {
      const files = Array.from(event.dataTransfer.files);

      for (const file of files) {
        // Create staged file reference with protobuf and UI properties
        const tempId = Date.now().toString() + Math.random().toString(36).substring(2, 9);
        const baseFileRef = create(FileReferenceSchema, {
          id: tempId,
          fileId: "", // Empty until uploaded
          caption: "",
          displayName: "",
          displayOrder: 0,
          fileCategory: "",
          metadata: {
            originalFilename: file.name,
            fileSize: file.size,
            fileType: file.type,
          },
        });

        // Create staged file by combining protobuf object with UI properties
        const stagedFile: StagedFileReference = {
          ...baseFileRef,
          file,
          preview: createPreview(file),
          isUploading: false,
        };

        // Add to staged files
        setStagedFiles(prev => [...prev, stagedFile]);
      }
    }
  };

  /** Triggers the hidden file input click */
  const handleClickUpload = () => {
    fileInputRef.current?.click();
  };

  /**
   * Uploads all staged files concurrently
   * 
   * This function handles the bulk upload process, managing concurrent uploads
   * with proper error handling and state updates. It provides real-time feedback
   * and tracks the upload progress for each file.
   * 
   * Enhanced with detailed progress tracking including:
   * - Individual file progress percentages
   * - Upload speed calculation
   * - Time remaining estimates
   * - Overall upload progress
   * 
   * @returns Promise resolving to the updated array of staged files
   */
  const handleUploadAllFiles = async (): Promise<StagedFileReference[]> => {
    const filesToUpload = stagedFiles.filter(file => file.file && !file.fileId && !file.isUploading && !file.uploadError);

    if (filesToUpload.length === 0) {
      console.log("No files to upload - all files already uploaded or in progress");
      return stagedFiles;
    }

    console.log(`Starting upload of ${filesToUpload.length} files:`, filesToUpload.map(fileData => fileData.metadata?.originalFilename));

    // Set all files to uploading state with initial progress tracking
    setStagedFiles(prev =>
      prev.map(file =>
        filesToUpload.some(fileData => fileData.id === file.id)
          ? {
            ...file,
            isUploading: true,
            uploadError: undefined,
            uploadProgress: 0,
            uploadStartTime: Date.now(),
            bytesUploaded: 0,
            uploadSpeed: 0,
            timeRemaining: 0,
          }
          : file
      )
    );

    // Track upload results for each file
    const uploadResults: { [key: string]: { success: boolean; fileId?: string; error?: string } } = {};

    // Upload files concurrently for better performance with progress simulation
    const uploadPromises = filesToUpload.map(async (file) => {
      if (!file.file) return;

      /**
       * SMOOTH UPLOAD BEHAVIOR - File Size-Based Progress Simulation
       * 
       * To prevent janky progress updates (especially for small files), we adjust
       * the simulation parameters based on file size:
       * 
       * Small files (< 1MB):
       * - Update every 800ms (slower updates to prevent jittery appearance)
       * - Progress increments of 2-6% (smaller, more consistent jumps)
       * - Minimum 2-second upload time (prevents instant completion)
       * 
       * Medium files (< 10MB):
       * - Update every 600ms (moderate update frequency)
       * - Progress increments of 5-12% (balanced progression)
       * - Minimum 1.5-second upload time
       * 
       * Large files (10MB+):
       * - Update every 400ms (faster updates for meaningful progress)
       * - Progress increments of 8-20% (larger jumps are acceptable)
       * - Minimum 1-second upload time (actual upload will take longer anyway)
       */
      const fileSize = file.file.size;
      const isSmallFile = fileSize < 1024 * 1024; // Less than 1MB
      const isMediumFile = fileSize < 10 * 1024 * 1024; // Less than 10MB

      // Adjust update frequency and increment size based on file size
      const updateInterval = isSmallFile ? 800 : isMediumFile ? 600 : 400; // ms
      const baseIncrement = isSmallFile ? 2 : isMediumFile ? 5 : 8; // base progress increment
      const maxIncrement = isSmallFile ? 6 : isMediumFile ? 12 : 20; // max progress increment
      const minUploadTime = isSmallFile ? 2000 : isMediumFile ? 1500 : 1000; // minimum upload time in ms

      /**
       * SMOOTH UPLOAD BEHAVIOR - Realistic Progress Simulation
       * 
       * Instead of random progress jumps that can look erratic, we use:
       * - Consistent base increments with controlled randomness
       * - Progress stops at 85% to reserve final portion for completion animation
       * - File-size-appropriate update intervals to prevent jank
       */
      const progressInterval = setInterval(() => {
        setStagedFiles(prev => prev.map(fileData => {
          if (fileData.id === file.id && fileData.isUploading) {
            const currentProgress = fileData.uploadProgress || 0;
            if (currentProgress < 85) { // Don't go to 100% until actual upload completes
              // Use a more consistent increment with slight randomness
              const increment = baseIncrement + Math.random() * (maxIncrement - baseIncrement);
              const newProgress = Math.min(currentProgress + increment, 85);
              const newBytesUploaded = (newProgress / 100) * (file.file?.size || 0);

              // Direct state update - no nested setStagedFiles calls
              const timeElapsed = Date.now() - (fileData.uploadStartTime || Date.now());
              const rawUploadSpeed = timeElapsed > 0 ? (newBytesUploaded / (timeElapsed / 1000)) : 0;

              // Speed smoothing algorithm
              const previousSpeed = fileData.uploadSpeed || 0;
              const smoothingFactor = 0.3;
              const uploadSpeed = previousSpeed === 0 ? rawUploadSpeed :
                previousSpeed * (1 - smoothingFactor) + rawUploadSpeed * smoothingFactor;

              const totalBytes = fileData.file?.size || 0;
              const remainingBytes = Math.max(0, totalBytes - newBytesUploaded);

              // Conservative time estimation
              let timeRemaining = 0;
              if (uploadSpeed > 0 && remainingBytes > 0) {
                const baseTimeRemaining = remainingBytes / uploadSpeed;
                const fileSize = fileData.file?.size || 0;
                const minBuffer = fileSize < 1024 * 1024 ? 1 : 0.5;
                timeRemaining = Math.max(baseTimeRemaining, minBuffer);
              }

              // Create new staged file with updated progress (avoiding mutation)
              return {
                ...fileData,
                uploadProgress: newProgress,
                uploadSpeed,
                timeRemaining,
                bytesUploaded: newBytesUploaded,
              };
            }
          }
          return fileData;
        }));
      }, updateInterval);

      // Track interval for cleanup
      activeIntervalsRef.current.add(progressInterval);

      try {
        /**
         * SMOOTH UPLOAD BEHAVIOR - Minimum Upload Time
         * 
         * To prevent small files from completing instantly (which looks broken),
         * we enforce minimum upload times. This gives users time to see progress
         * and creates a more consistent, professional experience.
         */
        const uploadStartTime = Date.now();

        // Perform the actual upload
        const result = await uploadFile(file.file, undefined, {
          category: 'report-media',
          uploadSource: 'report-media-panel',
          originalSize: file.file.size
        });

        // Calculate remaining time to meet minimum upload time
        const actualUploadTime = Date.now() - uploadStartTime;
        const remainingTime = Math.max(0, minUploadTime - actualUploadTime);

        // Wait for minimum time if upload was too fast
        if (remainingTime > 0) {
          await new Promise(resolve => setTimeout(resolve, remainingTime));
        }

        // Clear the progress interval
        clearInterval(progressInterval);
        activeIntervalsRef.current.delete(progressInterval);

        /**
         * SMOOTH UPLOAD BEHAVIOR - Gradual Completion Animation
         * 
         * Instead of jumping from 85% to 100% instantly, we smoothly animate
         * the final 15% over a short period. This provides visual satisfaction
         * and confirms the upload completed successfully.
         * 
         * FIXED: Each file gets its own finalProgressInterval to prevent race conditions
         */
        let finalProgress = 85;
        const finalProgressInterval = setInterval(() => {
          finalProgress = Math.min(finalProgress + 5, 100);

          // Direct state update for final progress - no nested calls, preserving protobuf structure
          setStagedFiles(prev => prev.map(fileData => {
            if (fileData.id === file.id) {
              return {
                ...fileData,
                uploadProgress: finalProgress,
                bytesUploaded: file.file?.size || 0,
              };
            }
            return fileData;
          }));

          if (finalProgress >= 100) {
            clearInterval(finalProgressInterval);
            activeIntervalsRef.current.delete(finalProgressInterval);
          }
        }, 100); // Quick 100ms updates for smooth final animation

        // Track final progress interval for cleanup
        activeIntervalsRef.current.add(finalProgressInterval);

        uploadResults[file.id] = result;
      } catch (error) {
        // Clear both intervals on error to prevent memory leaks
        clearInterval(progressInterval);
        activeIntervalsRef.current.delete(progressInterval);

        uploadResults[file.id] = {
          success: false,
          error: error instanceof Error ? error.message : "Upload failed"
        };
      }
    });

    // Wait for all uploads to complete
    await Promise.all(uploadPromises);

    // Create the final updated files array with results
    // Create updated files array preserving protobuf structure
    const updatedFiles = stagedFiles.map(file => {
      const uploadResult = uploadResults[file.id];
      if (uploadResult) {
        if (uploadResult.success && uploadResult.fileId) {
          return {
            ...file,
            fileId: uploadResult.fileId,
            isUploading: false,
            uploadError: undefined,
            uploadProgress: 100,
          };
        } else {
          return {
            ...file,
            isUploading: false,
            uploadError: uploadResult.error || "Upload failed",
            uploadProgress: 0,
          };
        }
      }
      return file;
    });

    // Update state with final results
    setStagedFiles(updatedFiles);

    const finalSuccessful = updatedFiles.filter(fileData => fileData.fileId && !fileData.uploadError);
    const finalFailed = updatedFiles.filter(fileData => fileData.uploadError);
    console.log(`Upload complete: ${finalSuccessful.length} successful, ${finalFailed.length} failed`);

    return updatedFiles;
  };

  /**
   * Metadata editing handlers
   * 
   * These functions handle updates to file metadata (title, description, category)
   * while maintaining immutable state updates for React optimization.
   */

  /** Updates the display name for a specific file */
  const handleDisplayNameChange = (id: string, displayName: string) => {
    setStagedFiles((prevFiles) =>
      prevFiles.map((file) => (file.id === id ? { ...file, displayName } : file))
    );
  };

  /** Updates the caption/description for a specific file */
  const handleCaptionChange = (id: string, caption: string) => {
    setStagedFiles((prevFiles) =>
      prevFiles.map((file) =>
        file.id === id ? { ...file, caption } : file
      )
    );
  };

  /** Updates the category for a specific file */
  const handleCategoryChange = (id: string, fileCategory: string) => {
    setStagedFiles((prevFiles) =>
      prevFiles.map((file) =>
        file.id === id ? { ...file, fileCategory } : file
      )
    );
  };

  /** Removes a file from the staged files list */
  const handleRemoveFile = (id: string) => {
    setStagedFiles((prevFiles) => {
      const fileToRemove = prevFiles.find(file => file.id === id);

      // Clean up object URL if it exists
      if (fileToRemove?.preview && objectUrlsRef.current.has(fileToRemove.preview)) {
        URL.revokeObjectURL(fileToRemove.preview);
        objectUrlsRef.current.delete(fileToRemove.preview);
      }

      return prevFiles.filter((fileData) => fileData.id !== id);
    });
  };

  /**
   * Renders file preview based on MIME type
   * 
   * This function creates appropriate preview components for different file types,
   * providing visual feedback and thumbnails where possible. It handles images,
   * videos, PDFs, audio files, and generic documents with fallback icons.
   * 
   * @param file - The staged file reference to render
   * @returns JSX element representing the file preview
   */
  const renderFilePreview = (file: StagedFileReference) => {
    const fileType = typeof file.metadata?.fileType === 'string' ? file.metadata.fileType : '';
    const originalFilename = typeof file.metadata?.originalFilename === 'string' ? file.metadata.originalFilename : 'Unknown';

    // Primary detection based on MIME type for images
    if (fileType.startsWith('image/')) {
      return (
        <Box
          sx={{
            width: "100%",
            height: "100%",
            backgroundImage: file.preview ? `url(${file.preview})` : 'none',
            backgroundSize: "cover",
            backgroundPosition: "center",
            cursor: "pointer",
          }}
        />
      );
    }

    // Video files with preview support
    if (fileType.startsWith('video/')) {
      return file.preview ? (
        <video
          src={file.preview}
          controls
          style={{
            width: "100%",
            height: "100%",
            objectFit: "cover",
            cursor: "pointer",
          }}
        />
      ) : (
        <Box
          sx={{
            display: "flex",
            flexDirection: "column",
            alignItems: "center",
            justifyContent: "center",
            height: "100%",
            backgroundColor: colors.grey[100],
            padding: "12px",
            cursor: "pointer",
          }}
        >
          <FaFileVideo style={{ fontSize: 32, color: colors.blue[600] }} />
          <Box
            sx={{
              width: "100%",
              mt: 1,
              textAlign: "center",
              display: "-webkit-box",
              WebkitLineClamp: 2,
              WebkitBoxOrient: "vertical",
              overflow: "hidden",
              textOverflow: "ellipsis",
              wordBreak: "break-word",
            }}
          >
            <Typography style="body3" color={colors.grey[700]}>
              {originalFilename}
            </Typography>
          </Box>
        </Box>
      );
    }

    // PDF files
    if (fileType === 'application/pdf') {
      return (
        <Box
          sx={{
            display: "flex",
            flexDirection: "column",
            alignItems: "center",
            justifyContent: "center",
            height: "100%",
            backgroundColor: colors.grey[100],
            padding: "12px",
            cursor: "pointer",
          }}
        >
          <FaFilePdf style={{ fontSize: 32, color: colors.grey[600] }} />
          <Box
            sx={{
              width: "100%",
              mt: 1,
              textAlign: "center",
              display: "-webkit-box",
              WebkitLineClamp: 2,
              WebkitBoxOrient: "vertical",
              overflow: "hidden",
              textOverflow: "ellipsis",
              wordBreak: "break-word",
            }}
          >
            <Typography style="body3" color={colors.grey[700]}>
              {originalFilename}
            </Typography>
          </Box>
        </Box>
      );
    }

    // Audio files
    if (fileType.startsWith('audio/')) {
      return (
        <Box
          sx={{
            display: "flex",
            flexDirection: "column",
            alignItems: "center",
            justifyContent: "center",
            height: "100%",
            backgroundColor: colors.grey[100],
            padding: "12px",
            cursor: "pointer",
          }}
        >
          <FaFileAudio style={{ fontSize: 32, color: colors.blue[600] }} />
          <Box
            sx={{
              width: "100%",
              mt: 1,
              textAlign: "center",
              display: "-webkit-box",
              WebkitLineClamp: 2,
              WebkitBoxOrient: "vertical",
              overflow: "hidden",
              textOverflow: "ellipsis",
              wordBreak: "break-word",
            }}
          >
            <Typography style="body3" color={colors.grey[700]}>
              {originalFilename}
            </Typography>
          </Box>
        </Box>
      );
    }

    // Fallback to category-based detection if MIME type is not available
    if (!fileType && file.fileCategory) {
      const category = file.fileCategory.toLowerCase();
      if (category.includes('photo') || category.includes('image')) {
        return (
          <Box
            sx={{
              width: "100%",
              height: "100%",
              backgroundImage: file.preview ? `url(${file.preview})` : 'none',
              backgroundSize: "cover",
              backgroundPosition: "center",
              cursor: "pointer",
            }}
          />
        );
      }
      if (category.includes('video')) {
        return (
          <Box
            sx={{
              display: "flex",
              flexDirection: "column",
              alignItems: "center",
              justifyContent: "center",
              height: "100%",
              backgroundColor: colors.grey[100],
              padding: "12px",
              cursor: "pointer",
            }}
          >
            <FaFileVideo style={{ fontSize: 32, color: colors.blue[600] }} />
            <Box
              sx={{
                width: "100%",
                mt: 1,
                textAlign: "center",
                display: "-webkit-box",
                WebkitLineClamp: 2,
                WebkitBoxOrient: "vertical",
                overflow: "hidden",
                textOverflow: "ellipsis",
                wordBreak: "break-word",
              }}
            >
              <Typography style="body3" color={colors.grey[700]}>
                {originalFilename}
              </Typography>
            </Box>
          </Box>
        );
      }
    }

    // Default fallback for unknown file types
    return (
      <Box
        sx={{
          display: "flex",
          flexDirection: "column",
          alignItems: "center",
          justifyContent: "center",
          height: "100%",
          backgroundColor: colors.grey[100],
          padding: "12px",
          cursor: "pointer",
        }}
      >
        <FaFileAlt style={{ fontSize: 32, color: colors.grey[500] }} />
        <Box
          sx={{
            width: "100%",
            mt: 1,
            textAlign: "center",
            display: "-webkit-box",
            WebkitLineClamp: 2,
            WebkitBoxOrient: "vertical",
            overflow: "hidden",
            textOverflow: "ellipsis",
            wordBreak: "break-word",
          }}
        >
          <Typography style="body3" color={colors.grey[700]}>
            {originalFilename}
          </Typography>
        </Box>
      </Box>
    );
  };

  /**
   * Utility functions for upload progress tracking
   */

  /**
   * Formats bytes to human-readable format
   * @param bytes - Number of bytes
   * @returns Formatted string (e.g., "2.5 MB")
   */
  const formatBytes = (bytes: number): string => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  /**
   * Formats time duration to human-readable format
   * @param seconds - Duration in seconds
   * @returns Formatted string (e.g., "2m 30s")
   */
  const formatDuration = (seconds: number): string => {
    if (seconds < 60) return `${Math.round(seconds)}s`;
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = Math.round(seconds % 60);
    return `${minutes}m ${remainingSeconds}s`;
  };

  /**
   * Calculates upload speed in bytes per second
   * @param bytesUploaded - Number of bytes uploaded
   * @param timeElapsed - Time elapsed in milliseconds
   * @returns Upload speed in bytes per second
   */
  const calculateUploadSpeed = (bytesUploaded: number, timeElapsed: number): number => {
    if (timeElapsed === 0) return 0;
    return (bytesUploaded / (timeElapsed / 1000));
  };

  /**
   * Updates upload progress for a specific file
   * 
   * SMOOTH UPLOAD BEHAVIOR - Enhanced Progress Updates
   * This function provides smooth, realistic progress updates that avoid
   * the jarring effects of rapid fluctuations, especially for small files.
   * 
   * @param fileId - ID of the file being updated
   * @param progress - Progress percentage (0-100)
   * @param bytesUploaded - Number of bytes uploaded
   */
  const updateFileProgress = (fileId: string, progress: number, bytesUploaded: number) => {
    setStagedFiles(prev => prev.map(file => {
      if (file.id === fileId) {
        const timeElapsed = Date.now() - (file.uploadStartTime || Date.now());
        const rawUploadSpeed = calculateUploadSpeed(bytesUploaded, timeElapsed);

        /**
         * SMOOTH UPLOAD BEHAVIOR - Speed Smoothing Algorithm
         * 
         * Raw upload speed calculations can fluctuate wildly, especially for small files.
         * We use exponential smoothing to create more stable, realistic speed readings:
         * 
         * - smoothingFactor of 0.3 means 70% previous speed + 30% new calculation
         * - This prevents jarring speed jumps from 500KB/s to 2MB/s to 200KB/s
         * - Results in more believable, gradually changing speed indicators
         */
        const previousSpeed = file.uploadSpeed || 0;
        const smoothingFactor = 0.3; // Adjust this value to control smoothing (0 = no smoothing, 1 = no change)
        const uploadSpeed = previousSpeed === 0 ? rawUploadSpeed :
          previousSpeed * (1 - smoothingFactor) + rawUploadSpeed * smoothingFactor;

        const totalBytes = file.file?.size || 0;
        const remainingBytes = Math.max(0, totalBytes - bytesUploaded);

        /**
         * SMOOTH UPLOAD BEHAVIOR - Conservative Time Estimation
         * 
         * Time remaining calculations for small files can be unrealistic or show "0s"
         * when there's clearly still time left. We add minimum buffers to prevent this:
         * 
         * - Files < 1MB get a 1-second minimum buffer
         * - Larger files get a 0.5-second minimum buffer
         * - This prevents "0s remaining" when there's obviously still time left
         * - Creates more realistic user expectations
         */
        let timeRemaining = 0;
        if (uploadSpeed > 0 && remainingBytes > 0) {
          const baseTimeRemaining = remainingBytes / uploadSpeed;
          // Add a minimum time buffer for small files to avoid showing "0s remaining"
          const fileSize = file.file?.size || 0;
          const minBuffer = fileSize < 1024 * 1024 ? 1 : 0.5; // 1s for files < 1MB, 0.5s for larger
          timeRemaining = Math.max(baseTimeRemaining, minBuffer);
        }

        return {
          ...file,
          uploadProgress: progress,
          uploadSpeed,
          timeRemaining,
          bytesUploaded,
        };
      }
      return file;
    }));
  };

  /**
   * Calculates and updates overall upload progress
   * 
   * SMOOTH UPLOAD BEHAVIOR - Overall Progress Dashboard
   * This function aggregates individual file progress into meaningful overall statistics
   * that provide users with a clear understanding of the total upload status.
   */
  const updateOverallProgress = () => {
    const uploadingFiles = stagedFiles.filter(fileData => fileData.isUploading || fileData.uploadProgress !== undefined);
    if (uploadingFiles.length === 0) {
      setOverallProgress(0);
      setUploadingSummary(null);
      return;
    }

    const totalFiles = uploadingFiles.length;
    const completedFiles = stagedFiles.filter(fileData => fileData.fileId && !fileData.isUploading).length;
    const failedFiles = stagedFiles.filter(fileData => fileData.uploadError).length;

    /**
     * SMOOTH UPLOAD BEHAVIOR - Byte-Based Progress Calculation
     * 
     * Instead of simple file counting (which could show 50% when one small file
     * and one large file are uploading), we calculate progress based on actual
     * data transferred. This provides more accurate progress representation.
     */
    const totalBytes = uploadingFiles.reduce((sum, file) => sum + (file.file?.size || 0), 0);
    const uploadedBytes = uploadingFiles.reduce((sum, file) => sum + (file.bytesUploaded || 0), 0);

    /**
     * SMOOTH UPLOAD BEHAVIOR - Average Speed Calculation
     * 
     * We only consider actively uploading files with valid speed readings
     * to prevent completed files from skewing the average speed calculation.
     * This provides more accurate "current upload speed" information.
     */
    const activeUploads = uploadingFiles.filter(fileData => fileData.isUploading && fileData.uploadSpeed);
    const averageSpeed = activeUploads.length > 0
      ? activeUploads.reduce((sum, file) => sum + (file.uploadSpeed || 0), 0) / activeUploads.length
      : 0;

    const remainingBytes = totalBytes - uploadedBytes;
    const estimatedTimeRemaining = averageSpeed > 0 ? remainingBytes / averageSpeed : 0;

    const overallProgressPercent = totalBytes > 0 ? (uploadedBytes / totalBytes) * 100 : 0;

    // Check if all uploads are complete (no files are currently uploading and all have fileIds or errors)
    const allUploadsComplete = uploadingFiles.length > 0 &&
      uploadingFiles.every(fileData => !fileData.isUploading && (fileData.fileId || fileData.uploadError));

    setOverallProgress(overallProgressPercent);
    setUploadingSummary({
      totalFiles,
      completedFiles,
      failedFiles,
      totalBytes,
      uploadedBytes,
      averageSpeed,
      estimatedTimeRemaining,
      isComplete: allUploadsComplete,
    });
  };

  // Update overall progress when staged files change
  React.useEffect(() => {
    updateOverallProgress();
  }, [stagedFiles]);

  // Cleanup intervals on component unmount to prevent memory leaks
  React.useEffect(() => {
    return () => {
      // Clear all active intervals when component unmounts
      activeIntervalsRef.current.forEach(interval => {
        clearInterval(interval);
      });
      activeIntervalsRef.current.clear();

      // Revoke all object URLs to prevent memory leaks
      objectUrlsRef.current.forEach(url => {
        URL.revokeObjectURL(url);
      });
      objectUrlsRef.current.clear();
    };
  }, []);

  return (
    <>
      {/* Panel Header */}
      <Box sx={{ mb: 3 }}>
        <Typography style="h1" color={colors.grey[900]}>
          Add Media
        </Typography>
      </Box>

      {/* File Upload Drop Zone / Upload Progress Dashboard - Seamless transition */}
      {!readOnly && (
        <Box
          sx={{
            mb: 4,
            transition: "all 0.3s ease-in-out",
            minHeight: uploadingSummary ? "auto" : "140px", // Maintain consistent height
          }}
        >
          {uploadingSummary ? (
            // Upload Progress Dashboard - Shows when uploads are active
            <UploadProgressDashboard
              overallProgress={overallProgress}
              uploadingSummary={uploadingSummary}
              formatBytes={formatBytes}
              formatDuration={formatDuration}
            />
          ) : (
            // File Upload Drop Zone - Shows when no uploads are active
            <FileUploadDropZone
              isDragging={isDragging}
              onDragEnter={handleDragEnter}
              onDragOver={handleDragOver}
              onDragLeave={handleDragLeave}
              onDrop={handleDrop}
              onClick={handleClickUpload}
              fileInputRef={fileInputRef}
              onFileSelect={handleFileUpload}
            />
          )}
        </Box>
      )}

      {/* Staged Files List - Shows files ready for upload/editing */}
      {stagedFiles.length > 0 && (
        <StagedFilesList
          stagedFiles={stagedFiles}
          readOnly={readOnly}
          onDisplayNameChange={handleDisplayNameChange}
          onCaptionChange={handleCaptionChange}
          onCategoryChange={handleCategoryChange}
          onRemoveFile={handleRemoveFile}
          renderFilePreview={renderFilePreview}
          formatBytes={formatBytes}
          formatDuration={formatDuration}
        />
      )}

      {/* Sticky Bottom Action Bar - Contains validation errors and action buttons */}
      {!readOnly && (
        <Box
          sx={{
            position: "fixed",
            bottom: 0,
            left: 0,
            right: 0,
            padding: "16px 24px",
            backgroundColor: "white",
            borderTop: "1px solid #E0E0E0",
            display: "flex",
            justifyContent: "space-between",
            alignItems: "center",
            gap: 2,
            zIndex: 10,
          }}
        >
          {/* Validation Error Display */}
          {validationError && (
            <Box sx={{ display: "flex", alignItems: "center", flex: 1 }}>
              <Typography style="body2" color={colors.rose[600]}>
                {validationError}
              </Typography>
            </Box>
          )}

          {/* Action Buttons */}
          <Box sx={{ display: "flex", gap: 2, marginLeft: "auto" }}>
            <Button
              label="Cancel"
              color="grey"
              prominence={false}
              onClick={() => {
                console.log("Cancel button clicked");
                // Clean up object URLs before closing
                cleanupAllObjectUrls();
                onCancel();
              }}
              disabled={isSaveLoading}
            />
            <Button
              label="Upload"
              color="blue"
              prominence={true}
              onClick={() => {
                console.log("Upload button clicked - starting handleSubmit");
                console.log("Button state:", { isSaveLoading, disabled: isSaveLoading });
                handleSubmit();
              }}
              isLoading={isSaveLoading}
              disabled={isSaveLoading}
            />
          </Box>
        </Box>
      )}

      {/* Bottom Spacing - Prevents content from being hidden behind sticky bar */}
      <Box sx={{ height: "100px" }} />
    </>
  );
}; 