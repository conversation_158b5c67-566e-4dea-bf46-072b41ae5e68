import { Button } from "@/design-system/components/Button";
import { Checkbox } from "@/design-system/components/Checkbox";
import { Dropdown, DropdownOption } from "@/design-system/components/Dropdown";
import { TextInput } from "@/design-system/components/TextInput";
import { Typography } from "@/design-system/components/Typography";
import { colors } from "@/design-system/tokens";
import AddIcon from "@mui/icons-material/Add";
import RemoveIcon from "@mui/icons-material/Remove";
import { Box } from "@mui/material";
import React, { useState } from "react";

interface PersonInfo {
  firstName?: string;
  middleName?: string;
  lastName?: string;
  dateOfBirth?: string;
  sex?: string;
  race?: string;
  ssn?: string;
}

interface OrganizationInfo {
  name: string;
  type?: string;
  streetAddress?: string;
  state?: string;
  zip?: string;
}

interface AdditionalVictimQuestionsProps {
  personInfo?: PersonInfo;
  organizationInfo?: OrganizationInfo;
  isOrganization?: boolean;
  victimNumber?: string;
  existingData?: any;
  isEditMode?: boolean;
  onBack?: () => void;
  onSaveToReport?: (additionalVictimData: any) => void;
  readOnly?: boolean;
  isSaveAndAddAnotherLoading?: boolean;
  isSaveToReportLoading?: boolean;
}

// Mock dropdown options - these would come from your data source
const victimTypeOptions: DropdownOption[] = [
  { value: "individual", label: "Individual" },
  { value: "business", label: "Business" },
  { value: "government", label: "Government" },
  { value: "religious_organization", label: "Religious Organization" },
  { value: "society_public", label: "Society/Public" },
  { value: "other", label: "Other" },
];

const residentStatusOptions: DropdownOption[] = [
  { value: "resident", label: "Resident" },
  { value: "nonresident", label: "Non-resident" },
  { value: "unknown", label: "Unknown" },
];

const officerActivityOptions: DropdownOption[] = [
  { value: "on_duty", label: "On Duty" },
  { value: "off_duty", label: "Off Duty" },
  { value: "unknown", label: "Unknown" },
];

const assignmentTypeOptions: DropdownOption[] = [
  { value: "patrol", label: "Patrol" },
  { value: "detective", label: "Detective" },
  { value: "other", label: "Other" },
];

const injuryTypeOptions: DropdownOption[] = [
  { value: "none", label: "None" },
  { value: "apparent_minor_injuries", label: "Apparent Minor Injuries" },
  { value: "apparent_major_injuries", label: "Apparent Major Injuries" },
  { value: "possible_internal_injury", label: "Possible Internal Injury" },
  { value: "severe_laceration", label: "Severe Laceration" },
  { value: "loss_of_teeth", label: "Loss of Teeth" },
  { value: "broken_bones", label: "Broken Bones" },
  { value: "unconsciousness", label: "Unconsciousness" },
  { value: "other_major_injury", label: "Other Major Injury" },
];

export const AdditionalVictimQuestions: React.FC<
  AdditionalVictimQuestionsProps
> = ({
  personInfo,
  organizationInfo,
  isOrganization = false,
  existingData,
  isEditMode = false,
  onBack,
  onSaveToReport,
  readOnly = false,
  isSaveAndAddAnotherLoading = false,
  isSaveToReportLoading = false,
}) => {
  const [formData, setFormData] = useState({
    victimType: existingData?.victimType || "",
    residentStatus: existingData?.residentStatus || "",
    isReportingParty: existingData?.isReportingParty || false,
    officerActivity: existingData?.officerActivity || "",
    assignmentType: existingData?.assignmentType || "",
    oriOtherJurisdiction: existingData?.oriOtherJurisdiction || "",
  });

  const [injuries, setInjuries] = useState<string[]>(
    existingData?.injuries || []
  );

  const handleAddInjury = () => {
    if (injuries.length < 5) {
      setInjuries([...injuries, ""]);
    }
  };

  const handleRemoveInjury = (index: number) => {
    const newInjuries = [...injuries];
    newInjuries.splice(index, 1);
    setInjuries(newInjuries);
  };

  const handleInjuryTypeChange = (index: number, value: string) => {
    const newInjuries = [...injuries];
    newInjuries[index] = value;
    setInjuries(newInjuries);
  };

  const handleSaveToReport = () => {
    // Create metadata structure that matches VictimsSection.tsx
    const victimData = {
      victimType: formData.victimType,
      residentStatus: formData.residentStatus,
      isReportingParty: formData.isReportingParty,
      injuries: injuries.filter((injury) => injury !== ""), // Remove empty injuries
      // Additional fields from the form
      officerActivity: formData.officerActivity,
      assignmentType: formData.assignmentType,
      oriOtherJurisdiction: formData.oriOtherJurisdiction,
    };

    onSaveToReport?.(victimData);
  };

  const getDisplayName = () => {
    if (isOrganization && organizationInfo) {
      return organizationInfo.name || "Organization";
    }
    if (personInfo?.firstName || personInfo?.lastName) {
      return `${personInfo.firstName || ""} ${personInfo.middleName || ""} ${
        personInfo.lastName || ""
      }`.trim();
    }
    return "Person";
  };

  const formatEntityInfo = () => {
    if (isOrganization && organizationInfo) {
      const parts = [];
      parts.push(
        organizationInfo.type ? `Type: ${organizationInfo.type}` : "Type: ---"
      );
      parts.push(
        organizationInfo.streetAddress
          ? `Address: ${organizationInfo.streetAddress}`
          : "Address: ---"
      );
      parts.push(
        organizationInfo.state
          ? `State: ${organizationInfo.state}`
          : "State: ---"
      );
      parts.push(
        organizationInfo.zip ? `ZIP: ${organizationInfo.zip}` : "ZIP: ---"
      );
      return parts.join(", ");
    } else {
      const parts = [];
      parts.push(
        personInfo?.dateOfBirth ? `DOB: ${personInfo.dateOfBirth}` : "DOB: ---"
      );
      parts.push(personInfo?.sex ? `Sex: ${personInfo.sex}` : "Sex: ---");
      parts.push(personInfo?.race ? `Race: ${personInfo.race}` : "Race: ---");
      parts.push(personInfo?.ssn ? `SSN: ${personInfo.ssn}` : "SSN: ---");
      return parts.join(", ");
    }
  };

  return (
    <Box
      sx={{
        p: 1,
        pb: 12,
        position: "relative",
        height: "100%",
        overflow: "auto",
      }}
    >
      {/* Header */}
      <Box sx={{ mb: 3 }}>
        <Typography style="h1" color={colors.grey[900]}>
          Victim Details
        </Typography>

        {/* Entity info */}
        <Box
          sx={{
            mt: 3,
            p: 2,
            backgroundColor: colors.grey[50],
            borderRadius: "8px",
            border: `1px solid ${colors.grey[200]}`,
          }}
        >
          <Box sx={{ mb: 1 }}>
            <Typography style="body3" color={colors.grey[900]}>
              {getDisplayName()}
            </Typography>
          </Box>
          <Typography style="tag2" color={colors.grey[600]}>
            {formatEntityInfo()}
          </Typography>
        </Box>
      </Box>

      {/* Form Fields */}
      <Box sx={{ display: "flex", flexDirection: "column", gap: 1.5 }}>
        {/* Victim Type */}
        <Dropdown
          title="Victim Type"
          placeholder="Select Type"
          options={victimTypeOptions}
          value={formData.victimType}
          onChange={(value) =>
            setFormData({ ...formData, victimType: value || "" })
          }
          readOnly={readOnly}
        />

        {/* Resident Status */}
        <Dropdown
          title="Resident Status"
          placeholder="Select Status"
          options={residentStatusOptions}
          value={formData.residentStatus}
          onChange={(value) =>
            setFormData({ ...formData, residentStatus: value || "" })
          }
          readOnly={readOnly}
        />

        {/* Officer Activity and Assignment Type - Side by side */}
        <Box sx={{ display: "flex", gap: 3 }}>
          <Box sx={{ flex: 1 }}>
            <Dropdown
              title="Officer Activity"
              placeholder="Select Officer Activity"
              options={officerActivityOptions}
              value={formData.officerActivity}
              onChange={(value) =>
                setFormData({ ...formData, officerActivity: value || "" })
              }
              readOnly={readOnly}
            />
          </Box>
          <Box sx={{ flex: 1 }}>
            <Dropdown
              title="Assignment Type"
              placeholder="Select Assignment Type"
              options={assignmentTypeOptions}
              value={formData.assignmentType}
              onChange={(value) =>
                setFormData({ ...formData, assignmentType: value || "" })
              }
              readOnly={readOnly}
            />
          </Box>
        </Box>

        {/* ORI Other Jurisdiction */}
        <TextInput
          title="ORI Other Jurisdiction"
          placeholder="Enter ORI"
          value={formData.oriOtherJurisdiction}
          onChange={(e: React.ChangeEvent<HTMLInputElement>) =>
            setFormData({ ...formData, oriOtherJurisdiction: e.target.value })
          }
          readOnly={readOnly}
        />

        {/* Reporting Party Section */}
        <Box sx={{ mt: 3 }}>
          <Box sx={{ mb: 2 }}>
            <Typography style="caps1" color={colors.grey[900]}>
              REPORTING PARTY
            </Typography>
          </Box>
          <Checkbox
            label="Victim was also Reporting Party"
            size="small"
            checked={formData.isReportingParty}
            onChange={(e: React.ChangeEvent<HTMLInputElement>) =>
              setFormData({
                ...formData,
                isReportingParty: e.target.checked,
              })
            }
            disabled={readOnly}
          />
        </Box>

        {/* Incident Related Injuries Section */}
        <Box sx={{ mt: 4 }}>
          <Box sx={{ mb: 1 }}>
            <Typography style="caps1" color={colors.grey[900]}>
              INCIDENT RELATED INJURIES
            </Typography>
          </Box>
          <Box sx={{ mb: 2 }}>
            <Typography style="body3" color={colors.grey[500]}>
              Add up to 5
            </Typography>
          </Box>

          {/* Show first injury dropdown if no injuries exist */}
          {injuries.length === 0 && (
            <Box sx={{ mb: 2 }}>
              <Dropdown
                title="Injury Type"
                placeholder="Select Type"
                options={injuryTypeOptions}
                value=""
                onChange={(value) => {
                  if (value) {
                    setInjuries([value]);
                  }
                }}
                readOnly={readOnly}
                enableSearch
              />
            </Box>
          )}

          {/* Render existing injuries */}
          {injuries.map((injury, index) => (
            <Box
              key={index}
              sx={{ mb: 2, display: "flex", alignItems: "end", gap: 2 }}
            >
              <Box sx={{ flex: 1 }}>
                <Dropdown
                  title={
                    index === 0 ? "Injury Type" : `Injury Type ${index + 1}`
                  }
                  placeholder="Select Type"
                  options={injuryTypeOptions}
                  value={injury}
                  onChange={(value) =>
                    handleInjuryTypeChange(index, value || "")
                  }
                  readOnly={readOnly}
                  enableSearch
                />
              </Box>
              {!readOnly && injuries.length > 1 && (
                <Button
                  label=""
                  rightIcon={<RemoveIcon />}
                  style="ghost"
                  color="grey"
                  size="medium"
                  onClick={() => handleRemoveInjury(index)}
                />
              )}
            </Box>
          ))}

          {/* Add Injury Button */}
          {!readOnly && injuries.length < 5 && (
            <Button
              label="Add Injury"
              style="ghost"
              color="blue"
              size="medium"
              leftIcon={<AddIcon />}
              onClick={handleAddInjury}
            />
          )}
        </Box>
      </Box>

      {/* Sticky bottom bar */}
      {!readOnly && (
        <Box
          sx={{
            position: "fixed",
            bottom: 0,
            left: 0,
            right: 0,
            padding: "16px 24px",
            backgroundColor: "white",
            borderTop: "1px solid #E0E0E0",
            display: "flex",
            justifyContent: "space-between",
            alignItems: "center",
            gap: 2,
            zIndex: 10,
          }}
        >
          {/* Action buttons */}
          <Box sx={{ display: "flex", gap: 2, marginLeft: "auto" }}>
            <Button
              label="Back"
              color="grey"
              prominence={false}
              onClick={onBack}
              disabled={isSaveAndAddAnotherLoading || isSaveToReportLoading}
            />
            <Button
              label="Done"
              color="blue"
              prominence={true}
              onClick={handleSaveToReport}
              isLoading={isSaveToReportLoading}
              disabled={isSaveAndAddAnotherLoading || isSaveToReportLoading}
            />
          </Box>
        </Box>
      )}
    </Box>
  );
};
