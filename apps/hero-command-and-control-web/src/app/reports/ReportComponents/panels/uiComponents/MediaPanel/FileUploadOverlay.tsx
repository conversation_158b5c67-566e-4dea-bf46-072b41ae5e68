import React from "react";
import { Box, CircularProgress } from "@mui/material";
import { Typography } from "@/design-system/components/Typography";

/**
 * File Upload Overlay Component
 * 
 * Shows upload progress overlay on individual files during upload,
 * including circular progress, percentage, speed, and time remaining.
 */
interface FileUploadOverlayProps {
  uploadProgress: number;
  uploadSpeed?: number;
  timeRemaining?: number;
  formatBytes: (bytes: number) => string;
  formatDuration: (seconds: number) => string;
}

export const FileUploadOverlay: React.FC<FileUploadOverlayProps> = ({
  uploadProgress,
  uploadSpeed,
  timeRemaining,
  formatBytes,
  formatDuration,
}) => {
  return (
    <Box
      sx={{
        position: "absolute",
        top: 0,
        left: 0,
        right: 0,
        bottom: 0,
        backgroundColor: "rgba(0, 0, 0, 0.8)",
        display: "flex",
        alignItems: "center",
        justifyContent: "center",
        flexDirection: "column",
        gap: 1,
        padding: "8px",
      }}
    >
      {/* Circular Progress with Percentage */}
      <Box sx={{ position: "relative", display: "inline-flex" }}>
        <CircularProgress 
          variant="determinate" 
          value={uploadProgress} 
          size={40} 
          sx={{ color: "white" }}
        />
        <Box
          sx={{
            top: 0,
            left: 0,
            bottom: 0,
            right: 0,
            position: 'absolute',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
          }}
        >
          <Typography style="body3" color="white">
            <span style={{ fontSize: "10px", fontWeight: 600 }}>
              {Math.round(uploadProgress)}%
            </span>
          </Typography>
        </Box>
      </Box>

      {/* Upload Status Text */}
      <Typography style="body3" color="white">
        <span style={{ textAlign: "center", fontSize: "11px" }}>
          Uploading...
        </span>
      </Typography>

      {/* Progress Details */}
      {uploadSpeed && uploadSpeed > 0 && (
        <Typography style="body3" color="white">
          <span style={{ textAlign: "center", fontSize: "10px", opacity: 0.8 }}>
            {formatBytes(uploadSpeed)}/s
          </span>
        </Typography>
      )}

      {timeRemaining && timeRemaining > 0 && timeRemaining < 3600 && (
        <Typography style="body3" color="white">
          <span style={{ textAlign: "center", fontSize: "10px", opacity: 0.8 }}>
            {formatDuration(timeRemaining)} left
          </span>
        </Typography>
      )}
    </Box>
  );
}; 