import { Button } from "@/design-system/components/Button";
import { TextInput } from "@/design-system/components/TextInput";
import { Typography } from "@/design-system/components/Typography";
import { colors } from "@/design-system/tokens";
import DeleteIcon from "@mui/icons-material/Delete";
import { Box, IconButton } from "@mui/material";
import { FileReference } from "proto/hero/reports/v2/reports_pb";
import React from "react";
import { FileUploadOverlay } from "./FileUploadOverlay";

/**
 * Extended FileReference interface for UI state management
 * 
 * This interface extends the base FileReference protobuf type with additional
 * UI-specific properties needed for managing the upload process and user interactions.
 */
export interface StagedFileReference extends FileReference {
  /** Original File object for staging before upload */
  file?: File;

  /** Preview URL (Object URL) for displaying file thumbnails/previews */
  preview?: string;

  /** Whether this file is currently being uploaded */
  isUploading?: boolean;

  /** Error message if upload failed */
  uploadError?: string;

  /** Upload progress percentage (0-100) */
  uploadProgress?: number;

  /** Upload speed in bytes per second */
  uploadSpeed?: number;

  /** Estimated time remaining in seconds */
  timeRemaining?: number;

  /** Timestamp when upload started */
  uploadStartTime?: number;

  /** Number of bytes uploaded so far */
  bytesUploaded?: number;
}

/**
 * Staged Files List Component
 * 
 * Displays the list of staged files with previews, metadata editing,
 * upload progress, and action buttons for each file.
 */
interface StagedFilesListProps {
  stagedFiles: StagedFileReference[];
  readOnly: boolean;
  onDisplayNameChange: (id: string, displayName: string) => void;
  onCaptionChange: (id: string, caption: string) => void;
  onCategoryChange: (id: string, fileCategory: string) => void;
  onRemoveFile: (id: string) => void;
  renderFilePreview: (file: StagedFileReference) => React.ReactNode;
  formatBytes: (bytes: number) => string;
  formatDuration: (seconds: number) => string;
}

export const StagedFilesList: React.FC<StagedFilesListProps> = ({
  stagedFiles,
  readOnly,
  onDisplayNameChange,
  onCaptionChange,
  onCategoryChange,
  onRemoveFile,
  renderFilePreview,
  formatBytes,
  formatDuration,
}) => {
  if (stagedFiles.length === 0) {
    return null;
  }

  return (
    <>
      <Box sx={{ mb: 2 }}>
        <Typography style="h2" color={colors.grey[500]}>
          Ready To Upload ({stagedFiles.length})
        </Typography>
      </Box>

      <Box sx={{ display: "flex", flexDirection: "column", gap: 5 }}>
        {stagedFiles.map((file) => (
          <Box key={file.id} sx={{ display: "flex", gap: 3 }}>
            {/* File Preview Container */}
            <Box
              sx={{
                width: "164px",
                height: "164px",
                borderRadius: "8px",
                overflow: "hidden",
                flexShrink: 0,
                border: `1px solid ${colors.grey[300]}`,
                position: "relative",
              }}
            >
              {renderFilePreview(file)}

              {/* Upload Overlay - Shows progress during upload */}
              {file.isUploading && (
                <FileUploadOverlay
                  uploadProgress={file.uploadProgress || 0}
                  uploadSpeed={file.uploadSpeed}
                  timeRemaining={file.timeRemaining}
                  formatBytes={formatBytes}
                  formatDuration={formatDuration}
                />
              )}

              {/* Error Overlay - Shows error state with retry option */}
              {file.uploadError && (
                <Box
                  sx={{
                    position: "absolute",
                    top: 0,
                    left: 0,
                    right: 0,
                    bottom: 0,
                    backgroundColor: "rgba(220, 38, 38, 0.8)",
                    display: "flex",
                    alignItems: "center",
                    justifyContent: "center",
                    flexDirection: "column",
                    gap: 1,
                    padding: "8px",
                  }}
                >
                  <Typography style="body3" color="white">
                    Upload Failed
                  </Typography>
                  <Button
                    label="Remove"
                    size="small"
                    color="grey"
                    style="ghost"
                    onClick={() => onRemoveFile(file.id)}
                  />
                </Box>
              )}

              {/* Remove Button - Allows deletion of staged files */}
              {!file.isUploading && (
                <IconButton
                  sx={{
                    position: "absolute",
                    top: "4px",
                    left: "4px",
                    backgroundColor: "rgba(0, 0, 0, 0.6)",
                    color: "white",
                    width: "24px",
                    height: "24px",
                    "&:hover": {
                      backgroundColor: "rgba(0, 0, 0, 0.8)",
                    },
                  }}
                  onClick={() => onRemoveFile(file.id)}
                  size="small"
                  aria-label={`Remove ${file.displayName || 'file'}`}
                >
                  <DeleteIcon sx={{ fontSize: "16px" }} />
                </IconButton>
              )}

              {/* Success Indicator - Shows when file is successfully uploaded */}
              {file.fileId && !file.isUploading && !file.uploadError && (
                <Box
                  sx={{
                    position: "absolute",
                    top: "8px",
                    right: "8px",
                    backgroundColor: colors.blue[600],
                    borderRadius: "50%",
                    width: "20px",
                    height: "20px",
                    display: "flex",
                    alignItems: "center",
                    justifyContent: "center",
                  }}
                >
                  <Box sx={{ color: "white", fontSize: "12px", fontWeight: 600 }}>
                    ✓
                  </Box>
                </Box>
              )}
            </Box>

            {/* File Metadata Editing Section */}
            <Box
              sx={{
                display: "flex",
                flexDirection: "column",
                gap: 2,
                width: "100%",
              }}
            >
              {/* Title Input */}
              <TextInput
                placeholder="Title"
                value={file.displayName}
                onChange={(e: React.ChangeEvent<HTMLInputElement>) =>
                  onDisplayNameChange(file.id, e.target.value)
                }
                disabled={readOnly || file.isUploading}
                aria-label="File title"
              />

              {/* Category Input */}
              <TextInput
                placeholder="Category (e.g., Evidence Photo, Crime Scene)"
                value={file.fileCategory}
                onChange={(e: React.ChangeEvent<HTMLInputElement>) =>
                  onCategoryChange(file.id, e.target.value)
                }
                disabled={readOnly || file.isUploading}
                aria-label="File category"
              />

              {/* Description Input */}
              <TextInput
                placeholder="Description"
                value={file.caption}
                onChange={(e: React.ChangeEvent<HTMLInputElement>) =>
                  onCaptionChange(file.id, e.target.value)
                }
                disabled={readOnly || file.isUploading}
                aria-label="File description"
              />

              {/* File Information and Status Display */}
              <Box sx={{ display: "flex", flexDirection: "column", gap: 1 }}>
                <Typography style="body3" color={colors.grey[500]}>
                  {typeof file.metadata?.originalFilename === 'string' ? file.metadata.originalFilename : 'Unknown'} • {
                    typeof file.metadata?.fileSize === 'number'
                      ? (file.metadata.fileSize / 1024 / 1024).toFixed(2)
                      : '0'
                  } MB
                </Typography>

                {/* Individual File Progress Bar - Shows during upload */}
                {file.isUploading && file.uploadProgress !== undefined && (
                  <Box sx={{ width: "100%" }}>
                    <Box sx={{ display: "flex", justifyContent: "space-between", alignItems: "center", mb: 0.5 }}>
                      <Typography style="body3" color={colors.blue[600]}>
                        {Math.round(file.uploadProgress)}% uploaded
                      </Typography>
                      {file.uploadSpeed && file.uploadSpeed > 0 && (
                        <Typography style="body3" color={colors.grey[500]}>
                          {formatBytes(file.uploadSpeed)}/s
                        </Typography>
                      )}
                    </Box>
                    <Box sx={{ width: "100%", backgroundColor: colors.blue[100], borderRadius: "2px", height: "4px" }}>
                      <Box
                        sx={{
                          width: `${file.uploadProgress}%`,
                          backgroundColor: colors.blue[600],
                          height: "100%",
                          borderRadius: "2px",
                          transition: "width 0.3s ease-in-out",
                        }}
                      />
                    </Box>
                    {file.timeRemaining && file.timeRemaining > 0 && file.timeRemaining < 3600 && (
                      <Box sx={{ mt: 0.5 }}>
                        <Typography style="body3" color={colors.grey[500]}>
                          <span style={{ fontSize: "11px" }}>
                            {formatDuration(file.timeRemaining)} remaining
                          </span>
                        </Typography>
                      </Box>
                    )}
                  </Box>
                )}

                {/* Error Message Display */}
                {file.uploadError && (
                  <Typography style="body3" color={colors.rose[600]}>
                    Error: {file.uploadError}
                  </Typography>
                )}

                {/* Success Message Display */}
                {file.fileId && !file.isUploading && (
                  <Typography style="body3" color={colors.blue[600]}>
                    ✓ Uploaded successfully
                  </Typography>
                )}

                {/* Ready State Display */}
                {!file.fileId && !file.isUploading && !file.uploadError && (
                  <Typography style="body3" color={colors.grey[500]}>
                    Ready to upload
                  </Typography>
                )}
              </Box>
            </Box>
          </Box>
        ))}
      </Box>
    </>
  );
}; 