import { Typography } from "@/design-system/components/Typography";
import { colors } from "@/design-system/tokens";
import { Box } from "@mui/material";
import React from "react";

/**
 * Upload Progress Dashboard Component
 * 
 * Displays overall upload progress with statistics including files completed,
 * data transferred, upload speed, time remaining, and failed files.
 */
interface UploadProgressDashboardProps {
  overallProgress: number;
  uploadingSummary: {
    totalFiles: number;
    completedFiles: number;
    failedFiles: number;
    totalBytes: number;
    uploadedBytes: number;
    averageSpeed: number;
    estimatedTimeRemaining: number;
    isComplete?: boolean;
  };
  formatBytes: (bytes: number) => string;
  formatDuration: (seconds: number) => string;
}

export const UploadProgressDashboard: React.FC<UploadProgressDashboardProps> = ({
  overallProgress,
  uploadingSummary,
  formatBytes,
  formatDuration,
}) => {
  const isComplete = uploadingSummary.isComplete;
  const isSuccess = isComplete && uploadingSummary.failedFiles === 0;

  return (
    <Box
      sx={{
        p: 3,
        backgroundColor: isSuccess ? "#F0F9FF" : colors.blue[50], // Light blue for success
        borderRadius: "8px",
        border: isSuccess ? `1px solid ${colors.blue[300]}` : `1px solid ${colors.blue[200]}`,
        animation: isComplete ? "successPulse 0.6s ease-in-out" : "fadeIn 0.3s ease-in-out",
        transition: "all 0.5s ease-in-out",
        "@keyframes fadeIn": {
          from: { opacity: 0, transform: "translateY(-10px)" },
          to: { opacity: 1, transform: "translateY(0)" }
        },
        "@keyframes successPulse": {
          "0%": { transform: "scale(1)" },
          "50%": { transform: "scale(1.02)" },
          "100%": { transform: "scale(1)" }
        },
        "@keyframes checkmarkAppear": {
          from: { opacity: 0, transform: "scale(0)" },
          to: { opacity: 1, transform: "scale(1)" }
        }
      }}>
      <Box sx={{ display: "flex", justifyContent: "space-between", alignItems: "center", mb: 2 }}>
        <Box sx={{ display: "flex", alignItems: "center", gap: 1 }}>
          <Typography style="h3" color={colors.blue[800]}>
            {isSuccess ? "Upload Complete!" : "Upload Progress"}
          </Typography>
          {isSuccess && (
            <Box sx={{
              color: "#10B981", // Green checkmark
              fontSize: "20px",
              animation: "checkmarkAppear 0.5s ease-in-out 0.2s both"
            }}>
              ✓
            </Box>
          )}
        </Box>
        <Typography style="body2" color={colors.blue[600]}>
          {Math.round(overallProgress)}%
        </Typography>
      </Box>

      {/* Progress Bar */}
      <Box sx={{ width: "100%", backgroundColor: colors.blue[100], borderRadius: "4px", height: "8px", mb: 2 }}>
        <Box
          sx={{
            width: `${overallProgress}%`,
            backgroundColor: colors.blue[600],
            height: "100%",
            borderRadius: "4px",
            transition: "width 0.3s ease-in-out",
          }}
        />
      </Box>

      {/* Upload Statistics */}
      <Box sx={{ display: "grid", gridTemplateColumns: "repeat(auto-fit, minmax(150px, 1fr))", gap: 2 }}>
        <Box>
          <Typography style="body3" color={colors.grey[500]}>
            Files
          </Typography>
          <Typography style="body2" color={colors.grey[900]}>
            {uploadingSummary.completedFiles} / {uploadingSummary.totalFiles}
          </Typography>
        </Box>

        <Box>
          <Typography style="body3" color={colors.grey[500]}>
            Data Transferred
          </Typography>
          <Typography style="body2" color={colors.grey[900]}>
            {formatBytes(uploadingSummary.uploadedBytes)} / {formatBytes(uploadingSummary.totalBytes)}
          </Typography>
        </Box>

        <Box>
          <Typography style="body3" color={colors.grey[500]}>
            Upload Speed
          </Typography>
          <Typography style="body2" color={colors.grey[900]}>
            {isComplete
              ? "Complete"
              : uploadingSummary.averageSpeed > 0
                ? `${formatBytes(uploadingSummary.averageSpeed)}/s`
                : "Calculating..."
            }
          </Typography>
        </Box>

        <Box>
          <Typography style="body3" color={colors.grey[500]}>
            Time Remaining
          </Typography>
          <Typography style="body2" color={colors.grey[900]}>
            {isComplete
              ? "Complete"
              : uploadingSummary.estimatedTimeRemaining > 0
                ? formatDuration(uploadingSummary.estimatedTimeRemaining)
                : "Calculating..."
            }
          </Typography>
        </Box>

        {uploadingSummary.failedFiles > 0 && (
          <Box>
            <Typography style="body3" color={colors.rose[600]}>
              Failed Files
            </Typography>
            <Typography style="body2" color={colors.rose[700]}>
              {uploadingSummary.failedFiles}
            </Typography>
          </Box>
        )}
      </Box>
    </Box>
  );
}; 