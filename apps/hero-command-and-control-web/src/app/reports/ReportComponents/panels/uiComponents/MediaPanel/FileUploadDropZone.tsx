import { Typography } from "@/design-system/components/Typography";
import { colors } from "@/design-system/tokens";
import { Box } from "@mui/material";
import React from "react";
import { MdOutlineFileUpload } from "react-icons/md";

/**
 * File Upload Drop Zone Component
 * 
 * A reusable drag-and-drop file upload zone that handles file selection,
 * drag-and-drop events, and provides visual feedback during interactions.
 */
interface FileUploadDropZoneProps {
  isDragging: boolean;
  onDragEnter: (event: React.DragEvent<HTMLDivElement>) => void;
  onDragOver: (event: React.DragEvent<HTMLDivElement>) => void;
  onDragLeave: (event: React.DragEvent<HTMLDivElement>) => void;
  onDrop: (event: React.DragEvent<HTMLDivElement>) => void;
  onClick: () => void;
  fileInputRef: React.RefObject<HTMLInputElement | null>;
  onFileSelect: (event: React.ChangeEvent<HTMLInputElement>) => void;
}

export const FileUploadDropZone: React.FC<FileUploadDropZoneProps> = ({
  isDragging,
  onDragEnter,
  onDragOver,
  onDragLeave,
  onDrop,
  onClick,
  fileInputRef,
  onFileSelect,
}) => {
  return (
    <Box
      sx={{
        border: isDragging
          ? `1px dashed ${colors.blue[600]}`
          : `1px dashed ${colors.grey[300]}`,
        borderRadius: "8px",
        padding: "24px",
        display: "flex",
        flexDirection: "column",
        alignItems: "center",
        justifyContent: "center",
        cursor: "pointer",
        backgroundColor: isDragging
          ? colors.blue[50]
          : colors.grey[50],
        transition: "all 0.2s ease-in-out",
        animation: "fadeIn 0.3s ease-in-out",
        "@keyframes fadeIn": {
          from: { opacity: 0, transform: "translateY(-10px)" },
          to: { opacity: 1, transform: "translateY(0)" }
        }
      }}
      onClick={onClick}
      onDragEnter={onDragEnter}
      onDragOver={onDragOver}
      onDragLeave={onDragLeave}
      onDrop={onDrop}
    >
      {/* Hidden file input for traditional file selection */}
      <input
        type="file"
        ref={fileInputRef}
        style={{ display: "none" }}
        multiple
        accept="image/*,video/*,application/pdf,audio/*"
        onChange={onFileSelect}
      />

      {/* Upload zone content */}
      <MdOutlineFileUpload
        style={{ fontSize: 48, color: colors.grey[400] }}
      />
      <Box sx={{ mt: 1 }}>
        <Typography style="body1" color={colors.grey[900]}>
          Add Files
        </Typography>
      </Box>
      <Box sx={{ mt: 1.5 }}>
        <Typography style="body3" color={colors.grey[500]}>
          Drag and drop or click to upload
        </Typography>
      </Box>
    </Box>
  );
}; 