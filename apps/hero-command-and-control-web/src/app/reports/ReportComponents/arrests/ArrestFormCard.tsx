import { Button } from "@/design-system/components/Button";
import { Checkbox } from "@/design-system/components/Checkbox";
import { Dropdown } from "@/design-system/components/Dropdown";
import { Label } from "@/design-system/components/Label";
import { InputMask, TextInput } from "@/design-system/components/TextInput";
import { Typography } from "@/design-system/components/Typography";
import { colors } from "@/design-system/tokens";
import AddIcon from "@mui/icons-material/Add";
import DeleteIcon from "@mui/icons-material/Delete";
import ExpandLessIcon from "@mui/icons-material/ExpandLess";
import ExpandMoreIcon from "@mui/icons-material/ExpandMore";
import MoreVertIcon from "@mui/icons-material/MoreVert";
import OpenInNewIcon from "@mui/icons-material/OpenInNew";
import {
  Box,
  Collapse,
  Dialog,
  DialogActions,
  DialogContent,
  IconButton,
  Menu,
  MenuItem,
} from "@mui/material";
import CircularProgress from "@mui/material/CircularProgress";
import React, { useEffect, useRef, useState } from "react";
import {
  ARREST_TYPE_OPTIONS,
  MULTI_ARREST_SEGMENT_INDICATOR_OPTIONS,
  RESIDENT_STATUS_OPTIONS,
  WEAPON_TYPE_OPTIONS,
} from "./constants";

interface PersonData {
  id: string;
  name: string;
  sex: string;
  height: string;
  hair: string;
  weight: string;
  eye: string;
  dateOfBirth: string;
}

interface ArrestFormData {
  arrestType: string;
  arrestTransactionNumber: string;
  multiArrestSegmentIndicator: string;
  arrestDate: string;
  arrestTime: string;
  residentStatus: string;
  weapons: Array<{
    id: string;
    weaponType: string;
  }>;
}

interface ArrestFormCardProps {
  arrest: any;
  arrestId: string;
  arrestIndex: number; // Add index for numbering
  onSave: (arrestId: string, data: ArrestFormData) => void;
  onDelete: (arrestId: string) => void;
  onSaveStatusChange?: (status: {
    isSaving: boolean;
    hasUnsavedChanges: boolean;
    source: string;
  }) => void;
  readOnly?: boolean;
  initialData?: Partial<ArrestFormData>;
  // Entity management props
  people?: PersonData[];
  relations?: any[];
  reportId?: string;
  associatedCases?: any[];
  onAddPersonToArrest?: (personId: string, arrestId: string) => void;
  onRemovePersonFromArrest?: (personId: string, arrestId: string) => void;
  onRemovePersonFromReport?: (personId: string, entityType: "person") => void;
  onOpenSidePanel?: (panelType: "PERSON") => void;
  onQuickAddPersonToArrest?: (personId: string, arrestId: string) => void;
  onEntityEdit?: (entityId: string, entityType: "person") => void;
  onSetActiveArrestRelation?: (relation: { arrestId: string }) => void;
}

const ArrestFormCard: React.FC<ArrestFormCardProps> = ({
  arrest,
  arrestId,
  arrestIndex,
  onSave,
  onDelete,
  onSaveStatusChange,
  readOnly = false,
  initialData,
  people = [],
  relations = [],
  reportId,
  associatedCases = [],
  onAddPersonToArrest,
  onRemovePersonFromArrest,
  onRemovePersonFromReport,
  onOpenSidePanel,
  onQuickAddPersonToArrest,
  onEntityEdit,
  onSetActiveArrestRelation,
}) => {
  const [isExpanded, setIsExpanded] = useState(true);
  const [hasUnsavedChanges, setHasUnsavedChanges] = useState(false);
  const formRef = useRef<HTMLDivElement>(null);
  const [menuAnchorEl, setMenuAnchorEl] = useState<null | HTMLElement>(null);
  const [selectedPersonForMenu, setSelectedPersonForMenu] =
    useState<PersonData | null>(null);
  const [removeModalOpen, setRemoveModalOpen] = useState(false);
  const [removeFromReport, setRemoveFromReport] = useState(false);
  const [quickAddExpanded, setQuickAddExpanded] = useState(true);
  const [loadingIds, setLoadingIds] = useState<Set<string>>(new Set());

  const [formData, setFormData] = useState<ArrestFormData>(() => ({
    arrestType: initialData?.arrestType || "",
    arrestTransactionNumber: initialData?.arrestTransactionNumber || "",
    multiArrestSegmentIndicator: initialData?.multiArrestSegmentIndicator || "",
    arrestDate: initialData?.arrestDate || "",
    arrestTime: initialData?.arrestTime || "",
    residentStatus: initialData?.residentStatus || "",
    weapons: (initialData?.weapons || []).map((weapon) => ({
      ...weapon,
      id: weapon.id || `weapon-${Date.now()}-${Math.random()}`,
    })),
  }));

  // Auto-save functionality
  useEffect(() => {
    if (hasUnsavedChanges && !readOnly) {
      const timeoutId = setTimeout(() => {
        saveArrestData();
      }, 1000);

      return () => clearTimeout(timeoutId);
    }
  }, [formData, hasUnsavedChanges]);

  // Report save status changes
  useEffect(() => {
    if (onSaveStatusChange) {
      onSaveStatusChange({
        isSaving: false,
        hasUnsavedChanges,
        source: `arrest-${arrestId}`,
      });
    }
  }, [hasUnsavedChanges, onSaveStatusChange, arrestId]);

  const saveArrestData = () => {
    if (readOnly) return;

    onSave(arrestId, formData);
    setHasUnsavedChanges(false);
  };

  const handleFormChange = (field: string, value: any) => {
    if (readOnly) return;

    setFormData((prev) => ({
      ...prev,
      [field]: value,
    }));
    setHasUnsavedChanges(true);
  };

  const handleDropdownChange = (field: string) => (value: string | null) => {
    handleFormChange(field, value || "");
  };

  const handleWeaponDropdownChange =
    (weaponId: string, field: string) => (value: string | null) => {
      if (readOnly) return;

      setFormData((prev) => ({
        ...prev,
        weapons: prev.weapons.map((weapon) =>
          weapon.id === weaponId ? { ...weapon, [field]: value || "" } : weapon
        ),
      }));
      setHasUnsavedChanges(true);
    };

  const addWeapon = () => {
    if (readOnly || formData.weapons.length >= 2) return;

    const newWeapon = {
      id: `weapon-${Date.now()}`,
      weaponType: "",
    };

    setFormData((prev) => ({
      ...prev,
      weapons: [...prev.weapons, newWeapon],
    }));
    setHasUnsavedChanges(true);
  };

  const removeWeapon = (weaponId: string) => {
    if (readOnly) return;

    setFormData((prev) => ({
      ...prev,
      weapons: prev.weapons.filter((weapon) => weapon.id !== weaponId),
    }));
    setHasUnsavedChanges(true);
  };

  const getRelatedPeople = (): PersonData[] => {
    if (!relations || !people) return [];

    // Find arrest-person relations for this specific arrest
    const arresteeRelations = relations.filter((rel: any) => {
      const isArresteeRelation =
        rel.relationType === "RELATION_TYPE_ARREST_ARRESTEE";

      if (!isArresteeRelation) return false;

      // Check if this arrest is involved in the relation
      const isArrestInvolved =
        (rel.objectA?.objectType === "arrest" &&
          rel.objectA?.reportScopedId === arrestId) ||
        (rel.objectB?.objectType === "arrest" &&
          rel.objectB?.reportScopedId === arrestId);

      return isArresteeRelation && isArrestInvolved;
    });

    const personIds = arresteeRelations
      .map((rel: any) => {
        if (rel.objectA?.objectType === "entity") return rel.objectA.globalId;
        if (rel.objectB?.objectType === "entity") return rel.objectB.globalId;
        return null;
      })
      .filter(Boolean);

    return people.filter((person) => personIds.includes(person.id));
  };

  // Helper function to get person role numbers (V/O/W/S/I) and RP tag for arrestees
  const getPersonRoleNumbers = (personId: string): string[] => {
    if (!relations) return [];

    // Find ALL person relations (victim/offender/witness/suspect/involved_party) for this person
    const personRelations = relations.filter((rel: any) => {
      const isPersonRelation =
        rel.relationType === "RELATION_TYPE_OFFENSE_VICTIM" ||
        rel.relationType === "RELATION_TYPE_OFFENSE_OFFENDER" ||
        rel.relationType === "RELATION_TYPE_OFFENSE_WITNESS" ||
        rel.relationType === "RELATION_TYPE_OFFENSE_SUSPECT" ||
        rel.relationType === "RELATION_TYPE_OFFENSE_INVOLVED_PARTY";

      if (!isPersonRelation) return false;

      const isPersonInvolved =
        (rel.objectA?.objectType === "entity" &&
          rel.objectA?.globalId === personId) ||
        (rel.objectB?.objectType === "entity" &&
          rel.objectB?.globalId === personId);

      return isPersonInvolved && rel.metadata?.victimOffenderNumber;
    });

    // Get all victim/offender numbers and remove duplicates
    const numbers = personRelations
      .map((rel) => rel.metadata?.victimOffenderNumber)
      .filter(Boolean);

    // Remove duplicates and ensure no multiple tags of same type
    const uniqueNumbers = [];
    const seenTypes = new Set();

    for (const number of numbers) {
      const type = number.charAt(0); // 'V' or 'O'
      if (!seenTypes.has(type)) {
        uniqueNumbers.push(number);
        seenTypes.add(type);
      }
    }

    // Check for reporting party status from victim report relations
    const victimReportRelation = relations.find((rel: any) => {
      const isVictimReportRelation =
        rel.relationType === "RELATION_TYPE_VICTIM_REPORT";
      const isPersonInvolved =
        (rel.objectA?.objectType === "entity" &&
          rel.objectA?.globalId === personId) ||
        (rel.objectB?.objectType === "entity" &&
          rel.objectB?.globalId === personId);
      const isReportInvolved =
        (rel.objectA?.objectType === "report" &&
          rel.objectA?.reportScopedId === reportId) ||
        (rel.objectB?.objectType === "report" &&
          rel.objectB?.reportScopedId === reportId);

      return isVictimReportRelation && isPersonInvolved && isReportInvolved;
    });

    // Add RP tag if person is reporting party
    if (victimReportRelation?.metadata?.isReportingParty) {
      uniqueNumbers.push("RP");
    }

    return uniqueNumbers;
  };

  // Helper function to get color based on designation prefix
  const getLabelColor = (
    designation: string
  ): "vine" | "rose" | "purple" | "amber" | "grey" | "blue" => {
    if (designation === "RP") {
      return "blue"; // Reporting Party - blue
    }

    const prefix = designation.charAt(0);
    switch (prefix) {
      case "V":
        return "vine"; // Victim - green
      case "O":
        return "rose"; // Offender - red
      case "W":
        return "purple"; // Witness - purple
      case "S":
        return "amber"; // Suspect - amber
      case "I":
        return "grey"; // Involved Party - grey
      default:
        return "grey";
    }
  };

  const getAvailableEntitiesForQuickAdd = (): PersonData[] => {
    if (!people || !associatedCases) return [];

    const relatedPeopleIds = getRelatedPeople().map((p) => p.id);

    // Get all people that aren't already arrestees for this arrest
    const availablePeople = people.filter(
      (person) => !relatedPeopleIds.includes(person.id)
    );

    return availablePeople;
  };

  const handleQuickAddPerson = (personId: string) => {
    if (onQuickAddPersonToArrest) {
      setLoadingIds((prev) => new Set([...prev, personId]));
      onQuickAddPersonToArrest(personId, arrestId);

      // Remove loading state after a delay
      setTimeout(() => {
        setLoadingIds((prev) => {
          const newSet = new Set(prev);
          newSet.delete(personId);
          return newSet;
        });
      }, 1000);
    }
  };

  const handleAddArrestee = () => {
    // Set the active arrest relation context so the side panel knows which arrest to associate with
    if (onSetActiveArrestRelation) {
      onSetActiveArrestRelation({ arrestId });
    }
    if (onOpenSidePanel) {
      onOpenSidePanel("PERSON");
    }
  };

  const handleMenuClick = (
    event: React.MouseEvent<HTMLElement>,
    person: PersonData
  ) => {
    event.stopPropagation();
    setMenuAnchorEl(event.currentTarget);
    setSelectedPersonForMenu(person);
  };

  const handleMenuClose = () => {
    setMenuAnchorEl(null);
    setSelectedPersonForMenu(null);
  };

  const handleOpenRecord = () => {
    if (selectedPersonForMenu) {
      window.open(`/entity?entityId=${selectedPersonForMenu.id}`, "_blank");
    }
    handleMenuClose();
  };

  const handleRemoveClick = () => {
    setRemoveModalOpen(true);
    setMenuAnchorEl(null);
  };

  const handleRemoveCancel = () => {
    setRemoveModalOpen(false);
    setRemoveFromReport(false);
    setSelectedPersonForMenu(null);
  };

  const handleRemoveConfirm = () => {
    if (selectedPersonForMenu) {
      if (onRemovePersonFromArrest) {
        onRemovePersonFromArrest(selectedPersonForMenu.id, arrestId);
      }

      if (removeFromReport && onRemovePersonFromReport) {
        onRemovePersonFromReport(selectedPersonForMenu.id, "person");
      }
    }

    setRemoveModalOpen(false);
    setRemoveFromReport(false);
    setSelectedPersonForMenu(null);
  };

  const arrestNumber = String(arrestIndex + 1).padStart(3, "0");
  const relatedPeople = getRelatedPeople();
  const availablePeopleForQuickAdd = getAvailableEntitiesForQuickAdd();

  return (
    <Box
      ref={formRef}
      sx={{
        border: `1px solid ${colors.grey[200]}`,
        borderRadius: 2,
        mb: 3,
        overflow: "hidden",
        backgroundColor: "#FFFFFF",
      }}
    >
      {/* Header */}
      <Box
        sx={{
          backgroundColor: colors.grey[50],
          borderBottom: `1px solid ${colors.grey[200]}`,
          px: 3,
          py: 2,
          display: "flex",
          alignItems: "center",
          justifyContent: "space-between",
        }}
      >
        <Box sx={{ display: "flex", alignItems: "center", gap: 2 }}>
          <Typography style="h2" color={colors.grey[900]}>
            Arrest {arrestNumber}
          </Typography>
        </Box>
        <Box sx={{ display: "flex", alignItems: "center", gap: 1 }}>
          {!readOnly && (
            <IconButton
              onClick={() => onDelete(arrestId)}
              size="small"
              sx={{ color: colors.grey[600] }}
            >
              <DeleteIcon fontSize="small" />
            </IconButton>
          )}
          <IconButton
            onClick={() => setIsExpanded(!isExpanded)}
            size="small"
            sx={{ color: colors.grey[600] }}
          >
            {isExpanded ? <ExpandLessIcon /> : <ExpandMoreIcon />}
          </IconButton>
        </Box>
      </Box>

      {/* Body */}
      <Collapse in={isExpanded}>
        <Box sx={{ p: 3, backgroundColor: "#FFFFFF" }}>
          {/* Arrest Details */}
          <Box sx={{ mb: 4 }}>
            <Box sx={{ mb: 3 }}>
              <Typography style="caps1" color={colors.grey[700]}>
                Arrest Details
              </Typography>
            </Box>

            <Box
              sx={{
                display: "grid",
                gridTemplateColumns: "1fr",
                gap: 2,
                mb: 3,
              }}
            >
              <Dropdown
                title="Type of Arrest"
                value={formData.arrestType}
                onChange={handleDropdownChange("arrestType")}
                options={ARREST_TYPE_OPTIONS}
                disabled={readOnly}
              />
            </Box>

            <Box
              sx={{
                display: "grid",
                gridTemplateColumns: "1fr 1fr",
                gap: 2,
                mb: 3,
              }}
            >
              <TextInput
                title="Arrest Transaction Number"
                value={formData.arrestTransactionNumber}
                onChange={(e: React.ChangeEvent<HTMLInputElement>) =>
                  handleFormChange("arrestTransactionNumber", e.target.value)
                }
                disabled={readOnly}
              />
              <Dropdown
                title="Multi-Arrest Segment Indicator"
                value={formData.multiArrestSegmentIndicator}
                onChange={handleDropdownChange("multiArrestSegmentIndicator")}
                options={MULTI_ARREST_SEGMENT_INDICATOR_OPTIONS}
                disabled={readOnly}
              />
            </Box>

            <Box
              sx={{
                display: "grid",
                gridTemplateColumns: "1fr 1fr",
                gap: 2,
                mb: 3,
              }}
            >
              <TextInput
                title="Arrest Date"
                value={formData.arrestDate}
                onChange={(e: React.ChangeEvent<HTMLInputElement>) =>
                  handleFormChange("arrestDate", e.target.value)
                }
                placeholder="MM/DD/YYYY"
                disabled={readOnly}
                mask={InputMask.Date}
              />
              <TextInput
                title="Arrest Time"
                value={formData.arrestTime}
                onChange={(e: React.ChangeEvent<HTMLInputElement>) =>
                  handleFormChange("arrestTime", e.target.value)
                }
                placeholder="HH:MM"
                disabled={readOnly}
                mask={InputMask.Time}
              />
            </Box>

            <Box sx={{ mb: 3 }}>
              <Typography style="caps1" color={colors.grey[700]}>
                Arrestee Details
              </Typography>
            </Box>

            <Box sx={{ mb: 3 }}>
              <Dropdown
                title="Resident Status"
                value={formData.residentStatus}
                onChange={handleDropdownChange("residentStatus")}
                options={RESIDENT_STATUS_OPTIONS}
                disabled={readOnly}
              />
            </Box>
          </Box>
          {/* Weapons Section */}
          <Box sx={{ mb: 4 }}>
            <Box sx={{ mb: 2 }}>
              <Typography style="caps1" color={colors.grey[700]}>
                Weapons Involved
              </Typography>
            </Box>
            <Box sx={{ mb: 3 }}>
              <Typography style="body4" color={colors.grey[500]}>
                Add up to 2
              </Typography>
            </Box>

            {formData.weapons.map((weapon, index) => (
              <Box key={weapon.id} sx={{ mb: 3 }}>
                <Box
                  sx={{
                    display: "flex",
                    flexDirection: "column",
                    gap: 2,
                    mb: 2,
                  }}
                >
                  <Box sx={{ width: "100%" }}>
                    <Dropdown
                      title="Weapon Type"
                      placeholder="Select weapon type"
                      options={WEAPON_TYPE_OPTIONS}
                      value={weapon.weaponType}
                      onChange={handleWeaponDropdownChange(
                        weapon.id,
                        "weaponType"
                      )}
                      readOnly={readOnly}
                      enableSearch
                    />
                  </Box>
                </Box>
                {index > 0 && !readOnly && (
                  <Button
                    label="Remove Weapon"
                    style="ghost"
                    color="grey"
                    size="small"
                    onClick={() => removeWeapon(weapon.id)}
                  />
                )}
              </Box>
            ))}

            {formData.weapons.length < 2 && !readOnly && (
              <Button
                label="Add Weapon"
                style="ghost"
                color="blue"
                size="small"
                leftIcon={<AddIcon />}
                onClick={addWeapon}
              />
            )}
          </Box>

          {/* Arrestee Section */}
          <Box sx={{ mb: 4 }}>
            <Box sx={{ mb: 3 }}>
              <Typography style="caps1" color={colors.grey[700]}>
                ARRESTEE
              </Typography>
            </Box>

            {relatedPeople.length > 0 && (
              <Box
                sx={{ display: "flex", flexDirection: "column", gap: 2, mb: 3 }}
              >
                {relatedPeople.map((person) => (
                  <Box
                    key={person.id}
                    onClick={() => {
                      if (readOnly) return;
                      onEntityEdit && onEntityEdit(person.id, "person");
                    }}
                    sx={{
                      padding: 2,
                      border: `1px solid ${colors.grey[200]}`,
                      borderRadius: "8px",
                      backgroundColor: colors.grey[50],
                      display: "flex",
                      justifyContent: "space-between",
                      alignItems: "center",
                      cursor: readOnly ? "default" : "pointer",
                      "&:hover:not(:has(.button-area:hover))": {
                        backgroundColor: colors.grey[100],
                      },
                      "&:active:not(:has(.button-area:hover))": {
                        backgroundColor: colors.grey[200],
                      },
                    }}
                  >
                    <Box sx={{ flex: 1 }}>
                      <Box
                        sx={{
                          mb: 1,
                          display: "flex",
                          alignItems: "center",
                          gap: 1,
                        }}
                      >
                        <Typography style="body3" color={colors.grey[900]}>
                          {person.name}
                        </Typography>
                        {(() => {
                          const numbers = getPersonRoleNumbers(person.id);
                          if (numbers.length === 0) return null;
                          return numbers.map((number, index) => (
                            <Label
                              key={index}
                              label={number}
                              size="small"
                              color={getLabelColor(number)}
                              prominence={false}
                              pilled
                            />
                          ));
                        })()}
                      </Box>
                      <Box sx={{ display: "flex", gap: 1, flexWrap: "wrap" }}>
                        <Typography style="tag2" color={colors.grey[500]}>
                          Sex:
                        </Typography>
                        <Typography style="tag2" color={colors.grey[900]}>
                          {person.sex || "---"}
                        </Typography>
                        <Typography style="tag2" color={colors.grey[500]}>
                          , Height:
                        </Typography>
                        <Typography style="tag2" color={colors.grey[900]}>
                          {person.height || "---"}
                        </Typography>
                        <Typography style="tag2" color={colors.grey[500]}>
                          , Weight:
                        </Typography>
                        <Typography style="tag2" color={colors.grey[900]}>
                          {person.weight || "---"}
                        </Typography>
                        <Typography style="tag2" color={colors.grey[500]}>
                          , Hair:
                        </Typography>
                        <Typography style="tag2" color={colors.grey[900]}>
                          {person.hair || "---"}
                        </Typography>
                        <Typography style="tag2" color={colors.grey[500]}>
                          , Eyes:
                        </Typography>
                        <Typography style="tag2" color={colors.grey[900]}>
                          {person.eye || "---"}
                        </Typography>
                      </Box>
                    </Box>
                    {!readOnly && (
                      <Box
                        className="button-area"
                        sx={{ display: "flex", alignItems: "center", gap: 1 }}
                      >
                        <IconButton
                          size="small"
                          onClick={(e) => handleMenuClick(e, person)}
                          sx={{
                            color: colors.grey[600],
                            "&:hover": {
                              bgcolor: colors.grey[200],
                              color: colors.grey[800],
                            },
                          }}
                        >
                          <MoreVertIcon fontSize="small" />
                        </IconButton>
                      </Box>
                    )}
                  </Box>
                ))}
              </Box>
            )}

            {/* Add Arrestee button - only show if no arrestees yet */}
            {!readOnly && relatedPeople.length === 0 && (
              <Box sx={{ mb: 3 }}>
                <Button
                  onClick={handleAddArrestee}
                  style="ghost"
                  color="blue"
                  size="small"
                  leftIcon={<AddIcon />}
                  label="Add Arrestee"
                />
              </Box>
            )}

            {/* Quick Add Section - only show if no arrestees yet */}
            {!readOnly &&
              relatedPeople.length === 0 &&
              availablePeopleForQuickAdd.length > 0 && (
                <Box sx={{ mt: 2 }}>
                  <Box
                    sx={{
                      display: "flex",
                      justifyContent: "space-between",
                      alignItems: "center",
                      cursor: "pointer",
                      py: 1,
                    }}
                    onClick={() => setQuickAddExpanded(!quickAddExpanded)}
                  >
                    <Typography style="caps2" color={colors.grey[500]}>
                      QUICK ADD
                    </Typography>
                    <IconButton size="small">
                      {quickAddExpanded ? (
                        <ExpandLessIcon />
                      ) : (
                        <ExpandMoreIcon />
                      )}
                    </IconButton>
                  </Box>

                  <Collapse in={quickAddExpanded}>
                    <Box
                      sx={{
                        display: "grid",
                        gridTemplateColumns: "repeat(3, 1fr)",
                        gap: 1.5,
                        pt: 1,
                      }}
                    >
                      {availablePeopleForQuickAdd.map((person) => (
                        <Box
                          key={person.id}
                          sx={{
                            px: 1.5,
                            border: `1px solid ${colors.grey[200]}`,
                            borderRadius: "8px",
                            backgroundColor: "white",
                            display: "flex",
                            alignItems: "center",
                            cursor: "pointer",
                            height: "68px",
                            boxShadow: "0px 0px 16px 0px rgba(0, 0, 0, 0.04)",
                            "&:hover": {
                              backgroundColor: colors.grey[50],
                            },
                          }}
                          onClick={() => handleQuickAddPerson(person.id)}
                        >
                          <Box
                            sx={{
                              mr: 2,
                              display: "flex",
                              alignItems: "center",
                            }}
                          >
                            {loadingIds.has(person.id) ? (
                              <CircularProgress
                                size={20}
                                sx={{ color: colors.grey[900] }}
                              />
                            ) : (
                              <AddIcon
                                sx={{ color: colors.grey[900], fontSize: 20 }}
                              />
                            )}
                          </Box>
                          <Box sx={{ flex: 1, minWidth: 0 }}>
                            <Box
                              sx={{
                                mb: 0.5,
                                display: "flex",
                                alignItems: "center",
                                gap: 1,
                              }}
                            >
                              <Typography
                                style="body3"
                                color={colors.grey[900]}
                              >
                                {person.name}
                              </Typography>
                              {getPersonRoleNumbers(person.id).map(
                                (number, index) => (
                                  <Label
                                    key={`${person.id}-${number}`}
                                    label={number}
                                    size="small"
                                    color={getLabelColor(number)}
                                    prominence={false}
                                    pilled
                                  />
                                )
                              )}
                            </Box>
                            <Box
                              sx={{
                                overflow: "hidden",
                                textOverflow: "ellipsis",
                                display: "-webkit-box",
                                WebkitLineClamp: 2,
                                WebkitBoxOrient: "vertical",
                              }}
                            >
                              <Typography style="tag2" color={colors.grey[500]}>
                                {person.dateOfBirth || "---"},{" "}
                                {person.sex || "---"}
                              </Typography>
                            </Box>
                          </Box>
                        </Box>
                      ))}
                    </Box>
                  </Collapse>
                </Box>
              )}
          </Box>

          {/* Person Actions Menu */}
          <Menu
            anchorEl={menuAnchorEl}
            open={Boolean(menuAnchorEl)}
            onClose={handleMenuClose}
            anchorOrigin={{
              vertical: "bottom",
              horizontal: "right",
            }}
            transformOrigin={{
              vertical: "top",
              horizontal: "right",
            }}
            sx={{
              mt: 0.25,
              "& .MuiPaper-root": {
                borderRadius: "8px",
                minWidth: "200px",
              },
              "& .MuiMenuItem-root": {
                py: 1.5,
                display: "flex",
                alignItems: "center",
                gap: 1.5,
              },
            }}
          >
            <MenuItem onClick={handleOpenRecord}>
              <OpenInNewIcon
                fontSize="small"
                sx={{ color: colors.grey[500] }}
              />
              <Typography style="body2" color={colors.grey[500]}>
                Open record in new tab
              </Typography>
            </MenuItem>
            <MenuItem onClick={handleRemoveClick}>
              <DeleteIcon fontSize="small" sx={{ color: colors.grey[500] }} />
              <Typography style="body2" color={colors.grey[500]}>
                Remove from arrest
              </Typography>
            </MenuItem>
          </Menu>

          {/* Remove confirmation modal */}
          <Dialog
            open={removeModalOpen}
            onClose={handleRemoveCancel}
            maxWidth="sm"
            fullWidth
            PaperProps={{
              sx: {
                borderRadius: "12px",
                p: 1,
              },
            }}
          >
            <DialogContent sx={{ p: 3 }}>
              <Box sx={{ mb: 2 }}>
                <Typography style="h2" color={colors.grey[900]}>
                  Delete {selectedPersonForMenu?.name || "Person"}
                </Typography>
              </Box>
              <Box sx={{ mb: 3 }}>
                <Typography style="body3" color={colors.grey[700]}>
                  {selectedPersonForMenu?.name || "Person"} will be deleted from
                  the arrest
                </Typography>
              </Box>

              <Box sx={{ display: "flex", alignItems: "center", gap: 2 }}>
                <Checkbox
                  checked={removeFromReport}
                  onChange={(e) => setRemoveFromReport(e.target.checked)}
                  label="Also delete from the report"
                  size="small"
                />
              </Box>
            </DialogContent>

            <DialogActions sx={{ p: 3, pt: 0 }}>
              <Button
                label="Cancel"
                color="grey"
                prominence={false}
                onClick={handleRemoveCancel}
              />
              <Button
                label="Delete"
                color="blue"
                prominence={true}
                onClick={handleRemoveConfirm}
              />
            </DialogActions>
          </Dialog>
        </Box>
      </Collapse>
    </Box>
  );
};

export default ArrestFormCard;
