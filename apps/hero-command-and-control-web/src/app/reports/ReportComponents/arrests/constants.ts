import { DropdownOption } from "@/design-system/components/Dropdown";

// Arrest Type Options based on the image
export const ARREST_TYPE_OPTIONS: DropdownOption[] = [
  { value: "0", label: "0 - On-View Arrest (Without Warrant)" },
  { value: "1", label: "1 - On-View Arrest (With Warrant)" },
  { value: "2", label: "2 - Taken Into Custody Without Arrest" },
  { value: "3", label: "3 - Summoned/Cited" },
];

// Multi-Arrest Segment Indicator Options
export const MULTI_ARREST_SEGMENT_INDICATOR_OPTIONS: DropdownOption[] = [
  { value: "N", label: "N - Not Applicable" },
  { value: "C", label: "C - Count Arrestee" },
  { value: "M", label: "M - Multiple Arrestee Segments" },
];

// Resident Status Options
export const RESIDENT_STATUS_OPTIONS: DropdownOption[] = [
  { value: "R", label: "R - Resident" },
  { value: "N", label: "N - Non-Resident" },
  { value: "U", label: "U - Unknown" },
];

// Weapon Type Options (reused from offense constants)
export const WEAPON_TYPE_OPTIONS: DropdownOption[] = [
  { value: "01", label: "01 - Unarmed" },
  { value: "11", label: "11 - Firearm" },
  { value: "12", label: "12 - Handgun" },
  { value: "13", label: "13 - Rifle" },
  { value: "14", label: "14 - Shotgun" },
  { value: "15", label: "15 - Other Firearm" },
  { value: "20", label: "20 - Knife/Cutting Instrument" },
  { value: "30", label: "30 - Blunt Object" },
  { value: "35", label: "35 - Motor Vehicle/Vessel" },
  { value: "40", label: "40 - Personal Weapons" },
  { value: "50", label: "50 - Poison" },
  { value: "60", label: "60 - Explosives" },
  { value: "65", label: "65 - Fire/Incendiary Device" },
  { value: "70", label: "70 - Drugs/Narcotics/Sleeping Pills" },
  { value: "85", label: "85 - Asphyxiation" },
  { value: "90", label: "90 - Other" },
  { value: "95", label: "95 - Unknown" },
  { value: "99", label: "99 - None" },
];
