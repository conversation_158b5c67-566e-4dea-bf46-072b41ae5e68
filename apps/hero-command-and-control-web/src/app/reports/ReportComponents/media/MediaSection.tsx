/**
 * @fileoverview MediaSection Component - Complete media file management for report sections
 * 
 * This is the main component for managing media files within report sections in the Hero
 * Command and Control web application. It provides a comprehensive interface for viewing,
 * managing, downloading, and deleting media files attached to specific report sections.
 * 
 * Core Functionality:
 * - Display media files in a tabular format with file previews
 * - Handle file uploads and attachments to report sections
 * - Provide download functionality with presigned URLs
 * - Enable file deletion with confirmation and cleanup
 * - Support various file types (images, videos, audio, documents, PDFs)
 * - Integrate with comment system for collaboration
 * - Handle loading states and error scenarios
 * 
 * Technical Architecture:
 * - Uses React Query for efficient API state management
 * - Integrates with filerepository service for file operations
 * - Manages protobuf data structures for report sections
 * - Implements optimistic updates for better UX
 * - <PERSON><PERSON> complex state synchronization between UI and backend
 * 
 * API Integration:
 * - Fetches media section data using useReportSection hook
 * - Updates section data using useUpdateReportSection mutation
 * - Manages file operations through filerepository service hooks
 * - <PERSON>les presigned URL generation for secure file access
 * 
 * @component
 * @example
 * // Basic usage in a report
 * <MediaSection
 *   reportId="report-123"
 *   mediaSectionId="section-456"
 *   onAddClick={() => openFileUploadDialog()}
 *   comments={sectionComments}
 *   onAddComment={handleAddComment}
 *   onResolveComment={handleResolveComment}
 * />
 * 
 * // Read-only mode for viewing
 * <MediaSection
 *   reportId="report-123"
 *   mediaSectionId="section-456"
 *   onAddClick={() => {}}
 *   readOnly={true}
 * />
 */

import { Button } from "@/design-system/components/Button";
import { Typography } from "@/design-system/components/Typography";
import { colors } from "@/design-system/tokens";
import { create } from "@bufbuild/protobuf";
import AddIcon from "@mui/icons-material/Add";
import DeleteIcon from "@mui/icons-material/Delete";
import DownloadIcon from "@mui/icons-material/Download";
import { Box, IconButton } from "@mui/material";
import { FileReference, FileReferenceSchema, SectionType } from "proto/hero/reports/v2/reports_pb";
import React, { useEffect, useState } from "react";
import {
  FaFile,
  FaFileAlt,
  FaFileAudio,
  FaFileImage,
  FaFilePdf,
  FaFileVideo,
} from "react-icons/fa";
import { useDeleteFile, useGetPresignedDownloadUrl } from "../../../apis/services/filerepository/hooks";
import { useReportSection, useUpdateReportSection } from "../../../apis/services/workflow/reports/v2/hooks";
import CommentSection from "../common/CommentSection";
import { LocalComment } from "../entities/EntityCard";

/**
 * Props interface for the MediaSection component
 * 
 * @interface MediaSectionProps
 */
interface MediaSectionProps {
  /** The unique identifier of the report containing this media section */
  reportId: string;

  /** The unique identifier of the media section (null for new sections) */
  mediaSectionId: string | null;

  /** Callback function triggered when the Add button is clicked to upload new files */
  onAddClick: () => void;

  /** Array of comments associated with this media section for collaboration */
  comments?: LocalComment[];

  /** Callback function for adding new comments to the section */
  onAddComment?: (text: string) => void;

  /** Callback function for resolving or unresolving comments */
  onResolveComment?: (id: string, resolved: boolean) => void;

  /** Whether the comment section should be initially expanded */
  commentsInitiallyExpanded?: boolean;

  /** Whether the component is in read-only mode (disables editing, deleting, and adding) */
  readOnly?: boolean;
}

/**
 * MediaSection Component
 * 
 * The primary component for managing media files within report sections. This component
 * handles the complete lifecycle of media files including display, download, deletion,
 * and section updates. It integrates closely with the backend services and maintains
 * complex state for optimal user experience.
 * 
 * State Management:
 * - mediaFiles: Array of FileReference objects from the backend
 * - deletingFileId: Tracks which file is currently being deleted
 * - downloadingFileId: Tracks which file is currently being downloaded/opened
 * 
 * The component uses React Query hooks for efficient data fetching and mutation
 * handling, providing automatic loading states, error handling, and cache management.
 * 
 * @param props - The props object containing configuration and event handlers
 * @returns A rendered MediaSection component with file management capabilities
 */
const MediaSection: React.FC<MediaSectionProps> = ({
  reportId,
  mediaSectionId,
  onAddClick,
  comments = [],
  onAddComment,
  onResolveComment,
  commentsInitiallyExpanded = false,
  readOnly = false,
}) => {
  // State for managing media files and loading states
  const [mediaFiles, setMediaFiles] = useState<FileReference[]>([]);
  const [deletingFileId, setDeletingFileId] = useState<string | null>(null);
  const [downloadingFileId, setDownloadingFileId] = useState<string | null>(null);

  // React Query hook for fetching media section data
  const { data: mediaSectionData, refetch: refetchMediaSection } = useReportSection(
    reportId,
    mediaSectionId || "",
    {
      enabled: !!reportId && !!mediaSectionId,
    }
  );

  // Mutation hook for updating report section data
  const updateReportSectionMutation = useUpdateReportSection({
    onSuccess: () => {
      console.log("Media section updated successfully");
      refetchMediaSection();
    },
    onError: (error) => {
      console.error("Error updating media section:", error);
    },
  });

  // Mutation hook for deleting files from the repository
  const deleteFileMutation = useDeleteFile({
    onSuccess: () => {
      console.log("File deleted successfully");
    },
    onError: (error) => {
      console.error("Error deleting file:", error);
    },
  });

  // Mutation hook for generating presigned download URLs
  const getDownloadUrlMutation = useGetPresignedDownloadUrl({
    onSuccess: () => {
      console.log("Download URL generated successfully");
    },
    onError: (error) => {
      console.error("Error generating download URL:", error);
    },
  });

  /**
   * Effect hook to extract and set media files from section data
   * 
   * This effect handles the complex protobuf data structure transformation
   * and ensures the component state stays synchronized with the backend data.
   * It supports both the protobuf structure and transformed data formats.
   */
  useEffect(() => {
    if (mediaSectionData) {
      console.log("MediaSection received data:", mediaSectionData);

      // Access media content from protobuf structure
      let mediaContent = null;
      if (mediaSectionData.content?.case === "mediaList") {
        mediaContent = mediaSectionData.content.value;
      } else if ((mediaSectionData as any).mediaList) {
        // Fallback for transformed data structure
        mediaContent = (mediaSectionData as any).mediaList;
      }

      console.log("MediaContent data:", mediaContent);
      if (mediaContent?.fileRefs) {
        console.log("Setting media files:", mediaContent.fileRefs);
        console.log("File details:", mediaContent.fileRefs.map((f: FileReference) => ({
          id: f.id,
          fileId: f.fileId,
          displayName: f.displayName,
          fileCategory: f.fileCategory,
          metadata: f.metadata
        })));
        setMediaFiles(mediaContent.fileRefs);
      } else {
        console.log("No fileRefs found in mediaContent");
        setMediaFiles([]);
      }
    } else {
      console.log("No mediaSectionData received");
    }
  }, [mediaSectionData]);

  /**
   * Determines the appropriate file icon based on MIME type
   * 
   * This function analyzes the file metadata to determine the file type
   * and returns the corresponding React icon component. It provides
   * fallback handling for unknown or missing MIME types.
   * 
   * @param metadata - File metadata object containing type information
   * @returns JSX element representing the file type icon
   */
  const getFileIcon = (metadata?: any) => {
    const fileType = (typeof metadata?.fileType === 'string' ? metadata.fileType.toLowerCase() : '') || '';

    // Detection based ONLY on MIME type
    if (fileType.startsWith('image/')) {
      return <FaFileImage style={{ fontSize: 20, color: colors.blue[600] }} />;
    }
    if (fileType.startsWith('video/')) {
      return <FaFileVideo style={{ fontSize: 20, color: colors.blue[600] }} />;
    }
    if (fileType.startsWith('audio/')) {
      return <FaFileAudio style={{ fontSize: 20, color: colors.blue[600] }} />;
    }
    if (fileType === 'application/pdf') {
      return <FaFilePdf style={{ fontSize: 20, color: colors.blue[600] }} />;
    }
    if (fileType.includes('text/') || fileType.includes('document') || fileType.includes('word')) {
      return <FaFileAlt style={{ fontSize: 20, color: colors.blue[600] }} />;
    }

    // Default icon if MIME type is unknown or missing
    return <FaFile style={{ fontSize: 20, color: colors.grey[500] }} />;
  };

  /**
   * Handles file downloads with presigned URLs
   * 
   * This function generates a secure presigned URL, fetches the file content,
   * and triggers a browser download with the correct filename. It includes
   * proper error handling and loading states.
   * 
   * @param fileId - The unique identifier of the file in the repository
   * @param fileName - The display name for the downloaded file
   * @param fileRefId - The reference ID for tracking the file in the UI
   */
  const handleDownloadFile = async (fileId: string, fileName: string, fileRefId: string) => {
    try {
      setDownloadingFileId(fileRefId);
      console.log('Downloading file:', fileId, fileName);

      // Get presigned download URL with 5-minute expiration
      const response = await getDownloadUrlMutation.mutateAsync({
        id: fileId,
        expiresIn: 300 // 5 minutes
      } as any);

      // The backend automatically logs 'download' when GetPresignedDownloadUrl is called
      // Fetch the file content and create a blob URL to force download
      const fileResponse = await fetch(response.presignedUrl);
      const blob = await fileResponse.blob();

      // Create a blob URL and trigger download
      const blobUrl = URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = blobUrl;
      link.download = fileName; // This forces download with the correct filename
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);

      // Clean up the blob URL to free memory
      URL.revokeObjectURL(blobUrl);

      console.log('File download initiated successfully');
    } catch (error) {
      console.error('Download failed:', error);
      alert('Failed to download file. Please try again.');
    } finally {
      setDownloadingFileId(null);
    }
  };

  /**
   * Handles opening files in new browser tabs
   * 
   * This function generates a presigned URL and opens the file in a new tab,
   * allowing users to view files without downloading them. Includes error
   * handling and loading states.
   * 
   * @param fileId - The unique identifier of the file in the repository
   * @param fileName - The display name of the file (for logging)
   * @param fileRefId - The reference ID for tracking the file in the UI
   */
  const handleOpenFile = async (fileId: string, fileName: string, fileRefId: string) => {
    try {
      setDownloadingFileId(fileRefId);
      console.log('Opening file in new tab:', fileId, fileName);

      // Get presigned download URL with 5-minute expiration
      const response = await getDownloadUrlMutation.mutateAsync({
        id: fileId,
        expiresIn: 300 // 5 minutes
      } as any);

      // The backend automatically logs 'download' when GetPresignedDownloadUrl is called
      // Open the file in a new tab (like in page.tsx)
      window.open(response.presignedUrl, '_blank');

      console.log('File opened in new tab successfully');
    } catch (error) {
      console.error('Failed to open file:', error);
      alert('Failed to open file. Please try again.');
    } finally {
      setDownloadingFileId(null);
    }
  };

  /**
   * Handles file deletion with confirmation and cleanup
   * 
   * This function performs a two-step deletion process:
   * 1. Soft deletes the file from the repository
   * 2. Updates the media section to remove the file reference
   * 
   * It includes confirmation dialog, error handling, and proper state management.
   * The function also handles display order reordering for remaining files.
   * 
   * @param fileId - The unique identifier of the file in the repository
   * @param fileRefId - The reference ID of the file in the section
   */
  const handleDeleteFile = async (fileId: string, fileRefId: string) => {
    if (!confirm('Are you sure you want to delete this file? This will remove it from the report and delete it from the database.')) {
      return;
    }

    try {
      setDeletingFileId(fileRefId);
      console.log('Deleting file:', fileId, 'with reference ID:', fileRefId);

      // Step 1: Soft delete the file in the file repository
      await deleteFileMutation.mutateAsync({ id: fileId } as any);

      // Step 2: Update the media section to remove the file reference
      if (mediaSectionData && mediaSectionId) {
        // Get current media content
        let currentMediaContent = null;
        if (mediaSectionData.content?.case === "mediaList") {
          currentMediaContent = mediaSectionData.content.value;
        } else if ((mediaSectionData as any).mediaList) {
          currentMediaContent = (mediaSectionData as any).mediaList;
        }

        if (currentMediaContent) {
          // Remove the file reference from the list
          const updatedFileRefs = (currentMediaContent.fileRefs || []).filter(
            (fileRef: FileReference) => fileRef.id !== fileRefId
          );

          // Update display order for remaining files using protobuf create
          const reorderedFileRefs = updatedFileRefs.map((fileRef: FileReference, index: number) =>
            create(FileReferenceSchema, {
              ...fileRef,
              displayOrder: index,
            })
          );

          // Create updated media content with metadata
          const updatedMediaContent = {
            ...currentMediaContent,
            fileRefs: reorderedFileRefs,
            metadata: {
              ...(currentMediaContent.metadata || {}),
              totalFiles: reorderedFileRefs.length,
              lastUpdated: new Date().toISOString(),
            },
          };

          // Update the media section using flat structure (like other sections)
          const section = {
            ...mediaSectionData,
            id: mediaSectionId,
            type: SectionType.MEDIA,
            mediaList: updatedMediaContent,
            reportId,
          };

          const updateRequest = {
            reportId,
            section,
          };

          console.log('Updating media section after file deletion:', updateRequest);
          // @ts-expect-error TODO: Fix type issue - Type mismatch with protobuf, but structure is correct
          await updateReportSectionMutation.mutateAsync(updateRequest);
        }
      }

      console.log('File deleted and media section updated successfully');
    } catch (error) {
      console.error('Error deleting file:', error);
      alert('Failed to delete file. Please try again.');
    } finally {
      setDeletingFileId(null);
    }
  };

  return (
    <Box
      sx={{
        display: "flex",
        width: "100%",
        flexDirection: "column",
        alignItems: "flex-start",
        gap: "24px",
        borderRadius: "12px",
        border: `1px solid ${colors.grey[200]}`,
        background: "#FFF",
        overflow: "hidden",
        boxShadow: "0px 0px 16px 0px rgba(0, 0, 0, 0.04)",
      }}
    >
      {/* Section Header - Contains title and Add button */}
      <Box
        sx={{
          display: "flex",
          width: "100%",
          justifyContent: "space-between",
          alignItems: "center",
          padding: "24px",
          paddingBottom: "0px",
        }}
      >
        <Typography style="h2" color={colors.grey[900]}>
          Media
        </Typography>

        {/* Add Button - Only shown in edit mode */}
        {!readOnly && (
          <Button
            label="Add"
            leftIcon={<AddIcon />}
            color="blue"
            prominence={false}
            onClick={onAddClick}
          />
        )}
      </Box>

      {/* Media Files Table - Displays files in a structured table format */}
      {mediaFiles.length > 0 && (
        <Box sx={{ width: "100%", px: 3 }}>
          {/* Table Header - Column labels for the file information */}
          <Box
            sx={{
              display: "grid",
              gridTemplateColumns: "60px 1fr 1fr 120px 80px",
              gap: 2,
              py: 1,
              borderBottom: `1px solid ${colors.grey[200]}`,
              mb: 1,
            }}
          >
            <Typography style="body3" color={colors.grey[500]}>
              Preview
            </Typography>
            <Typography style="body3" color={colors.grey[500]}>
              Title
            </Typography>
            <Typography style="body3" color={colors.grey[500]}>
              Description
            </Typography>
            <Typography style="body3" color={colors.grey[500]}>
              Category
            </Typography>
            <Typography style="body3" color={colors.grey[500]}>
              Actions
            </Typography>
          </Box>

          {/* Table Rows - Each row represents a media file */}
          {mediaFiles.map((file, index) => (
            <Box
              key={file.id || index}
              sx={{
                display: "grid",
                gridTemplateColumns: "60px 1fr 1fr 120px 80px",
                gap: 2,
                py: 2,
                alignItems: "center",
                borderBottom: index < mediaFiles.length - 1 ? `1px solid ${colors.grey[100]}` : 'none',
              }}
            >
              {/* File Icon Column - Shows type-appropriate icon */}
              <Box
                sx={{
                  width: 48,
                  height: 48,
                  borderRadius: "8px",
                  border: `1px solid ${colors.grey[200]}`,
                  display: "flex",
                  alignItems: "center",
                  justifyContent: "center",
                  backgroundColor: colors.grey[50],
                }}
              >
                {getFileIcon(file.metadata)}
              </Box>

              {/* Title Column - Clickable to open file in new tab */}
              <Box
                onClick={() => handleOpenFile(
                  file.fileId,
                  file.displayName || (typeof file.metadata?.originalFilename === 'string' ? file.metadata.originalFilename : '') || 'Untitled',
                  file.id
                )}
                sx={{
                  cursor: downloadingFileId === file.id ? 'wait' : 'pointer',
                  display: 'flex',
                  alignItems: 'center',
                  gap: 1,
                  '&:hover': {
                    textDecoration: 'underline',
                  },
                }}
              >
                {/* Loading spinner for file operations */}
                {downloadingFileId === file.id && (
                  <Box
                    sx={{
                      width: 12,
                      height: 12,
                      border: `2px solid ${colors.blue[200]}`,
                      borderTop: `2px solid ${colors.blue[600]}`,
                      borderRadius: '50%',
                      animation: 'spin 1s linear infinite',
                      '@keyframes spin': {
                        '0%': { transform: 'rotate(0deg)' },
                        '100%': { transform: 'rotate(360deg)' },
                      },
                    }}
                  />
                )}
                <Typography
                  style="body2"
                  color={downloadingFileId === file.id ? colors.blue[300] : colors.blue[600]}
                >
                  {file.displayName || (typeof file.metadata?.originalFilename === 'string' ? file.metadata.originalFilename : '') || 'Untitled'}
                </Typography>
              </Box>

              {/* Description Column - Shows file caption or default text */}
              <Typography style="body3" color={colors.grey[700]}>
                {file.caption || 'No description'}
              </Typography>

              {/* Category Column - Shows file category as a styled tag */}
              <Box
                sx={{
                  display: "flex",
                  alignItems: "center",
                }}
              >
                <Box
                  sx={{
                    px: 2,
                    py: 0.5,
                    borderRadius: "12px",
                    backgroundColor: colors.grey[100],
                    display: "inline-flex",
                    alignItems: "center",
                    justifyContent: "center",
                    maxWidth: "100%",
                  }}
                >
                  <Typography style="tag2" color={colors.grey[700]}>
                    {file.fileCategory || 'Unknown'}
                  </Typography>
                </Box>
              </Box>

              {/* Action Buttons Column - Download and Delete buttons */}
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 0.5 }}>
                {/* Download Button - Triggers file download */}
                <IconButton
                  onClick={() => handleDownloadFile(
                    file.fileId,
                    file.displayName || (typeof file.metadata?.originalFilename === 'string' ? file.metadata.originalFilename : '') || 'Untitled',
                    file.id
                  )}
                  size="small"
                  disabled={downloadingFileId === file.id}
                  sx={{
                    color: downloadingFileId === file.id ? colors.grey[300] : colors.blue[600],
                    "&:hover": {
                      color: downloadingFileId === file.id ? colors.grey[300] : colors.blue[700],
                      backgroundColor: downloadingFileId === file.id ? 'transparent' : colors.blue[50],
                    },
                  }}
                  aria-label={`Download ${file.displayName || 'file'}`}
                >
                  {downloadingFileId === file.id ? (
                    <Box
                      sx={{
                        width: 16,
                        height: 16,
                        border: `2px solid ${colors.blue[200]}`,
                        borderTop: `2px solid ${colors.blue[600]}`,
                        borderRadius: '50%',
                        animation: 'spin 1s linear infinite',
                        '@keyframes spin': {
                          '0%': { transform: 'rotate(0deg)' },
                          '100%': { transform: 'rotate(360deg)' },
                        },
                      }}
                    />
                  ) : (
                    <DownloadIcon sx={{ fontSize: 16 }} />
                  )}
                </IconButton>

                {/* Delete Button - Only shown in edit mode */}
                {!readOnly && (
                  <IconButton
                    onClick={() => handleDeleteFile(file.fileId, file.id)}
                    size="small"
                    disabled={deletingFileId === file.id}
                    sx={{
                      color: deletingFileId === file.id ? colors.grey[300] : colors.grey[400],
                      "&:hover": {
                        color: deletingFileId === file.id ? colors.grey[300] : colors.grey[600],
                        backgroundColor: deletingFileId === file.id ? 'transparent' : colors.grey[100],
                      },
                    }}
                    aria-label={`Delete ${file.displayName || 'file'}`}
                  >
                    {deletingFileId === file.id ? (
                      <Box
                        sx={{
                          width: 16,
                          height: 16,
                          border: `2px solid ${colors.grey[300]}`,
                          borderTop: `2px solid ${colors.grey[600]}`,
                          borderRadius: '50%',
                          animation: 'spin 1s linear infinite',
                          '@keyframes spin': {
                            '0%': { transform: 'rotate(0deg)' },
                            '100%': { transform: 'rotate(360deg)' },
                          },
                        }}
                      />
                    ) : (
                      <DeleteIcon sx={{ fontSize: 16 }} />
                    )}
                  </IconButton>
                )}
              </Box>
            </Box>
          ))}
        </Box>
      )}

      {/* Comment Section - Enables collaboration and feedback on the media section */}
      <CommentSection
        comments={comments}
        onAddComment={onAddComment}
        onResolveComment={onResolveComment}
        initiallyExpanded={commentsInitiallyExpanded}
        readOnly={readOnly}
      />
    </Box>
  );
};

export default MediaSection; 