import {
  Accordion,
  AccordionDetails,
  AccordionSummary,
  Alert,
  Box,
  Button,
  CircularProgress,
  IconButton,
  Paper,
  Tooltip,
  Typography,
} from "@mui/material";
import ExpandMoreIcon from "@mui/icons-material/ExpandMore";
import EditIcon from "@mui/icons-material/Edit";
import DeleteIcon from "@mui/icons-material/Delete";
import SaveIcon from "@mui/icons-material/Save";
import CancelIcon from "@mui/icons-material/Cancel";
import React from "react";
import {
  PermissionSet,
  PermissionCategory,
  Action,
  Category,
} from "proto/hero/permissions/v1/permissions_pb";
import { colors } from "@/design-system/tokens";
import { CategorizedPermissionEditor } from "../../components/CategorizedPermissionEditor";

interface PermissionSetCardProps {
  permissionSet: PermissionSet;
  isEditingPermissions: boolean;
  isExpanded: boolean;
  editedPermissions: PermissionCategory[]; // Current state of permissions *while editing*
  availableCategories: Category[]; // Master list of all possible categories/actions
  onToggleExpansion: (setId: string) => void;
  onTogglePermissionEdit: (set: PermissionSet) => void;
  onOpenDeleteDialog: (set: PermissionSet) => void;
  onPermissionChange: (
    categoryIndex: number,
    actionIndex: number | 'all',
    value: boolean
  ) => void;
  onSavePermissions: (setId: string) => void;
  onCancelPermissionEdit: () => void;
  isLoading: boolean; // Loading state specifically for saving this set's permissions
  isGloballyDisabled: boolean; // Disabled if another operation (create, edit dialog) is active
  apiError: string | null; // API error specific to editing this set
}

export const PermissionSetCard: React.FC<PermissionSetCardProps> = ({
  permissionSet,
  isEditingPermissions,
  isExpanded,
  editedPermissions,
  availableCategories,
  onToggleExpansion,
  onTogglePermissionEdit,
  onOpenDeleteDialog,
  onPermissionChange,
  onSavePermissions,
  onCancelPermissionEdit,
  isLoading,
  isGloballyDisabled,
  apiError,
}) => {
  const handleExpansionToggle = () => {
    onToggleExpansion(permissionSet.id);
  };

  // Calculate action count for collapsed view
  const { canDoAllCount, specificActionCount } = permissionSet.categories?.reduce(
    (acc: { canDoAllCount: number, specificActionCount: number }, category: PermissionCategory) => {
      if (category.canDoAll) {
        acc.canDoAllCount++;
      } else {
        acc.specificActionCount += category.actions?.filter((action: Action) => action.canDoAction).length || 0;
      }
      return acc;
    },
    { canDoAllCount: 0, specificActionCount: 0 }
  ) || { canDoAllCount: 0, specificActionCount: 0 };

  // --- Render Logic for Actions ---
  const renderActions = () => {
    let categoriesToRender: PermissionCategory[] = [];

    if (isEditingPermissions) {
      categoriesToRender = editedPermissions;
    } else {
      // Display mode: reconstruct based on availableCategories and permissionSet
      categoriesToRender = availableCategories
        .map((availableCat) => {
          const categoryInPermissionSet = permissionSet.categories?.find(
            (cat: PermissionCategory) => cat.name === availableCat.name
          );

          const currentCanDoAll = categoryInPermissionSet?.canDoAll ?? false;
          const actions: Action[] = availableCat.actions.map((actionName: string) => {
            if (currentCanDoAll) {
              return {
                name: actionName,
                canDoAction: true,
              } as Action;
            }
            const actionInPermissionSet = categoryInPermissionSet?.actions?.find(
              (act: Action) => act.name === actionName
            );
            return {
              name: actionName,
              canDoAction: actionInPermissionSet?.canDoAction ?? false,
            } as Action;
          });

          return {
            name: availableCat.name,
            canDoAll: currentCanDoAll,
            actions: actions,
          } as PermissionCategory;
        })
        .filter(
          (cat) => cat.canDoAll || (cat.actions?.some((action: Action) => action.canDoAction) ?? false)
        );
    }

    if (categoriesToRender.length === 0 && !isEditingPermissions) {
        return <Typography sx={{ fontStyle: 'italic', color: colors.grey[600], p: 1 }}>No actions assigned or categories fully allowed.</Typography>;
    }
     if (categoriesToRender.length === 0 && isEditingPermissions) {
        return <Typography sx={{ fontStyle: 'italic', color: colors.grey[600], p: 1 }}>No categories available for editing.</Typography>;
    }

    return (
      <CategorizedPermissionEditor
        permissionsToRender={categoriesToRender}
        mode={isEditingPermissions ? 'edit' : 'view'}
        displayStyle="list"
        isLoading={isLoading} // isLoading for *this* card's save operation
        onPermissionChange={onPermissionChange}
        parentName={permissionSet.name || 'permission-set'}
        isGloballyDisabled={isGloballyDisabled || (isEditingPermissions && isLoading)}
      />
    );
  };


  return (
    <Paper sx={{ mb: 2, overflow: "hidden", border: isEditingPermissions ? `2px solid ${colors.blue[600]}` : '1px solid #e0e0e0' }}>
      <Accordion
        expanded={isExpanded}
        onChange={handleExpansionToggle}
        disableGutters
        elevation={0}
        sx={{ '&:before': { display: 'none' } }} // Remove Accordion's default top border
      >
        <AccordionSummary
          expandIcon={<ExpandMoreIcon />}
          aria-controls={`permissionset-${permissionSet.id}-content`}
          id={`permissionset-${permissionSet.id}-header`}
          sx={{
             backgroundColor: colors.grey[50],
             borderBottom: '1px solid #e0e0e0',
             minHeight: '48px', // Ensure consistent height
             '& .MuiAccordionSummary-content': { alignItems: 'center', justifyContent: 'space-between', mr: 1}, // Space out content and actions
             '&:hover': { backgroundColor: colors.grey[100] }
          }}
        >
            {/* Left side: Name and Count */}
            <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
               <Typography variant="h6">{permissionSet.name}</Typography>
               {!isExpanded && (
                 <Tooltip title={`${canDoAllCount} categories fully allowed, ${specificActionCount} specific actions`} placement="bottom-start">
                    <Typography variant="body2" color="text.secondary" sx={{ whiteSpace: 'nowrap' }}>
                      ({canDoAllCount > 0 ? `${canDoAllCount} All` : ''}
                      {canDoAllCount > 0 && specificActionCount > 0 ? ' / ' : ''}
                      {specificActionCount > 0 ? `${specificActionCount} Specific` : ''}
                      {canDoAllCount === 0 && specificActionCount === 0 ? '0 actions' : ''})
                    </Typography>
                 </Tooltip>
               )}
            </Box>

            {/* Right side: Action Buttons (visible when collapsed or not editing) */}
            {!isEditingPermissions && (
              <Box sx={{ display: 'flex', gap: 0.5 }} onClick={(e) => e.stopPropagation()} > {/* Stop propagation to prevent accordion toggle */}
                 <Tooltip title="Edit Actions">
                   <span> {/* Span needed for Tooltip when button is disabled */}
                     <IconButton
                       size="small"
                       onClick={() => onTogglePermissionEdit(permissionSet)}
                       disabled={isGloballyDisabled}
                       color="primary"
                     >
                       <EditIcon fontSize="small" />
                     </IconButton>
                   </span>
                 </Tooltip>
                 <Tooltip title="Delete Set">
                   <span> {/* Span needed for Tooltip when button is disabled */}
                     <IconButton
                       size="small"
                       onClick={() => onOpenDeleteDialog(permissionSet)}
                       disabled={isGloballyDisabled}
                       color="error"
                     >
                       <DeleteIcon fontSize="small" />
                     </IconButton>
                   </span>
                 </Tooltip>
              </Box>
            )}
            {/* Edit Mode Buttons (visible only when editing this specific set) */}
            {isEditingPermissions && (
               <Box sx={{ display: 'flex', gap: 0.5 }} onClick={(e) => e.stopPropagation()}> {/* Stop propagation */}
                 <Button
                   size="small"
                   variant="contained"
                   color="primary"
                   startIcon={isLoading ? <CircularProgress size={16} color="inherit"/> : <SaveIcon />}
                   onClick={() => onSavePermissions(permissionSet.id)}
                   disabled={isLoading || isGloballyDisabled}
                 >
                   Save
                 </Button>
                 <Button
                   size="small"
                   variant="outlined"
                   color="inherit"
                   startIcon={<CancelIcon />}
                   onClick={onCancelPermissionEdit}
                   disabled={isLoading || isGloballyDisabled}
                 >
                   Cancel
                 </Button>
                 {/* Keep delete accessible even in edit mode */}
                 <Tooltip title="Delete Set">
                   <span>
                     <IconButton
                       size="small"
                       onClick={() => onOpenDeleteDialog(permissionSet)}
                       disabled={isLoading || isGloballyDisabled}
                       color="error"
                       sx={{ ml: 1}} // Add some margin
                     >
                       <DeleteIcon fontSize="small" />
                     </IconButton>
                   </span>
                 </Tooltip>
               </Box>
            )}
        </AccordionSummary>
        <AccordionDetails sx={{ p: 2, borderTop: '1px solid #e0e0e0', backgroundColor: colors.grey[50] }}>
          {/* API Error Display */}
          {apiError && (
             <Alert severity="error" sx={{ mb: 2 }} onClose={onCancelPermissionEdit}> {/* Assuming cancel also clears error */}
               {apiError}
             </Alert>
           )}
          {/* Action Details/Editor */}
          <Typography variant="overline" color="text.secondary" sx={{ display: 'block', mb: 1}}>Actions</Typography>
          {renderActions()}
        </AccordionDetails>
      </Accordion>
    </Paper>
  );
}; 