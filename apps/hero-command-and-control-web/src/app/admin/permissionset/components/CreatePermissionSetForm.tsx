import React, { useState, useEffect } from 'react';
import {
  Box,
  Button,
  TextField,
  Typography,
  Checkbox,
  FormControlLabel,
  Paper,
  CircularProgress,
  Alert,
  IconButton,
} from '@mui/material';
import CloseIcon from '@mui/icons-material/Close';
import { Category, Action, PermissionCategory } from 'proto/hero/permissions/v1/permissions_pb'; // Adjust import path if needed
import { colors } from "@/design-system/tokens";

// Interface for the data structure passed *to* the onSave handler
// Matches NewSetFormData in parent page
interface NewSetFormDataForForm {
  name: string;
  categories: PermissionCategory[];
}

interface CreatePermissionSetFormProps {
  availableCategories: Category[];
  onSave: (setData: NewSetFormDataForForm) => void;
  onCancel: () => void;
  isLoading: boolean;
  isLoadingCategories: boolean;
  apiError: string | null;
  onClearApiError: () => void;
}

export const CreatePermissionSetForm: React.FC<CreatePermissionSetFormProps> = ({
  availableCategories,
  onSave,
  onCancel,
  isLoading,
  isLoadingCategories,
  apiError,
  onClearApiError,
}) => {
  const [name, setName] = useState('');
  const [nameError, setNameError] = useState<string | null>(null);
  // Initialize form state based on available categories, all initially false
  const [formPermissions, setFormPermissions] = useState<PermissionCategory[]>([]);

  useEffect(() => {
    // Initialize or reset form permissions when availableCategories change or component mounts
    if (availableCategories && availableCategories.length > 0) {
        const initialPermissions: PermissionCategory[] = availableCategories.map(cat => ({
        name: cat.name,
        canDoAll: false, // Added for category-level selection
        // Assuming cat.actions are strings (action names) based on CreateRoleForm
        actions: cat.actions.map((actionName: string) => ({ 
            name: actionName, 
            canDoAction: false,
        } as Action)),
        } as PermissionCategory));
        setFormPermissions(initialPermissions);
    } else {
        // Handle the case where categories might be loading or empty
        setFormPermissions([]);
    }
  }, [availableCategories]);


  const handleNameChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    setName(event.target.value);
    if (event.target.value.trim()) {
        setNameError(null); // Clear error if name is not empty
    }
     if (apiError) {
       onClearApiError(); // Clear API error on interaction
     }
  };

  const handlePermissionChange = (
    categoryIndex: number,
    actionIndex: number | 'all', // Modified to accept 'all' for category-level changes
    isChecked: boolean
  ) => {
     if (apiError) {
       onClearApiError(); // Clear API error on interaction
     }
    setFormPermissions(currentPermissions => {
      // Deep copy to avoid direct state mutation
      const updatedPermissions = JSON.parse(JSON.stringify(currentPermissions)) as PermissionCategory[];
      const category = updatedPermissions[categoryIndex];
      if (!category) return updatedPermissions;

      if (actionIndex === 'all') {
        category.canDoAll = isChecked;
        category.actions?.forEach(action => {
          action.canDoAction = isChecked;
        });
      } else {
        if (category.actions?.[actionIndex]) {
          category.actions[actionIndex].canDoAction = isChecked;
          if (!isChecked) {
            category.canDoAll = false;
          } else {
            const allActionsChecked = category.actions.every(act => act.canDoAction);
            if (allActionsChecked) {
              category.canDoAll = true;
            }
          }
        }
      }
      return updatedPermissions;
    });
  };

  const handleSave = () => {
    if (!name.trim()) {
      setNameError('Permission set name cannot be empty.');
      return;
    }
    setNameError(null);
    onClearApiError(); // Clear previous errors


    console.log('formPermissions', formPermissions);

    // Filter categories and actions similar to the update logic
     const categoriesForSave: PermissionCategory[] = formPermissions
      .map(category => {
        if (category.canDoAll) {
          return {
            name: category.name,
            canDoAll: true,
            actions: [], // Send empty actions array if canDoAll is true
            $typeName: "hero.permissions.v1.PermissionCategory" as const
          };
        }
        // If not canDoAll, then filter actions based on what's individually selected.
        const selectedActions = category.actions
            ?.filter(action => action.canDoAction)
            .map(action => ({
                name: action.name,
                canDoAction: true, // Explicitly true as they are filtered
            })) ?? [];
        return {
          name: category.name,
          canDoAll: false,
          actions: selectedActions,
        } as PermissionCategory;
      })
      // Filter out categories that are not "canDoAll" and have no selected actions.
      .filter(category => category.name && (category.canDoAll || (category.actions && category.actions.length > 0)));


    // Construct payload matching the expected NewSetFormDataForForm interface
    const newSetData: NewSetFormDataForForm = {
      name: name.trim(),
      categories: categoriesForSave,
    };
    onSave(newSetData);
  };

  // Display loading indicator while categories are being fetched
  if (isLoadingCategories) {
    return (
      <Paper sx={{ p: 3, mb: 2, display: 'flex', justifyContent: 'center', alignItems: 'center', border: `1px solid ${colors.grey[300]}` }}>
        <CircularProgress size={24} sx={{ mr: 1 }} />
        <Typography>Loading permission categories...</Typography>
      </Paper>
    );
  }

  // Main form rendering
  return (
    <Paper sx={{ p: 3, mb: 2, border: `1px solid ${colors.grey[300]}` }}>
      <Typography variant="h6" gutterBottom sx={{ color: colors.grey[800] }}>
        Create New Permission Set
      </Typography>

      {/* Display API errors */}
      {apiError && (
         <Alert severity="error" sx={{ mb: 2 }} action={
             <IconButton
                 aria-label="close"
                 color="inherit"
                 size="small"
                 onClick={onClearApiError}
             >
                 <CloseIcon fontSize="inherit" />
             </IconButton>
         }>
           {apiError}
         </Alert>
      )}

      {/* Name Input */}
      <TextField
        label="Permission Set Name"
        value={name}
        onChange={handleNameChange}
        fullWidth
        required
        error={!!nameError}
        helperText={nameError}
        sx={{ mb: 3 }}
        InputLabelProps={{ shrink: true }}
        disabled={isLoading} // Disable field when saving
      />

      {/* Assign Actions Section */}
      <Typography variant="subtitle1" gutterBottom sx={{ color: colors.grey[700], mb: 1 }}>
        Assign Actions:
      </Typography>

      {/* Handle case where no categories are available */}
      {formPermissions.length === 0 && !isLoadingCategories && (
          <Typography sx={{ fontStyle: 'italic', color: colors.grey[600], mb: 2 }}>
              No permission categories available to assign actions. Check category definitions.
          </Typography>
      )}

      {/* Map through categories and their actions */}
      {formPermissions.map((category, catIndex) => (
        <Box key={`${category.name}-${catIndex}`} sx={{ mb: 2, pl: 1 }}> {/* Added index to key for safety */}
          <Typography variant="body1" sx={{ fontWeight: 'medium', color: colors.grey[800] }}>
            {category.name}
          </Typography>
          <Box sx={{ display: 'flex', flexDirection: 'column', pl: 2 }}>
            {/* Select All Checkbox for the category */}
            <FormControlLabel
              control={
                <Checkbox
                  checked={category.canDoAll ?? false}
                  onChange={(e) => handlePermissionChange(catIndex, 'all', e.target.checked)}
                  name={`${category.name}-select-all`}
                  size="small"
                  disabled={isLoading}
                />
              }
              label="Select All Actions in this Category"
              sx={{ color: colors.grey[700], mb: 0.5, fontStyle: 'italic' }}
            />
            {/* Ensure actions array exists before mapping */}
            {category.actions?.map((action, actIndex) => (
              <FormControlLabel
                key={`${action.name}-${actIndex}`} // Added index to key for safety
                control={
                  <Checkbox
                    checked={action.canDoAction ?? false} // Default to false if undefined
                    onChange={(e) => handlePermissionChange(catIndex, actIndex, e.target.checked)}
                    name={`${category.name}-${action.name}`}
                    size="small"
                    disabled={isLoading || !!category.canDoAll} // Disable if isLoading or category.canDoAll is true
                  />
                }
                label={action.name} // Display action name
                sx={{ color: colors.grey[700] }}
              />
            ))}
             {/* Handle case where a category might have no actions defined */}
             {!category.actions || category.actions.length === 0 && (
                <Typography sx={{ fontStyle: 'italic', color: colors.grey[500], pl: 2 }}>
                    No actions defined for this category.
                </Typography>
             )}
          </Box>
        </Box>
      ))}

      {/* Form Actions */}
      <Box sx={{ display: 'flex', justifyContent: 'flex-end', gap: 1, mt: 3 }}>
        <Button onClick={onCancel} disabled={isLoading} color="inherit">
          Cancel
        </Button>
        <Button
          variant="contained"
          onClick={handleSave}
          disabled={isLoading || !name.trim()} // Also disable if name is empty
          startIcon={isLoading ? <CircularProgress size={16} color="inherit" /> : null}
        >
          {isLoading ? 'Saving...' : 'Save Permission Set'}
        </Button>
      </Box>
    </Paper>
  );
}; 