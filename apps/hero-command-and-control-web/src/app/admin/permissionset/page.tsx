'use client'
import { colors } from "@/design-system/tokens";
import AddIcon from "@mui/icons-material/Add";
import CloseIcon from '@mui/icons-material/Close';
import {
  <PERSON>ert,
  Box,
  Button,
  CircularProgress,
  Dialog,
  DialogActions,
  DialogContent,
  DialogContentText,
  DialogTitle,
  Typography,
} from "@mui/material";
import { useQueryClient } from "@tanstack/react-query";
import React, { useState } from "react";

// --- Remove placeholder comments and update imports ---
import {
  useCreatePermissionSet,
  useDeletePermissionSet,
  useListActionsByCategory,
  useListPermissionSets,
  useUpdatePermissionSet,
} from "@/app/apis/services/perms/hooks";
import {
  Action,
  Category,
  CreatePermissionSetRequest, // Use the correct type directly
  DeletePermissionSetRequest,
  ListActionsByCategoryRequest,
  ListPermissionSetsRequest,
  PermissionCategory,
  PermissionSet,
  UpdatePermissionSetRequest,
} from "proto/hero/permissions/v1/permissions_pb";
// --- Remove TODO comments for components ---
// import { PermissionSetCard } from "./components/PermissionSetCard";
import { CreatePermissionSetForm } from './components/CreatePermissionSetForm';
import { PermissionSetCard } from "./components/PermissionSetCard";
// import { PermissionSetCard } from './components/PermissionSetCard';

// Define a type for the PermissionSet data structure
// TODO: Update this type based on the actual proto definition
type PermissionSetData = PermissionSet;

// Interface for the data structure passed *from* the Create form
interface NewSetFormData {
  name: string;
  categories: PermissionCategory[];
}

const PermissionSetAdminPage: React.FC = () => {
  const queryClient = useQueryClient();
  // State for dialogs and forms visibility
  const [isCreating, setIsCreating] = useState(false);
  const [editDialogOpen, setEditDialogOpen] = useState(false);
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [selectedSet, setSelectedSet] = useState<PermissionSetData | null>(null);
  const [apiError, setApiError] = useState<string | null>(null);
  const [expandedSets, setExpandedSets] = useState<Record<string, boolean>>({});

  // State for inline permission editing
  const [editingSetId, setEditingSetId] = useState<string | null>(null);
  // TODO: Adjust this state based on how PermissionSet actions are stored
  const [editedPermissions, setEditedPermissions] = useState<PermissionCategory[]>([]);
  const [inlineSaveLoading, setInlineSaveLoading] = useState(false);

  // Fetch permission sets
  const {
    data: setsData,
    isLoading: isLoadingSets,
    error: listSetsError,
  } = useListPermissionSets(
    {} as ListPermissionSetsRequest, // Add typeName if needed, or keep empty object {} if request is empty
    { staleTime: 5 * 60 * 1000 }
  );

  // Fetch available categories and actions
  const { data: categoriesData, isLoading: isLoadingCategories } = useListActionsByCategory(
    {} as ListActionsByCategoryRequest, // Add typeName
    { staleTime: Infinity }
  );
  const availableCategories: Category[] = categoriesData?.categories || [];

  // Mutations
  const createSetMutation = useCreatePermissionSet({ // Use correct hook
    onSuccess: () => {
      queryClient.refetchQueries({ queryKey: ['permissionSets', 'list'] }); // Use correct queryKey
      setIsCreating(false);
      setApiError(null);
    },
    onError: (error: Error) => {
      console.error("Error creating permission set:", error);
      setApiError(`Failed to create set: ${error.message}`);
    },
  });

  const updateSetMutation = useUpdatePermissionSet({ // Use correct hook
    onSuccess: () => {
      queryClient.refetchQueries({ queryKey: ['permissionSets', 'list'] }); // Use correct queryKey
      setEditingSetId(null);
      setEditedPermissions([]);
      setSelectedSet(null);
      setApiError(null);
      setInlineSaveLoading(false);
    },
    onError: (error: Error) => {
      console.error("Error updating permission set:", error);
      setApiError(`Failed to update set: ${error.message}`);
      setInlineSaveLoading(false);
    },
  });

  const deleteSetMutation = useDeletePermissionSet({ // Use correct hook
    onSuccess: () => {
      queryClient.refetchQueries({ queryKey: ['permissionSets', 'list'] }); // Use correct queryKey
      setDeleteDialogOpen(false);
      setSelectedSet(null);
      setApiError(null);
    },
    onError: (error: Error) => {
      console.error("Error deleting permission set:", error);
      setApiError(`Failed to delete set: ${error.message}`);
    },
  });

  // --- Handlers ---  

  const handleCreateOpen = () => {
    setApiError(null);
    setIsCreating(true);
    setEditingSetId(null);
    setEditDialogOpen(false);
  };

  const handleCreateCancel = () => {
    setIsCreating(false);
    setApiError(null);
  };

  // TODO: Decide if an edit dialog is needed or if all editing is inline
  const _handleOpenSetDetailsDialog = (set: PermissionSetData) => {
    setApiError(null);
    setSelectedSet(set);
    setEditDialogOpen(true);
    setEditingSetId(null);
    setIsCreating(false);
  };

  const handleEditClose = () => {
    setEditDialogOpen(false);
    setSelectedSet(null);
    setApiError(null);
  };

  const handleDeleteOpen = (set: PermissionSetData) => {
    setApiError(null);
    setSelectedSet(set);
    setDeleteDialogOpen(true);
  };

  const handleDeleteClose = () => {
    setDeleteDialogOpen(false);
    setSelectedSet(null);
    setApiError(null);
  };

  const handleDeleteConfirm = () => {
    // TODO: Adjust payload based on actual DeletePermissionSetRequest (assuming it needs name)
    if (selectedSet?.id) {
      setApiError(null);
      // Use correct request type and add $typeName
      const payload: DeletePermissionSetRequest = {
        // DeletePermissionSetRequest uses 'id'
        id: selectedSet.id,
      } as DeletePermissionSetRequest;
      deleteSetMutation.mutate(payload);
    }
  };

  // TODO: Adapt this logic for managing actions within a PermissionSet
  const handleTogglePermissionEdit = (set: PermissionSetData) => {
    setApiError(null);
    if (editingSetId === set.id) { // Use ID for uniqueness
      setEditingSetId(null);
      setEditedPermissions([]);
    } else {
      setEditingSetId(set.id);
      setExpandedSets(prev => ({ ...prev, [set.id]: true })); // Use ID for expansion state

      // Initialize based on actions defined in the set
      const initialPermissions: PermissionCategory[] = availableCategories.map(availableCat => {
        const setCategory = set.categories?.find(sc => sc.name === availableCat.name);
        const currentCanDoAll = setCategory?.canDoAll ?? false;

        const actions: Action[] = availableCat.actions.map(actionNameString => {
          const setAction = setCategory?.actions?.find(sa => sa.name === actionNameString);
          // If the category is marked as canDoAll, all its actions are initially true for editing UI
          const initialCanDoAction = currentCanDoAll || (setAction?.canDoAction ?? false);

          return {
            name: actionNameString,
            canDoAction: initialCanDoAction,
          } as Action;
        });

        return {
          name: availableCat.name,
          canDoAll: currentCanDoAll, // Correctly use canDoAll from the set
          actions: actions,
        } as PermissionCategory;
      });

      setEditedPermissions(initialPermissions);
      setEditDialogOpen(false);
      setIsCreating(false);
    }
  };

  // TODO: Adapt this logic for managing actions within a PermissionSet
  const handlePermissionChange = (
    categoryIndex: number,
    actionIndex: number | 'all', // Updated type
    value: boolean
  ) => {
    setEditedPermissions(currentCategories => {
      const updatedCategories = JSON.parse(JSON.stringify(currentCategories)) as PermissionCategory[];
      if (!updatedCategories[categoryIndex]) return updatedCategories; // Should not happen

      const category = updatedCategories[categoryIndex];

      if (actionIndex === 'all') {
        category.canDoAll = value;
        // When canDoAll is toggled, update all actions in that category accordingly
        category.actions = category.actions?.map(action => ({
          ...action,
          canDoAction: value, // Set all actions to the value of canDoAll
        })) || [];
      } else {
        // Specific action changed
        if (category.actions && category.actions[actionIndex]) {
          category.actions[actionIndex].canDoAction = value;
        }
        // Recalculate canDoAll based on the individual actions
        // If any action is false, canDoAll must be false. If all actions are true, canDoAll becomes true.
        category.canDoAll = category.actions?.every(action => action.canDoAction) ?? false;
      }
      return updatedCategories;
    });
    if (editingSetId) {
      setInlineSaveLoading(false);
    }
  };

  // TODO: Adapt this logic for saving actions within a PermissionSet
  const handleSavePermissions = (setId: string) => {
    setApiError(null);
    setInlineSaveLoading(true);

    // Find the original set to get its name and description
    const originalSet = permissionSets.find(s => s.id === setId);
    if (!originalSet) {
      console.error("Original set not found for saving permissions");
      setApiError("Failed to save: Could not find the original permission set.");
      setInlineSaveLoading(false);
      return;
    }

    // Use correct request type and add $typeName
    // The `editedPermissions` state already holds the PermissionCategory[] structure.
    const payload: UpdatePermissionSetRequest = {
      permissionSet: {
        id: setId,
        name: originalSet.name,
        description: originalSet.description,
        categories: editedPermissions.map(cat => ({
          name: cat.name,
          canDoAll: cat.actions ? cat.actions.every(a => a.canDoAction) : false, // Recalculate for safety
          actions: cat.actions ? cat.actions.map(act => ({
            name: act.name,
            canDoAction: act.canDoAction,
          } as Action)) : [],
        })),
      } as PermissionSet,
    } as UpdatePermissionSetRequest;
    updateSetMutation.mutate(payload);
  };

  const handleCancelPermissionEdit = () => {
    setEditingSetId(null);
    setEditedPermissions([]);
    setApiError(null);
    setInlineSaveLoading(false);
  };

  const handleToggleSetExpansion = (setId: string) => {
    setExpandedSets(prev => ({ ...prev, [setId]: !prev[setId] }));
  };

  // TODO: Adapt this logic for saving a new PermissionSet
  const handleSaveNewSet = (formData: NewSetFormData) => {

    console.log("handleSaveNewSet", formData);
    setApiError(null);
    // Use correct request type and add $typeName
    // formData.categories should be PermissionCategory[] from the form.

    const createPayload: CreatePermissionSetRequest = {
      permissionSet: {
        id: '', // Add empty ID to satisfy the PermissionSet type for the request
        name: formData.name, // Name comes directly from form data
        description: "", // Assuming description is not part of the create form for now
        // Map formData.categories to ensure correct structure and $typeNames
        categories: formData.categories.map(cat => ({
          name: cat.name,
          canDoAll: cat.canDoAll,
          actions: cat.actions ? cat.actions.map(act => ({
            name: act.name,
            canDoAction: act.canDoAction,
          } as Action)) : [],
        })),
      } as PermissionSet,
    } as CreatePermissionSetRequest;
    createSetMutation.mutate(createPayload);
  };

  // Use actual permission sets data field from the response
  const permissionSets: PermissionSetData[] = setsData?.permissionSets || [];
  const isMutating =
    createSetMutation.isPending ||
    updateSetMutation.isPending ||
    deleteSetMutation.isPending;

  const inlineCreateLoading = createSetMutation.isPending;
  const dialogDeleteLoading = deleteSetMutation.isPending && deleteDialogOpen;

  return (
    <Box sx={{ p: 3, width: "100%", display: 'flex', flexDirection: 'column', height: 'calc(100vh - 64px)' }}>
      {/* Header */}
      <Box
        sx={{
          display: "flex",
          justifyContent: "space-between",
          alignItems: "center",
          mb: 3,
        }}
      >
        <Typography variant="h4" sx={{ color: colors.grey[900] }}>
          Admin - Permission Set Management
        </Typography>
        <Button
          variant="contained"
          startIcon={isCreating ? <CloseIcon /> : <AddIcon />}
          onClick={isCreating ? handleCreateCancel : handleCreateOpen}
          color={isCreating ? "inherit" : "primary"}
          disabled={editDialogOpen || !!editingSetId}
        >
          {isCreating ? "Cancel Creation" : "Create New Set"}
        </Button>
      </Box>

      {/* Scrollable Content Area */}
      <Box sx={{ flexGrow: 1, overflowY: 'auto', pr: 1 }}>

        {/* Global Errors */}
        {listSetsError && (
          <Alert severity="error" sx={{ mb: 2 }}>
            Error loading permission sets: {listSetsError.message}
          </Alert>
        )}
        {/* Display API errors NOT specific to a dialog/inline form */}
        {apiError && !editDialogOpen && !deleteDialogOpen && !isCreating && !editingSetId && (
          <Alert severity="error" sx={{ mb: 2 }} onClose={() => setApiError(null)}>
            {apiError}
          </Alert>
        )}

        {/* Inline Create Permission Set Form */}
        {isCreating && (
          // TODO: Replace with actual CreatePermissionSetForm component
          <CreatePermissionSetForm
            availableCategories={availableCategories}
            onSave={handleSaveNewSet} // Use the dedicated create handler
            onCancel={handleCreateCancel}
            isLoading={inlineCreateLoading}
            isLoadingCategories={isLoadingCategories}
            apiError={apiError} // Pass down the API error state
            onClearApiError={() => setApiError(null)} // Allow form to clear the error
          />
        )}

        {/* Permission Sets List/Loading Indicator */}
        {isLoadingSets ? (
          <Box sx={{ display: 'flex', justifyContent: 'center', p: 3 }}>
            <CircularProgress />
          </Box>
        ) : permissionSets.length === 0 && !isCreating ? (
          <Typography sx={{ textAlign: 'center', p: 3 }}>
            No permission sets found. Click &quot;Create New Set&quot; to add one.
          </Typography>
        ) : (
          // Map over sets and render PermissionSetCard for each
          permissionSets.map((set) => {
            const isEditingThisSetPermissions = editingSetId === set.id; // Use ID
            const isExpanded = expandedSets[set.id] ?? false; // Use ID

            return (
              <PermissionSetCard
                key={set.id}
                permissionSet={set}
                isEditingPermissions={isEditingThisSetPermissions}
                isExpanded={isExpanded}
                editedPermissions={isEditingThisSetPermissions ? editedPermissions : []} // Pass state only if editing this set
                availableCategories={availableCategories} // Pass full list for reconstruction
                onToggleExpansion={handleToggleSetExpansion}
                onTogglePermissionEdit={handleTogglePermissionEdit}
                onOpenDeleteDialog={handleDeleteOpen}
                onPermissionChange={handlePermissionChange} // Pass handler
                onSavePermissions={handleSavePermissions}
                onCancelPermissionEdit={handleCancelPermissionEdit}
                isLoading={isEditingThisSetPermissions && inlineSaveLoading} // Pass loading state only if editing this set
                isGloballyDisabled={isMutating || isCreating || editDialogOpen} // Disable buttons if any mutation, create, or edit dialog is active
                apiError={isEditingThisSetPermissions ? apiError : null} // Pass API error only if editing this set
              />
            );
          })
        )}

      </Box> {/* Close Scrollable Content Area */}

      {/* Edit Permission Set Dialog (Optional - maybe only name/description?) */}
      <Dialog
        open={editDialogOpen} // Currently unused for inline editing
        onClose={handleEditClose}
        fullWidth
        maxWidth="sm"
      >
        <DialogTitle>
          Edit Permission Set: {selectedSet?.name || ''}
        </DialogTitle>
        <DialogContent>
          {/* Show Edit API error within dialog */}
          {apiError && editDialogOpen && (
            <Alert severity="error" sx={{ mb: 2 }} onClose={() => setApiError(null)}>
              {apiError}
            </Alert>
          )}

          <Typography color="text.secondary" sx={{ mt: 2, textAlign: 'center', fontStyle: 'italic' }}>
            Permission set name and actions are edited inline.
          </Typography>

        </DialogContent>
        <DialogActions sx={{ p: '16px 24px' }}>
          <Button onClick={handleEditClose}> Close </Button>
          {/* Save button removed as editing happens inline */}
        </DialogActions>
      </Dialog>

      {/* Delete Confirmation Dialog */}
      <Dialog
        open={deleteDialogOpen}
        onClose={handleDeleteClose}
        aria-labelledby="alert-dialog-title"
        aria-describedby="alert-dialog-description"
      >
        <DialogTitle id="alert-dialog-title">Confirm Deletion</DialogTitle>
        <DialogContent>
          <DialogContentText id="alert-dialog-description">
            Are you sure you want to delete the permission set &quot;{selectedSet?.name}&quot;?
            This action cannot be undone.
          </DialogContentText>
          {/* Show Delete API error within dialog */}
          {apiError && deleteDialogOpen && (
            <Alert severity="error" sx={{ mt: 2 }} onClose={() => setApiError(null)}>
              {apiError}
            </Alert>
          )}
        </DialogContent>
        <DialogActions>
          <Button onClick={handleDeleteClose} disabled={dialogDeleteLoading}>Cancel</Button>
          <Button
            onClick={handleDeleteConfirm}
            color="error"
            variant="contained"
            disabled={dialogDeleteLoading}
            autoFocus
          >
            {dialogDeleteLoading ? <CircularProgress size={24} /> : "Delete"}
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default PermissionSetAdminPage; 