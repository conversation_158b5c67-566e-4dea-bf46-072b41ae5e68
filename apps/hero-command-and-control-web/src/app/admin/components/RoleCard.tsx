import { useAddAssetToRole, useListRoleAssets, useRemoveAssetFromRole } from '@/app/apis/services/perms/hooks';
import { colors } from '@/design-system/tokens';
import ChevronRightIcon from '@mui/icons-material/ChevronRight';
import DeleteIcon from '@mui/icons-material/Delete';
import EditIcon from '@mui/icons-material/Edit';
import ExpandMoreIcon from '@mui/icons-material/ExpandMore';
import {
    Box,
    Button,
    Checkbox,
    CircularProgress,
    IconButton,
    Paper,
    TextField,
    Tooltip,
    Typography
} from '@mui/material';
import { Asset as AssetType } from 'proto/hero/assets/v2/assets_pb';
import { AddAssetToRoleRequest, Category, ListRoleAssetsRequest, PermissionCategory, PermissionSet, RemoveAssetFromRoleRequest, Role } from 'proto/hero/permissions/v1/permissions_pb';
import React, { useState } from 'react';
import { CategorizedPermissionEditor } from './CategorizedPermissionEditor';

// Define the RoleData type based on Role
type RoleData = Role;

interface RoleCardProps {
    role: RoleData;
    isEditingPermissions: boolean;
    isExpanded: boolean;
    editedPermissions: PermissionCategory[]; // Only relevant when isEditingPermissions is true
    availableCategories: Category[]; // Needed for rendering full permission list
    onToggleExpansion: (roleName: string) => void;
    onTogglePermissionEdit: (role: RoleData) => void;
    onOpenDeleteDialog: (role: RoleData) => void;
    onPermissionChange: (categoryIndex: number, actionIndex: number | 'all', value: boolean) => void;
    onSavePermissions: (roleId: string, roleName: string) => void;
    onCancelPermissionEdit: () => void;
    isLoading: boolean; // Inline save loading state for *this* card
    isGloballyDisabled: boolean; // Disables edit/delete if other actions are pending
    apiError: string | null; // Error specific to this card's inline save

    // Props for Permission Sets
    availablePermissionSets: PermissionSet[];
    editedSelectedPermissionSetIds: string[]; // Relevant when isEditingPermissions is true
    onSelectedPermissionSetsChange: (selectedIds: string[]) => void;
}

export const RoleCard: React.FC<RoleCardProps> = ({
    role,
    isEditingPermissions,
    isExpanded,
    editedPermissions,
    onToggleExpansion,
    onTogglePermissionEdit,
    onOpenDeleteDialog,
    onPermissionChange,
    onSavePermissions,
    onCancelPermissionEdit,
    isLoading,
    isGloballyDisabled,
    apiError,
    availablePermissionSets,
    editedSelectedPermissionSetIds,
    onSelectedPermissionSetsChange,
}) => {
    const currentRoleCategories = role.categories || [];
    const [newUserName, setNewUserName] = useState('');

    // --- Hooks ---
    const { mutate: addAssetToRole, isPending: isAddingAsset, error: addAssetError } = useAddAssetToRole();
    const { mutate: removeAssetFromRole, isPending: _isRemovingAsset } = useRemoveAssetFromRole();
    const {
        data: roleAssets,
        isLoading: isLoadingAssets,
        error: assetsError
    } = useListRoleAssets(
        { roleId: role.id } as ListRoleAssetsRequest,
        {
            enabled: isExpanded && !isEditingPermissions, // Only fetch when expanded and NOT editing permissions
        }
    );

    // --- Event Handlers ---
    const onRemoveAssetFromRole = (roleId: string, assetId: string): void => {
        removeAssetFromRole({ roleId, assetId } as RemoveAssetFromRoleRequest);
    };

    const onAddAssetToRole = (roleId: string, assetId: string) => {
        addAssetToRole({ roleId, assetId } as AddAssetToRoleRequest, {
            onSuccess: () => {
                setNewUserName('');
                // Optionally: invalidate role assets query to refetch
                // queryClient.invalidateQueries(['listRoleAssets', roleName]);
            },
            // onError: (error) => { // Handle error if needed, e.g., show a snackbar }
        });
    };

    // Handler for the add user button click
    const handleAddAssetClick = () => {
        if (newUserName.trim()) {
            onAddAssetToRole(role.id, newUserName.trim());
        }
    };

    // --- Derived State/Calculations ---
    let canDoAllCount = 0;
    let specificActionCount = 0;
    if (!isExpanded) { // Only calculate if needed
        currentRoleCategories.forEach(category => {
            if (category.canDoAll) {
                canDoAllCount++;
            } else {
                category.actions?.forEach(action => {
                    if (action.canDoAction) {
                        specificActionCount++;
                    }
                });
            }
        });
    }

    return (
        <Paper key={role.name} sx={{ p: 2, mb: 3, width: "100%" }}>
            {/* Role Header and Actions */}
            <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: isEditingPermissions ? 1 : (isExpanded ? 2 : 0) /* No bottom margin if collapsed */ }}>
                {/* Clickable area for expansion */}
                <Box sx={{ display: 'flex', alignItems: 'center', cursor: 'pointer' }} onClick={() => onToggleExpansion(role.name)}>
                    {/* Expansion Icon */}
                    <IconButton size="small" sx={{ mr: 0.5 }} aria-label={isExpanded ? "Collapse role" : "Expand role"}>
                        {isExpanded ? <ExpandMoreIcon /> : <ChevronRightIcon />}
                    </IconButton>
                    <Typography variant="h6" component="div">
                        {role.name}
                    </Typography>
                    {/* Collapsed Permission Summary */}
                    {!isExpanded && (
                        <Tooltip title={`${canDoAllCount} categories fully allowed, ${specificActionCount} specific actions allowed`} placement="bottom-start">
                            {/* Wrap Typography in a Box for Tooltip */}
                            <Box component="span" sx={{ display: 'inline-flex', alignItems: 'center' }}>
                                <Typography variant="caption" color="text.secondary" sx={{ ml: 1.5, display: 'inline-flex', alignItems: 'center' }}>
                                    ({canDoAllCount} All / {specificActionCount} Specific)
                                </Typography>
                            </Box>
                        </Tooltip>
                    )}
                </Box>
                <Box>
                    {/* Toggle Permissions Edit Button */}
                    <Tooltip title={isEditingPermissions ? "Cancel Permission Edit" : "Edit Permissions"}>
                        <span> {/* Added span for Tooltip when button is disabled */}
                            <IconButton
                                size="small"
                                onClick={() => onTogglePermissionEdit(role)}
                                sx={{ mr: 1 }}
                                color={isEditingPermissions ? "primary" : "default"}
                                disabled={isLoading || isGloballyDisabled} // Disable if saving *this* role, or globally disabled
                            >
                                <EditIcon fontSize="small" />
                            </IconButton>
                        </span>
                    </Tooltip>
                    <Tooltip title="Delete Role">
                        <span> {/* Added span for Tooltip when button is disabled */}
                            <IconButton
                                size="small"
                                onClick={() => onOpenDeleteDialog(role)}
                                color="error"
                                disabled={isEditingPermissions || isLoading || isGloballyDisabled} // Disable if editing *this* role, saving *this* role, or globally disabled
                            >
                                <DeleteIcon fontSize="small" />
                            </IconButton>
                        </span>
                    </Tooltip>
                    {/* TODO: Add button to open the Name/Description Edit Dialog? */}
                    {/* Example:
                    <Tooltip title="Edit Role Details">
                       <IconButton size="small" onClick={() => handleOpenRoleDetailsDialog(role)} disabled={...}>
                           <SettingsIcon fontSize="small" />
                       </IconButton>
                    </Tooltip> */}
                </Box>
            </Box>

            {/* Show Save/Cancel when editing permissions inline */}
            {isEditingPermissions && (
                <Box sx={{ display: 'flex', justifyContent: 'flex-end', alignItems: 'center', mb: 2, gap: 1 }}>
                    {/* Show inline save error specifically here */}
                    {apiError && (
                        <Typography color="error" variant="caption" sx={{ flexGrow: 1, textAlign: 'right', mr: 1 }}>{apiError}</Typography>
                    )}
                    <Button onClick={onCancelPermissionEdit} size="small" disabled={isLoading}>
                        Cancel
                    </Button>
                    <Button
                        variant="contained"
                        color="primary"
                        size="small"
                        onClick={() => onSavePermissions(role.id, role.name)} // Save inline permission changes
                        disabled={isLoading} // Disable while saving
                    >
                        {isLoading ? <CircularProgress size={20} color="inherit" /> : "Save Permissions"}
                    </Button>
                </Box>
            )}

            {/* Permissions Display/Edit Area - Conditionally Rendered */}
            {isExpanded && (
                <Box sx={{ mt: 2 }}>
                    {/* Asset Listing and Assigned Permission Sets (only when not editing permissions) */}
                    {!isEditingPermissions && (
                        <>
                            <Box sx={{ mt: 3, pt: 2, borderTop: `1px solid ${colors.grey[200]}` }}>
                                <Typography variant="subtitle1" sx={{ mb: 1, fontWeight: 'medium' }}>Assigned Users/Assets</Typography>
                                {isLoadingAssets && <CircularProgress size={24} />}
                                {assetsError && <Typography color="error" variant="body2">Error loading assets: {assetsError.message}</Typography>}
                                {!isLoadingAssets && !assetsError && (
                                    roleAssets && roleAssets.assets && roleAssets.assets.length > 0 ? (
                                        // Use MUI List for better structure and spacing
                                        <Box component="ul" sx={{ listStyle: 'none', p: 0, m: 0 }}>
                                            {roleAssets.assets.map((asset: AssetType, index) => {
                                                const userName = asset.name; // Assuming asset.name is the user identifier
                                                // const isRemovingThisUser = isRemovingUser?.[userName]; // Check specific loading state if implemented
                                                // const removeErrorForThisUser = removeUserError?.[userName]; // Check specific error state if implemented

                                                return (
                                                    <Box
                                                        component="li"
                                                        key={index} // Use a more stable key if available, like asset.id
                                                        sx={{
                                                            display: 'flex',
                                                            justifyContent: 'space-between',
                                                            alignItems: 'center',
                                                            py: 0.5, // Add some padding
                                                            borderBottom: index < roleAssets.assets.length - 1 ? `1px dashed ${colors.grey[200]}` : 'none', // Dashed divider
                                                        }}
                                                    >
                                                        <Typography variant="body2">{userName}</Typography>
                                                        {/* TODO: Display removeErrorForThisUser if implemented */}
                                                        <Tooltip title={`Remove ${userName} from ${role.name}`}>
                                                            <span> {/* Span for tooltip when button disabled */}
                                                                <IconButton
                                                                    size="small"
                                                                    color="warning" // Or 'error'
                                                                    onClick={() => onRemoveAssetFromRole(role.id, asset.id)}
                                                                    disabled={isGloballyDisabled || isLoading} // Disable if globally disabled or permissions save is loading for this card
                                                                    // disabled={isGloballyDisabled || isLoading || isRemovingThisUser} // Include specific loading state if implemented
                                                                    aria-label={`Remove user ${userName} from role ${role.name}`}
                                                                >
                                                                    {/* TODO: Show spinner if isRemovingThisUser is true */}
                                                                    <DeleteIcon fontSize="small" />
                                                                </IconButton>
                                                            </span>
                                                        </Tooltip>
                                                    </Box>
                                                );
                                            })}
                                        </Box>
                                    ) : (
                                        <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>No users/assets assigned to this role.</Typography>
                                    )
                                )}
                                {/* Add User Section */}
                                <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mt: 2 }}>
                                    <TextField
                                        label="Username or Asset ID"
                                        variant="outlined"
                                        size="small"
                                        value={newUserName}
                                        onChange={(e) => setNewUserName(e.target.value)}
                                        disabled={isAddingAsset || isGloballyDisabled || isLoading}
                                        sx={{ flexGrow: 1 }}
                                    />
                                    <Button
                                        variant="outlined"
                                        size="small"
                                        onClick={handleAddAssetClick}
                                        disabled={!newUserName.trim() || isAddingAsset || isGloballyDisabled || isLoading}
                                        startIcon={isAddingAsset ? <CircularProgress size={16} /> : null}
                                    >
                                        Add User/Asset
                                    </Button>
                                </Box>
                                {/* Display Add User Error */}
                                {addAssetError && (
                                    <Typography color="error" variant="caption" sx={{ mt: 1, display: 'block' }}>
                                        Error adding asset: {addAssetError.message}
                                    </Typography>
                                )}
                            </Box>

                            {/* Assigned Permission Sets Display */}
                            <Box sx={{ mt: 3, pt: 2, borderTop: `1px solid ${colors.grey[200]}` }}>
                                <Typography variant="subtitle1" sx={{ mb: 1, fontWeight: 'medium' }}>
                                    Assigned Permission Sets
                                </Typography>
                                {role.permissionSets && role.permissionSets.length > 0 ? (
                                    <Box component="ul" sx={{ listStyle: 'none', p: 0, m: 0, mt: 1 }}>
                                        {role.permissionSets.map((ps: PermissionSet) => (
                                            <Box component="li" key={ps.id} sx={{ py: 1, borderBottom: `1px dashed ${colors.grey[100]}`, '&:last-child': { borderBottom: 'none' } }}>
                                                <Typography variant="body2" sx={{ fontWeight: 'medium' }}>{ps.name}</Typography>
                                                {ps.description && <Typography variant="caption" color="text.secondary" sx={{ display: 'block', mt: 0.5 }}>{ps.description}</Typography>}
                                            </Box>
                                        ))}
                                    </Box>
                                ) : (
                                    <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
                                        No permission sets assigned to this role.
                                    </Typography>
                                )}
                            </Box>
                        </>
                    )}

                    {/* Permissions Table for Categories & Actions */}
                    {(currentRoleCategories.length > 0) || isEditingPermissions ? (
                        <CategorizedPermissionEditor
                            permissionsToRender={isEditingPermissions ? editedPermissions : currentRoleCategories}
                            mode={isEditingPermissions ? 'edit' : 'view'}
                            displayStyle="table"
                            isLoading={isLoading}
                            onPermissionChange={onPermissionChange}
                            parentName={role.name}
                            isGloballyDisabled={isGloballyDisabled}
                        />
                    ) : (
                        !isEditingPermissions && <Typography variant="body2" color="text.secondary">No permission categories defined for this role.</Typography> // Show this only in view mode if no categories
                    )}

                    {/* Attach Permission Sets Section - Only in Edit Mode */}
                    {isEditingPermissions && (
                        <Box sx={{ mt: 3, pt: 2, borderTop: `1px solid ${colors.grey[200]}` }}>
                            <Typography variant="subtitle1" sx={{ mb: 2, fontWeight: 'medium' }}>
                                Attach Permission Sets
                            </Typography>
                            {availablePermissionSets && availablePermissionSets.length > 0 ? (
                                <Box sx={{ maxHeight: '250px', overflowY: 'auto', pr: 1 }}>
                                    {availablePermissionSets.map((ps: PermissionSet) => (
                                        <Box key={ps.id} sx={{ display: 'flex', alignItems: 'center', mb: 0.5 }}>
                                            <Checkbox
                                                size="small"
                                                checked={editedSelectedPermissionSetIds.includes(ps.id)}
                                                onChange={(e) => {
                                                    const currentSelectedIds = editedSelectedPermissionSetIds || [];
                                                    const newSelectedIds = e.target.checked
                                                        ? [...currentSelectedIds, ps.id]
                                                        : currentSelectedIds.filter(id => id !== ps.id);
                                                    onSelectedPermissionSetsChange(newSelectedIds);
                                                }}
                                                disabled={isLoading}
                                                sx={{ py: 0.5, mr: 0.5 }}
                                            />
                                            <Tooltip title={ps.description || 'No description'} placement="right">
                                                <Typography variant="body2">{ps.name}</Typography>
                                            </Tooltip>
                                        </Box>
                                    ))}
                                </Box>
                            ) : (
                                <Typography variant="body2" color="text.secondary">
                                    No permission sets available to attach.
                                </Typography>
                            )}
                        </Box>
                    )}
                </Box>
            )}
        </Paper>
    );
}; 