import React from 'react';
import {
    Box,
    Checkbox,
    FormControlLabel,
    Paper,
    Table,
    TableBody,
    TableCell,
    TableContainer,
    TableHead,
    TableRow,
    Typography,
} from '@mui/material';
import CheckCircleIcon from '@mui/icons-material/CheckCircle';
import CancelIcon from '@mui/icons-material/Cancel';
import { PermissionCategory } from 'proto/hero/permissions/v1/permissions_pb';
import { colors } from '@/design-system/tokens';

interface CategorizedPermissionEditorProps {
    permissionsToRender: PermissionCategory[];
    mode: 'edit' | 'view';
    displayStyle: 'list' | 'table'; // 'list' for PermissionSetCard, 'table' for RoleCard
    isLoading: boolean; // For disabling inputs during save
    onPermissionChange: (
        categoryIndex: number,
        actionIndex: number | 'all', // 'all' for canDoAll
        value: boolean
    ) => void;
    parentName: string; // For aria-labels, e.g., role name or permission set name
    isGloballyDisabled?: boolean; // Optional: if the whole UI section is disabled
}

export const CategorizedPermissionEditor: React.FC<CategorizedPermissionEditorProps> = ({
    permissionsToRender,
    mode,
    displayStyle,
    isLoading,
    onPermissionChange,
    parentName,
    isGloballyDisabled,
}) => {
    const isReadOnly = mode === 'view';

    if (!permissionsToRender || permissionsToRender.length === 0) {
        return (
            <Typography sx={{ fontStyle: 'italic', color: colors.grey[600], p: 1, textAlign: displayStyle === 'list' ? 'left' : 'center', mt: displayStyle === 'table' ? 2 : 0 }}>
                {displayStyle === 'list' ? 'No actions assigned.' : 'No permission categories defined.'}
            </Typography>
        );
    }

    // LIST DISPLAY STYLE (for PermissionSetCard)
    if (displayStyle === 'list') {
        return (
            <Box>
                {permissionsToRender.map((category, catIndex) => (
                    <Box key={`${parentName}-cat-${category.name}-${catIndex}`} sx={{ mb: 1 }}>
                        <Typography variant="subtitle2" sx={{ fontWeight: 'bold', mb: 0.5 }}>
                            {category.name || 'Unnamed Category'}
                        </Typography>
                        <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 0.5, pl: 1 }}>
                            {/* 'Allow All' checkbox for list style in edit mode */}
                            {!isReadOnly && (
                                <FormControlLabel
                                    control={
                                        <Checkbox
                                            size="small"
                                            checked={!!category.canDoAll}
                                            onChange={(e) => onPermissionChange(catIndex, 'all', e.target.checked)}
                                            disabled={isLoading || isGloballyDisabled}
                                            sx={{ py: 0, px: 0.5, fontStyle: 'italic' }}
                                        />
                                    }
                                    label="Allow All Actions in Category"
                                    sx={{ mr: 1, width: '100%', mb: 0.5 }} // Make it full width for clarity
                                />
                            )}
                             {/* Display 'Allow All' status in view mode for list style */}
                             {isReadOnly && category.canDoAll && (
                                <Box sx={{ display: 'flex', alignItems: 'center', mb: 0.5, pl: 0, width: '100%'}}>
                                    <CheckCircleIcon color="success" fontSize="small" sx={{ mr: 0.5 }} />
                                    <Typography variant="body2" color="text.secondary" sx={{ fontStyle: 'italic' }}>
                                        All actions in this category are allowed.
                                    </Typography>
                                </Box>
                            )}
                            {/* Only show individual actions if not canDoAll or in view mode and not canDoAll */}
                            {(!category.canDoAll || isReadOnly) && category.actions?.map((action, actIndex) => (
                                <FormControlLabel
                                    key={`${parentName}-act-${category.name}-${action.name}-${actIndex}`}
                                    control={
                                        <Checkbox
                                            size="small"
                                            checked={isReadOnly ? (!!category.canDoAll || !!action.canDoAction) : !!action.canDoAction}
                                            onChange={(e) => onPermissionChange(catIndex, actIndex, e.target.checked)}
                                            disabled={!isReadOnly && (isLoading || isGloballyDisabled || !!category.canDoAll)}
                                            sx={{ py: 0, px: 0.5 }}
                                        />
                                    }
                                    label={action.name || 'Unnamed Action'}
                                    sx={{ mr: 1 }}
                                />
                            ))}
                        </Box>
                    </Box>
                ))}
            </Box>
        );
    }

    // TABLE DISPLAY STYLE (for RoleCard)
    return (
        <Box sx={{ mt: 2, pr: isReadOnly ? 0 : 1 }}>
            {permissionsToRender.map((category, categoryIndex) => {
                const categoryName = category.name || '';
                return (
                    <Box key={`${parentName}-${categoryName || 'category'}-${categoryIndex}`} sx={{ mb: 2 }}>
                        <Box sx={{ display: 'flex', alignItems: 'center', mb: 1, p: 0 }}>
                            <Typography variant="subtitle2" sx={{ fontWeight: 'bold' }}>
                                {categoryName || 'Unnamed Category'}
                            </Typography>
                        </Box>
                        <TableContainer component={Paper} variant="outlined" sx={{ mb: 1 }}>
                            <Table size="small" aria-label={`${parentName} ${categoryName || 'category'} permissions`}>
                                <TableHead>
                                    <TableRow>
                                        <TableCell sx={{ fontWeight: 'bold' }}>Action</TableCell>
                                        <TableCell align="center" sx={{ fontWeight: 'bold' }}>Allowed</TableCell>
                                    </TableRow>
                                </TableHead>
                                <TableBody>
                                    <TableRow sx={{ backgroundColor: !isReadOnly ? colors.grey[50] : 'inherit' }}>
                                        <TableCell component="th" scope="row" sx={{ fontStyle: 'italic' }}>
                                            Allow All Actions in Category
                                        </TableCell>
                                        <TableCell align="center">
                                            {isReadOnly ? (
                                                category.canDoAll ? <CheckCircleIcon color="success" fontSize="small" /> : <CancelIcon color="disabled" fontSize="small" />
                                            ) : (
                                                <Checkbox
                                                    size="small"
                                                    checked={!!category.canDoAll}
                                                    onChange={(e) => onPermissionChange(categoryIndex, 'all', e.target.checked)}
                                                    disabled={isLoading || isGloballyDisabled}
                                                />
                                            )}
                                        </TableCell>
                                    </TableRow>
                                    {category.actions?.map((action, actionIndex) => (
                                        <TableRow key={action.name || `action-${actionIndex}`}>
                                            <TableCell component="th" scope="row">
                                                {action.name || 'Unnamed Action'}
                                            </TableCell>
                                            <TableCell align="center">
                                                {isReadOnly ? (
                                                    (!!category.canDoAll || !!action.canDoAction) ? <CheckCircleIcon color="success" fontSize="small" /> : <CancelIcon color="disabled" fontSize="small" />
                                                ) : (
                                                    <Checkbox
                                                        size="small"
                                                        checked={!!action.canDoAction} // Individual checkboxes are not checked if canDoAll is true in edit mode
                                                        onChange={(e) => onPermissionChange(categoryIndex, actionIndex, e.target.checked)}
                                                        disabled={isLoading || isGloballyDisabled || !!category.canDoAll}
                                                    />
                                                )}
                                            </TableCell>
                                        </TableRow>
                                    ))}
                                </TableBody>
                            </Table>
                        </TableContainer>
                    </Box>
                );
            })}
        </Box>
    );
}; 