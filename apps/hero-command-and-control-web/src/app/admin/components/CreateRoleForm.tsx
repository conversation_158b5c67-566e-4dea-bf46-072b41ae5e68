import React, { useState, useEffect, useCallback } from 'react';
import {
    Box,
    Button,
    CircularProgress,
    Paper,
    TextField,
    Typography,
    Checkbox,
    Table,
    TableBody,
    TableCell,
    TableContainer,
    TableHead,
    TableRow,
    Alert,
    DialogActions // Import DialogActions if used within the form structure
} from '@mui/material';
import { colors } from '@/design-system/tokens';
import ExpandMoreIcon from '@mui/icons-material/ExpandMore';
import ChevronRightIcon from '@mui/icons-material/ChevronRight';
import {
    PermissionCategory,
    Action,
    Category,
    Role,
    PermissionSet
} from 'proto/hero/permissions/v1/permissions_pb';

interface CreateRoleFormProps {
    availableCategories: Category[];
    availablePermissionSets: PermissionSet[];
    onSave: (roleData: Role) => void;
    onCancel: () => void;
    isLoading: boolean; // Loading state for the save mutation
    isLoadingCategories: boolean;
    isLoadingPermissionSets: boolean;
    apiError: string | null;
    onClearApiError: () => void;
}

export const CreateRoleForm: React.FC<CreateRoleFormProps> = ({
    availableCategories,
    availablePermissionSets,
    onSave,
    onCancel,
    isLoading,
    isLoadingCategories,
    isLoadingPermissionSets,
    apiError,
    onClearApiError
}) => {
    const [roleName, setRoleName] = useState("");
    const [editedPermissions, setEditedPermissions] = useState<PermissionCategory[]>([]);
    const [selectedPermissionSetIds, setSelectedPermissionSetIds] = useState<string[]>([]);
    const [collapsedCategories, setCollapsedCategories] = useState<Record<string, boolean>>({});

    // Initialize permissions when categories become available or form mounts
    const initializePermissionsForCreate = useCallback(() => {
        const initialPermissions: PermissionCategory[] = availableCategories.map((cat: Category) => ({
            name: cat.name,
            canDoAll: false,
            actions: cat.actions.map((actionName: string): Action => ({
                name: actionName,
                canDoAction: false,
            } as Action)),
        } as PermissionCategory));
        setEditedPermissions(initialPermissions);

        // Initialize collapsed state
        const initialCollapsedState: Record<string, boolean> = {};
        availableCategories.forEach(cat => {
            initialCollapsedState[cat.name || ''] = false; // Start expanded
        });
        setCollapsedCategories(initialCollapsedState);
    }, [availableCategories]);

    useEffect(() => {
        initializePermissionsForCreate();
    }, [initializePermissionsForCreate]);

    // Handle permission changes
    const handlePermissionChange = (
        categoryIndex: number,
        actionIndex: number | 'all',
        value: boolean
    ) => {
        setEditedPermissions(currentCategories => {
            const updatedCategories = JSON.parse(JSON.stringify(currentCategories)) as PermissionCategory[];
            if (!updatedCategories[categoryIndex]) return updatedCategories;
            const category = updatedCategories[categoryIndex];

            if (actionIndex === 'all') {
                category.canDoAll = value;
                if (value) {
                    category.actions?.forEach(a => a.canDoAction = true);
                }
            } else {
                if (category.actions && category.actions[actionIndex]) {
                    const action = category.actions[actionIndex];
                    action.canDoAction = value;
                    if (!value) {
                        category.canDoAll = false;
                    } else {
                        const allActionsTrue = category.actions.every(a => a.canDoAction);
                        if (allActionsTrue) {
                            category.canDoAll = true;
                        }
                    }
                }
            }
            return updatedCategories;
        });
    };

    // Toggle category collapse state
    const toggleCategoryCollapse = (categoryName: string) => {
        setCollapsedCategories(prev => ({
            ...prev,
            [categoryName]: !prev[categoryName]
        }));
    };

    // Handle the save action
    const handleSave = () => {
        // Prepare categories for saving
        const categoriesForSave = editedPermissions.map(category => {
            if (category.canDoAll) {
                return { ...category, actions: [] };
            }
            return category;
        });

        const selectedPermissionSetObjects = availablePermissionSets.filter(ps =>
            selectedPermissionSetIds.includes(ps.id)
        );

        const roleDataForSave: Role = {
            id: "",
            name: roleName,
            categories: categoriesForSave,
            permissionSets: selectedPermissionSetObjects,
        } as Role;
        onSave(roleDataForSave); // Pass data up to parent
    };

    // --- Render Helper for Permissions Table (Specific to Create Form) ---
    const renderCreatePermissionsTable = (
        isTableLoading: boolean, // Renamed from isLoading to avoid conflict
    ) => {
        if (isLoadingCategories) {
            return (
                <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', p: 3 }}>
                    <CircularProgress size={24} />
                    <Typography sx={{ ml: 2 }}>Loading permission definitions...</Typography>
                </Box>
            );
        }

        if (!editedPermissions || editedPermissions.length === 0) {
            return (
                <Typography color="text.secondary" sx={{ mt: 2, textAlign: 'center' }}>
                    No permission categories available or initialized.
                </Typography>
            );
        }

        return (
            <Box sx={{ mt: 3, pr: 1 }}>
                <Typography variant="subtitle1" sx={{ mb: 1, fontWeight: 'medium' }}>Permissions</Typography>
                {editedPermissions.map((category, categoryIndex) => {
                    const categoryName = category.name || '';
                    const isCollapsed = collapsedCategories[categoryName];

                    return (
                        <Box key={categoryName || `category-${categoryIndex}`} sx={{ mb: 2 }}>
                            {/* Category Header */}
                            <Box
                                sx={{
                                    display: 'flex',
                                    alignItems: 'center',
                                    cursor: 'pointer',
                                    mb: 1,
                                    '&:hover': { backgroundColor: colors.grey[50] },
                                    p: 0.5,
                                    borderRadius: 1,
                                }}
                                onClick={() => toggleCategoryCollapse(categoryName)}
                            >
                                {isCollapsed ? <ChevronRightIcon fontSize="small" sx={{ mr: 0.5 }} /> : <ExpandMoreIcon fontSize="small" sx={{ mr: 0.5 }} />}
                                <Typography variant="subtitle2" sx={{ fontWeight: 'bold' }}>
                                    {categoryName || 'Unnamed Category'}
                                </Typography>
                            </Box>

                            {!isCollapsed && (
                                <TableContainer component={Paper} variant="outlined" sx={{ mb: 1 }}>
                                    <Table size="small" aria-label={`New Role ${category.name || 'category'} permissions`}>
                                        <TableHead>
                                            <TableRow>
                                                <TableCell sx={{ fontWeight: 'bold' }}>Action</TableCell>
                                                <TableCell align="center" sx={{ fontWeight: 'bold' }}>Allowed</TableCell>
                                            </TableRow>
                                        </TableHead>
                                        <TableBody>
                                            <TableRow sx={{ backgroundColor: colors.grey[50] }}>
                                                <TableCell component="th" scope="row" sx={{ fontStyle: 'italic' }}>
                                                    Allow All Actions in Category
                                                </TableCell>
                                                <TableCell align="center">
                                                    <Checkbox
                                                        size="small"
                                                        checked={!!category.canDoAll}
                                                        onChange={(e) => handlePermissionChange(categoryIndex, 'all', e.target.checked)}
                                                        disabled={isTableLoading}
                                                    />
                                                </TableCell>
                                            </TableRow>
                                            {category.actions?.map((action, actionIndex) => (
                                                <TableRow key={action.name || `action-${actionIndex}`}>
                                                    <TableCell component="th" scope="row">
                                                        {action.name || 'Unnamed Action'}
                                                    </TableCell>
                                                    <TableCell align="center">
                                                        <Checkbox
                                                            size="small"
                                                            checked={!!action.canDoAction}
                                                            onChange={(e) => handlePermissionChange(categoryIndex, actionIndex, e.target.checked)}
                                                            disabled={isTableLoading || !!category.canDoAll}
                                                        />
                                                    </TableCell>
                                                </TableRow>
                                            ))}
                                        </TableBody>
                                    </Table>
                                </TableContainer>
                            )}
                        </Box>
                    )
                })}
            </Box>
        );
    };

    return (
        <Paper sx={{ p: 3, mb: 3, border: `1px solid ${colors.blue[200]}` }}>
            <Typography variant="h6" sx={{ mb: 2 }}>Create New Role</Typography>

            {apiError && (
                <Alert severity="error" sx={{ mb: 2 }} onClose={onClearApiError}>
                    {apiError}
                </Alert>
            )}

            <TextField
                autoFocus
                margin="dense"
                id="new-role-name"
                label="Role Name"
                type="text"
                fullWidth
                variant="outlined"
                value={roleName}
                onChange={(e) => setRoleName(e.target.value)}
                required
                sx={{ mb: 2 }}
                disabled={isLoading}
            />

            {/* Section to Attach Permission Sets */}
            <Box sx={{ mt: 3, mb: 2 }}>
                <Typography variant="subtitle1" sx={{ mb: 1, fontWeight: 'medium' }}>
                    Attach Permission Sets (Optional)
                </Typography>
                {isLoadingPermissionSets ? (
                    <Box sx={{ display: 'flex', alignItems: 'center' }}>
                        <CircularProgress size={20} sx={{ mr: 1 }} />
                        <Typography variant="body2" color="text.secondary">
                            Loading permission sets...
                        </Typography>
                    </Box>
                ) : availablePermissionSets && availablePermissionSets.length > 0 ? (
                    <Box sx={{ maxHeight: '200px', overflowY: 'auto', pr: 1, border: `1px solid ${colors.grey[200]}`, borderRadius: 1, p:1 }}>
                        {availablePermissionSets.map((ps: PermissionSet) => (
                            <Box key={ps.id} sx={{ display: 'flex', alignItems: 'center', mb: 0.5 }}>
                                <Checkbox
                                    size="small"
                                    checked={selectedPermissionSetIds.includes(ps.id)}
                                    onChange={(e) => {
                                        const currentSelectedIds = selectedPermissionSetIds;
                                        const newSelectedIds = e.target.checked
                                            ? [...currentSelectedIds, ps.id]
                                            : currentSelectedIds.filter(id => id !== ps.id);
                                        setSelectedPermissionSetIds(newSelectedIds);
                                    }}
                                    disabled={isLoading}
                                    sx={{ py: 0.25, mr: 0.5 }}
                                />
                                 <Typography variant="body2" component="span" sx={{ cursor: 'default' }} title={ps.description || ''}>
                                    {ps.name}
                                    {ps.description && <Typography variant="caption" color="text.secondary" component="span" sx={{ ml: 0.5 }}>({ps.description.substring(0,30)}{ps.description.length > 30 ? '...': ''})</Typography>}
                                </Typography>
                            </Box>
                        ))}
                    </Box>
                ) : (
                    <Typography variant="body2" color="text.secondary">
                        No permission sets available to attach.
                    </Typography>
                )}
            </Box>

            {renderCreatePermissionsTable(isLoading)} {/* Pass mutation loading state */}

            <DialogActions sx={{ pt: 3, px: 0 }}>
                <Button onClick={onCancel} disabled={isLoading}>
                    Cancel
                </Button>
                <Button
                    variant="contained"
                    onClick={handleSave} // Use internal save handler
                    disabled={isLoading || !roleName || isLoadingCategories || isLoadingPermissionSets}
                >
                    {isLoading ? <CircularProgress size={24} /> : "Create Role"}
                </Button>
            </DialogActions>
        </Paper>
    );
}; 