import { useSituation } from "@/app/apis/services/workflow/situations/hooks";
import {
  formatPhoneNumberForDisplay,
  standardizeUSPhoneNumber,
  useCallerDisplayInfo,
} from "@/app/utils/caller-identification";
import AddCircleOutlineIcon from "@mui/icons-material/AddCircleOutline";
import AddIcCallIcon from "@mui/icons-material/AddIcCall";
import CallIcon from "@mui/icons-material/Call";
import CallEndIcon from "@mui/icons-material/CallEnd";
import ContactsIcon from "@mui/icons-material/Contacts";
import MicIcon from "@mui/icons-material/Mic";
import MicOffIcon from "@mui/icons-material/MicOff";
import PauseIcon from "@mui/icons-material/Pause";
import PauseCircleIcon from "@mui/icons-material/PauseCircle";
import PhoneInTalkIcon from "@mui/icons-material/PhoneInTalk";
import SearchIcon from "@mui/icons-material/Search";
import SignalCellularAltIcon from "@mui/icons-material/SignalCellularAlt";
import { Box, Typography } from "@mui/material";
import InputAdornment from "@mui/material/InputAdornment";
import Menu from "@mui/material/Menu";
import MenuItem from "@mui/material/MenuItem";
import TextField from "@mui/material/TextField";
import { Open_Sans } from "next/font/google";
import { QueuedCall } from "proto/hero/communications/v1/conversation_pb";
import React, { useEffect, useState } from "react";

const openSans = Open_Sans({
  subsets: ["latin"],
  weight: ["400", "600"],
});

interface CellularCallControlWidgetProps {
  activeCall: QueuedCall | null;
  holdCall: QueuedCall | null;
  isMuted?: boolean;
  isConnecting?: boolean;
  callStatusTimestamps: { [callSid: string]: number };
  selectedSituationId?: string | null;
  onEndCall?: () => void;
  onHoldToggle?: () => void;
  onDial?: (phoneNumber: string) => void;
  onToggleMute?: () => void;
  onSwitchCall?: () => void;
}

interface CallHeaderProps {
  backgroundColor: string;
  iconColor: string;
  textColor: string;
  situationId?: string;
  children: React.ReactNode;
}

const CallHeader: React.FC<CallHeaderProps> = ({
  backgroundColor,
  iconColor,
  textColor,
  situationId,
  children,
}) => (
  <div style={onCallHeaderStyle}>
    <span style={idTextStyle}>
      {situationId?.replace(/[^0-9]/g, "").slice(0, 3)}
    </span>
    <div
      style={{
        ...timerContainerStyle,
        backgroundColor,
      }}
    >
      <PhoneInTalkIcon sx={{ width: 20, height: 20, color: iconColor }} />
      <span
        style={{
          ...timerTextStyle,
          color: textColor,
        }}
      >
        {children}
      </span>
    </div>
  </div>
);

export default function CellularCallControlWidget({
  activeCall,
  holdCall,
  isMuted = false,
  isConnecting = false,
  callStatusTimestamps,
  selectedSituationId,
  onEndCall,
  onHoldToggle,
  onDial,
  onToggleMute,
  onSwitchCall,
}: CellularCallControlWidgetProps) {
  const currentCall = activeCall || holdCall;
  const [phoneNumber, setPhoneNumber] = useState("");
  const [phoneNumberError, setPhoneNumberError] = useState("");
  const [showDialer, setShowDialer] = useState(false);

  const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null);
  const [searchQuery, setSearchQuery] = useState("");

  const contacts = [
    { id: 1, name: "Local EMS", phone: "3237706911" },
    { id: 2, name: "Maintenance", phone: "3237706911" },
    { id: 3, name: "IT Department", phone: "3237706911" },
    { id: 4, name: "Fire Department", phone: "3237706911" },
  ];

  const { displayTitle, displayNumber } = useCallerDisplayInfo(
    currentCall?.caller
  );

  const uppercaseTitle = displayTitle.toUpperCase();

  const isOutbound = currentCall?.attributes?.direction === "outbound";

  const { data: situationForCurrentCall } = useSituation(
    currentCall?.situationId || "",
    5000
  );

  const { data: situationForHoldCall } = useSituation(
    holdCall?.situationId || "",
    5000
  );

  useEffect(() => {
    if (activeCall) {
      setShowDialer(false);
    }
  }, [activeCall]);

  const handleStartNew = () => {
    setShowDialer(true);
  };

  const handleDial = () => {
    try {
      standardizeUSPhoneNumber(phoneNumber);
      if (onDial) {
        onDial(phoneNumber);
      }
    } catch (error) {
      setPhoneNumberError("Please enter a valid US phone number");
    }
  };

  const handleEndCall = () => {
    setPhoneNumber("");
    if (onEndCall) {
      onEndCall();
    }
  };

  const handleClose = () => {
    setShowDialer(false);
    setPhoneNumber("");
    setPhoneNumberError("");
  };

  const handlePhoneNumberChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setPhoneNumber(e.target.value);
    if (phoneNumberError) setPhoneNumberError("");
  };

  const handleOpenContactsMenu = (event: React.MouseEvent<HTMLElement>) => {
    setAnchorEl(event.currentTarget);
  };

  const handleCloseContactsMenu = () => {
    setAnchorEl(null);
  };

  const filteredContacts = contacts.filter((contact) =>
    contact.name.toLowerCase().includes(searchQuery.toLowerCase())
  );

  const renderStartNewCall = () => {
    const isDisabled = !!activeCall;
    return (
      <button
        style={{ ...startNewButtonStyle, opacity: isDisabled ? 0.6 : 1 }}
        onClick={handleStartNew}
        disabled={isDisabled}
      >
        <AddIcCallIcon sx={{ color: "#0060FF", height: 24, width: 24 }} />
        <span className={openSans.className} style={buttonTextStyle}>
          Start New
        </span>
      </button>
    );
  };

  const renderDialer = () => {
    return (
      <div style={startCallContainerStyle}>
        <div style={dialerHeaderStyle}>
          <span className={openSans.className} style={dialerTitleStyle}>
            START NEW CALL
          </span>
          <button style={closeButtonStyle} onClick={handleClose}>
            ✕
          </button>
        </div>
        <div style={startCallRowStyle}>
          <button style={contactButtonStyle} onClick={handleOpenContactsMenu}>
            <ContactsIcon sx={{ height: 29, width: 29, color: "#0060FF" }} />
          </button>
          <div style={textFieldContainerStyle}>
            <TextField
              variant="outlined"
              placeholder="Type Number Here"
              value={formatPhoneNumberForDisplay(phoneNumber)}
              onChange={handlePhoneNumberChange}
              error={!!phoneNumberError}
              helperText={phoneNumberError}
              fullWidth
              slotProps={{
                input: {
                  startAdornment: (
                    <SignalCellularAltIcon sx={signalIconStyle} />
                  ),
                  style: textFieldInputStyle,
                },
              }}
            />
          </div>
        </div>
        <button
          style={{
            ...dialButtonStyle,
            opacity: !phoneNumber ? 0.4 : 1,
          }}
          onClick={handleDial}
          disabled={!phoneNumber}
        >
          <CallIcon sx={{ color: "#ffffff", height: 24, width: 24 }} />
          <span className={openSans.className} style={dialButtonTextStyle}>
            Dial
          </span>
        </button>
      </div>
    );
  };

  const renderContactsMenu = () => {
    return (
      <Menu
        anchorEl={anchorEl}
        open={Boolean(anchorEl)}
        onClose={handleCloseContactsMenu}
        disablePortal
        disableAutoFocusItem
        disableEnforceFocus
        anchorOrigin={{ vertical: "top", horizontal: "left" }}
        transformOrigin={{ vertical: "bottom", horizontal: "left" }}
        PaperProps={{
          style: {
            minWidth: "234px",
            borderRadius: "12px",
            marginTop: "-8px",
            padding: 0,
          },
        }}
      >
        <Box sx={{ maxHeight: 350, overflowY: "auto" }}>
          {filteredContacts.map((contact) => (
            <MenuItem
              key={contact.id}
              onClick={() => {
                setPhoneNumber(contact.phone);
                handleCloseContactsMenu();
              }}
            >
              <Box display="flex" flexDirection="column" width="100%">
                <Box
                  display="flex"
                  justifyContent="space-between"
                  alignItems="center"
                >
                  <Typography fontWeight={600} sx={{ fontSize: "14px" }}>
                    {contact.name}
                  </Typography>
                  <AddCircleOutlineIcon sx={{ color: "#1C1B1F4D", height: "16px" }} />
                </Box>
                <Typography color="text.secondary" sx={{ fontSize: "12px" }}>
                  {contact.phone ? `(${contact.phone.slice(0, 3)}) ${contact.phone.slice(3, 6)}-${contact.phone.slice(6)}` : "No phone number"}
                </Typography>
              </Box>
            </MenuItem>
          ))}

          <Box
            sx={{
              position: "sticky",
              bottom: 0,
              backgroundColor: "#fff",
              zIndex: 1,
              px: 1.5,
              py: 1,
            }}
          >
            <TextField
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              placeholder="Search"
              size="small"
              fullWidth
              variant="outlined"
              onKeyDown={(e) => e.stopPropagation()}
              sx={{
                "& .MuiOutlinedInput-root": {
                  borderRadius: "8px",
                },
                "& .MuiOutlinedInput-input": {
                  fontSize: "12px",
                  "&::placeholder": {
                    fontSize: "12px",
                  },
                },
              }}
              InputProps={{
                startAdornment: (
                  <InputAdornment position="start">
                    <SearchIcon fontSize="small" />
                  </InputAdornment>
                ),
              }}
            />
          </Box>
        </Box>
      </Menu>
    );
  };

  const renderOnHoldCall = () => {
    return (
      <div style={onHoldCallContainerStyle}>
        <div style={onHoldLeftColumnStyle}>
          <div style={onHoldTopRowStyle}>
            <PauseCircleIcon sx={{ width: 16, height: 16, color: "#E37820" }} />
            <span style={onHoldTextStyle}>ON HOLD</span>
          </div>
          <div style={onHoldBottomRowStyle}>
            <span style={onHoldIdTextStyle}>
              #{situationForHoldCall?.id?.replace(/[^0-9]/g, "")?.slice(0, 3) || "Unknown"}
            </span>
            <span style={onHoldSituationTextStyle}>
              {situationForHoldCall?.type}
            </span>
          </div>
        </div>
        <button style={switchCallButtonStyle} onClick={onSwitchCall}>
          <span style={switchCallButtonTextStyle}>Switch Call</span>
        </button>
      </div>
    );
  };

  const renderContactName = () => (
    <div style={contactNameStyle}>
      {situationForCurrentCall?.reporterName
        ? situationForCurrentCall.reporterName
        : displayTitle === "UNKNOWN CALLER"
          ? displayNumber
          : uppercaseTitle}
    </div>
  );

  const renderEndCallButton = () => (
    <button
      style={{
        ...callControlButtonStyle,
        backgroundColor: "#FFEAEA",
      }}
      onClick={handleEndCall}
    >
      <CallEndIcon sx={{ width: 24, height: 24, color: "#E31549" }} />
    </button>
  );

  const renderFullCallControl = () => {
    if (isConnecting) {
      return (
        <>
          <CallHeader
            backgroundColor="#D1DAFF"
            iconColor="#0048FF"
            textColor="#0048FF"
            situationId={situationForCurrentCall?.id}
          >
            Connecting...
          </CallHeader>
          {renderContactName()}
          <div style={onCallButtonsStyle}>
            {renderEndCallButton()}
          </div>
        </>
      );
    }

    return (
      <>
        <CallHeader
          backgroundColor={holdCall && !activeCall ? "#FBE9D0" : "#0CB9A240"}
          iconColor={holdCall && !activeCall ? "#E37820" : "#027464"}
          textColor={holdCall && !activeCall ? "#ED8414" : "#027464"}
          situationId={situationForCurrentCall?.id}
        >
          {(() => {
            const callSid = currentCall?.callSid || "";
            const startTime = callStatusTimestamps[callSid];
            if (!startTime) return "00:00";
            const elapsedSeconds = Math.floor(
              (Date.now() - startTime) / 1000
            );
            const minutes = String(
              Math.floor(elapsedSeconds / 60)
            ).padStart(2, "0");
            const seconds = String(elapsedSeconds % 60).padStart(2, "0");
            return `${minutes}:${seconds}`;
          })()}
        </CallHeader>
        {renderContactName()}
        <div style={onCallButtonsStyle}>
          <button
            style={{
              ...callControlButtonStyle,
              backgroundColor: isMuted ? "#FBE9D0" : "#E5EFFF",
            }}
            onClick={onToggleMute}
          >
            {isMuted ? (
              <MicOffIcon sx={{ width: 24, height: 24, color: "#E37820" }} />
            ) : (
              <MicIcon sx={{ width: 24, height: 24, color: "#0060FF" }} />
            )}
          </button>
          {!isOutbound && (
            <button
              style={{
                ...callControlButtonStyle,
                backgroundColor:
                  holdCall && !activeCall ? "#0060FF" : "#E5EFFF",
              }}
              onClick={onHoldToggle}
            >
              <PauseIcon
                sx={{
                  width: 24,
                  height: 24,
                  color: holdCall && !activeCall ? "#FFF2E4" : "#0060FF",
                }}
              />
            </button>
          )}
          {renderEndCallButton()}
        </div>
      </>
    );
  };

  return (
    <div style={containerStyle}>
      {!activeCall && !holdCall && !showDialer && renderStartNewCall()}
      {!activeCall && !holdCall && showDialer && renderDialer()}
      {(activeCall || holdCall) && (
        <div style={onCallContainerStyle}>
          {activeCall &&
            holdCall?.callSid &&
            activeCall?.callSid !== holdCall?.callSid &&
            selectedSituationId === holdCall?.situationId &&
            renderOnHoldCall()}
          {renderFullCallControl()}
          {!activeCall && !showDialer && renderStartNewCall()}
          {showDialer && renderDialer()}
        </div>
      )}
      {renderContactsMenu()}
    </div>
  );
}

// Styling Constants
const containerStyle: React.CSSProperties = {
  display: "inline-flex",
  width: "100%",
  alignItems: "center",
  justifyContent: "center",
  borderTopLeftRadius: "8px",
  borderTopRightRadius: "8px",
  border: "1.5px solid #D2D6DC",
  backgroundColor: "#FFFFFF",
  paddingTop: "12px",
  paddingRight: "16px",
  paddingBottom: "24px",
  paddingLeft: "16px",
};

const startNewButtonStyle: React.CSSProperties = {
  width: "100%",
  height: "48px",
  borderRadius: "8px",
  padding: "12px 20px",
  gap: "13px",
  backgroundColor: "#0060FF1A",
  border: "none",
  display: "flex",
  alignItems: "center",
  justifyContent: "center",
  cursor: "pointer",
};

const buttonTextStyle: React.CSSProperties = {
  fontWeight: 600,
  fontSize: "14px",
  lineHeight: "100%",
  letterSpacing: "0px",
  verticalAlign: "middle",
  color: "#0060FF",
};

const startCallContainerStyle: React.CSSProperties = {
  width: "100%",
  display: "flex",
  flexDirection: "column",
  gap: "16px",
};

const startCallRowStyle: React.CSSProperties = {
  display: "flex",
  width: "100%",
  flexDirection: "row",
  alignItems: "center",
  gap: "16px",
};

const contactButtonStyle: React.CSSProperties = {
  width: "52px",
  height: "52px",
  minWidth: "52px",
  borderRadius: "8px",
  padding: "12px",
  backgroundColor: "#0060FF1A",
  border: "none",
  cursor: "pointer",
  display: "flex",
  alignItems: "center",
  justifyContent: "center",
};

const textFieldContainerStyle: React.CSSProperties = {
  flex: 1,
  minWidth: 0,
};

const textFieldInputStyle: React.CSSProperties = {
  height: "52px",
  borderRadius: "8px",
  padding: "8px 10px",
  gap: "8px",
  fontFamily: "Roboto",
  fontWeight: 400,
  fontSize: "14px",
  lineHeight: "100%",
  letterSpacing: "0px",
  verticalAlign: "middle",
};

const signalIconStyle: React.CSSProperties = {
  width: "20px",
  height: "20px",
  color: "#1C1B1F4D",
};

const dialButtonStyle: React.CSSProperties = {
  width: "100%",
  height: "52px",
  borderRadius: "8px",
  padding: "12px 20px",
  gap: "13px",
  backgroundColor: "#0060FF",
  border: "none",
  display: "flex",
  alignItems: "center",
  justifyContent: "center",
  cursor: "pointer",
};

const dialButtonTextStyle: React.CSSProperties = {
  fontWeight: 600,
  fontSize: "14px",
  lineHeight: "100%",
  letterSpacing: "0px",
  verticalAlign: "middle",
  color: "#ffffff",
};

const onCallContainerStyle: React.CSSProperties = {
  display: "flex",
  flex: 1,
  width: "100%",
  flexDirection: "column",
  gap: "4px",
  alignItems: "flex-start",
};

const onCallHeaderStyle: React.CSSProperties = {
  display: "flex",
  width: "100%",
  flexDirection: "row",
  gap: "8px",
  alignItems: "center",
};

const idTextStyle: React.CSSProperties = {
  fontFamily: "Roboto",
  fontWeight: 700,
  fontSize: "20px",
  lineHeight: "100%",
  letterSpacing: "0px",
  verticalAlign: "middle",
  color: "#000000",
};

const titleTextStyle: React.CSSProperties = {
  fontFamily: "Roboto",
  fontWeight: 400,
  fontSize: "20px",
  lineHeight: "100%",
  letterSpacing: "0px",
  verticalAlign: "middle",
  color: "#000000",
};

const contactNameStyle: React.CSSProperties = {
  fontFamily: "Roboto",
  fontWeight: 400,
  fontSize: "14px",
  lineHeight: "100%",
  letterSpacing: "0px",
  verticalAlign: "middle",
  color: "#6B7280",
};

const timerContainerStyle: React.CSSProperties = {
  display: "inline-flex",
  borderRadius: "22px",
  padding: "6px 12px",
  gap: "6px",
  backgroundColor: "#0CB9A240",
  alignItems: "center",
  justifyContent: "center",
  marginLeft: "auto",
};

const timerTextStyle: React.CSSProperties = {
  fontFamily: "Roboto",
  fontWeight: 600,
  fontSize: "14px",
  lineHeight: "100%",
  letterSpacing: "0px",
  verticalAlign: "middle",
  color: "#027464",
};

const phoneInTalkIconStyle: React.CSSProperties = {
  color: "#027464",
  width: "20px",
  height: "20px",
};

const onCallButtonsStyle: React.CSSProperties = {
  display: "flex",
  flexDirection: "row",
  gap: "8px",
  width: "100%",
  marginTop: "6px",
  marginBottom: "6px",
};

const callControlButtonStyle: React.CSSProperties = {
  flex: 1,
  height: "48px",
  borderRadius: "8px",
  padding: "12px 20px",
  border: "none",
  cursor: "pointer",
  display: "flex",
  alignItems: "center",
  justifyContent: "center",
};

const onHoldCallContainerStyle: React.CSSProperties = {
  display: "inline-flex",
  flexDirection: "row",
  alignItems: "center",
  justifyContent: "space-between",
  width: "100%",
  boxSizing: "border-box",
  borderRadius: "8px",
  backgroundColor: "#F3F4F6",
  padding: "8px 12px",
  marginBottom: "8px",
};

const onHoldLeftColumnStyle: React.CSSProperties = {
  display: "flex",
  flexDirection: "column",
  gap: "4px",
};

const onHoldTopRowStyle: React.CSSProperties = {
  display: "flex",
  flexDirection: "row",
  alignItems: "center",
  gap: "4px",
};

const onHoldTextStyle: React.CSSProperties = {
  fontFamily: "Roboto",
  fontWeight: 600,
  fontSize: "12px",
  lineHeight: "100%",
  letterSpacing: "0px",
  verticalAlign: "middle",
  color: "#ED8414",
};

const onHoldBottomRowStyle: React.CSSProperties = {
  display: "flex",
  flexDirection: "row",
  gap: "8px",
};

const onHoldIdTextStyle: React.CSSProperties = {
  fontFamily: "Roboto",
  fontWeight: 700,
  fontSize: "16px",
  lineHeight: "100%",
  letterSpacing: "0px",
  verticalAlign: "middle",
  color: "#000000",
};

const onHoldSituationTextStyle: React.CSSProperties = {
  fontFamily: "Roboto",
  fontWeight: 400,
  fontSize: "16px",
  lineHeight: "100%",
  letterSpacing: "0px",
  verticalAlign: "middle",
  color: "#000000",
};

const switchCallButtonStyle: React.CSSProperties = {
  width: "30.5%",
  height: "39px",
  paddingTop: "10px",
  paddingRight: "14px",
  paddingBottom: "10px",
  paddingLeft: "14px",
  gap: "10px",
  borderRadius: "29px",
  backgroundColor: "#2A7AFF",
  border: "none",
  cursor: "pointer",
  display: "flex",
  alignItems: "center",
  justifyContent: "center",
};

const switchCallButtonTextStyle: React.CSSProperties = {
  fontFamily: "Roboto",
  fontWeight: 600,
  fontSize: "14px",
  lineHeight: "100%",
  letterSpacing: "0px",
  color: "#FFFFFF",
  verticalAlign: "middle",
};

const dialerHeaderStyle: React.CSSProperties = {
  display: "flex",
  width: "100%",
  justifyContent: "space-between",
  alignItems: "center",
};

const dialerTitleStyle: React.CSSProperties = {
  fontWeight: 600,
  fontSize: "16px",
  lineHeight: "100%",
  color: "#111827",
};

const closeButtonStyle: React.CSSProperties = {
  background: "none",
  border: "none",
  color: "#6B7280",
  fontSize: "16px",
  cursor: "pointer",
  padding: "4px 8px",
  display: "flex",
  alignItems: "center",
  justifyContent: "center",
};
