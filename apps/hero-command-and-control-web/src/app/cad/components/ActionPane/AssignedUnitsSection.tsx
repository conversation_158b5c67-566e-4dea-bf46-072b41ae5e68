import { stringToAssetType } from "@/app/apis/services/workflow/assets/enumConverters";
import { hookOrderStatusToString } from "@/app/apis/services/workflow/orders/enumConverters";
import {
  getAssetStatusLabel,
  toTitleCase
} from "@/app/utils/utils";
import AddIcon from "@mui/icons-material/Add";
import MoreVertIcon from '@mui/icons-material/MoreVert';
import {
  Box,
  Button,
  Checkbox,
  Chip,
  ClickAwayListener,
  Divider,
  FormControlLabel,
  IconButton,
  ListItemText,
  Menu,
  MenuItem,
  Paper,
  Popper,
  Tab,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Tabs,
  Typography
} from "@mui/material";
import { Asset, AssetType } from "proto/hero/assets/v2/assets_pb";
import { Order, OrderStatus } from "proto/hero/orders/v2/orders_pb";
import { Situation } from "proto/hero/situations/v2/situations_pb";
import React, { useEffect, useState } from "react";
import { Button as DesignSystemButton } from "../../../../design-system/components/Button";
import { DatePicker } from "../../../../design-system/components/DatePicker";
import { InputType, TextInput } from "../../../../design-system/components/TextInput";

// Constants for order status mappings
const ORDER_STATUS_MAPPINGS = {
  DISPATCHED: {
    status: "dispatched",
    orderStatus: OrderStatus.CREATED,
    orderStatusString: "ORDER_STATUS_CREATED",
    label: "Dispatched"
  },
  EN_ROUTE: {
    status: "en-route",
    orderStatus: OrderStatus.ACKNOWLEDGED,
    orderStatusString: "ORDER_STATUS_ACKNOWLEDGED",
    label: "En-route"
  },
  ON_SCENE: {
    status: "on-scene",
    orderStatus: OrderStatus.IN_PROGRESS,
    orderStatusString: "ORDER_STATUS_IN_PROGRESS",
    label: "On Scene"
  },
  DONE: {
    status: "done",
    orderStatus: OrderStatus.COMPLETED,
    orderStatusString: "ORDER_STATUS_COMPLETED",
    label: "Done"
  },
  CANCELLED: {
    status: "cancelled",
    orderStatus: OrderStatus.CANCELLED,
    orderStatusString: "ORDER_STATUS_CANCELLED",
    label: "Cancelled"
  }
} as const;

// Helper function to get status mapping by status string
const getStatusMapping = (status: string) => {
  return Object.values(ORDER_STATUS_MAPPINGS).find(mapping => mapping.status === status);
};

// Helper function to get status mapping by order status
const getStatusMappingByOrderStatus = (orderStatus: OrderStatus) => {
  return Object.values(ORDER_STATUS_MAPPINGS).find(mapping => mapping.orderStatus === orderStatus);
};

// Helper function to check if order status matches a specific status
const isOrderStatus = (order: Order, targetStatus: OrderStatus): boolean => {
  return hookOrderStatusToString(order.status) === `ORDER_STATUS_${OrderStatus[targetStatus]}`;
};

// Helper function to check if status update matches a specific status
const isStatusUpdateStatus = (statusUpdate: any, targetStatus: OrderStatus): boolean => {
  return hookOrderStatusToString(statusUpdate.newStatus) === `ORDER_STATUS_${OrderStatus[targetStatus]}`;
};

// Helper function to get order status string from OrderStatus enum
const getOrderStatusString = (orderStatus: OrderStatus): string => {
  return `ORDER_STATUS_${OrderStatus[orderStatus]}`;
};

// TimePickerDropdown component for selecting time
interface TimePickerDropdownProps {
  open: boolean;
  anchorEl: HTMLElement | null;
  onClose: () => void;
  onTimeSelect: (time: string, date?: Date) => void;
  currentTime?: string;
}

const TimePickerDropdown: React.FC<TimePickerDropdownProps> = ({
  open,
  anchorEl,
  onClose,
  onTimeSelect,
  currentTime,
}) => {
  // Generate hour options (00-23)
  const generateHourOptions = () => {
    const options = [];
    for (let hour = 0; hour < 24; hour++) {
      const hourString = hour.toString().padStart(2, '0');
      options.push({ value: hourString, label: hourString });
    }
    return options;
  };

  // Generate minute options (00-59)
  const generateMinuteOptions = () => {
    const options = [];
    for (let minute = 0; minute < 60; minute++) {
      const minuteString = minute.toString().padStart(2, '0');
      options.push({ value: minuteString, label: minuteString });
    }
    return options;
  };

  const hourOptions = generateHourOptions();
  const minuteOptions = generateMinuteOptions();

  // Parse current time if provided
  const parseCurrentTime = () => {
    if (currentTime) {
      const [hours, minutes] = currentTime.split(':');
      return {
        hour: hours || '00',
        minute: minutes || '00',
        date: new Date() // Default to today
      };
    }

    // Default to current time
    const now = new Date();
    return {
      hour: now.getHours().toString().padStart(2, '0'),
      minute: now.getMinutes().toString().padStart(2, '0'),
      date: now
    };
  };

  const [selectedHour, setSelectedHour] = useState<string>(parseCurrentTime().hour);
  const [selectedMinute, setSelectedMinute] = useState<string>(parseCurrentTime().minute);
  const [selectedDate, setSelectedDate] = useState<Date | null>(parseCurrentTime().date);
  const [error, setError] = useState<string>('');

  // Update state when currentTime changes
  useEffect(() => {
    const parsed = parseCurrentTime();
    setSelectedHour(parsed.hour);
    setSelectedMinute(parsed.minute);
    setSelectedDate(parsed.date);
    setError(''); // Clear error when time changes
  }, [currentTime]);

  // Validate time input
  const validateTime = (hour: string, minute: string): boolean => {
    // Check if values are empty
    if (!hour || !minute) {
      setError('Please enter both hour and minute');
      return false;
    }

    const hourNum = parseInt(hour);
    const minuteNum = parseInt(minute);

    if (isNaN(hourNum) || isNaN(minuteNum)) {
      setError('Please enter valid numbers for hour and minute');
      return false;
    }

    if (hourNum < 0 || hourNum > 23) {
      setError('Hour must be between 0 and 23');
      return false;
    }

    if (minuteNum < 0 || minuteNum > 59) {
      setError('Minute must be between 0 and 59');
      return false;
    }

    setError('');
    return true;
  };

  // Format time input with proper padding
  const formatTimeInput = (value: string, maxValue: number): string => {
    const num = parseInt(value);
    if (isNaN(num)) return value;

    // If user enters a single digit and it's valid, format it
    if (value.length === 1 && num >= 0 && num <= maxValue) {
      return num.toString().padStart(2, '0');
    }

    // If user enters two digits, validate and format
    if (value.length === 2 && num >= 0 && num <= maxValue) {
      return num.toString().padStart(2, '0');
    }

    return value;
  };

  const handleHourChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const value = e.target.value;
    // Only allow numbers and limit to 2 digits
    if (/^\d{0,2}$/.test(value)) {
      setSelectedHour(value);
      // Clear error when user starts typing
      if (error) setError('');

      // Auto-format single digit if it's valid and user has finished typing (2 digits or valid single digit)
      if (value.length === 2) {
        const hourNum = parseInt(value);
        if (hourNum >= 0 && hourNum <= 23) {
          setSelectedHour(hourNum.toString().padStart(2, '0'));
        }
      }
    }
  };

  const handleMinuteChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const value = e.target.value;
    // Only allow numbers and limit to 2 digits
    if (/^\d{0,2}$/.test(value)) {
      setSelectedMinute(value);
      // Clear error when user starts typing
      if (error) setError('');

      // Auto-format single digit if it's valid and user has finished typing (2 digits or valid single digit)
      if (value.length === 2) {
        const minuteNum = parseInt(value);
        if (minuteNum >= 0 && minuteNum <= 59) {
          setSelectedMinute(minuteNum.toString().padStart(2, '0'));
        }
      }
    }
  };

  const handleHourSelect = (option: { value: string; label: string }) => {
    setSelectedHour(option.value);
    setError(''); // Clear error when selecting from dropdown
  };

  const handleMinuteSelect = (option: { value: string; label: string }) => {
    setSelectedMinute(option.value);
    setError(''); // Clear error when selecting from dropdown
  };

  const handleHourBlur = () => {
    if (selectedHour && selectedHour.length === 1) {
      const hourNum = parseInt(selectedHour);
      if (hourNum >= 0 && hourNum <= 23) {
        setSelectedHour(hourNum.toString().padStart(2, '0'));
      }
    }
  };

  const handleMinuteBlur = () => {
    if (selectedMinute && selectedMinute.length === 1) {
      const minuteNum = parseInt(selectedMinute);
      if (minuteNum >= 0 && minuteNum <= 59) {
        setSelectedMinute(minuteNum.toString().padStart(2, '0'));
      }
    }
  };

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      e.preventDefault();
      handleApply();
    }
  };

  const handleApply = () => {
    // Format the inputs with proper padding
    const formattedHour = formatTimeInput(selectedHour, 23);
    const formattedMinute = formatTimeInput(selectedMinute, 59);

    // Validate the formatted time
    if (!validateTime(formattedHour, formattedMinute)) {
      return;
    }

    // Create a new date with the selected time
    const newDate = new Date(selectedDate!);
    newDate.setHours(parseInt(formattedHour), parseInt(formattedMinute), 0, 0);

    // Format as HH:MM with proper padding
    const timeString = `${formattedHour}:${formattedMinute}`;

    // Call the callback and close the dropdown
    onTimeSelect(timeString, newDate);
    onClose();
  };

  return (
    <Popper
      open={open}
      anchorEl={anchorEl}
      placement="bottom-start"
      style={{ zIndex: 1300 }}
    >
      <Paper
        sx={{
          mt: 1,
          borderRadius: "8px",
          display: "flex",
          flexDirection: "column",
          width: 320,
          p: 2,
        }}
        onKeyDown={handleKeyDown}
        tabIndex={0}
      >
        <Typography
          sx={{
            fontSize: "14px",
            fontWeight: 600,
            color: "#111827",
            mb: 2,
          }}
        >
          Set Time
        </Typography>

        {/* Date Picker */}
        <Box sx={{ mb: 2 }}>
          <DatePicker
            title="Date"
            placeholder="MM/DD/YYYY"
            value={selectedDate}
            onChange={setSelectedDate}
          />
        </Box>

        {/* Time Selection */}
        <Box sx={{ display: "flex", gap: 2, mb: 2 }}>
          <Box sx={{ flex: 1 }}>
            <TextInput
              title="Hour"
              placeholder="HH"
              value={selectedHour}
              onChange={handleHourChange}
              onBlur={handleHourBlur}
              type={InputType.Dropdown}
              options={hourOptions}
              onSelectOption={handleHourSelect}
              errorMessage={selectedHour && (parseInt(selectedHour) < 0 || parseInt(selectedHour) > 23) ? "Invalid hour" : undefined}
            />
          </Box>
          <Box sx={{ flex: 1 }}>
            <TextInput
              title="Minute"
              placeholder="MM"
              value={selectedMinute}
              onChange={handleMinuteChange}
              onBlur={handleMinuteBlur}
              type={InputType.Dropdown}
              options={minuteOptions}
              onSelectOption={handleMinuteSelect}
              errorMessage={selectedMinute && (parseInt(selectedMinute) < 0 || parseInt(selectedMinute) > 59) ? "Invalid minute" : undefined}
            />
          </Box>
        </Box>

        {/* Error Message */}
        {error && (
          <Box sx={{ mb: 2 }}>
            <Typography
              sx={{
                fontSize: "12px",
                color: "#DC2626",
                fontWeight: 500,
              }}
            >
              {error}
            </Typography>
          </Box>
        )}

        {/* Action Buttons */}
        <Box sx={{ display: "flex", justifyContent: "flex-end", gap: 1 }}>
          <DesignSystemButton
            label="Cancel"
            style="ghost"
            color="grey"
            size="small"
            onClick={onClose}
          />
          <DesignSystemButton
            label="Apply"
            style="filled"
            color="blue"
            size="small"
            onClick={handleApply}
            disabled={!selectedHour || !selectedMinute || !selectedDate || !!error}
          />
        </Box>
      </Paper>
    </Popper>
  );
};

interface AssignedUnitsSectionProps {
  orders: Order[];
  allResponders: Asset[];
  selectedSituation?: Situation;
  onRemoveOfficer: (orderId: string) => void;
  onChangeStatus: (orderId: string, newStatus: string) => void;
  onUpdateTimestamp: (orderId: string, status: string, newTimestamp: string, date?: Date) => void;
  onDispatch: (selectedResponderIds: string[]) => void;
  isDispatching?: boolean;
  optimisticDispatchedUnits?: string[];
}

export const orderStatuses = [
  { value: ORDER_STATUS_MAPPINGS.DISPATCHED.orderStatusString, label: ORDER_STATUS_MAPPINGS.DISPATCHED.label },
  { value: ORDER_STATUS_MAPPINGS.EN_ROUTE.orderStatusString, label: ORDER_STATUS_MAPPINGS.EN_ROUTE.label },
  { value: ORDER_STATUS_MAPPINGS.ON_SCENE.orderStatusString, label: ORDER_STATUS_MAPPINGS.ON_SCENE.label },
  { value: ORDER_STATUS_MAPPINGS.DONE.orderStatusString, label: ORDER_STATUS_MAPPINGS.DONE.label },
  { value: ORDER_STATUS_MAPPINGS.CANCELLED.orderStatusString, label: ORDER_STATUS_MAPPINGS.CANCELLED.label },
];

export const AssignedUnitsSection: React.FC<AssignedUnitsSectionProps> = ({
  orders,
  allResponders,
  selectedSituation,
  onRemoveOfficer,
  onChangeStatus,
  onUpdateTimestamp,
  onDispatch,
  isDispatching,
  optimisticDispatchedUnits,
}) => {
  // State for optimistic status updates
  const [optimisticStatusUpdates, setOptimisticStatusUpdates] = useState<Map<string, { status: string; timestamp: string }>>(new Map());

  // State for optimistic timestamp updates
  const [optimisticTimestampUpdates, setOptimisticTimestampUpdates] = useState<Map<string, { status: string; timestamp: string }>>(new Map());

  // State for tracking which unit is selected as primary
  const [primaryUnitId, setPrimaryUnitId] = useState<string | null>(null);

  // State for the remove menu
  const [removeMenuAnchor, setRemoveMenuAnchor] = useState<HTMLElement | null>(null);
  const [removeMenuOrderId, setRemoveMenuOrderId] = useState<string>("");
  const removeMenuOpen = Boolean(removeMenuAnchor);

  // State for tabs
  const [activeTab, setActiveTab] = useState<number>(0);

  // Filter orders to only show ASSIST_MEMBER orders for this situation (excluding cancelled orders)
  const assistMemberOrders = React.useMemo(() => {
    const filtered = orders.filter(order =>
      // @ts-expect-error TODO: Fix type issue - hooks return string representations of enums
      order.type === "ORDER_TYPE_ASSIST_MEMBER" &&
      order.situationId === selectedSituation?.id &&
      hookOrderStatusToString(order.status) !== "ORDER_STATUS_CANCELLED"
    );

    return filtered;
  }, [orders, selectedSituation?.id]);

  // Helper function to get timestamp for a specific status from order
  // Shows the most recent status update for each state, including same-state updates (e.g., "CREATED -> CREATED")
  // for timestamp corrections. Prioritizes statusUpdateTimestamp over entryTimestamp for manual corrections.
  const getTimestampForStatus = (order: Order, status: string) => {
    // Check for optimistic timestamp updates first (for manual timestamp edits)
    const optimisticTimestampUpdate = optimisticTimestampUpdates.get(order.id);
    if (optimisticTimestampUpdate && optimisticTimestampUpdate.status === status) {
      return optimisticTimestampUpdate.timestamp;
    }

    // Check for optimistic status updates second (for status changes)
    const optimisticUpdate = optimisticStatusUpdates.get(order.id);
    if (optimisticUpdate && optimisticUpdate.status === status) {
      return optimisticUpdate.timestamp;
    }

    // Check if order is cancelled - don't show timestamps for cancelled orders
    if (isOrderStatus(order, OrderStatus.CANCELLED)) {
      return undefined;
    }

    // Get the most recent status update entry for each status
    let mostRecentUpdate: any = undefined;

    switch (status) {
      case ORDER_STATUS_MAPPINGS.DISPATCHED.status:
        // Find the most recent CREATED status update (including same-state updates like CREATED -> CREATED)
        mostRecentUpdate = order.statusUpdates
          ?.filter(update => isStatusUpdateStatus(update, OrderStatus.CREATED))
          ?.sort((a, b) => new Date(b.entryTimestamp).getTime() - new Date(a.entryTimestamp).getTime())[0];
        break;
      case ORDER_STATUS_MAPPINGS.EN_ROUTE.status:
        // Find the most recent ACKNOWLEDGED status update (including same-state updates)
        mostRecentUpdate = order.statusUpdates
          ?.filter(update => isStatusUpdateStatus(update, OrderStatus.ACKNOWLEDGED))
          ?.sort((a, b) => new Date(b.entryTimestamp).getTime() - new Date(a.entryTimestamp).getTime())[0];
        break;
      case ORDER_STATUS_MAPPINGS.ON_SCENE.status:
        // Find the most recent IN_PROGRESS status update (including same-state updates)
        mostRecentUpdate = order.statusUpdates
          ?.filter(update => isStatusUpdateStatus(update, OrderStatus.IN_PROGRESS))
          ?.sort((a, b) => new Date(b.entryTimestamp).getTime() - new Date(a.entryTimestamp).getTime())[0];
        break;
      case ORDER_STATUS_MAPPINGS.DONE.status:
        // Find the most recent COMPLETED status update (including same-state updates)
        mostRecentUpdate = order.statusUpdates
          ?.filter(update => isStatusUpdateStatus(update, OrderStatus.COMPLETED))
          ?.sort((a, b) => new Date(b.entryTimestamp).getTime() - new Date(a.entryTimestamp).getTime())[0];
        break;
    }

    // If we found a timestamp in statusUpdates, use it
    if (mostRecentUpdate?.entryTimestamp) {
      // Use statusUpdateTimestamp if available (for manual corrections), otherwise use regular entryTimestamp
      const timestampToUse = mostRecentUpdate.statusUpdateTimestamp || mostRecentUpdate.entryTimestamp;
      return new Date(timestampToUse).toLocaleTimeString("en-US", {
        hour12: false,
        hour: "2-digit",
        minute: "2-digit",
      });
    }

    // Only fall back to legacy timestamp fields if there are no status updates at all
    // This prevents showing legacy timestamps when we have status updates but none for this specific status
    if (!order.statusUpdates || order.statusUpdates.length === 0) {
      switch (status) {
        case ORDER_STATUS_MAPPINGS.DISPATCHED.status:
          return order.createTime ? new Date(order.createTime).toLocaleTimeString("en-US", {
            hour12: false,
            hour: "2-digit",
            minute: "2-digit",
          }) : undefined;
        case ORDER_STATUS_MAPPINGS.EN_ROUTE.status:
          return order.acknowledgedTime ? new Date(order.acknowledgedTime).toLocaleTimeString("en-US", {
            hour12: false,
            hour: "2-digit",
            minute: "2-digit",
          }) : undefined;
        case ORDER_STATUS_MAPPINGS.ON_SCENE.status:
          return order.updateTime ? new Date(order.updateTime).toLocaleTimeString("en-US", {
            hour12: false,
            hour: "2-digit",
            minute: "2-digit",
          }) : undefined;
        case ORDER_STATUS_MAPPINGS.DONE.status:
          return order.completionTime ? new Date(order.completionTime).toLocaleTimeString("en-US", {
            hour12: false,
            hour: "2-digit",
            minute: "2-digit",
          }) : undefined;
        default:
          return undefined;
      }
    }

    // If we have status updates but none for this status, return undefined
    return undefined;
  };

  // Helper function to check if a status has a timestamp
  const hasStatusTimestamp = (order: Order, status: string) => {
    // Check if there's a status update for this status (including same-state updates)
    const hasStatusUpdate = order.statusUpdates?.some(update => {
      switch (status) {
        case ORDER_STATUS_MAPPINGS.DISPATCHED.status:
          return isStatusUpdateStatus(update, OrderStatus.CREATED);
        case ORDER_STATUS_MAPPINGS.EN_ROUTE.status:
          return isStatusUpdateStatus(update, OrderStatus.ACKNOWLEDGED);
        case ORDER_STATUS_MAPPINGS.ON_SCENE.status:
          return isStatusUpdateStatus(update, OrderStatus.IN_PROGRESS);
        case ORDER_STATUS_MAPPINGS.DONE.status:
          return isStatusUpdateStatus(update, OrderStatus.COMPLETED);
        default:
          return false;
      }
    });

    if (hasStatusUpdate) {
      return true;
    }

    // Fallback to legacy timestamp fields
    return getTimestampForStatus(order, status) !== undefined;
  };

  // Helper function to check if any later status is completed
  const hasLaterStatusCompleted = (order: Order, columnIndex: number) => {
    const statusMap = [
      ORDER_STATUS_MAPPINGS.DISPATCHED.status,      // column 1 - Dispatched
      ORDER_STATUS_MAPPINGS.EN_ROUTE.status,     // column 2 - En-route  
      ORDER_STATUS_MAPPINGS.ON_SCENE.status,  // column 3 - On Scene
      ORDER_STATUS_MAPPINGS.DONE.status,    // column 4 - Done
    ];

    // Check if any status to the right (higher column index) has a timestamp
    for (let i = columnIndex; i < statusMap.length; i++) {
      if (hasStatusTimestamp(order, statusMap[i])) {
        return true;
      }
    }
    return false;
  };




  // Helper function: Calculate distance (in miles) between two coordinates using the Haversine formula
  const calculateDistance = (
    lat1: number,
    lon1: number,
    lat2: number,
    lon2: number
  ): number => {
    const R = 3958.8; // Radius of the Earth in miles
    const dLat = ((lat2 - lat1) * Math.PI) / 180;
    const dLon = ((lon2 - lon1) * Math.PI) / 180;
    const a =
      Math.sin(dLat / 2) ** 2 +
      Math.cos((lat1 * Math.PI) / 180) *
      Math.cos((lat2 * Math.PI) / 180) *
      Math.sin(dLon / 2) ** 2;
    const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));
    return R * c;
  };

  // Helper function to get asset types for each tab
  const getAssetTypesForTab = (tabIndex: number): AssetType[] => {
    switch (tabIndex) {
      case 0: // Patrol
        return [AssetType.RESPONDER];
      case 1: // Security
        return [AssetType.CAMERA, AssetType.BOT];
      case 2: // Admin
        return [AssetType.DISPATCHER, AssetType.SUPERVISOR];
      default:
        return [];
    }
  };

  // Memoize the sorted available responders based on distance from the selected situation.
  const sortedAvailableResponders = React.useMemo(() => {
    // Filter for only available responders for the dispatch menu
    const availableResponders = allResponders.filter(responder =>
      // @ts-expect-error TODO: Fix type issue - hooks return string representations of enums
      responder.status === "ASSET_STATUS_AVAILABLE"
    );

    if (
      !selectedSituation ||
      typeof selectedSituation.latitude !== "number" ||
      typeof selectedSituation.longitude !== "number"
    ) {
      return availableResponders;
    }
    return [...availableResponders].sort((a, b) => {
      let distA = Number.MAX_VALUE;
      let distB = Number.MAX_VALUE;
      if (typeof a.latitude === "number" && typeof a.longitude === "number") {
        distA = calculateDistance(
          selectedSituation.latitude,
          selectedSituation.longitude,
          a.latitude,
          a.longitude
        );
      }
      if (typeof b.latitude === "number" && typeof b.longitude === "number") {
        distB = calculateDistance(
          selectedSituation.latitude,
          selectedSituation.longitude,
          b.latitude,
          b.longitude
        );
      }
      return distA - distB;
    });
  }, [allResponders, selectedSituation]);

  // Filter available responders by active tab
  const filteredAvailableResponders = React.useMemo(() => {
    const assetTypesForTab = getAssetTypesForTab(activeTab);

    return sortedAvailableResponders.filter(responder => {
      const assetType = typeof responder.type === 'string'
        ? stringToAssetType(responder.type)
        : responder.type;
      return assetTypesForTab.includes(assetType);
    });
  }, [sortedAvailableResponders, activeTab]);

  // State for the Add Responders menu (for available responders)
  const [addResponderAnchor, setAddResponderAnchor] =
    useState<HTMLElement | null>(null);
  const addMenuOpen = Boolean(addResponderAnchor);
  const [checkedResponders, setCheckedResponders] = useState<string[]>([]);

  // State for time picker dropdown
  const [timePickerAnchor, setTimePickerAnchor] = useState<HTMLElement | null>(null);
  const [timePickerOrderId, setTimePickerOrderId] = useState<string>("");
  const [timePickerStatus, setTimePickerStatus] = useState<string>("");
  const timePickerOpen = Boolean(timePickerAnchor);

  const handleClickAway = (event: MouseEvent | TouchEvent) => {
    // Check if click is inside add responder menu
    if (
      addResponderAnchor &&
      addResponderAnchor.contains(event.target as Node)
    ) {
      return;
    }

    // Check if click is inside time picker dropdown
    if (
      timePickerAnchor &&
      timePickerAnchor.contains(event.target as Node)
    ) {
      return;
    }

    // Check if click is inside remove menu
    if (
      removeMenuAnchor &&
      removeMenuAnchor.contains(event.target as Node)
    ) {
      return;
    }

    // Close add responder menu if open
    if (addMenuOpen) {
      handleCloseAddMenu();
    }

    // Close time picker if open
    if (timePickerOpen) {
      setTimePickerAnchor(null);
      setTimePickerOrderId("");
      setTimePickerStatus("");
    }

    // Close remove menu if open
    if (removeMenuOpen) {
      setRemoveMenuAnchor(null);
      setRemoveMenuOrderId("");
    }
  };

  const handleOpenAddMenu = (event: React.MouseEvent<HTMLButtonElement>) => {
    setAddResponderAnchor(event.currentTarget);
  };

  const handleCloseAddMenu = () => {
    setAddResponderAnchor(null);
    setCheckedResponders([]);
    setActiveTab(0); // Reset to first tab when closing
  };

  // Handle tab change
  const handleTabChange = (event: React.SyntheticEvent, newValue: number) => {
    setActiveTab(newValue);
    // Clear checked responders when switching tabs
    setCheckedResponders([]);
  };

  const handleToggleResponder = (responderId: string) => {
    setCheckedResponders((prev) =>
      prev.includes(responderId)
        ? prev.filter((id) => id !== responderId)
        : [...prev, responderId]
    );
  };

  const handleClearAll = () => {
    setCheckedResponders([]);
  };

  const handleDispatchClick = () => {
    // Dispatch new assets
    if (checkedResponders.length > 0) {
      onDispatch(checkedResponders);
    }

    handleCloseAddMenu();
  };

  const handlePrimaryUnitSelect = (orderId: string) => {
    setPrimaryUnitId(orderId);
  };

  const handleOpenRemoveMenu = (event: React.MouseEvent<HTMLButtonElement>, orderId: string) => {
    event.stopPropagation();
    setRemoveMenuAnchor(event.currentTarget);
    setRemoveMenuOrderId(orderId);
  };

  const handleCloseRemoveMenu = () => {
    setRemoveMenuAnchor(null);
    setRemoveMenuOrderId("");
  };

  const handleRemoveOfficerFromIncident = () => {
    if (removeMenuOrderId) {
      onRemoveOfficer(removeMenuOrderId);
    }
    handleCloseRemoveMenu();
  };

  // Handle time selection from dropdown
  const handleTimeSelect = (time: string, date?: Date) => {
    if (timePickerOrderId && timePickerStatus) {
      // Add optimistic timestamp update
      setOptimisticTimestampUpdates(prev => {
        const newMap = new Map(prev);
        newMap.set(timePickerOrderId, { status: timePickerStatus, timestamp: time });
        return newMap;
      });

      // Call the parent function to update the server
      onUpdateTimestamp(timePickerOrderId, timePickerStatus, time, date);
    }
  };

  // Handle cell click for status change or time editing
  const handleCellClick = (order: Order, columnIndex: number, event: React.MouseEvent) => {

    const statusMap: { [key: number]: string } = {
      1: ORDER_STATUS_MAPPINGS.DISPATCHED.status,     // Dispatched
      2: ORDER_STATUS_MAPPINGS.EN_ROUTE.status,    // En-route
      3: ORDER_STATUS_MAPPINGS.ON_SCENE.status, // On Scene
      4: ORDER_STATUS_MAPPINGS.DONE.status,   // Done
    };

    const orderStatusMap: { [key: number]: OrderStatus } = {
      1: ORDER_STATUS_MAPPINGS.DISPATCHED.orderStatus,     // Dispatched
      2: ORDER_STATUS_MAPPINGS.EN_ROUTE.orderStatus, // En-route
      3: ORDER_STATUS_MAPPINGS.ON_SCENE.orderStatus, // On Scene
      4: ORDER_STATUS_MAPPINGS.DONE.orderStatus, // Done
    };

    const status = statusMap[columnIndex];
    const orderStatus = orderStatusMap[columnIndex];

    if (status && orderStatus) {
      // Check if this cell already has a timestamp
      const hasExistingTimestamp = getTimestampForStatus(order, status) !== undefined;

      // Check if any later status is completed (this would be "in the past")
      const isInPast = hasLaterStatusCompleted(order, columnIndex);

      // If cell has existing timestamp, show time picker dropdown for timestamp editing
      if (hasExistingTimestamp) {
        setTimePickerOrderId(order.id);
        setTimePickerStatus(status);
        setTimePickerAnchor(event.currentTarget as HTMLElement);
        return;
      }

      // If cell is in the past (later status completed), still allow editing via dropdown
      if (isInPast) {
        setTimePickerOrderId(order.id);
        setTimePickerStatus(status);
        setTimePickerAnchor(event.currentTarget as HTMLElement);
        return;
      }

      // Only change status if this is a new status (no existing timestamp and not in past)
      // This means we're advancing to a new status, not just updating a timestamp
      const currentTime = new Date().toLocaleTimeString("en-US", {
        hour12: false,
        hour: "2-digit",
        minute: "2-digit",
      });

      // Add optimistic update
      setOptimisticStatusUpdates(prev => {
        const newMap = new Map(prev);
        newMap.set(order.id, { status, timestamp: currentTime });
        return newMap;
      });

      // Send the status change - this should only happen when advancing to a new status
      onChangeStatus(order.id, getOrderStatusString(orderStatus));
    }
  };

  // Clear optimistic updates when server data reflects the changes
  useEffect(() => {
    const clearOptimisticUpdates = () => {
      // Clear optimistic status updates
      setOptimisticStatusUpdates(prev => {
        const newMap = new Map(prev);
        let hasChanges = false;

        // Check each optimistic update
        for (const [orderId, optimisticUpdate] of newMap.entries()) {
          // Find the corresponding order
          const order = assistMemberOrders.find(o => o.id === orderId);
          if (order) {
            // Check if the order status now matches our optimistic update
            const statusMatches = (() => {
              switch (optimisticUpdate.status) {
                case ORDER_STATUS_MAPPINGS.DISPATCHED.status:
                  return isOrderStatus(order, OrderStatus.CREATED);
                case ORDER_STATUS_MAPPINGS.EN_ROUTE.status:
                  return isOrderStatus(order, OrderStatus.ACKNOWLEDGED);
                case ORDER_STATUS_MAPPINGS.ON_SCENE.status:
                  return isOrderStatus(order, OrderStatus.IN_PROGRESS);
                case ORDER_STATUS_MAPPINGS.DONE.status:
                  return isOrderStatus(order, OrderStatus.COMPLETED);
                default:
                  return false;
              }
            })();

            if (statusMatches) {
              newMap.delete(orderId);
              hasChanges = true;
            }
          }
        }

        return hasChanges ? newMap : prev;
      });

      // Clear optimistic timestamp updates
      setOptimisticTimestampUpdates(prev => {
        const newMap = new Map(prev);
        let hasChanges = false;

        // Check each optimistic timestamp update
        for (const [orderId, optimisticUpdate] of newMap.entries()) {
          // Find the corresponding order
          const order = assistMemberOrders.find(o => o.id === orderId);
          if (order) {
            // Check if the order now has a timestamp that matches our optimistic update
            // We need to check the actual server data, not the optimistic data
            const serverTimestamp = (() => {
              // Get the most recent status update entry for this status from server data
              let mostRecentUpdate: any = undefined;

              switch (optimisticUpdate.status) {
                case ORDER_STATUS_MAPPINGS.DISPATCHED.status:
                  mostRecentUpdate = order.statusUpdates
                    ?.filter(update => isStatusUpdateStatus(update, OrderStatus.CREATED))
                    ?.sort((a, b) => new Date(b.entryTimestamp).getTime() - new Date(a.entryTimestamp).getTime())[0];
                  break;
                case ORDER_STATUS_MAPPINGS.EN_ROUTE.status:
                  mostRecentUpdate = order.statusUpdates
                    ?.filter(update => isStatusUpdateStatus(update, OrderStatus.ACKNOWLEDGED))
                    ?.sort((a, b) => new Date(b.entryTimestamp).getTime() - new Date(a.entryTimestamp).getTime())[0];
                  break;
                case ORDER_STATUS_MAPPINGS.ON_SCENE.status:
                  mostRecentUpdate = order.statusUpdates
                    ?.filter(update => isStatusUpdateStatus(update, OrderStatus.IN_PROGRESS))
                    ?.sort((a, b) => new Date(b.entryTimestamp).getTime() - new Date(a.entryTimestamp).getTime())[0];
                  break;
                case ORDER_STATUS_MAPPINGS.DONE.status:
                  mostRecentUpdate = order.statusUpdates
                    ?.filter(update => isStatusUpdateStatus(update, OrderStatus.COMPLETED))
                    ?.sort((a, b) => new Date(b.entryTimestamp).getTime() - new Date(a.entryTimestamp).getTime())[0];
                  break;
              }

              if (mostRecentUpdate?.entryTimestamp) {
                const timestampToUse = mostRecentUpdate.statusUpdateTimestamp || mostRecentUpdate.entryTimestamp;
                return new Date(timestampToUse).toLocaleTimeString("en-US", {
                  hour12: false,
                  hour: "2-digit",
                  minute: "2-digit",
                });
              }

              return undefined;
            })();

            if (serverTimestamp === optimisticUpdate.timestamp) {
              newMap.delete(orderId);
              hasChanges = true;
            }
          }
        }

        return hasChanges ? newMap : prev;
      });
    };

    // Clear optimistic updates when orders change
    clearOptimisticUpdates();
  }, [assistMemberOrders]);

  // Fallback: Clear optimistic updates after 10 seconds to prevent them from staying forever
  useEffect(() => {
    const timeout = setTimeout(() => {
      setOptimisticStatusUpdates(prev => {
        if (prev.size > 0) {
          return new Map();
        }
        return prev;
      });

      setOptimisticTimestampUpdates(prev => {
        if (prev.size > 0) {
          return new Map();
        }
        return prev;
      });
    }, 10000);

    return () => clearTimeout(timeout);
  }, [optimisticStatusUpdates, optimisticTimestampUpdates]);

  return (
    <ClickAwayListener onClickAway={handleClickAway}>
      <Box>
        {/* Title */}
        <Box
          sx={{
            display: "flex",
            alignItems: "center",
            justifyContent: "space-between",
            position: "relative",
            mb: 2,
          }}
        >
          <Typography fontSize={12} fontWeight={600} sx={{ color: "#2B2C2C" }}>
            ASSIGNED UNITS
          </Typography>
          <Button
            variant="outlined"
            startIcon={
              isDispatching ? (
                <Box
                  sx={{
                    width: 15,
                    height: 15,
                    border: "2px solid #374151",
                    borderTop: "2px solid transparent",
                    borderRadius: "50%",
                    animation: "spin 1s linear infinite",
                    "@keyframes spin": {
                      "0%": { transform: "rotate(0deg)" },
                      "100%": { transform: "rotate(360deg)" },
                    },
                  }}
                />
              ) : (
                <AddIcon
                  sx={{
                    width: 15,
                    height: 15,
                    color: "Blue/600"
                  }}
                />
              )
            }
            disabled={isDispatching}
            sx={{
              backgroundColor: "#FFF",
              color: "Blue/600",
              textTransform: "none",
              border: "none",
              height: "29px",
              borderRadius: "10px",
              textAlign: "center",
              fontFeatureSettings: "'liga' off, 'clig' off",
              fontFamily: "Roboto",
              fontStyle: "normal",
              fontSize: "14px",
              fontWeight: 500,
              lineHeight: "140%",
              letterSpacing: "0.1px",
              px: 1.5,
              transition: "all 0.2s ease-in-out",
              "& .MuiButton-startIcon": {
                marginRight: "4px",
              },
              "&:hover": {
                backgroundColor: "#E3F2FD",
                borderColor: "#2196F3",
                color: "#1976D2",
              },
              "&:disabled": {
                backgroundColor: "#F3F4F6",
                color: "#9CA3AF",
                borderColor: "#F3F4F6",
              },
            }}
            onClick={handleOpenAddMenu}
          >
            {isDispatching ? "Dispatching..." : "Dispatch"}
          </Button>
        </Box>
        {/* Units Timeline Table */}
        <Box sx={{ mt: 1 }}>
          {assistMemberOrders.length > 0 ? (
            <TableContainer
              sx={{
                border: "1px solid #E1E4E9",
                borderRadius: "8px",
                maxWidth: "100%",
                maxHeight: "160px", // Header + 3 rows of data (approximately 40px per row)
                overflowY: "auto",
                "& .MuiTableCell-root": {
                  borderColor: "#E1E4E9",
                  textAlign: "left",
                  py: 0.5,
                  px: 1,
                  borderRight: "1px solid #E1E4E9",
                  whiteSpace: "nowrap",
                  "&:last-child": {
                    borderRight: "none",
                  },
                },
                "& .MuiTableRow-root": {
                  "&:last-child td": {
                    borderBottom: "none",
                  },
                },
              }}
            >
              <Table size="small">
                <TableHead>
                  <TableRow>
                    <TableCell
                      sx={{ width: "2fr", fontSize: "10px", color: "#6E757D" }}
                    >
                      Unit
                    </TableCell>
                    <TableCell
                      sx={{ width: "1fr", fontSize: "10px", color: "#6E757D" }}
                    >
                      Dispatched
                    </TableCell>
                    <TableCell
                      sx={{ width: "1fr", fontSize: "10px", color: "#6E757D" }}
                    >
                      En-route
                    </TableCell>
                    <TableCell
                      sx={{ width: "1fr", fontSize: "10px", color: "#6E757D" }}
                    >
                      On Scene
                    </TableCell>
                    <TableCell
                      sx={{ width: "1fr", fontSize: "10px", color: "#6E757D" }}
                    >
                      Cleared
                    </TableCell>
                    <TableCell sx={{ width: "0.2fr" }}></TableCell>
                  </TableRow>
                </TableHead>
                <TableBody>
                  {assistMemberOrders.map((order) => {
                    // Find the asset for this order
                    const asset = allResponders.find(res => res.id === order.assetId) ||
                      { id: order.assetId, name: "Unknown Unit" } as Asset;

                    const truncatedId = asset.id?.replace(/[^0-9]/g, "")?.slice(0, 3);
                    const isOptimistic = optimisticDispatchedUnits?.includes(asset.id);
                    const optimisticStatus = optimisticStatusUpdates.get(order.id);

                    return (
                      <TableRow
                        key={order.id}
                        sx={{
                          backgroundColor: isOptimistic ? "rgba(0, 96, 255, 0.05)" : "transparent",
                          opacity: isOptimistic ? 0.8 : 1,
                          transition: "all 0.2s ease-in-out",
                        }}
                      >
                        <TableCell>
                          <Box
                            sx={{
                              display: "flex",
                              alignItems: "center",
                              gap: 0.3,
                              maxWidth: "100%",
                            }}
                          >
                            <Typography
                              sx={{
                                fontWeight: 700,
                                fontSize: "12px",
                                color: "#111827",
                              }}
                            >
                              {truncatedId}
                            </Typography>
                            <Typography
                              sx={{
                                color: "#6B7280",
                                fontSize: "12px",
                                overflow: "hidden",
                                textOverflow: "ellipsis",
                                whiteSpace: "nowrap",
                              }}
                            >
                              {asset.name
                                ? asset.name.includes(" ")
                                  ? toTitleCase(asset.name.split(" ")[0].trim())
                                  : toTitleCase(asset.name)
                                : ""}
                            </Typography>
                          </Box>
                        </TableCell>
                        {/* Dispatched Column */}
                        <TableCell
                          sx={{
                            fontSize: "12px",
                            fontWeight: 600,
                            color: optimisticStatus?.status === ORDER_STATUS_MAPPINGS.DISPATCHED.status ? "#0060FF" : "#111827",
                            cursor: "pointer",
                            transition: "all 0.2s ease-in-out",
                            '&:hover': {
                              backgroundColor: "rgba(0, 96, 255, 0.1)",
                              color: "#0060FF"
                            },
                          }}
                          onClick={(event) => handleCellClick(order, 1, event)}
                        >
                          <Box sx={{ display: 'flex', alignItems: 'center', gap: 0.5 }}>
                            {(isOptimistic || optimisticStatus?.status === ORDER_STATUS_MAPPINGS.DISPATCHED.status || optimisticTimestampUpdates.get(order.id)?.status === ORDER_STATUS_MAPPINGS.DISPATCHED.status) && (
                              <Box
                                sx={{
                                  width: 8,
                                  height: 8,
                                  border: "1px solid #0060FF",
                                  borderTop: "1px solid transparent",
                                  borderRadius: "50%",
                                  animation: "spin 1s linear infinite",
                                  "@keyframes spin": {
                                    "0%": { transform: "rotate(0deg)" },
                                    "100%": { transform: "rotate(360deg)" },
                                  },
                                }}
                              />
                            )}
                            <Typography sx={{ fontSize: "12px", color: isOptimistic || optimisticStatus?.status === ORDER_STATUS_MAPPINGS.DISPATCHED.status || optimisticTimestampUpdates.get(order.id)?.status === ORDER_STATUS_MAPPINGS.DISPATCHED.status ? "#0060FF" : "inherit" }}>
                              {(() => {
                                const timestamp = getTimestampForStatus(order, ORDER_STATUS_MAPPINGS.DISPATCHED.status);
                                if (timestamp) return timestamp;
                                if (isOptimistic) return "Dispatching...";
                                return "-";
                              })()}
                            </Typography>
                          </Box>
                        </TableCell>
                        {/* En-route Column */}
                        <TableCell
                          sx={{
                            fontSize: "12px",
                            fontWeight: 600,
                            color: optimisticStatus?.status === ORDER_STATUS_MAPPINGS.EN_ROUTE.status ? "#0060FF" : "#111827",
                            cursor: "pointer",
                            transition: "all 0.2s ease-in-out",
                            '&:hover': {
                              backgroundColor: "rgba(0, 96, 255, 0.1)",
                              color: "#0060FF"
                            },
                          }}
                          onClick={(event) => handleCellClick(order, 2, event)}
                        >
                          <Box sx={{ display: 'flex', alignItems: 'center', gap: 0.5 }}>
                            {(optimisticStatus?.status === ORDER_STATUS_MAPPINGS.EN_ROUTE.status || optimisticTimestampUpdates.get(order.id)?.status === ORDER_STATUS_MAPPINGS.EN_ROUTE.status) && (
                              <Box
                                sx={{
                                  width: 8,
                                  height: 8,
                                  border: "1px solid #0060FF",
                                  borderTop: "1px solid transparent",
                                  borderRadius: "50%",
                                  animation: "spin 1s linear infinite",
                                  "@keyframes spin": {
                                    "0%": { transform: "rotate(0deg)" },
                                    "100%": { transform: "rotate(360deg)" },
                                  },
                                }}
                              />
                            )}
                            <Typography sx={{ fontSize: "12px", color: optimisticStatus?.status === ORDER_STATUS_MAPPINGS.EN_ROUTE.status || optimisticTimestampUpdates.get(order.id)?.status === ORDER_STATUS_MAPPINGS.EN_ROUTE.status ? "#0060FF" : "inherit" }}>
                              {(() => {
                                const timestamp = getTimestampForStatus(order, ORDER_STATUS_MAPPINGS.EN_ROUTE.status);
                                return timestamp || "-";
                              })()}
                            </Typography>
                          </Box>
                        </TableCell>
                        {/* On Scene Column */}
                        <TableCell
                          sx={{
                            fontSize: "12px",
                            fontWeight: 600,
                            color: optimisticStatus?.status === ORDER_STATUS_MAPPINGS.ON_SCENE.status ? "#0060FF" : "#111827",
                            cursor: "pointer",
                            transition: "all 0.2s ease-in-out",
                            '&:hover': {
                              backgroundColor: "rgba(0, 96, 255, 0.1)",
                              color: "#0060FF"
                            },
                          }}
                          onClick={(event) => handleCellClick(order, 3, event)}
                        >
                          <Box sx={{ display: 'flex', alignItems: 'center', gap: 0.5 }}>
                            {(optimisticStatus?.status === ORDER_STATUS_MAPPINGS.ON_SCENE.status || optimisticTimestampUpdates.get(order.id)?.status === ORDER_STATUS_MAPPINGS.ON_SCENE.status) && (
                              <Box
                                sx={{
                                  width: 8,
                                  height: 8,
                                  border: "1px solid #0060FF",
                                  borderTop: "1px solid transparent",
                                  borderRadius: "50%",
                                  animation: "spin 1s linear infinite",
                                  "@keyframes spin": {
                                    "0%": { transform: "rotate(0deg)" },
                                    "100%": { transform: "rotate(360deg)" },
                                  },
                                }}
                              />
                            )}
                            <Typography sx={{ fontSize: "12px", color: optimisticStatus?.status === ORDER_STATUS_MAPPINGS.ON_SCENE.status || optimisticTimestampUpdates.get(order.id)?.status === ORDER_STATUS_MAPPINGS.ON_SCENE.status ? "#0060FF" : "inherit" }}>
                              {(() => {
                                const timestamp = getTimestampForStatus(order, ORDER_STATUS_MAPPINGS.ON_SCENE.status);
                                return timestamp || "-";
                              })()}
                            </Typography>
                          </Box>
                        </TableCell>
                        {/* Done Column */}
                        <TableCell
                          sx={{
                            fontSize: "12px",
                            fontWeight: 600,
                            color: optimisticStatus?.status === ORDER_STATUS_MAPPINGS.DONE.status ? "#0060FF" : "#111827",
                            cursor: "pointer",
                            transition: "all 0.2s ease-in-out",
                            '&:hover': {
                              backgroundColor: "rgba(0, 96, 255, 0.1)",
                              color: "#0060FF"
                            },
                          }}
                          onClick={(event) => handleCellClick(order, 4, event)}
                        >
                          <Box sx={{ display: 'flex', alignItems: 'center', gap: 0.5 }}>
                            {(optimisticStatus?.status === ORDER_STATUS_MAPPINGS.DONE.status || optimisticTimestampUpdates.get(order.id)?.status === ORDER_STATUS_MAPPINGS.DONE.status) && (
                              <Box
                                sx={{
                                  width: 8,
                                  height: 8,
                                  border: "1px solid #0060FF",
                                  borderTop: "1px solid transparent",
                                  borderRadius: "50%",
                                  animation: "spin 1s linear infinite",
                                  "@keyframes spin": {
                                    "0%": { transform: "rotate(0deg)" },
                                    "100%": { transform: "rotate(360deg)" },
                                  },
                                }}
                              />
                            )}
                            <Typography sx={{ fontSize: "12px", color: optimisticStatus?.status === ORDER_STATUS_MAPPINGS.DONE.status || optimisticTimestampUpdates.get(order.id)?.status === ORDER_STATUS_MAPPINGS.DONE.status ? "#0060FF" : "inherit" }}>
                              {(() => {
                                const timestamp = getTimestampForStatus(order, ORDER_STATUS_MAPPINGS.DONE.status);
                                return timestamp || "-";
                              })()}
                            </Typography>
                          </Box>
                        </TableCell>
                        <TableCell sx={{ width: "40px", padding: "4px" }}>
                          <IconButton
                            size="small"
                            onClick={(e) => handleOpenRemoveMenu(e, order.id)}
                            sx={{
                              p: 0.25,
                              minWidth: "24px",
                              width: "24px",
                              height: "24px",
                              color: "#111827",
                              "&:hover": {
                                backgroundColor: "rgba(0, 0, 0, 0.04)",
                                color: "#6B7280"
                              },
                            }}
                          >
                            <MoreVertIcon sx={{ fontSize: "16px" }} />
                          </IconButton>
                        </TableCell>
                      </TableRow>
                    );
                  })}
                </TableBody>
              </Table>
            </TableContainer>
          ) : (
            <Typography
              variant="body2"
              sx={{
                color: "#6B7280",
                fontSize: "12px",
                textAlign: "center",
                py: 2
              }}
            >
              NO UNITS ASSIGNED
            </Typography>
          )}
        </Box>

        {/* Add Responders Menu implemented with Popper and Paper wrapped in ClickAwayListener */}
        <Popper
          open={addMenuOpen}
          anchorEl={addResponderAnchor}
          placement="bottom-start"
          style={{ zIndex: 1300 }}
        >
          <Paper
            sx={{
              mt: 1,
              borderRadius: "16px",
              display: "flex",
              flexDirection: "column",
              width: 350,
              p: 1,
            }}
          >
            {/* Tabs */}
            <Box sx={{ mb: 1 }}>
              <Tabs
                value={activeTab}
                onChange={handleTabChange}
                sx={{
                  borderBottom: '1px solid #E5E7EB',
                  '& .MuiTab-root': {
                    display: 'flex',
                    height: '40px',
                    minWidth: '36px',
                    minHeight: '40px',
                    padding: '8px 16px',
                    justifyContent: 'center',
                    alignItems: 'center',
                    gap: '8px',
                    background: '#FFF',
                    textTransform: 'none',
                    fontFamily: 'Roboto',
                    fontSize: '14px',
                    fontStyle: 'normal',
                    fontWeight: 400,
                    lineHeight: '140%',
                    letterSpacing: '0.17px',
                    color: '#98A1AE',
                    textAlign: 'center',
                    fontFeatureSettings: "'liga' off, 'clig' off",
                    // Removed borderRadius and borderBottom from here
                    '&.Mui-selected': {
                      color: '#4A5565',
                      fontWeight: 500,
                      letterSpacing: '0.1px',
                      // Removed borderBottom and marginBottom from here
                    },

                  },
                  // Removed '& .MuiTabs-indicator' block
                }}
                TabIndicatorProps={{ sx: { backgroundColor: '#0060FF', height: '2px' } }}
              >
                <Tab label="Patrol" />
                <Tab label="Security" />
                <Tab label="Admin" />
              </Tabs>
            </Box>

            <Box
              sx={{
                overflowY: "auto",
                overflowX: "hidden",
                px: 1,
                maxHeight: 200,
              }}
            >
              {filteredAvailableResponders.length > 0 ? (
                filteredAvailableResponders.map((res) => {
                  const truncatedId = res.id?.replace(/[^0-9]/g, "")?.slice(0, 3);
                  let distanceText = "";
                  if (
                    selectedSituation &&
                    typeof res.latitude === "number" &&
                    typeof res.longitude === "number" &&
                    typeof selectedSituation.latitude === "number" &&
                    typeof selectedSituation.longitude === "number"
                  ) {
                    const distance = calculateDistance(
                      selectedSituation.latitude,
                      selectedSituation.longitude,
                      res.latitude,
                      res.longitude
                    );
                    distanceText = ` ${distance.toFixed(2)} mi away`;
                  }
                  return (
                    <MenuItem key={res.id} sx={{ padding: 0, width: "100%" }}>
                      <FormControlLabel
                        sx={{
                          width: 350,
                          padding: "4px 8px",
                          display: "flex",
                          alignItems: "center",
                        }}
                        control={
                          <Checkbox
                            size="small"
                            // sx={{ width: 16, height: 16 }}
                            checked={checkedResponders.includes(res.id)}
                            onChange={() => handleToggleResponder(res.id)}
                          />
                        }
                        label={
                          <Box
                            sx={{
                              display: "flex",
                              alignItems: "center",
                              width: "100%",
                            }}
                          >
                            <Box
                              sx={{
                                flex: 1,
                                overflow: "hidden",
                                whiteSpace: "nowrap",
                                textOverflow: "ellipsis",
                              }}
                            >
                              <Typography
                                component="span"
                                fontWeight="bold"
                                sx={{ mr: 1, fontSize: 15 }}
                              >
                                {truncatedId}
                              </Typography>
                              <Typography
                                component="span"
                                noWrap
                                fontSize={13}
                                sx={{ mr: 1 }}
                              >
                                {res.name
                                  ? res.name.includes("+")
                                    ? toTitleCase(res.name.split("+")[0].trim())
                                      .length > 14
                                      ? `${toTitleCase(
                                        res.name.split("+")[0].trim()
                                      ).slice(0, 13)}...`
                                      : toTitleCase(res.name.split("+")[0].trim())
                                    : toTitleCase(res.name).length > 14
                                      ? `${toTitleCase(res.name).slice(0, 13)}...`
                                      : toTitleCase(res.name)
                                  : ""}
                              </Typography>
                              <Typography
                                component="span"
                                fontSize={10}
                                color="#727374"
                              >
                                {distanceText}
                              </Typography>
                            </Box>
                            <Chip
                              label={
                                getAssetStatusLabel(String(res.status)) ||
                                "Unknown"
                              }
                              size="small"
                              sx={{
                                backgroundColor: "#0CB9A214",
                                color: "#00806F",
                                borderRadius: "16px",
                                padding: "2px 8px",
                                height: "auto",
                                fontSize: "12px",
                                display: "flex",
                                justifyContent: "center",
                                "& .MuiChip-label": {
                                  p: 0,
                                  color: "#00806F",
                                },
                                position: "absolute",
                                right: "4px",
                              }}
                            />
                          </Box>
                        }
                      />
                    </MenuItem>
                  );
                })
              ) : (
                <Box
                  sx={{
                    display: "flex",
                    justifyContent: "center",
                    alignItems: "center",
                    py: 3,
                    px: 2,
                  }}
                >
                  <Typography
                    sx={{
                      fontSize: "14px",
                      color: "#6B7280",
                      fontStyle: "italic",
                    }}
                  >
                    None available.
                  </Typography>
                </Box>
              )}
            </Box>
            <Divider sx={{ my: 1 }} />
            <Box
              sx={{
                display: "flex",
                gap: "12px",
                justifyContent: "flex-end",
                px: 1,
                pb: 1,
              }}
            >
              <DesignSystemButton
                label="Clear"
                style="ghost"
                color="grey"
                size="medium"
                onClick={handleClearAll}

              />
              <DesignSystemButton
                color="blue"
                label={isDispatching ? "Dispatching..." : "Dispatch"}
                prominence
                size="medium"
                style="filled"
                disabled={isDispatching}
                onClick={handleDispatchClick}

              />
            </Box>
          </Paper>
        </Popper>

        {/* Time Picker Dropdown */}
        <TimePickerDropdown
          open={timePickerOpen}
          anchorEl={timePickerAnchor}
          onClose={() => {
            setTimePickerAnchor(null);
            setTimePickerOrderId("");
            setTimePickerStatus("");
          }}
          onTimeSelect={handleTimeSelect}
          currentTime={timePickerOrderId && timePickerStatus ? (() => {
            const foundOrder = assistMemberOrders.find(o => o.id === timePickerOrderId);
            return foundOrder ? getTimestampForStatus(foundOrder, timePickerStatus) : undefined;
          })() : undefined}
        />

        {/* Remove Menu */}
        <Menu
          anchorEl={removeMenuAnchor}
          open={removeMenuOpen}
          onClose={handleCloseRemoveMenu}
          anchorOrigin={{
            vertical: 'bottom',
            horizontal: 'right',
          }}
          transformOrigin={{
            vertical: 'top',
            horizontal: 'right',
          }}
          PaperProps={{
            sx: {
              borderRadius: "8px",
              boxShadow: "0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06)",
              minWidth: "160px",
            }
          }}
        >
          <MenuItem onClick={handleRemoveOfficerFromIncident} sx={{ py: 1 }}>
            <ListItemText
              primary="Remove from Incident"
              primaryTypographyProps={{
                fontSize: "14px",
                color: "#111827"
              }}
            />
          </MenuItem>
        </Menu>
      </Box>
    </ClickAwayListener>
  );
};
