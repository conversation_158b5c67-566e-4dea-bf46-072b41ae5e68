"use client";
import {
  useAddCaseUpdate,
  useAddReportToCase,
  useCreateCase,
  useListCasesBySituationId,
} from "@/app/apis/services/workflow/cases/hooks";
import { addOrderStatusUpdate } from "@/app/apis/services/workflow/orders/endpoints";
import {
  hookOrderStatusToString,
  hookOrderTypeToString,
} from "@/app/apis/services/workflow/orders/enumConverters";
import {
  useCreateOrder,
  useListOrdersForSituation,
  useUpdateOrder,
} from "@/app/apis/services/workflow/orders/hooks";
import {
  useCreateReport,
  useCreateReportSection,
} from "@/app/apis/services/workflow/reports/v2/hooks";
import {
  useAddAdditionalInfoToSituation,
  useAddSituationUpdate,
  useSituation,
  useUpdateSituation,
} from "@/app/apis/services/workflow/situations/hooks";
import { useCallContext } from "@/app/contexts/Call/CallContext";
import { useDispatcher } from "@/app/contexts/User/DispatcherContext";
import {
  formatPhoneNumberForDisplay,
  useCallerDisplayInfo,
} from "@/app/utils/caller-identification";
import {
  getIncidentLabel,
  getIncidentStatusLabel,
  getOrderStatusLabel,
  toTitleCase,
} from "@/app/utils/utils";
import { create } from "@bufbuild/protobuf";
import ArrowForwardIcon from "@mui/icons-material/ArrowForward";
import CheckCircleIcon from "@mui/icons-material/CheckCircle";
import HeadsetMicIcon from "@mui/icons-material/HeadsetMic";
import MedicalServicesIcon from "@mui/icons-material/MedicalServices";
import PublishedWithChangesIcon from "@mui/icons-material/PublishedWithChanges";
import {
  Box,
  Button,
  Chip,
  Divider,
  IconButton,
  MenuItem,
  Stack,
  TextField,
  Typography,
} from "@mui/material";
import mapboxgl from "mapbox-gl";
import { Asset } from "proto/hero/assets/v2/assets_pb";
import {
  AddCaseUpdateRequest,
  AddReportToCaseRequest,
  CreateCaseRequest,
  CreateCaseResponse,
  ListCasesBySituationIdRequest,
} from "proto/hero/cases/v1/cases_pb";
import {
  AddOrderStatusUpdateRequestSchema,
  CreateOrderRequest,
  ListOrdersForSituationRequest,
  OrderStatus,
  OrderStatusUpdateEntrySchema,
  OrderType,
  UpdateOrderRequest,
} from "proto/hero/orders/v2/orders_pb";
import {
  CreateReportRequest,
  CreateReportResponse,
  ReportStatus,
  ReportType
} from "proto/hero/reports/v2/reports_pb";
import {
  AddAdditionalInfoRequest,
  AddSituationUpdateRequest,
  SituationStatus,
  SituationType,
  UpdateEntry,
  UpdateSituationRequest,
} from "proto/hero/situations/v2/situations_pb";
import React, { useEffect, useMemo, useRef, useState } from "react";
import { getChipStyles } from "../UnitsSection/utils";
import styles from "./ActionPane.module.css";
import { AssignedUnitsSection } from "./AssignedUnitsSection";
import CallMismatchBanner from "./CallMismatchBanner";
import { incidentTypes, priorityLevels } from "./constants";
import LoadingSkelton from "./LoadingSkeleton";
import LocationAutocomplete from "./LocationAutocomplete";
import ResolveModal from "./ResolveModal";
import { createUpdateEntry, parseAdditionalInfo } from "./utils";

mapboxgl.accessToken =
  "pk.eyJ1IjoiaGVyby1zYWZldHkiLCJhIjoiY202bDM3bGlzMDRybTJrcHJibm5sYTFzMSJ9.-ekjZGG1E_cWYCOnBrdEag";

type ActionPaneProps = {
  selectedSituationId?: string;
  reportDate?: Date;
  onLocationSelect: (location: {
    placeName: string;
    address: string;
    coordinates: [number, number];
  }) => void;
  onResolveSituation: () => void;
  onCollapseActionPane: () => void;
  responders: Asset[];
};

export type RespondingOfficer = {
  unit: string;
  primary: boolean;
  dispatched: string;
  enRoute: string;
  onScene: string;
  cleared: string;
};

import { OfficerShieldIcon } from "@/design-system/components/icons";

export const ActionPane: React.FC<ActionPaneProps> = ({
  selectedSituationId,
  reportDate,
  onLocationSelect,
  onResolveSituation,
  onCollapseActionPane,
  responders,
}) => {
  // Use Call Context hook
  const { currentActiveCall, heldCalls, resumeSpecificCall } = useCallContext();
  const { asset } = useDispatcher();
  const { data: situation, isLoading: situationLoading } = useSituation(
    selectedSituationId || "",
    5000
  );

  // Fetch caller display name if available
  const { displayTitle, isUnknown } = useCallerDisplayInfo(
    situation?.contactNo
  );

  // Case fetching to populate the case button
  const { data: casesForSituation, refetch: refetchCases } =
    useListCasesBySituationId(
      {
        situationId: selectedSituationId || "",
        pageSize: 10,
        pageToken: "",
      } as ListCasesBySituationIdRequest,
      {
        // Refetch every minute - we don't need constant polling, but just in case another dispatcher creates a case while this one is open
        refetchInterval: 60000,
        enabled: !!selectedSituationId,
      }
    );

  // Add state to track a newly created case for optimistic UI updates
  const [localCreatedCase, setLocalCreatedCase] = useState<any>(null);

  // Check if there's already a case for this situation
  const hasExistingCase = useMemo(() => {
    return (
      (casesForSituation?.cases && casesForSituation.cases.length > 0) ||
      localCreatedCase !== null
    );
  }, [casesForSituation, localCreatedCase]);

  // Store the latest case for display purposes
  const latestCase = useMemo(() => {
    if (localCreatedCase) return localCreatedCase;
    if (casesForSituation?.cases && casesForSituation.cases.length > 0) {
      return casesForSituation.cases[0];
    }
    return null;
  }, [casesForSituation, localCreatedCase]);

  const { data: ordersForSituation } = useListOrdersForSituation(
    {
      situationId: selectedSituationId || "",
      pageSize: 100,
      pageToken: "",
    } as ListOrdersForSituationRequest,
    {
      refetchInterval: 3000,
    }
  );

  const isAllAssistMemberOrdersCompleted = ordersForSituation?.orders?.every(
    (order) =>
      hookOrderStatusToString(order.status) === "ORDER_STATUS_COMPLETED" ||
      hookOrderStatusToString(order.status) === "ORDER_STATUS_CANCELLED" ||
      hookOrderStatusToString(order.status) === "ORDER_STATUS_REJECTED" ||
      hookOrderTypeToString(order.type) !== "ORDER_TYPE_ASSIST_MEMBER"
  );

  const [openResolveModal, setOpenResolveModal] = useState(false);

  const handleOpenResolveModal = () => {
    setOpenResolveModal(true);
  };

  const handleCloseResolveModal = () => {
    setOpenResolveModal(false);
  };

  const { mutate: addReportToCase } = useAddReportToCase();
  const { mutate: addCaseUpdate } = useAddCaseUpdate();

  // Create Report hook - moved here to be available for handleNextResolve
  const { mutateAsync: createReportAsync } = useCreateReport({
    onSuccess: (response: CreateReportResponse) => {
      if (response.report && selectedSituationId) {
        // Create all report sections
        createReportSections(response.report.id).then(() => {
          addSituationUpdate({
            id: selectedSituationId,
            update: createUpdateEntry(
              `Report created: #${response.report?.id
                ?.replace(/[^0-9]/g, "")
                .slice(0, 4)}`,
              "info change",
              "dispatch",
              "Dispatch"
            ),
          } as AddSituationUpdateRequest);
        });

        // Note: Report-case linking is now handled in handleNextResolve
        // to avoid duplicate linking attempts
      }
    },
  });

  const handleNextResolve = async (disposition: string, primaryResponderId?: string, shouldCreateCase?: boolean) => {
    setOpenResolveModal(false);

    try {
      // Get the case ID if available
      const caseId = latestCase?.id;

      // Save the disposition to additionalInfoJson
      if (situation) {
        const currentAdditionalInfo = situation.additionalInfoJson
          ? parseAdditionalInfo(situation.additionalInfoJson)
          : {};

        const updatedAdditionalInfo = {
          ...currentAdditionalInfo,
          disposition: disposition,
          primaryResponderId: primaryResponderId,
        };

        await new Promise<void>((resolve, reject) => {
          addAdditionalInfo(
            {
              id: situation.id,
              additionalInfoJson: JSON.stringify(updatedAdditionalInfo),
            } as AddAdditionalInfoRequest,
            {
              onSuccess: () => resolve(),
              onError: (error) => reject(error),
            }
          );
        });
      }

      // Update the situation status to resolved
      await new Promise<void>((resolve, reject) => {
        updateSituation(
          {
            situation: {
              ...situation,
              status: SituationStatus.RESOLVED,
            },
          } as UpdateSituationRequest,
          {
            onSuccess: () => resolve(),
            onError: (error) => reject(error),
          }
        );
      });

      // If shouldCreateCase is true and no case exists, create one first
      if (situation && shouldCreateCase && !caseId) {
        try {
          // Create the case and wait for it to complete
          const caseResponse = await createCaseAsync({
            case: {
              situationIds: [selectedSituationId],
              status: "CASE_STATUS_OPEN",
            },
          } as unknown as CreateCaseRequest);

          if (caseResponse.case?.id) {
            // Create report without caseId in payload
            const reportResponse = await createReportAsync({
              report: {
                title: getIncidentLabel(situation.type?.toString() || ""),
                situationId: selectedSituationId,
                status: ReportStatus.ASSIGNED,
                reportType: ReportType.INCIDENT_PRIMARY,
                author: primaryResponderId,
              } as any,
            } as CreateReportRequest);

            if (reportResponse.report?.id) {
              // Convert mutate to a promise using Promise constructor
              await new Promise<void>((resolve, reject) => {
                addReportToCase(
                  {
                    caseId: caseResponse.case!.id,
                    reportId: reportResponse.report!.id,
                  } as unknown as AddReportToCaseRequest,
                  {
                    onSuccess: () => resolve(),
                    onError: (error) => reject(error),
                  }
                );
              });
            }
          }
        } catch (error) {
          console.error("Error in case creation workflow:", error);
          throw error; // Re-throw to be caught by outer try-catch
        }
      } else if (situation && caseId) {
        // If case already exists, just create the report
        try {
          const reportResponse = await createReportAsync({
            report: {
              title: getIncidentLabel(situation.type?.toString() || ""),
              situationId: selectedSituationId,
              status: ReportStatus.ASSIGNED,
              reportType: ReportType.INCIDENT_PRIMARY,
              author: primaryResponderId,
            } as any,
          } as CreateReportRequest);

          if (reportResponse.report?.id) {
            // Convert mutate to a promise using Promise constructor
            await new Promise<void>((resolve, reject) => {
              addReportToCase(
                {
                  caseId: caseId,
                  reportId: reportResponse.report!.id,
                } as unknown as AddReportToCaseRequest,
                {
                  onSuccess: () => resolve(),
                  onError: (error) => reject(error),
                }
              );
            });
          }
        } catch (error) {
          console.error("Error in report creation workflow:", error);
          throw error; // Re-throw to be caught by outer try-catch
        }
      }

      // All operations completed successfully
      onResolveSituation();
    } catch (error) {
      console.error("Error in resolve workflow:", error);
      // Here you could add error handling UI feedback
      // For example, show a toast/snackbar notification
    }
  };

  // ------------------------------------------
  // Orders/Assets fetch
  // ------------------------------------------
  const assignedAssetIdsForSituation =
    ordersForSituation?.orders
      ?.filter(
        (order) =>
          hookOrderStatusToString(order.status) !== "ORDER_STATUS_CANCELLED" &&
          hookOrderStatusToString(order.status) !== "ORDER_STATUS_REJECTED"
      )
      .map((order) => order.assetId)
      .filter((id) => id != null) || [];

  // Optimistic state for dispatched units
  const [optimisticDispatchedUnits, setOptimisticDispatchedUnits] = useState<
    Set<string>
  >(new Set());
  const [isDispatching, setIsDispatching] = useState(false);

  // State to track removed officers
  const [removedOfficerIds, setRemovedOfficerIds] = useState<Set<string>>(
    new Set()
  );

  const availableResponders = useMemo(
    () =>
      responders.filter(
        (responder) =>
          // @ts-expect-error TODO: Fix type issue TODO: fix type
          responder.status === "ASSET_STATUS_AVAILABLE" &&
          !optimisticDispatchedUnits.has(responder.id)
      ),
    [responders, optimisticDispatchedUnits]
  );

  const assignedResponders = useMemo(
    () =>
      responders.filter(
        (res) =>
          assignedAssetIdsForSituation.includes(res.id) ||
          optimisticDispatchedUnits.has(res.id) ||
          removedOfficerIds.has(res.id)
      ),
    [
      responders,
      assignedAssetIdsForSituation,
      optimisticDispatchedUnits,
      removedOfficerIds,
    ]
  );

  // Get responders who actually have orders for this situation (for ResolveModal)
  const respondersWithOrders = useMemo(
    () =>
      responders.filter((res) => assignedAssetIdsForSituation.includes(res.id)),
    [responders, assignedAssetIdsForSituation]
  );

  // ------------------------------------------
  // Hooks for updating situation
  // ------------------------------------------
  const { mutate: updateSituation } = useUpdateSituation();
  const { mutate: addAdditionalInfo } = useAddAdditionalInfoToSituation();
  const { mutate: updateOrder } = useUpdateOrder();
  const { mutateAsync: createOrder } = useCreateOrder();
  const { mutate: addSituationUpdate } = useAddSituationUpdate();

  const handleAssignAgents = async (agentIds: string[]) => {
    if (!selectedSituationId) return;

    // Optimistic update: immediately add units to dispatched list
    setOptimisticDispatchedUnits((prev) => new Set([...prev, ...agentIds]));
    setIsDispatching(true);

    try {
      // Create all orders in parallel for better performance
      const orderPromises = agentIds.map(async (agentId) => {
        try {
          const orderResponse = await createOrder({
            order: {
              situationId: selectedSituationId,
              assetId: agentId,
              status: OrderStatus.CREATED,
              type: OrderType.ASSIST_MEMBER,
            },
          } as CreateOrderRequest);

          return { success: true, agentId, order: orderResponse };
        } catch (error) {
          console.error("Failed to create order for agent:", agentId, error);
          return { success: false, agentId, error };
        }
      });

      // Wait for all orders to complete
      const results = await Promise.all(orderPromises);

      // Log summary of results
      const successful = results.filter((r) => r.success).length;
      const failed = results.filter((r) => !r.success).length;

      if (failed > 0) {
        console.warn(
          `Dispatch completed: ${successful} successful, ${failed} failed`
        );

        // Rollback optimistic updates for failed dispatches
        const failedAgentIds = results
          .filter((r) => !r.success)
          .map((r) => r.agentId);
        setOptimisticDispatchedUnits((prev) => {
          const newSet = new Set(prev);
          failedAgentIds.forEach((id) => newSet.delete(id));
          return newSet;
        });


      }

      // Add situation updates for successfully created orders
      const successfulResults = results.filter((r) => r.success);
      for (const result of successfulResults) {
        const responder = responders.find(r => r.id === result.agentId);
        // Slice the unit UUID number to first 3 digits (ex. 06cf98e4-ad1a-45c5-923f-31ad88cd02b9 --> 069)
        const unitNumber = result.agentId ? String(result.agentId).replace(/[^0-9]/g, "").slice(0, 3) : "Unknown";
        const responderName = responder?.name || `Unit ${unitNumber}`;

        addSituationUpdate({
          id: selectedSituationId,
          update: createUpdateEntry(
            `Dispatched ${responderName}`,
            "order created",
            result.agentId,
            unitNumber
          ),
        } as AddSituationUpdateRequest);
      }

      // Don't clear optimistic state here - let it persist until server data updates
      // The optimistic state will be naturally replaced when ordersForSituation refetches
    } catch (error) {
      console.error("Dispatch failed:", error);

      // Rollback all optimistic updates on complete failure
      setOptimisticDispatchedUnits(new Set());
    } finally {
      setIsDispatching(false);
    }
  };

  // Clear optimistic state when server data catches up
  useEffect(() => {
    if (optimisticDispatchedUnits.size > 0 && ordersForSituation?.orders) {
      const serverAssetIds = new Set(
        ordersForSituation.orders
          .filter(
            (order) =>
              hookOrderStatusToString(order.status) !==
              "ORDER_STATUS_CANCELLED" &&
              hookOrderStatusToString(order.status) !== "ORDER_STATUS_REJECTED"
          )
          .map((order) => order.assetId)
          .filter((id) => id != null)
      );

      // Check if all optimistic units are now in server data
      const allOptimisticUnitsInServer = Array.from(
        optimisticDispatchedUnits
      ).every((optimisticId) => serverAssetIds.has(optimisticId));

      if (allOptimisticUnitsInServer) {
        setOptimisticDispatchedUnits(new Set());
      }
    }
  }, [ordersForSituation?.orders, optimisticDispatchedUnits]);

  // Sync removed officers with server data
  useEffect(() => {
    if (ordersForSituation?.orders) {
      const cancelledAssetIds = new Set(
        ordersForSituation.orders
          .filter(
            (order) =>
              hookOrderStatusToString(order.status) === "ORDER_STATUS_CANCELLED"
          )
          .map((order) => order.assetId)
          .filter((id) => id != null)
      );

      // Update removed officers set to match server data
      setRemovedOfficerIds(cancelledAssetIds);
    }
  }, [ordersForSituation?.orders]);

  // ------------------------------------------
  // Elapsed time
  // ------------------------------------------
  const effectiveReportDate = situation?.createTime
    ? new Date(situation.createTime)
    : reportDate;
  const reportedDate = effectiveReportDate
    ? effectiveReportDate.toISOString().split("T")[0].replace(/-/g, ".")
    : "";

  const reportedTime = effectiveReportDate
    ? effectiveReportDate.toLocaleTimeString("en-US", {
      hour12: false,
      hour: "2-digit",
      minute: "2-digit",
    })
    : "";

  const additionalInfo = situation?.additionalInfoJson
    ? parseAdditionalInfo(situation.additionalInfoJson)
    : {};

  // ------------------------------------------
  // Build initial formData
  // ------------------------------------------
  const buildInitialFormData = () => {
    return {
      locationQuery: situation?.address || "",
      commonName: additionalInfo.commonName || "",
      locationDetails: additionalInfo.locationDetails || "",
      incidentType: situation
        ? String(situation.type)
        : "SITUATION_TYPE_UNSPECIFIED",
      priority: situation ? `p${situation.priority}` : "",
      incidentDescription: situation ? situation.description : "",
      reporterName: situation ? situation.reporterName : "",
      contactNo: situation ? situation.contactNo : "",
      weapons: additionalInfo.weapons || "",
      incidentUpdates: situation?.updates
        ? situation.updates.map((update) => ({
          eventType: update.eventType,
          description: update.message,
          author: update.updaterId,
          occurredAt: update.timestamp,
        }))
        : [],
    };
  };

  const [formData, setFormData] = useState(buildInitialFormData());

  // If situation changes, reset form.
  const isInitialRender = useRef(true);
  useEffect(() => {
    if (!situationLoading && situation) {
      setFormData(buildInitialFormData());
    }
  }, [selectedSituationId, situationLoading]);

  // Keep "situation" fields updated on server (ignore additionalInfoJson).
  // Only update priority and incident type immediately, other fields update on blur
  useEffect(() => {
    if (!situation) return;
    if (isInitialRender.current) {
      isInitialRender.current = false;
      return;
    }
    // Update situation without touching items/weapons in additional info
    updateSituation({
      situation: {
        id: situation.id,
        priority: formData.priority
          ? Number(formData.priority?.replace("p", ""))
          : situation.priority,
        type: formData.incidentType
          ? // @ts-expect-error TODO: Fix type issue TODO: fix type
          (formData.incidentType as SituationType)
          : situation.type,
        description: situation.description,
        reporterName: situation.reporterName,
        contactNo: situation.contactNo,
      },
    } as UpdateSituationRequest);
  }, [formData.priority, formData.incidentType]);

  // Update text fields on blur
  const handleTextFieldBlur = (
    field: "reporterName" | "contactNo" | "incidentDescription",
    value: string
  ) => {
    if (!situation) return;

    updateSituation({
      situation: {
        ...situation,
        [field === "incidentDescription" ? "description" : field]: value,
      },
    } as UpdateSituationRequest);
  };

  const isInitialAdditionalInfoRender = useRef(true);
  useEffect(() => {
    if (!situation) return;
    if (isInitialAdditionalInfoRender.current) {
      isInitialAdditionalInfoRender.current = false;
      return;
    }

    // Build the final additional info object
    const newAdditionalInfo = {
      weapons: formData.weapons,
      commonName: formData.commonName,
      locationDetails: formData.locationDetails,
    };

    addAdditionalInfo({
      id: situation.id,
      additionalInfoJson: JSON.stringify(newAdditionalInfo),
    } as AddAdditionalInfoRequest);
  }, [formData.weapons, formData.commonName, formData.locationDetails]);

  useEffect(() => {
    // Listen to coordinate changes only to update the form
    setFormData((prev) => ({
      ...prev,
      locationQuery: situation?.address || "",
    }));
  }, [situation?.address]);

  useEffect(() => {
    // Listen to updates only to update the form
    setFormData((prev) => {
      // Get server updates
      const serverUpdates = situation?.updates
        ? situation.updates.map((update) => ({
          eventType: update.eventType,
          description: update.message,
          author: update.updaterId,
          occurredAt: update.timestamp,
        }))
        : [];

      // Get optimistic updates (those with _dispatchOperationId)
      const optimisticUpdates = prev.incidentUpdates.filter(
        (update) => (update as any)._dispatchOperationId
      );

      // Debug logging
      if (
        process.env.NODE_ENV === "development" &&
        (serverUpdates.length > 0 || optimisticUpdates.length > 0)
      ) {
        console.log("Merging updates:", {
          serverUpdates: serverUpdates.map((u) => ({
            description: u.description,
            eventType: u.eventType,
          })),
          optimisticUpdates: optimisticUpdates.map((u) => ({
            description: u.description,
            eventType: u.eventType,
          })),
        });
      }

      // Remove optimistic updates that have corresponding server updates
      const filteredOptimisticUpdates = optimisticUpdates.filter(
        (optimisticUpdate) => {
          // Check if there's a server update that matches this optimistic update
          const hasCorrespondingServerUpdate = serverUpdates.some(
            (serverUpdate) => {
              // For dispatch updates, check if the messages match
              if (
                optimisticUpdate.eventType === "order created" &&
                serverUpdate.eventType === "order created" &&
                optimisticUpdate.description
                  .toLowerCase()
                  .includes("dispatched") &&
                serverUpdate.description.toLowerCase().includes("dispatched")
              ) {
                // Extract the unit numbers from both messages
                const optimisticUnits =
                  optimisticUpdate.description.match(/\d+/g) || [];
                const serverUnits =
                  serverUpdate.description.match(/\d+/g) || [];

                // Check if the unit numbers match
                const matches =
                  optimisticUnits.length === serverUnits.length &&
                  optimisticUnits.every(
                    (unit, index) => unit === serverUnits[index]
                  );

                // Also check if the messages are very similar (in case of minor formatting differences)
                const optimisticMessage = optimisticUpdate.description
                  .toLowerCase()
                  .replace(/\s+/g, " ")
                  .trim();
                const serverMessage = serverUpdate.description
                  .toLowerCase()
                  .replace(/\s+/g, " ")
                  .trim();
                const messageSimilarity = optimisticMessage === serverMessage;

                if (
                  process.env.NODE_ENV === "development" &&
                  (matches || messageSimilarity)
                ) {
                  console.log("Found matching dispatch updates:", {
                    optimistic: optimisticUpdate.description,
                    server: serverUpdate.description,
                    optimisticUnits,
                    serverUnits,
                    unitMatch: matches,
                    messageSimilarity,
                    finalMatch: matches || messageSimilarity,
                  });
                }

                return matches || messageSimilarity;
              }

              return false;
            }
          );

          // Keep the optimistic update only if there's no corresponding server update
          return !hasCorrespondingServerUpdate;
        }
      );

      // Debug logging
      if (
        process.env.NODE_ENV === "development" &&
        filteredOptimisticUpdates.length !== optimisticUpdates.length
      ) {
        console.log("Filtered optimistic updates:", {
          before: optimisticUpdates.length,
          after: filteredOptimisticUpdates.length,
          removed: optimisticUpdates.length - filteredOptimisticUpdates.length,
        });
      }

      // Merge server updates with filtered optimistic updates
      const mergedUpdates = [...serverUpdates, ...filteredOptimisticUpdates];

      // Debug logging for final result
      if (
        process.env.NODE_ENV === "development" &&
        mergedUpdates.length !== serverUpdates.length + optimisticUpdates.length
      ) {
        console.log("Final merge result:", {
          serverUpdates: serverUpdates.length,
          optimisticUpdates: optimisticUpdates.length,
          filteredOptimisticUpdates: filteredOptimisticUpdates.length,
          finalMergedUpdates: mergedUpdates.length,
          removedOptimisticUpdates:
            optimisticUpdates.length - filteredOptimisticUpdates.length,
        });
      }

      return {
        ...prev,
        incidentUpdates: mergedUpdates,
      };
    });
  }, [situation?.updates]);

  // Fallback: Remove optimistic updates after 30 seconds to prevent them from staying forever
  useEffect(() => {
    const timeout = setTimeout(() => {
      setFormData((prev) => {
        const optimisticUpdates = prev.incidentUpdates.filter(
          (update) => (update as any)._dispatchOperationId
        );

        if (optimisticUpdates.length > 0) {
          return {
            ...prev,
            incidentUpdates: prev.incidentUpdates.filter(
              (update) => !(update as any)._dispatchOperationId
            ),
          };
        }

        return prev;
      });
    }, 30000); // 30 seconds

    return () => clearTimeout(timeout);
  }, [formData.incidentUpdates]);

  // ------------------------------------------
  // Handle location picks
  // ------------------------------------------
  const [searchedLocation, setSearchedLocation] = useState<{
    placeName: string;
    address: string;
    coordinates: [number, number];
  } | null>(null);

  const handleLocationSelect = async (suggestion: any) => {
    try {
      const retrieveUrl = `https://api.mapbox.com/search/searchbox/v1/retrieve/${suggestion.mapbox_id}?access_token=${mapboxgl.accessToken}&session_token=session-token-123`;
      const response = await fetch(retrieveUrl);
      const data = await response.json();
      if (data.features && data.features.length > 0) {
        const feature = data.features[0];

        // Use name for main address field, with fallback to actual address
        const displayAddress =
          feature.properties.name ||
          feature.properties.address ||
          feature.properties.full_address ||
          "";
        const actualAddress =
          feature.properties.address || feature.properties.full_address || "";

        setFormData((prev) => ({
          ...prev,
          locationQuery: displayAddress,
        }));
        setSearchedLocation({
          placeName: feature.properties.name || "Unknown",
          address:
            feature.properties.full_address ||
            feature.properties.address ||
            "Unknown address",
          coordinates: feature.geometry.coordinates,
        });
        onLocationSelect({
          placeName: feature.properties.name,
          address: displayAddress,
          coordinates: feature.geometry.coordinates,
        });

        // Build location metadata for additionalInfoJson
        const locationMetadata: any = {};

        // Add addressCommonName with actual street address
        if (actualAddress) {
          locationMetadata.addressCommonName = actualAddress;
        }

        // Add addressCity if place name exists
        if (feature.properties.context?.place?.name) {
          locationMetadata.addressCity = feature.properties.context.place.name;
        }

        // Add addressState if region exists (format: "CA - California")
        if (
          feature.properties.context?.region?.region_code &&
          feature.properties.context?.region?.name
        ) {
          locationMetadata.addressState = `${feature.properties.context.region.region_code} - ${feature.properties.context.region.name}`;
        }

        // Add addressZip if postcode exists
        if (feature.properties.context?.postcode?.name) {
          locationMetadata.addressZip =
            feature.properties.context.postcode.name;
        }

        // Get current additional info and merge with location metadata
        if (situation) {
          const currentAdditionalInfo = situation.additionalInfoJson
            ? parseAdditionalInfo(situation.additionalInfoJson)
            : {};

          const updatedAdditionalInfo = {
            ...currentAdditionalInfo,
            ...locationMetadata,
          };

          // Save the updated additional info
          addAdditionalInfo({
            id: situation.id,
            additionalInfoJson: JSON.stringify(updatedAdditionalInfo),
          } as AddAdditionalInfoRequest);
        }

        addSituationUpdate({
          id: selectedSituationId,
          update: createUpdateEntry(
            `Changed location to ${displayAddress}`,
            "info change",
            "dispatch",
            "Dispatch"
          ),
        } as AddSituationUpdateRequest);
      }
    } catch (error) {
      console.error("Error retrieving feature:", error);
    }
  };

  const [message, setMessage] = useState("");

  const handleSend = () => {
    const trimmed = message.trim();
    if (!trimmed) return;

    addSituationUpdate({
      id: selectedSituationId,
      update: createUpdateEntry(trimmed, "quick note", "dispatch", "Dispatch"),
    } as AddSituationUpdateRequest);

    setMessage("");
  };

  const handleKeyDown = (e: React.KeyboardEvent<HTMLDivElement>) => {
    if (e.key === "Enter" && !e.shiftKey) {
      e.preventDefault();
      handleSend();
    }
  };

  // Auto-scroll to bottom when new messages are added
  const eventLogRef = useRef<HTMLDivElement>(null);

  const { incidentUpdates } = formData;

  useEffect(() => {
    if (eventLogRef.current) {
      eventLogRef.current.scrollTop = eventLogRef.current.scrollHeight;
    }
  }, [incidentUpdates]); // Scroll when incidentUpdates changes

  // ------------------------------------------
  // Menu for adding responders
  // ------------------------------------------
  // Handle dispatch
  const handleDispatch = async (checkedResponders: string[]) => {
    if (checkedResponders && checkedResponders.length > 0) {
      await handleAssignAgents(checkedResponders);
    }
  };

  const handleRemoveOfficer = (orderId: string) => {
    const order = ordersForSituation?.orders?.find(
      (order) => order.id === orderId
    );
    if (order) {
      updateOrder(
        {
          order: {
            ...order,
            status: OrderStatus.CANCELLED,
          },
        } as UpdateOrderRequest,
        {
          onSuccess: () => {
            addSituationUpdate({
              id: selectedSituationId,
              update: createUpdateEntry(
                "Cancelled",
                "status change",
                order.assetId,
                order.assetId?.replace(/[^0-9]/g, "")?.slice(0, 3)
              ),
            } as AddSituationUpdateRequest);
          },
          onError: () => {
            console.error("Error cancelling order:", orderId);
          },
        }
      );
    }
  };

  const handleChangeStatus = (orderId: string, newStatus: string) => {
    const order = ordersForSituation?.orders?.find(
      (order) => order.id === orderId
    );
    if (order) {
      updateOrder(
        // @ts-expect-error TODO: Fix type issue TODO: fix type
        {
          order: {
            ...order,
            status: newStatus,
          },
        } as UpdateOrderRequest,
        {
          onSuccess: () => {
            addSituationUpdate({
              id: selectedSituationId,
              update: createUpdateEntry(
                getOrderStatusLabel(newStatus),
                "status change",
                order.assetId,
                order.assetId?.replace(/[^0-9]/g, "")?.slice(0, 3)
              ),
            } as AddSituationUpdateRequest);
          },
        }
      );
    }
  };

  // Handle updating timestamps for order status changes
  // This creates same-state status updates (e.g., "CREATED -> CREATED") with corrected timestamps
  // without changing the actual order status, allowing for timestamp corrections without disturbing the flow
  const handleUpdateTimestamp = (
    orderId: string,
    status: string,
    newTimestamp: string,
    selectedDate?: Date
  ) => {
    // Validate the timestamp format (HH:MM)
    const timeRegex = /^([0-1]?[0-9]|2[0-3]):[0-5][0-9]$/;
    if (!timeRegex.test(newTimestamp)) {
      console.error("Invalid timestamp format. Expected HH:MM format.");
      return;
    }

    // Create a new date object with the selected date (or current date if not provided) and the new time
    const dateToUse = selectedDate || new Date();
    const [hours, minutes] = newTimestamp.split(":").map(Number);
    dateToUse.setHours(hours, minutes, 0, 0);

    // Find the order by ID
    const order = ordersForSituation?.orders?.find(
      (order) => order.id === orderId
    );

    if (order) {
      // Map status strings to order status enums
      const statusToOrderStatusMap: { [key: string]: OrderStatus } = {
        dispatched: OrderStatus.CREATED,
        "en-route": OrderStatus.ACKNOWLEDGED,
        "on-scene": OrderStatus.IN_PROGRESS,
        done: OrderStatus.COMPLETED,
      };

      const targetOrderStatus = statusToOrderStatusMap[status];

      if (targetOrderStatus) {
        // Create the update entry with CURRENT time (when the edit was made)
        const updateEntry = {
          message: `Updated ${status} time to ${newTimestamp}`,
          timestamp: new Date().toISOString(), // Current time when edit was made
          updateSource: "UPDATE_SOURCE_HUMAN_OPERATOR" as const,
          displayName: order.assetId?.replace(/[^0-9]/g, "")?.slice(0, 3),
          eventType: "info change",
          updaterId: order.assetId,
        } as unknown as UpdateEntry;

        // Add situation update with the current timestamp (when edit was made)
        addSituationUpdate({
          id: selectedSituationId,
          update: updateEntry,
        } as AddSituationUpdateRequest);

        // Add a new status update entry with the same status but the corrected timestamp
        // This will appear as the "most recent" entry for this status
        addOrderStatusUpdate(
          create(AddOrderStatusUpdateRequestSchema, {
            id: orderId,
            statusUpdate: create(OrderStatusUpdateEntrySchema, {
              entryTimestamp: new Date().toISOString(), // When the edit was made (current time)
              newStatus: targetOrderStatus,
              previousStatus: targetOrderStatus, // Same status (no actual status change)
              newTypeSpecificStatus: order.typeSpecificStatus || "",
              previousTypeSpecificStatus: order.typeSpecificStatus || "",
              note: `Manually corrected timestamp to ${newTimestamp}`,
              updaterId: order.assetId,
              updateSource: "UPDATE_SOURCE_HUMAN_OPERATOR" as any,
              statusUpdateTimestamp: dateToUse.toISOString(), // The corrected timestamp
            }),
          })
        )
          .then(() => {
            console.log(
              `Updated ${status} timestamp to ${newTimestamp} for order ${order.id}`
            );
          })
          .catch((error: any) => {
            console.error("Error updating order timestamp:", error);
          });
      }
    } else {
      console.error(`No order found for order ID ${orderId}`);
    }
  };

  // Add section creation function
  const createReportSections = async (reportId: string) => {
    try {
      // Parse caller name based on word count
      const callerName = formData.reporterName
        ? formData.reporterName.trim()
        : "";
      const callerNameParts = callerName.split(/\s+/);

      let firstName = "";
      let middleName = "";
      let lastName = "";

      if (callerNameParts.length === 1) {
        firstName = callerNameParts[0];
      } else if (callerNameParts.length === 2) {
        firstName = callerNameParts[0];
        lastName = callerNameParts[1];
      } else if (callerNameParts.length === 3) {
        firstName = callerNameParts[0];
        middleName = callerNameParts[1];
        lastName = callerNameParts[2];
      } else if (callerNameParts.length > 3) {
        firstName = callerNameParts[0];
        lastName = callerNameParts.slice(1).join(" ");
      }

      // Get the current date for end time
      const currentDate = new Date();

      // Get location metadata from additionalInfoJson
      const locationMetadata = situation?.additionalInfoJson
        ? parseAdditionalInfo(situation.additionalInfoJson)
        : {};

      // 1. Create Incident Details Section with pre-filled data
      await createReportSection({
        reportId,
        section: {
          // @ts-expect-error TODO: Fix type issue TODO: fix type
          type: "SECTION_TYPE_INCIDENT_DETAILS",
          incidentDetails: {
            initialType: formData.incidentType,
            finalType: formData.incidentType,
            incidentLocationStreetAddress:
              locationMetadata.addressCommonName ||
              formData.locationQuery ||
              "",
            incidentLocationCommonName:
              // Map commonName from situation's additionalInfoJson to report's incidentLocationCommonName
              locationMetadata.commonName ||
              formData.commonName ||
              formData.locationQuery ||
              locationMetadata.addressCommonName ||
              "",
            incidentLocationCity: locationMetadata.addressCity || "",
            incidentLocationState: locationMetadata.addressState || "",
            incidentLocationZipCode: locationMetadata.addressZip || "",
            reportingPerson: {
              firstName,
              middleName,
              lastName,
              phoneNumber: formData.contactNo,
              role: "Caller",
            },
            incidentStartTime: situation?.createTime
              ? new Date(situation.createTime).toISOString()
              : new Date().toISOString(),
            incidentEndTime: currentDate.toISOString(),
          },
        },
      });

      // 2. Create People Entity List Section
      await createReportSection({
        reportId,
        section: {
          // @ts-expect-error TODO: Fix type issue TODO: fix type
          type: "SECTION_TYPE_ENTITY_LIST_PEOPLE",
          entityList: {
            title: "People",
            entityRefs: [],
          },
        },
      });

      // 3. Create Vehicles Entity List Section
      await createReportSection({
        reportId,
        section: {
          // @ts-expect-error TODO: Fix type issue TODO: fix type
          type: "SECTION_TYPE_ENTITY_LIST_VEHICLE",
          entityList: {
            title: "Vehicles",
            entityRefs: [],
          },
        },
      });

      // 4. Create Properties Entity List Section
      await createReportSection({
        reportId,
        section: {
          // @ts-expect-error TODO: Fix type issue TODO: fix type
          type: "SECTION_TYPE_ENTITY_LIST_PROPERTIES",
          entityList: {
            title: "Properties",
            entityRefs: [],
          },
        },
      });

      // 5. Create Organizations Entity List Section
      await createReportSection({
        reportId,
        section: {
          // @ts-expect-error TODO: Fix type issue TODO: fix type
          type: "SECTION_TYPE_ENTITY_LIST_ORGANIZATIONS",
          entityList: {
            title: "Organizations",
            entityRefs: [],
          },
        },
      });

      // 6. Create Narrative Section
      await createReportSection({
        reportId,
        section: {
          // @ts-expect-error TODO: Fix type issue TODO: fix type
          type: "SECTION_TYPE_NARRATIVE",
          narrative: {
            richText: "",
          },
        },
      });

      // 7. Create Offenses Section
      await createReportSection({
        reportId,
        section: {
          // @ts-expect-error TODO: Fix type issue TODO: fix type
          type: "SECTION_TYPE_OFFENSE",
        },
      });

      // 7. Create Arrests Section
      await createReportSection({
        reportId,
        section: {
          // @ts-expect-error TODO: Fix type issue TODO: fix type
          type: "SECTION_TYPE_ARREST",
        },
      });

      // 8. Create Media Section
      await createReportSection({
        reportId,
        section: {
          // @ts-expect-error TODO: Fix type issue TODO: fix type
          type: "SECTION_TYPE_MEDIA",
        },
      });
    } catch (error) {
      console.error("Error creating report sections:", error);
    }
  };

  const { mutateAsync: createReportSection } = useCreateReportSection();

  const { mutateAsync: createCaseAsync } = useCreateCase({
    onSuccess: (response: CreateCaseResponse) => {
      if (selectedSituationId && response.case) {
        // Store the created case locally for optimistic UI updates
        setLocalCreatedCase(response.case);

        // Refetch cases to get the server data
        refetchCases();

        // Add situation update
        addSituationUpdate({
          id: selectedSituationId,
          update: createUpdateEntry(
            // Slice the case UUID to get the first 3 digits (ex. 06cf98e4-ad1a-45c5-923f-31ad88cd02b9 --> 069)
            `Case created: #${response.case.id
              .replace(/[^0-9]/g, "")
              .slice(0, 3)}`,
            "info change",
            "dispatch",
            "Dispatch"
          ),
        } as AddSituationUpdateRequest);

        // Add system update to the case timeline
        if (response.case.id) {
          const timestamp = new Date().toISOString();

          addCaseUpdate({
            caseId: response.case.id,
            update: {
              displayName: "System",
              message: "Case created",
              eventType: "SYSTEM_UPDATE",
              updateSource: "UPDATE_SOURCE_HUMAN_OPERATOR",
              updaterId: asset?.id || "",
              timestamp: timestamp,
              data: {
                selectedTime: timestamp,
              },
            },
          } as unknown as AddCaseUpdateRequest);
        }
      }
    },
    onError: () => {
      setIsCreatingCase(false);
    },
  });

  const [isCreatingCase, setIsCreatingCase] = useState(false);

  const handleCreateCase = () => {
    if (!situation || !selectedSituationId) return;

    setIsCreatingCase(true);

    // Cast to unknown first to avoid type errors
    const caseRequest = {
      case: {
        situationIds: [selectedSituationId],
        status: "CASE_STATUS_OPEN",
      },
    } as unknown as CreateCaseRequest;

    createCaseAsync(caseRequest).catch(() => {
      setIsCreatingCase(false);
    }).finally(() => {
      setIsCreatingCase(false);
    });
  };

  if (!selectedSituationId) return null;
  if (situationLoading) {
    return <LoadingSkelton />;
  }

  // Find call state for the selected situation
  const getCallState = (situationId?: string): "none" | "active" | "hold" => {
    if (!situationId) return "none";

    // Check if this situation has the active call
    if (currentActiveCall?.situationId === situationId) return "active";

    // Check if this situation has a held call
    if (heldCalls?.heldCalls?.some((call) => call.situationId === situationId))
      return "hold";

    return "none";
  };

  const handleSwapCall = () => {
    if (!selectedSituationId) return;

    // Find the held call for this situation
    const matchingHeldCall = heldCalls?.heldCalls?.find(
      (call) => call.situationId === selectedSituationId
    );
    if (matchingHeldCall) {
      resumeSpecificCall(matchingHeldCall);
    }
  };

  return (
    <Box
      className={styles.actionPane}
      p={0}
      style={{ overflowY: "auto", borderRight: "1px solid #e0e0e0" }}
    >
      <Box>
        <CallMismatchBanner
          selectedSituationId={selectedSituationId}
          globalActiveCallId={currentActiveCall?.callSid}
          selectedSituationCallStatus={getCallState(selectedSituationId)}
          onSwapCall={handleSwapCall}
        />
      </Box>

      {/* Sticky Header */}
      <Box
        sx={{
          position: "sticky",
          top: 0,
          backgroundColor: "#ffffff",
          zIndex: 10,
        }}
      >
        <Box
          sx={{ display: "flex", flexDirection: "column" }}
          pt={2}
          pl={2}
          pr={2}
        >
          {/* Top Row: Incident ID, Status Chip, and Right Buttons */}
          <Box
            sx={{
              display: "flex",
              justifyContent: "space-between",
              alignItems: "center",
            }}
          >
            {/* Left Section: Incident ID and Chip */}
            <Box sx={{ display: "flex", alignItems: "center", gap: 1 }}>
              {/* Incident Id */}
              <Typography
                color="black"
                fontSize={22}
                fontWeight={700}
                lineHeight={"44px"}
              >
                {selectedSituationId
                  ? selectedSituationId ? String(selectedSituationId).replace(/[^0-9]/g, "").slice(0, 3) : "Unknown"
                  : null}
              </Typography>

              {/* Incident Status Chip */}
              <Chip
                label={getIncidentStatusLabel(
                  String(situation?.status || ""),
                  isAllAssistMemberOrdersCompleted
                )}
                sx={{
                  backgroundColor: "#0060FF1A",
                  color: "#0060FF",
                  borderRadius: "7px",
                  fontSize: "10px",
                  padding: "0px",
                  height: "24px",
                  "& .MuiChip-label": {
                    padding: "2px 8px",
                  },
                }}
              />
            </Box>

            {/* Right Section: Buttons */}
            <Box sx={{ display: "flex", alignItems: "center", gap: 1 }}>
              {hasExistingCase || isCreatingCase ? (
                <Button
                  startIcon={
                    <MedicalServicesIcon
                      sx={{ color: "white", height: 16, width: 16 }}
                    />
                  }
                  sx={{
                    color: "white",
                    backgroundColor: "#4CAF50",
                    padding: "4px 12px",
                    borderRadius: "6px",
                    fontSize: "10px",
                    fontWeight: "500",
                    textTransform: "none",
                    fontFamily: "Roboto",
                    height: "24px",
                    minWidth: "64px",
                    "&:disabled": {
                      backgroundColor: "#4CAF50",
                      color: "white",
                    },
                  }}
                  disabled
                >
                  {isCreatingCase
                    ? "Creating case..."
                    : latestCase?.id
                      ? `#${String(latestCase.id).replace(/[^0-9]/g, "").slice(0, 3)}`
                      : "Case Created"}
                </Button>
              ) : (
                <Button
                  startIcon={
                    <MedicalServicesIcon
                      sx={{ color: "#374151", height: 16, width: 16 }}
                    />
                  }
                  sx={{
                    color: "#374151",
                    backgroundColor: "#E5E7EB",
                    padding: "4px 12px",
                    borderRadius: "6px",
                    fontSize: "10px",
                    fontWeight: "500",
                    textTransform: "none",
                    fontFamily: "Roboto",
                    height: "24px",
                    minWidth: "64px",
                    transition: "all 0.2s ease-in-out",
                    "&:hover": {
                      backgroundColor: "#E3F2FD",
                      borderColor: "#2196F3",
                      color: "#1976D2",
                    },
                  }}
                  onClick={handleCreateCase}
                >
                  Case #
                </Button>
              )}
              <Button
                startIcon={
                  <CheckCircleIcon
                    sx={{ color: "#374151", height: 16, width: 16 }}
                  />
                }
                sx={{
                  color: "#374151",
                  backgroundColor: "#E5E7EB",
                  padding: "4px 12px",
                  borderRadius: "6px",
                  fontSize: "10px",
                  fontWeight: 600,
                  textTransform: "none",
                  fontFamily: "Roboto",
                  transition: "all 0.2s ease-in-out",
                  "&:hover": {
                    backgroundColor: "#E3F2FD",
                    borderColor: "#2196F3",
                    color: "#1976D2",
                  },
                }}
                onClick={handleOpenResolveModal}
              >
                Resolve
              </Button>
              <IconButton
                onClick={onCollapseActionPane}
                sx={{
                  backgroundColor: "transparent",
                  color: "#374151",
                  transition: "all 0.2s ease-in-out",
                  "&:hover": {
                    backgroundColor: "rgba(55, 65, 81, 0.1)",
                    transform: "scale(1.05)",
                  },
                }}
              >
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  width="16"
                  height="16"
                  viewBox="0 0 18 18"
                  fill="none"
                >
                  <path
                    d="M13.048 12.5865V5.4135L9.452 9L13.048 12.5865ZM2.30775 17.5C1.80908 17.5 1.38308 17.3234 1.02975 16.9703C0.676583 16.6169 0.5 16.1909 0.5 15.6923V2.30775C0.5 1.80908 0.676583 1.38308 1.02975 1.02975C1.38308 0.676583 1.80908 0.5 2.30775 0.5H15.6923C16.1909 0.5 16.6169 0.676583 16.9703 1.02975C17.3234 1.38308 17.5 1.80908 17.5 2.30775V15.6923C17.5 16.1909 17.3234 16.6169 16.9703 16.9703C16.6169 17.3234 16.1909 17.5 15.6923 17.5H2.30775ZM5 16V2H2.30775C2.23075 2 2.16025 2.03208 2.09625 2.09625C2.03208 2.16025 2 2.23075 2 2.30775V15.6923C2 15.7692 2.03208 15.8398 2.09625 15.9038C2.16025 15.9679 2.23075 16 2.30775 16H5ZM6.5 16H15.6923C15.7692 16 15.8398 15.9679 15.9038 15.9038C15.9679 15.8398 16 15.7692 16 15.6923V2.30775C16 2.23075 15.9679 2.16025 15.9038 2.09625C15.8398 2.03208 15.7692 2 15.6923 2H6.5V16Z"
                    fill="#1C1B1F"
                  />
                </svg>
              </IconButton>
            </Box>
          </Box>

          {/* Bottom Row: Date and Time */}
          <Box sx={{ display: "flex", alignItems: "center", gap: 3, mt: 1 }}>
            {/* Date Column */}
            <Box
              sx={{
                display: "flex",
                flexDirection: "column",
                alignItems: "flex-start",
              }}
            >
              <Typography color="#6B7280" fontSize={10} fontWeight={400}>
                Date
              </Typography>
              <Typography color="#374151" fontSize={12} fontWeight={600}>
                {reportedDate}
              </Typography>
            </Box>

            {/* Time Column */}
            <Box
              sx={{
                display: "flex",
                flexDirection: "column",
                alignItems: "flex-start",
              }}
            >
              <Typography color="#6B7280" fontSize={10} fontWeight={400}>
                Time
              </Typography>
              <Typography color="#374151" fontSize={12} fontWeight={600}>
                {reportedTime}
              </Typography>
            </Box>
            {/* Phone# Column */}
            <Box
              sx={{
                display: "flex",
                flexDirection: "column",
                alignItems: "flex-start",
              }}
            >
              <Typography color="#6B7280" fontSize={10} fontWeight={400}>
                Phone#
              </Typography>
              <Typography color="#374151" fontSize={12} fontWeight={600}>
                {formData.contactNo
                  ? formatPhoneNumberForDisplay(formData.contactNo)
                  : "N/A"}
              </Typography>
            </Box>
          </Box>
        </Box>

        <Divider sx={{ margin: "16px -16px 0px -16px" }} />
      </Box>

      {/* Content below the sticky header in vertical layout */}
      <Box
        sx={{
          display: "flex",
          flexDirection: "column",
          height: "calc(100vh)",
          overflow: "hidden",
        }}
      >
        {/* 1. Incident Details Section (Fixed Height) */}
        <Box
          sx={{
            py: 1.5,
            px: 2,
            borderBottom: "1px solid #e0e0e0",
            minHeight: 288,
            maxHeight: "40vh",
            overflowY: "auto",
            flexShrink: 0,
            display: "flex",
            flexDirection: "column",
          }}
        >
          <Box
            sx={{
              display: "flex",
            }}
          >
          </Box>
          <Stack spacing={1.5}>
            {/* Row 1: Location and Common Name */}
            <Box sx={{ display: "flex", gap: 1.5 }}>
              <Box sx={{ flexGrow: 298, flexBasis: 0, minWidth: 0 }}>
                <Typography
                  variant="body2"
                  sx={{
                    color: "var(--Grey-500, #697282)",
                    fontFamily: "Roboto",
                    fontSize: "12px",
                    fontStyle: "normal",
                    fontWeight: 400,
                    lineHeight: "140%",
                    letterSpacing: "0.17px",
                    fontFeatureSettings: "'liga' off, 'clig' off",
                    marginBottom: "6px",
                  }}
                >
                  Location
                </Typography>
                <LocationAutocomplete
                  value={formData.locationQuery || ""}
                  onChange={(value: string) =>
                    setFormData((prev) => ({ ...prev, locationQuery: value }))
                  }
                  onSelect={handleLocationSelect}
                />
              </Box>
              <Box sx={{ flexGrow: 178, flexBasis: 0, minWidth: 0 }}>
                <Typography
                  variant="body2"
                  sx={{
                    color: "var(--Grey-500, #697282)",
                    fontFamily: "Roboto",
                    fontSize: "12px",
                    fontStyle: "normal",
                    fontWeight: 400,
                    lineHeight: "140%",
                    letterSpacing: "0.17px",
                    fontFeatureSettings: "'liga' off, 'clig' off",
                    marginBottom: "6px",
                  }}
                >
                  Common Name
                </Typography>
                <TextField
                  placeholder="Common name"
                  variant="outlined"
                  size="small"
                  value={formData.commonName || ""}
                  onChange={(e) =>
                    setFormData((prev) => ({ ...prev, commonName: e.target.value }))
                  }
                  InputProps={{
                    style: {
                      fontSize: "14px",
                      display: "flex",
                      alignItems: "center",
                    },
                  }}
                  InputLabelProps={{ shrink: false }}
                  sx={{
                    width: "100%",
                    "& input::placeholder": {
                      color: "#969EA8",
                      opacity: 1,
                    },
                    "& .MuiInputLabel-root": {
                      display: "none",
                    },
                    "& .MuiOutlinedInput-root": {
                      height: "32px",
                      "& fieldset": {
                        borderColor: "#E1E4E9",
                        borderRadius: "8px",
                      },
                      "&:hover fieldset": {
                        borderColor: "#E1E4E9",
                      },
                      "&.Mui-focused fieldset": {
                        borderColor: "#E1E4E9",
                      },
                      "& input": {
                        height: "32px",
                      },
                    },
                  }}
                />
              </Box>
            </Box>

            {/* Row 2: Location Details */}
            <Box>
              <Typography
                variant="body2"
                sx={{
                  color: "var(--Grey-500, #697282)",
                  fontFamily: "Roboto",
                  fontSize: "12px",
                  fontStyle: "normal",
                  fontWeight: 400,
                  lineHeight: "140%",
                  letterSpacing: "0.17px",
                  fontFeatureSettings: "'liga' off, 'clig' off",
                  marginBottom: "6px",
                }}
              >
                Location Details
              </Typography>
              <TextField
                placeholder="Location details"
                variant="outlined"
                fullWidth
                size="small"
                value={formData.locationDetails || ""}
                onChange={(e) =>
                  setFormData((prev) => ({ ...prev, locationDetails: e.target.value }))
                }
                InputProps={{
                  style: {
                    fontSize: "14px",
                    display: "flex",
                    alignItems: "center",
                  },
                }}
                InputLabelProps={{ shrink: false }}
                sx={{
                  "& input::placeholder": {
                    color: "#969EA8",
                    opacity: 1,
                  },
                  "& .MuiInputLabel-root": {
                    display: "none",
                  },
                  "& .MuiOutlinedInput-root": {
                    height: "32px",
                    "& fieldset": {
                      borderColor: "#E1E4E9",
                      borderRadius: "8px",
                    },
                    "&:hover fieldset": {
                      borderColor: "#E1E4E9",
                    },
                    "&.Mui-focused fieldset": {
                      borderColor: "#E1E4E9",
                    },
                    "& input": {
                      height: "32px",
                    },
                  },
                }}
              />
            </Box>

            {/* Row 3: Caller Name and Caller Number */}
            <Box sx={{ display: "flex", gap: 1.5 }}>
              <Box sx={{ flexGrow: 298, flexBasis: 0, minWidth: 0 }}>
                <Typography
                  variant="body2"
                  sx={{
                    color: "var(--Grey-500, #697282)",
                    fontFamily: "Roboto",
                    fontSize: "12px",
                    fontStyle: "normal",
                    fontWeight: 400,
                    lineHeight: "140%",
                    letterSpacing: "0.17px",
                    fontFeatureSettings: "'liga' off, 'clig' off",
                    marginBottom: "6px",
                  }}
                >
                  Caller Name
                </Typography>
                <TextField
                  placeholder="Caller name"
                  variant="outlined"
                  size="small"
                  value={formData.reporterName || (isUnknown ? "" : displayTitle)}
                  onChange={(e) =>
                    setFormData((prev) => ({
                      ...prev,
                      reporterName: e.target.value,
                    }))
                  }
                  onBlur={(e) =>
                    handleTextFieldBlur("reporterName", e.target.value)
                  }
                  InputProps={{
                    style: {
                      fontSize: "14px",
                      display: "flex",
                      alignItems: "center",
                    },
                  }}
                  InputLabelProps={{ shrink: false }}
                  sx={{
                    width: "100%",
                    "& input::placeholder": {
                      color: "#969EA8",
                      opacity: 1,
                    },
                    "& .MuiInputLabel-root": {
                      display: "none",
                    },
                    "& .MuiOutlinedInput-root": {
                      height: "32px",
                      "& fieldset": {
                        borderColor: "#E1E4E9",
                        borderRadius: "8px",
                      },
                      "&:hover fieldset": {
                        borderColor: "#E1E4E9",
                      },
                      "&.Mui-focused fieldset": {
                        borderColor: "#E1E4E9",
                      },
                      "& input": {
                        height: "32px",
                      },
                    },
                  }}
                />
              </Box>
              <Box sx={{ flexGrow: 178, flexBasis: 0, minWidth: 0 }}>
                <Typography
                  variant="body2"
                  sx={{
                    color: "var(--Grey-500, #697282)",
                    fontFamily: "Roboto",
                    fontSize: "12px",
                    fontStyle: "normal",
                    fontWeight: 400,
                    lineHeight: "140%",
                    letterSpacing: "0.17px",
                    fontFeatureSettings: "'liga' off, 'clig' off",
                    marginBottom: "6px",
                  }}
                >
                  Caller Number
                </Typography>
                <TextField
                  placeholder="Caller Number"
                  variant="outlined"
                  size="small"
                  value={
                    formData.contactNo
                      ? formatPhoneNumberForDisplay(formData.contactNo)
                      : ""
                  }
                  onChange={(e) =>
                    setFormData((prev) => ({ ...prev, contactNo: e.target.value }))
                  }
                  onBlur={(e) => handleTextFieldBlur("contactNo", e.target.value)}
                  InputProps={{
                    style: {
                      fontSize: "14px",
                      display: "flex",
                      alignItems: "center",
                    },
                  }}
                  InputLabelProps={{ shrink: false }}
                  sx={{
                    width: "100%",
                    "& input::placeholder": {
                      color: "#969EA8",
                      opacity: 1,
                    },
                    "& .MuiInputLabel-root": {
                      display: "none",
                    },
                    "& .MuiOutlinedInput-root": {
                      height: "32px",
                      "& fieldset": {
                        borderColor: "#E1E4E9",
                        borderRadius: "8px",
                      },
                      "&:hover fieldset": {
                        borderColor: "#E1E4E9",
                      },
                      "&.Mui-focused fieldset": {
                        borderColor: "#E1E4E9",
                      },
                      "& input": {
                        height: "32px",
                      },
                    },
                  }}
                />
              </Box>
            </Box>

            {/* Row 4: Type and Priority */}
            <Box sx={{ display: "flex", gap: 1.5 }}>
              <Box sx={{ flexGrow: 298, flexBasis: 0, minWidth: 0 }}>
                <Typography
                  variant="body2"
                  sx={{
                    color: "var(--Grey-500, #697282)",
                    fontFamily: "Roboto",
                    fontSize: "12px",
                    fontStyle: "normal",
                    fontWeight: 400,
                    lineHeight: "140%",
                    letterSpacing: "0.17px",
                    fontFeatureSettings: "'liga' off, 'clig' off",
                    marginBottom: "6px",
                  }}
                >
                  Type
                </Typography>
                <TextField
                  select
                  variant="outlined"
                  size="small"
                  value={formData.incidentType}
                  onChange={(e) => {
                    const newType = e.target.value;
                    setFormData((prev) => ({ ...prev, incidentType: newType }));
                    if (newType) {
                      addSituationUpdate(
                        {
                          id: selectedSituationId,
                          update: createUpdateEntry(
                            `Changed incident type to ${getIncidentLabel(newType)}`,
                            "info change",
                            "dispatch",
                            "Dispatch"
                          ),
                        } as AddSituationUpdateRequest,
                        {
                          onSuccess: () => {
                            console.log(
                              "NewType change logged successfully:",
                              newType
                            );
                          },
                        }
                      );
                    }
                  }}
                  InputLabelProps={{ shrink: false }}
                  sx={{
                    width: "100%",
                    "& .MuiInputLabel-root": {
                      display: "none",
                    },
                    "& .MuiOutlinedInput-root": {
                      height: "32px",
                      fontSize: "14px",
                      "& fieldset": {
                        borderColor: "#E1E4E9",
                        borderRadius: "8px",
                      },
                      "&:hover fieldset": {
                        borderColor: "#E1E4E9",
                      },
                      "&.Mui-focused fieldset": {
                        borderColor: "#E1E4E9",
                      },
                    },
                  }}
                >
                  {incidentTypes.map((option) => (
                    <MenuItem key={option.value} value={option.value} sx={{ fontSize: "14px" }}>
                      {option.label}
                    </MenuItem>
                  ))}
                </TextField>
              </Box>
              <Box sx={{ flexGrow: 178, flexBasis: 0, minWidth: 0 }}>
                <Typography
                  variant="body2"
                  sx={{
                    color: "var(--Grey-500, #697282)",
                    fontFamily: "Roboto",
                    fontSize: "12px",
                    fontStyle: "normal",
                    fontWeight: 400,
                    lineHeight: "140%",
                    letterSpacing: "0.17px",
                    fontFeatureSettings: "'liga' off, 'clig' off",
                    marginBottom: "6px",
                  }}
                >
                  Priority
                </Typography>
                <TextField
                  select
                  variant="outlined"
                  size="small"
                  value={formData.priority}
                  onChange={(e) => {
                    const newPriority = e.target.value;
                    setFormData((prev) => ({ ...prev, priority: newPriority }));
                    if (newPriority) {
                      addSituationUpdate(
                        {
                          id: selectedSituationId,
                          update: createUpdateEntry(
                            `Changed priority to ${newPriority}`,
                            "info change",
                            "dispatch",
                            "Dispatch"
                          ),
                        } as AddSituationUpdateRequest,
                        {
                          onSuccess: () => {
                            console.log(
                              "NewPriority change logged successfully:",
                              newPriority
                            );
                          },
                        }
                      );
                    }
                  }}
                  InputLabelProps={{ shrink: false }}
                  sx={{
                    width: "100%",
                    "& .MuiInputLabel-root": {
                      display: "none",
                    },
                    "& .MuiOutlinedInput-root": {
                      height: "32px",
                      fontSize: "14px",
                      "& fieldset": {
                        borderColor: "#E1E4E9",
                        borderRadius: "8px",
                      },
                      "&:hover fieldset": {
                        borderColor: "#E1E4E9",
                      },
                      "&.Mui-focused fieldset": {
                        borderColor: "#E1E4E9",
                      },
                    },
                  }}
                >
                  {priorityLevels.map((option) => (
                    <MenuItem key={option.value} value={option.value} sx={{ fontSize: "14px" }}>
                      {option.label}
                    </MenuItem>
                  ))}
                </TextField>
              </Box>
            </Box>

            {/* Row 5: Incident Description */}
            <Box>
              <Typography
                variant="body2"
                sx={{
                  color: "var(--Grey-500, #697282)",
                  fontFamily: "Roboto",
                  fontSize: "12px",
                  fontStyle: "normal",
                  fontWeight: 400,
                  lineHeight: "140%",
                  letterSpacing: "0.17px",
                  fontFeatureSettings: "'liga' off, 'clig' off",
                  marginBottom: "6px",
                }}
              >
                Incident Description
              </Typography>
              <TextField
                placeholder="Incident Description"
                variant="outlined"
                fullWidth
                multiline
                rows={2}
                value={formData.incidentDescription || ""}
                onChange={(e) =>
                  setFormData((prev) => ({
                    ...prev,
                    incidentDescription: e.target.value,
                  }))
                }
                onBlur={(e) =>
                  handleTextFieldBlur("incidentDescription", e.target.value)
                }
                InputProps={{
                  style: {
                    fontSize: "14px",
                    display: "flex",
                    alignItems: "center",
                  },
                }}
                InputLabelProps={{ shrink: false }}
                sx={{
                  "& textarea::placeholder": {
                    color: "#969EA8",
                    opacity: 1,
                  },
                  "& .MuiInputLabel-root": {
                    display: "none",
                  },
                  "& .MuiOutlinedInput-root": {
                    height: "56px",
                    "& fieldset": {
                      borderColor: "#E1E4E9",
                      borderRadius: "8px",
                    },
                    "&:hover fieldset": {
                      borderColor: "#E1E4E9",
                    },
                    "&.Mui-focused fieldset": {
                      borderColor: "#E1E4E9",
                    },
                    "& textarea": {
                      height: "56px",
                    },
                  },
                }}
              />
            </Box>
          </Stack>
        </Box>

        {/* 2. Assigned Units Section */}
        <Box
          sx={{
            p: 2,
            borderBottom: "1px solid #e0e0e0",
            overflowY: "auto",
            flexShrink: 0,
            position: "relative",
          }}
        >
          <AssignedUnitsSection
            orders={ordersForSituation?.orders || []}
            allResponders={responders}
            selectedSituation={situation}
            onRemoveOfficer={handleRemoveOfficer}
            onChangeStatus={handleChangeStatus}
            onUpdateTimestamp={handleUpdateTimestamp}
            onDispatch={handleDispatch}
            isDispatching={isDispatching}
            optimisticDispatchedUnits={Array.from(optimisticDispatchedUnits)}
          />
        </Box>

        {/* 3. Event Log Section (Stretches from below incident details to above quick note) */}
        <Box
          sx={{
            flex: 1,
            backgroundColor: "#F8FAFC",
            display: "flex",
            flexDirection: "column",
            borderTop: "1px solid #e0e0e0",
            minHeight: 0, // Important for flex child to shrink
          }}
        >
          {/* Event Log Header */}
          <Box
            sx={{
              pt: 1.5,
              px: 2,
              pb: 0,
              backgroundColor: "#F8FAFC",
              flexShrink: 0,
            }}
          >
            <Box sx={{ display: "flex" }}>
              <Typography
                fontSize={12}
                fontWeight={600}
                sx={{ color: "#2B2C2C" }}
              >
                EVENT UPDATES
              </Typography>
            </Box>
          </Box>

          {/* Event Log Content - scrollable area with fixed height */}
          <Box
            ref={eventLogRef}
            sx={{
              flex: 1,
              backgroundColor: "#F8FAFC",
              overflowY: "auto",
              display: "flex",
              flexDirection: "column",
              minHeight: 0, // Important for flex child to shrink
            }}
          >
            {/* Spacer to push content to bottom */}
            <Box sx={{ flex: 1 }} />

            {/* Event Log Messages at bottom */}
            <Box sx={{ p: 1, pl: 2, pr: 2 }}>
              {incidentUpdates
                .slice()
                .sort((a, b) => {
                  const aTime = a.occurredAt
                    ? new Date(a.occurredAt).getTime()
                    : 0;
                  const bTime = b.occurredAt
                    ? new Date(b.occurredAt).getTime()
                    : 0;
                  return aTime - bTime; // Oldest first, newest at bottom
                })
                .map((update, index, sortedUpdates) => {
                  const isDispatcher = update.author === "dispatch";
                  const previousUpdate = index > 0 ? sortedUpdates[index - 1] : null;
                  const isSameSource = previousUpdate && previousUpdate.author === update.author;

                  return (
                    <Box
                      key={index}
                      sx={{
                        display: "flex",
                        flexWrap: "nowrap",
                        alignItems: "center",
                        gap: 1.5, // 12px gap (1.5 * 8px = 12px)
                        mt: index === 0 ? 0 : (!isSameSource ? 2 : 1), // 16px for new source, 8px for same source
                        height: "20px",
                        minHeight: "20px",
                      }}
                    >
                      <Box
                        sx={{
                          width: 48,
                          minWidth: 48,
                          maxWidth: 48,
                          display: "flex",
                          alignItems: "center",
                          justifyContent: "flex-start",
                        }}
                      >
                        {update.author && update.author !== "" && !isSameSource ? (
                          <>
                            {isDispatcher ? (
                              // Dispatcher updates - keep current logic
                              <>
                                <HeadsetMicIcon sx={{ color: "#697282", fontSize: 14, mr: 1 }} />
                                <Typography
                                  color="#7C7D7E"
                                  fontWeight={600}
                                  fontSize={14}
                                  lineHeight="20px"
                                  sx={{ whiteSpace: "nowrap" }}
                                >
                                  Dis.
                                </Typography>
                              </>
                            ) : (update.eventType === "order created" || update.eventType === "status change") ? (
                              // Officer updates with order created or status change - use Sys icon
                              <>
                                <PublishedWithChangesIcon sx={{ color: "#697282", fontSize: 14, mr: 1 }} />
                                <Typography
                                  color="#7C7D7E"
                                  fontWeight={600}
                                  fontSize={14}
                                  lineHeight="20px"
                                  sx={{ whiteSpace: "nowrap" }}
                                >
                                  Sys.
                                </Typography>
                              </>
                            ) : (
                              // Officer updates with other event types - use officer badge
                              <>
                                <OfficerShieldIcon style={{ fontSize: 14, marginRight: 8, verticalAlign: "middle" }} />
                                <Typography
                                  color="#0060FF"
                                  fontWeight={600}
                                  fontSize={14}
                                  lineHeight="20px"
                                  sx={{ whiteSpace: "nowrap" }}
                                >
                                  {update.author?.replace(/[^0-9]/g, "")?.slice(0, 3)}
                                </Typography>
                              </>
                            )}
                          </>
                        ) : (!update.author || update.author === "") && !isSameSource ? (
                          // Authorless updates - keep current fallback
                          <>
                            <PublishedWithChangesIcon sx={{ color: "#697282", fontSize: 14, mr: 1 }} />
                            <Typography
                              color="#7C7D7E"
                              fontWeight={600}
                              fontSize={14}
                              lineHeight="20px"
                              sx={{ whiteSpace: "nowrap" }}
                            >
                              Sys.
                            </Typography>
                          </>
                        ) : (
                          // Always render an empty box for alignment if no icon/label
                          <Box sx={{ width: 32, height: 24 }} />
                        )}
                      </Box>

                      <Box sx={{ width: "75%", wordWrap: "break-word" }}>
                        {update.eventType === "order created" ? (
                          <Box sx={{ display: "flex", flexDirection: "row", alignItems: "center", gap: 1.5, width: "100%" }}>
                            <Typography
                              sx={{
                                color: "#697282",
                                fontFamily: "Roboto",
                                fontSize: "14px",
                                fontStyle: "italic",
                                fontWeight: 400,
                                lineHeight: "20px",
                                letterSpacing: "0.17px",
                                fontFeatureSettings: "'liga' off, 'clig' off",
                                overflow: "hidden",
                                textOverflow: "ellipsis",
                                whiteSpace: "nowrap",
                                flex: "0 1 auto", // Don't grow, allow shrinking, auto basis
                                minWidth: 0, // Allow shrinking
                              }}
                            >
                              {update.author === "dispatch"
                                ? (() => {
                                  // For dispatch events, extract unit numbers from description
                                  const unitMatch = update.description.match(/\d+/g);
                                  if (unitMatch && unitMatch.length > 0) {
                                    return unitMatch.join(", ");
                                  }
                                  return "Units";
                                })()
                                : update.author && update.author !== ""
                                  ? responders.find(r => r.id === update.author)?.name || "Unknown Unit"
                                  : "Unknown Unit"
                              }
                            </Typography>
                            <Chip
                              label={"Dispatched"}
                              sx={{
                                ...getChipStyles("ASSET_STATUS_ON_BREAK"),
                                display: "flex",
                                height: "20px",
                                minHeight: "20px",
                                padding: "4px 6px",
                                justifyContent: "center",
                                alignItems: "center",
                                gap: "4px",
                                fontSize: 12,
                                "& .MuiChip-label": { padding: "0" },
                                flexShrink: 0, // Prevent chip from shrinking
                              }}
                            />
                          </Box>
                        ) : update.eventType === "status change" ? (
                          <Box sx={{ display: "flex", flexDirection: "row", alignItems: "center", gap: 1.5, width: "100%" }}>
                            <Typography
                              sx={{
                                color: "#697282",
                                fontFamily: "Roboto",
                                fontSize: "14px",
                                fontStyle: "italic",
                                fontWeight: 400,
                                lineHeight: "20px",
                                letterSpacing: "0.17px",
                                fontFeatureSettings: "'liga' off, 'clig' off",
                                overflow: "hidden",
                                textOverflow: "ellipsis",
                                whiteSpace: "nowrap",
                                flex: "0 1 auto", // Don't grow, allow shrinking, auto basis
                                minWidth: 0, // Allow shrinking
                              }}
                            >
                              {update.author && update.author !== ""
                                ? responders.find(r => r.id === update.author)?.name || "Unknown Unit"
                                : "Unknown Unit"
                              }
                            </Typography>
                            <Chip
                              label={toTitleCase(update.description)}
                              sx={{
                                ...getChipStyles("ASSET_STATUS_ON_BREAK"),
                                display: "flex",
                                height: "20px",
                                minHeight: "20px",
                                padding: "4px 6px",
                                justifyContent: "center",
                                alignItems: "center",
                                gap: "4px",
                                fontSize: 12,
                                "& .MuiChip-label": { padding: "0" },
                                flexShrink: 0, // Prevent chip from shrinking
                              }}
                            />
                          </Box>
                        ) : (
                          <Typography
                            color={
                              update.eventType === "info change"
                                ? "#6B7280"
                                : "#000000"
                            }
                            fontSize={14}
                            fontWeight={400}
                            lineHeight="normal"
                            sx={{
                              fontStyle:
                                update.eventType === "info change"
                                  ? "italic"
                                  : "normal",
                            }}
                          >
                            {update.description}
                          </Typography>
                        )}
                      </Box>

                      <Box sx={{ width: "15%", textAlign: "right" }}>
                        <Typography
                          color={"#374151"}
                          fontSize={14}
                          fontWeight={400}
                          lineHeight={"normal"}
                        >
                          {new Date(update.occurredAt).toLocaleTimeString(
                            "en-US",
                            {
                              hour12: false,
                              hour: "2-digit",
                              minute: "2-digit",
                            }
                          )}
                        </Typography>
                      </Box>
                    </Box>
                  );
                })}
            </Box>
          </Box>
        </Box>

        {/* 4. Quick Note Section (Fixed at bottom) */}
        <Box
          sx={{
            p: 2,
            borderTop: "none",
            backgroundColor: "#F9FAFB",
            flexShrink: 0,
          }}
        >
          <Box sx={{ position: "relative" }}>
            <TextField
              multiline
              fullWidth
              rows={1}
              value={message}
              onChange={(e) => setMessage(e.target.value)}
              onKeyDown={handleKeyDown}
              placeholder="Add an update..."
              variant="outlined"
              size="small"
              InputProps={{
                style: {
                  fontSize: "14px",
                  display: "flex",
                  alignItems: "center",
                },
              }}
              InputLabelProps={{
                sx: {
                  fontSize: "14px",
                  color: "#969EA8",
                  "&:not(.MuiInputLabel-shrink)": {
                    transform: "translate(12px, 6px) scale(1) !important",
                  },
                },
              }}
              sx={{
                "& textarea::placeholder": { color: "#969EA8", opacity: 1 },
                "& .MuiOutlinedInput-root": {
                  backgroundColor: "#ffffff",
                  borderRadius: "8px",
                  height: "36px", // Shorter height
                  padding: "5px 10px", // 10px vertical spacing
                  "& fieldset": {
                    borderColor: "#E1E4E9",
                    borderRadius: "8px",
                  },
                  "&:hover fieldset": {
                    borderColor: "#E1E4E9",
                  },
                  "&.Mui-focused fieldset": {
                    borderColor: "#E1E4E9",
                  },
                  "& textarea": {
                    height: "26px", // Adjusted for shorter box
                    padding: "0",
                    resize: "none",
                  },
                },
              }}
            />

            <Button
              variant="contained"
              sx={{
                position: "absolute",
                bottom: 6, // Adjusted for shorter box
                right: 8,
                minWidth: "auto",
                width: 24,
                height: 24,
                borderRadius: "50%",
                padding: 0,
                backgroundColor: "#EAF2FF",
                boxShadow: "none",
                transition: "all 0.2s ease-in-out",
                "&:hover": {
                  backgroundColor: "#D1E7FF",
                  transform: "scale(1.1)",
                  boxShadow: "0 2px 4px rgba(104, 161, 255, 0.2)",
                },
              }}
              onClick={handleSend}
            >
              <ArrowForwardIcon
                fontSize="small"
                sx={{ color: "#68A1FF", fontSize: 16 }}
              />
            </Button>
          </Box>
        </Box>
      </Box>
      <ResolveModal
        open={openResolveModal}
        onClose={handleCloseResolveModal}
        onNext={handleNextResolve}
        isAllAssistMemberOrdersCompleted={
          isAllAssistMemberOrdersCompleted || false
        }
        hasExistingCase={hasExistingCase}
        availableResponders={respondersWithOrders}
        ordersForSituation={ordersForSituation?.orders || []}
        incidentId={situation?.id}
      />
    </Box>
  );
};
