"use client";

import { Header, SaveStatus } from "@/design-system/components/Header";
import { useRouter } from "next/navigation";
import { Report } from "proto/hero/reports/v2/reports_pb";
import { useState } from "react";
import { useBreadcrumbHeader } from "../../../hooks/useBreadcrumbHeader";

interface ReportingHeaderProps {
  report: Report | undefined;
  saveStatuses?: SaveStatus[];
}

export default function NibrsReviewHeader({
  report,
  saveStatuses = [],
}: ReportingHeaderProps) {
  const router = useRouter();
  const effectiveReportDate = report?.createdAt
    ? new Date(report.createdAt)
    : new Date();
  const reportedDate = effectiveReportDate
    .toISOString()
    .split("T")[0]
    .replace(/-/g, "/");

  const [isSubmitting, setIsSubmitting] = useState(false);
  const reportId = report?.id?.replace(/[^0-9]/g, "").slice(0, 3) || "";

  const { breadcrumbs } = useBreadcrumbHeader({
    id: `nibrs-review-${report?.id}`,
    label: `NIBRS Review ${reportId}`,
    path: `/nibrs-review?reportId=${report?.id}`,
  });

  const handleSubmit = () => {};

  const handleReportIdClick = () => {
    if (report?.id) {
      router.push(`/reports?reportId=${report.id}`);
    }
  };

  return (
    <>
      <Header
        breadcrumbs={breadcrumbs}
        title={`NIBRS Review ${reportId}`}
        metadata={[
          {
            label: "Report Created",
            value: report?.createdAt ? reportedDate : "N/A",
          },
          {
            label: "Report ID",
            value: report?.id || "N/A",
            onClick: report?.id ? handleReportIdClick : undefined,
          },
        ]}
        saveStatuses={saveStatuses}
        actions={[
          {
            label: "Finish Review",
            onClick: handleSubmit,
            isLoading: isSubmitting,
          },
        ]}
      />
    </>
  );
}
