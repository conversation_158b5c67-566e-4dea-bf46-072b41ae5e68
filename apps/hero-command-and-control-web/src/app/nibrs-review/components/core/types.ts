// Panel types enum
export enum PanelType {
  PERSON = "PERSON",
  VEHICLE = "VEHICLE",
  PROPERTY = "PROPERTY",
  ORGANIZATION = "ORGANIZATION",
}

// Helper function to get panel title based on type
export const getPanelTitle = (
  type: PanelType | null,
  offenseContext?: {
    relationType?:
      | "victim"
      | "offender"
      | "witness"
      | "suspect"
      | "involved_party";
  } | null,
  isEditing?: boolean
): string => {
  if (!type) return "";

  // In edit mode, don't show offense context
  if (isEditing) {
    switch (type) {
      case PanelType.PERSON:
        return "Edit Person";
      case PanelType.VEHICLE:
        return "Edit Vehicle";
      case PanelType.PROPERTY:
        return "Edit Property";
      case PanelType.ORGANIZATION:
        return "Edit Organization";
      default:
        return "";
    }
  }

  switch (type) {
    case PanelType.PERSON:
      if (offenseContext?.relationType) {
        switch (offenseContext.relationType) {
          case "victim":
            return "Add Victim";
          case "offender":
            return "Add Offender";
          case "witness":
            return "Add Witness";
          case "suspect":
            return "Add Suspect";
          case "involved_party":
            return "Add Involved Party";
          default:
            return "Add Person";
        }
      }
      return "Add Person";
    case PanelType.VEHICLE:
      if (offenseContext) {
        return "Add Vehicle to Offense";
      }
      return "Add Vehicle";
    case PanelType.PROPERTY:
      if (offenseContext) {
        return "Add Property to Offense";
      }
      return "Add Property";
    case PanelType.ORGANIZATION:
      if (offenseContext) {
        return "Add Organization to Offense";
      }
      return "Add Organization";
    default:
      return "";
  }
};

// Interface for tracking save status of components
export interface SaveStatus {
  isSaving: boolean;
  hasUnsavedChanges: boolean;
  source: string;
}

// Navigation item interface
export interface NavItem {
  id: string;
  label: string;
}

// Interface for section IDs
export interface SectionIds {
  peopleListSectionId: string | null;
  vehicleListSectionId: string | null;
  propertyListSectionId: string | null;
  organizationListSectionId: string | null;
}

// Interface for notification state
export interface NotificationState {
  open: boolean;
  message: string;
  severity: "success" | "error";
}

// Interface for entities state
export interface EntitiesState {
  people: any[];
  vehicles: any[];
  properties: any[];
  organizations: any[];
}
