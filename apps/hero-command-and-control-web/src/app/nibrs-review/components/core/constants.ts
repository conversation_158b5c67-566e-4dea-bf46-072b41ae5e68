// Section type constants
export const SECTION_TYPES = {
  ENTITY_LIST_PEOPLE: "SECTION_TYPE_ENTITY_LIST_PEOPLE",
  ENTITY_LIST_VEHICLE: "SECTION_TYPE_ENTITY_LIST_VEHICLE",
  ENTITY_LIST_PROPERTIES: "SECTION_TYPE_ENTITY_LIST_PROPERTIES",
  ENTITY_LIST_ORGANIZATIONS: "SECTION_TYPE_ENTITY_LIST_ORGANIZATIONS",
};

// Entity type constants from the protobuf definition
export const ENTITY_TYPES = {
  PERSON: "ENTITY_TYPE_PERSON",
  VEHICLE: "ENTITY_TYPE_VEHICLE",
  PROPERTY: "ENTITY_TYPE_PROPERTY",
  ORGANIZATION: "ENTITY_TYPE_ORGANIZATION",
};

// Scroll constants
export const SCROLL_THROTTLE_MS = 80;
export const SCROLL_OFFSET_TOP = 120;
export const PANEL_CLOSE_TIMEOUT_MS = 300;

// Form action constants
export const FORM_RESET_TIMEOUT_MS = 50;
