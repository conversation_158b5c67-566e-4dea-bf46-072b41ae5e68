import { create } from "@bufbuild/protobuf";
import { GetLatestEntityRequest } from "proto/hero/entity/v1/entity_pb";
import {
  CreateRelationRequestSchema,
  DeleteRelationRequestSchema,
  ObjectReferenceSchema,
  RelationSchema,
  UpdateRelationRequestSchema,
} from "proto/hero/reports/v2/reports_pb";
import { getLatestEntity as getLatestEntityEndpoint } from "../../../../apis/services/workflow/entity/endpoints";
import { updateEntityListSection } from "../utils/sectionUtils";
import {
  entitiesToPersonData,
  entitiesToPropertyData,
  entitiesToVehicleData,
} from "../utils/utils";

interface RelationHandlersProps {
  // State
  reportId: string | null;
  report: any;
  people: any[];
  vehicles: any[];
  properties: any[];
  organizations: any[];
  setPeople: (people: any[]) => void;
  setVehicles: (vehicles: any[]) => void;
  setProperties: (properties: any[]) => void;
  setOrganizations: (organizations: any[]) => void;
  setNotification: (notification: any) => void;
  associatedCases: any[];

  // Section IDs
  peopleListSectionId: string | null;
  vehicleListSectionId: string | null;
  propertyListSectionId: string | null;
  organizationListSectionId: string | null;
  reportSections: any;

  // API mutations
  createRelationMutation: any;
  deleteRelationMutation: any;
  updateRelationMutation: any;
  updateReportSectionMutation: any;
}

// Helper function to get the next victim/offender number for case-level numbering
const getNextVictimOffenderNumber = (
  relations: any[],
  entityId: string,
  relationType:
    | "victim"
    | "offender"
    | "witness"
    | "suspect"
    | "involved_party",
  associatedCases?: any[]
): string => {
  const existingRelation = relations.find((rel) => {
    const relationTypeMapping = {
      victim: "RELATION_TYPE_OFFENSE_VICTIM",
      offender: "RELATION_TYPE_OFFENSE_OFFENDER",
      witness: "RELATION_TYPE_OFFENSE_WITNESS",
      suspect: "RELATION_TYPE_OFFENSE_SUSPECT",
      involved_party: "RELATION_TYPE_OFFENSE_INVOLVED_PARTY",
    };

    const targetRelationType = relationTypeMapping[relationType];
    const isRelevantRelation = rel.relationType === targetRelationType;

    if (!isRelevantRelation) return false;

    const entityInvolved =
      (rel.objectA?.objectType === "entity" &&
        rel.objectA?.globalId === entityId) ||
      (rel.objectB?.objectType === "entity" &&
        rel.objectB?.globalId === entityId);

    return entityInvolved && rel.metadata?.victimOffenderNumber;
  });

  if (existingRelation) {
    return existingRelation.metadata.victimOffenderNumber;
  }

  // If not found in current report relations, check case entities for existing numbers
  // Look through associated case entities to see if this entity already has a number
  if (associatedCases && associatedCases.length > 0) {
    const firstCase = associatedCases[0];
    if (firstCase?.entityRefs) {
      const existingEntityRef = firstCase.entityRefs.find(
        (ref: any) => ref.id === entityId
      );
      if (existingEntityRef?.metadata?.victimOffenderNumber) {
        return existingEntityRef.metadata.victimOffenderNumber;
      }
    }
  }

  // Find the highest existing number for this type
  const prefixMapping = {
    victim: "V",
    offender: "O",
    witness: "W",
    suspect: "S",
    involved_party: "I",
  };

  const prefix = prefixMapping[relationType];

  // Get numbers from current report relations
  const relationTypeMapping = {
    victim: "RELATION_TYPE_OFFENSE_VICTIM",
    offender: "RELATION_TYPE_OFFENSE_OFFENDER",
    witness: "RELATION_TYPE_OFFENSE_WITNESS",
    suspect: "RELATION_TYPE_OFFENSE_SUSPECT",
    involved_party: "RELATION_TYPE_OFFENSE_INVOLVED_PARTY",
  };

  const targetRelationType = relationTypeMapping[relationType];

  const reportNumbers = relations
    .filter((rel) => {
      const isRelevantRelation = rel.relationType === targetRelationType;
      return (
        isRelevantRelation &&
        rel.metadata?.victimOffenderNumber?.startsWith(prefix)
      );
    })
    .map((rel) => {
      const numberStr = rel.metadata.victimOffenderNumber.slice(1); // Remove prefix
      return parseInt(numberStr, 10);
    })
    .filter((num) => !isNaN(num));

  // Get numbers from case entities (if available)
  const caseNumbers: number[] = [];
  if (associatedCases && associatedCases.length > 0) {
    const firstCase = associatedCases[0];
    if (firstCase?.entityRefs) {
      firstCase.entityRefs.forEach((ref: any) => {
        if (ref.metadata?.victimOffenderNumber?.startsWith(prefix)) {
          const numberStr = ref.metadata.victimOffenderNumber.slice(1);
          const num = parseInt(numberStr, 10);
          if (!isNaN(num)) {
            caseNumbers.push(num);
          }
        }
      });
    }
  }

  const allNumbers = [...reportNumbers, ...caseNumbers];
  const highestNumber = allNumbers.length > 0 ? Math.max(...allNumbers) : 0;
  return `${prefix}${highestNumber + 1}`;
};

export const createRelationHandlers = ({
  reportId,
  report,
  people,
  vehicles,
  properties,
  organizations,
  setPeople,
  setVehicles,
  setProperties,
  setOrganizations,
  setNotification,
  associatedCases,
  peopleListSectionId,
  vehicleListSectionId,
  propertyListSectionId,
  organizationListSectionId,
  reportSections,
  createRelationMutation,
  deleteRelationMutation,
  updateRelationMutation,
  updateReportSectionMutation,
}: RelationHandlersProps) => {
  const handleAddPersonToOffense = async (
    personId: string,
    offenseId: string,
    relationType:
      | "victim"
      | "offender"
      | "witness"
      | "suspect"
      | "involved_party",
    entity?: any
  ) => {
    if (!reportId) return;

    // Create the relation type string
    let relationTypeString: string;
    switch (relationType) {
      case "victim":
        relationTypeString = "RELATION_TYPE_OFFENSE_VICTIM";
        break;
      case "offender":
        relationTypeString = "RELATION_TYPE_OFFENSE_OFFENDER";
        break;
      case "witness":
        relationTypeString = "RELATION_TYPE_OFFENSE_WITNESS";
        break;
      case "suspect":
        relationTypeString = "RELATION_TYPE_OFFENSE_SUSPECT";
        break;
      case "involved_party":
        relationTypeString = "RELATION_TYPE_OFFENSE_INVOLVED_PARTY";
        break;
      default:
        return;
    }

    let personEntity = entity;

    if (!personEntity) {
      // Try to find in local state first
      personEntity = people.find((p) => p.id === personId);
    }

    // If still not found, fetch from backend
    if (!personEntity) {
      try {
        personEntity = await getLatestEntityEndpoint({
          id: personId,
        } as GetLatestEntityRequest);

        if (personEntity) {
          // Add to local state
          const updatedPeople = [...people, personEntity];
          setPeople(updatedPeople);

          // Update report section so the entity appears in People list
          if (peopleListSectionId) {
            updateEntityListSection(
              "People",
              personEntity,
              peopleListSectionId,
              reportId,
              reportSections,
              updateReportSectionMutation,
              { people, vehicles, properties, organizations },
              updatedPeople
            );
          }
        }
      } catch (error) {
        console.error("Error fetching person for relation:", error);
        return;
      }
    }

    const personData = personEntity
      ? entitiesToPersonData([personEntity])[0]
      : null;

    if (!personData) {
      console.error("Person data not found for relation creation");
      return;
    }

    // Get the numbered label for all person types
    const metadata: any = {};
    const numberedLabel = getNextVictimOffenderNumber(
      report?.relations || [],
      personId,
      relationType,
      associatedCases
    );
    metadata.victimOffenderNumber = numberedLabel;

    // Create object references
    const offenseObjectRef = create(ObjectReferenceSchema, {
      objectType: "offense",
      reportScopedId: offenseId,
      displayName: `Offense ${offenseId}`,
    });

    const personObjectRef = create(ObjectReferenceSchema, {
      objectType: "entity",
      globalId: personId,
      displayName: personData.name,
    });

    // Create relation with metadata
    const relation = create(RelationSchema, {
      reportId: reportId,
      objectA: offenseObjectRef,
      objectB: personObjectRef,
      relationType: relationTypeString,
      description: `${personData.name} is a ${relationType} in this offense`,
      metadata: metadata,
    });

    // Create the request
    const createRelationRequest = create(CreateRelationRequestSchema, {
      reportId: reportId,
      relation: relation,
    });

    createRelationMutation.mutate(createRelationRequest);
  };

  const handleAddVehicleToOffense = async (
    vehicleId: string,
    offenseId: string,
    entity?: any
  ) => {
    console.log("handleAddVehicleToOffense called:", {
      vehicleId,
      offenseId,
      reportId,
    });
    if (!reportId) return;

    let vehicleEntity = entity;
    if (!vehicleEntity) {
      vehicleEntity = vehicles.find((v) => v.id === vehicleId);
    }

    if (!vehicleEntity) {
      try {
        vehicleEntity = await getLatestEntityEndpoint({
          id: vehicleId,
        } as GetLatestEntityRequest);
        if (vehicleEntity) {
          const updatedVehicles = [...vehicles, vehicleEntity];
          setVehicles(updatedVehicles);

          if (vehicleListSectionId) {
            updateEntityListSection(
              "Vehicles",
              vehicleEntity,
              vehicleListSectionId,
              reportId,
              reportSections,
              updateReportSectionMutation,
              { people, vehicles, properties, organizations },
              updatedVehicles
            );
          }
        }
      } catch (error) {
        console.error("Error fetching vehicle for relation:", error);
        return;
      }
    }

    const vehicleData = vehicleEntity
      ? entitiesToVehicleData([vehicleEntity])[0]
      : null;

    if (!vehicleData) {
      console.error("Vehicle data not found for relation creation");
      return;
    }

    console.log("Creating vehicle relation with data:", vehicleData);

    // Create object references
    const offenseObjectRef = create(ObjectReferenceSchema, {
      objectType: "offense",
      reportScopedId: offenseId,
      displayName: `Offense ${offenseId}`,
    });

    const vehicleObjectRef = create(ObjectReferenceSchema, {
      objectType: "entity",
      globalId: vehicleId,
      displayName: `${vehicleData.make} ${vehicleData.model}`,
    });

    // Create relation
    const relation = create(RelationSchema, {
      reportId: reportId,
      objectA: offenseObjectRef,
      objectB: vehicleObjectRef,
      relationType: "RELATION_TYPE_OFFENSE_VEHICLE",
      description: `${vehicleData.make} ${vehicleData.model} is involved in this offense`,
    });

    // Create the request
    const createRelationRequest = create(CreateRelationRequestSchema, {
      reportId: reportId,
      relation: relation,
    });

    console.log("Submitting vehicle relation request:", createRelationRequest);
    createRelationMutation.mutate(createRelationRequest);
  };

  const handleAddPropertyToOffense = async (
    propertyId: string,
    offenseId: string,
    entity?: any
  ) => {
    console.log("handleAddPropertyToOffense called:", {
      propertyId,
      offenseId,
      reportId,
    });
    if (!reportId) return;

    let propertyEntity = entity;
    if (!propertyEntity) {
      propertyEntity = properties.find((p) => p.id === propertyId);
    }

    if (!propertyEntity) {
      try {
        propertyEntity = await getLatestEntityEndpoint({
          id: propertyId,
        } as GetLatestEntityRequest);
        if (propertyEntity) {
          const updatedProperties = [...properties, propertyEntity];
          setProperties(updatedProperties);

          if (propertyListSectionId) {
            updateEntityListSection(
              "Properties",
              propertyEntity,
              propertyListSectionId,
              reportId,
              reportSections,
              updateReportSectionMutation,
              { people, vehicles, properties, organizations },
              updatedProperties
            );
          }
        }
      } catch (error) {
        console.error("Error fetching property for relation:", error);
        return;
      }
    }

    const propertyData = propertyEntity
      ? entitiesToPropertyData([propertyEntity])[0]
      : null;

    if (!propertyData) {
      console.error("Property data not found for relation creation");
      return;
    }

    console.log("Creating property relation with data:", propertyData);

    // Create object references
    const offenseObjectRef = create(ObjectReferenceSchema, {
      objectType: "offense",
      reportScopedId: offenseId,
      displayName: `Offense ${offenseId}`,
    });

    const propertyObjectRef = create(ObjectReferenceSchema, {
      objectType: "entity",
      globalId: propertyId,
      displayName: propertyData.category,
    });

    // Create relation
    const relation = create(RelationSchema, {
      reportId: reportId,
      objectA: offenseObjectRef,
      objectB: propertyObjectRef,
      relationType: "RELATION_TYPE_OFFENSE_PROPERTY",
      description: `${propertyData.category} is involved in this offense`,
    });

    // Create the request
    const createRelationRequest = create(CreateRelationRequestSchema, {
      reportId: reportId,
      relation: relation,
    });

    console.log("Submitting property relation request:", createRelationRequest);
    createRelationMutation.mutate(createRelationRequest);
  };

  const handleAddOrganizationToOffense = async (
    organizationId: string,
    offenseId: string,
    entity?: any
  ) => {
    if (!reportId) return;

    // Create the offense object reference
    const offenseObjectRef = create(ObjectReferenceSchema, {
      objectType: "offense",
      reportScopedId: offenseId,
      displayName: `Offense ${offenseId}`,
    });

    // Create the organization object reference
    const organizationObjectRef = create(ObjectReferenceSchema, {
      objectType: "entity",
      globalId: organizationId,
      displayName:
        entity?.data?.organizationInformationSection?.name || "Organization",
    });

    // Create the relation
    const relation = create(RelationSchema, {
      reportId: reportId,
      objectA: offenseObjectRef,
      objectB: organizationObjectRef,
      relationType: "RELATION_TYPE_OFFENSE_ORGANIZATION",
    });

    // Create the request
    const createRelationRequest = create(CreateRelationRequestSchema, {
      reportId: reportId,
      relation: relation,
    });

    console.log(
      "Submitting organization relation request:",
      createRelationRequest
    );
    createRelationMutation.mutate(createRelationRequest);
  };

  const handleCreateVictimEntityRelation = async (
    personId: string,
    victimQuestionsData: any,
    entity?: any
  ) => {
    if (!reportId) return;

    let personEntity = entity;

    if (!personEntity) {
      // Try to find in local state first
      personEntity = people.find((p) => p.id === personId);
    }

    // If still not found, fetch from backend
    if (!personEntity) {
      try {
        personEntity = await getLatestEntityEndpoint({
          id: personId,
        } as GetLatestEntityRequest);

        if (personEntity) {
          // Add to local state
          const updatedPeople = [...people, personEntity];
          setPeople(updatedPeople);

          // Update report section so the entity appears in People list
          if (peopleListSectionId) {
            updateEntityListSection(
              "People",
              personEntity,
              peopleListSectionId,
              reportId,
              reportSections,
              updateReportSectionMutation,
              { people, vehicles, properties, organizations },
              updatedPeople
            );
          }
        }
      } catch (error) {
        console.error("Error fetching person for victim relation:", error);
        return;
      }
    }

    const personData = personEntity
      ? entitiesToPersonData([personEntity])[0]
      : null;

    if (!personData) {
      console.error("Person data not found for victim relation creation");
      return;
    }

    // Create object references
    const reportObjectRef = create(ObjectReferenceSchema, {
      objectType: "report",
      reportScopedId: reportId,
      displayName: `Report ${reportId}`,
    });

    const personObjectRef = create(ObjectReferenceSchema, {
      objectType: "entity",
      globalId: personId,
      displayName: personData.name,
    });

    // Create relation with victim questions metadata
    const relation = create(RelationSchema, {
      reportId: reportId,
      objectA: reportObjectRef,
      objectB: personObjectRef,
      relationType: "RELATION_TYPE_VICTIM_ENTITY",
      metadata: victimQuestionsData,
    });

    // Create the request
    const createRelationRequest = create(CreateRelationRequestSchema, {
      reportId: reportId,
      relation: relation,
    });

    createRelationMutation.mutate(createRelationRequest);
  };

  const handleRemovePersonFromOffense = (
    personId: string,
    offenseId: string
  ) => {
    if (!reportId || !report?.relations) {
      return;
    }

    // Find the relation to delete
    const relationToDelete = report.relations.find((relation: any) => {
      const isOffenseInvolved =
        (relation.objectA?.objectType === "offense" &&
          relation.objectA?.reportScopedId === offenseId) ||
        (relation.objectB?.objectType === "offense" &&
          relation.objectB?.reportScopedId === offenseId);

      const isPersonInvolved =
        (relation.objectA?.objectType === "entity" &&
          relation.objectA?.globalId === personId) ||
        (relation.objectB?.objectType === "entity" &&
          relation.objectB?.globalId === personId);

      return isOffenseInvolved && isPersonInvolved;
    });

    if (!relationToDelete) {
      console.error("Person relation not found for deletion");
      return;
    }

    // Create delete request
    const deleteRelationRequest = create(DeleteRelationRequestSchema, {
      reportId: reportId,
      relationId: relationToDelete.id,
    });

    deleteRelationMutation.mutate(deleteRelationRequest);
  };

  const handleRemoveVehicleFromOffense = (
    vehicleId: string,
    offenseId: string
  ) => {
    if (!reportId || !report?.relations) {
      return;
    }

    // Find the relation to delete
    const relationToDelete = report.relations.find((relation: any) => {
      const isOffenseInvolved =
        (relation.objectA?.objectType === "offense" &&
          relation.objectA?.reportScopedId === offenseId) ||
        (relation.objectB?.objectType === "offense" &&
          relation.objectB?.reportScopedId === offenseId);

      const isVehicleInvolved =
        (relation.objectA?.objectType === "entity" &&
          relation.objectA?.globalId === vehicleId) ||
        (relation.objectB?.objectType === "entity" &&
          relation.objectB?.globalId === vehicleId);

      return (
        isOffenseInvolved &&
        isVehicleInvolved &&
        relation.relationType === "RELATION_TYPE_OFFENSE_VEHICLE"
      );
    });

    if (!relationToDelete) {
      console.error("Vehicle relation not found for deletion");
      return;
    }

    // Create delete request
    const deleteRelationRequest = create(DeleteRelationRequestSchema, {
      reportId: reportId,
      relationId: relationToDelete.id,
    });

    deleteRelationMutation.mutate(deleteRelationRequest);
  };

  const handleRemovePropertyFromOffense = (
    propertyId: string,
    offenseId: string
  ) => {
    if (!reportId || !report?.relations) {
      return;
    }

    // Find the relation to delete
    const relationToDelete = report.relations.find((relation: any) => {
      const isOffenseInvolved =
        (relation.objectA?.objectType === "offense" &&
          relation.objectA?.reportScopedId === offenseId) ||
        (relation.objectB?.objectType === "offense" &&
          relation.objectB?.reportScopedId === offenseId);

      const isPropertyInvolved =
        (relation.objectA?.objectType === "entity" &&
          relation.objectA?.globalId === propertyId) ||
        (relation.objectB?.objectType === "entity" &&
          relation.objectB?.globalId === propertyId);

      return (
        isOffenseInvolved &&
        isPropertyInvolved &&
        relation.relationType === "RELATION_TYPE_OFFENSE_PROPERTY"
      );
    });

    if (!relationToDelete) {
      console.error("Property relation not found for deletion");
      return;
    }

    // Create delete request
    const deleteRelationRequest = create(DeleteRelationRequestSchema, {
      reportId: reportId,
      relationId: relationToDelete.id,
    });

    deleteRelationMutation.mutate(deleteRelationRequest);
  };

  const handleRemoveOrganizationFromOffense = (
    organizationId: string,
    offenseId: string
  ) => {
    if (!reportId || !report?.relations) {
      return;
    }

    // Find the relation to delete
    const relationToDelete = report.relations.find((relation: any) => {
      const isOffenseInvolved =
        (relation.objectA?.objectType === "offense" &&
          relation.objectA?.reportScopedId === offenseId) ||
        (relation.objectB?.objectType === "offense" &&
          relation.objectB?.reportScopedId === offenseId);

      const isOrganizationInvolved =
        (relation.objectA?.objectType === "entity" &&
          relation.objectA?.globalId === organizationId) ||
        (relation.objectB?.objectType === "entity" &&
          relation.objectB?.globalId === organizationId);

      return (
        isOffenseInvolved &&
        isOrganizationInvolved &&
        relation.relationType === "RELATION_TYPE_OFFENSE_ORGANIZATION"
      );
    });

    if (!relationToDelete) {
      console.error("Organization relation not found for deletion");
      return;
    }

    // Create delete request
    const deleteRelationRequest = create(DeleteRelationRequestSchema, {
      reportId: reportId,
      relationId: relationToDelete.id,
    });

    deleteRelationMutation.mutate(deleteRelationRequest);
  };

  const handleSetActiveOffenseRelation = (
    context: {
      offenseId: string;
      relationType:
        | "victim"
        | "offender"
        | "witness"
        | "suspect"
        | "involved_party";
    } | null
  ) => {
    // This is now handled in the parent component
    // We're returning this function for compatibility
    return context;
  };

  const handleCreateVictimReportRelation = async (
    personId: string,
    reportId: string,
    entity?: any,
    additionalVictimData?: any
  ) => {
    if (!reportId) return;

    const reportObjectRef = create(ObjectReferenceSchema, {
      objectType: "report",
      reportScopedId: reportId,
      displayName: `Report ${reportId}`,
    });

    const personObjectRef = create(ObjectReferenceSchema, {
      objectType: "entity",
      globalId: personId,
      displayName: entity.name,
    });

    // Create the relation
    const relation = create(RelationSchema, {
      reportId: reportId,
      objectA: reportObjectRef,
      objectB: personObjectRef,
      relationType: "RELATION_TYPE_VICTIM_REPORT",
      metadata: additionalVictimData,
    });

    const createRelationRequest = create(CreateRelationRequestSchema, {
      reportId: reportId,
      relation: relation,
    });

    createRelationMutation.mutate(createRelationRequest);
  };

  const handleUpdateVictimReportRelation = async (
    relationId: string,
    victimData: any
  ) => {
    if (!reportId || !report?.relations) return;

    // Find the existing relation
    const existingRelation = report.relations.find(
      (rel: any) => rel.id === relationId
    );
    if (!existingRelation) {
      console.error("Victim report relation not found for update");
      return;
    }

    // Create updated relation with new metadata
    const updatedRelation = create(RelationSchema, {
      ...existingRelation,
      metadata: victimData,
    });

    const updateRelationRequest = create(UpdateRelationRequestSchema, {
      reportId: reportId,
      relation: updatedRelation,
    });

    updateRelationMutation.mutate(updateRelationRequest);
  };

  const handleSetActiveVehicleOffenseContext = (
    context: { offenseId: string } | null
  ) => {
    console.log("Setting active vehicle offense context:", context);
    // This is now handled in the parent component
    return context;
  };

  const handleSetActivePropertyOffenseContext = (
    context: { offenseId: string } | null
  ) => {
    console.log("Setting active property offense context:", context);
    // This is now handled in the parent component
    return context;
  };

  const handleSetActiveOrganizationOffenseContext = (
    context: { offenseId: string } | null
  ) => {
    console.log("Setting active organization offense context:", context);
    // This is now handled in the parent component
    return context;
  };

  return {
    handleAddPersonToOffense,
    handleAddVehicleToOffense,
    handleAddPropertyToOffense,
    handleAddOrganizationToOffense,
    handleCreateVictimEntityRelation,
    handleRemovePersonFromOffense,
    handleRemoveVehicleFromOffense,
    handleRemovePropertyFromOffense,
    handleRemoveOrganizationFromOffense,
    handleSetActiveOffenseRelation,
    handleCreateVictimReportRelation,
    handleUpdateVictimReportRelation,
    handleSetActiveVehicleOffenseContext,
    handleSetActivePropertyOffenseContext,
    handleSetActiveOrganizationOffenseContext,
  };
};
