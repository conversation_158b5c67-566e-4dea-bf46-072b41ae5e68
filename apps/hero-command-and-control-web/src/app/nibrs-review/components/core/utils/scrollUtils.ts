import { SCROLL_OFFSET_TOP } from "../constants";
import { NavItem } from "../types";

// Find the section that should be marked as active during scrolling
export const findActiveSection = (
  container: HTMLDivElement, 
  navItems: NavItem[]
): { activeSection: string } => {
  if (!container || !navItems || navItems.length === 0) {
    return { activeSection: "" };
  }

  const { top: cTop, height: cHeight } = container.getBoundingClientRect();
  const cBottom = cTop + cHeight;

  let firstWithTitleVisible: string | null = null;
  let bestFallback: { id: string; ratio: number } = {
    id: navItems[0].id,
    ratio: 0,
  };

  navItems.forEach(({ id }) => {
    if (!id) return;
    
    const el = document.getElementById(id);
    if (!el) return;

    const rect = el.getBoundingClientRect();

    const titleY = rect.top + 50;
    const titleVisible = titleY >= cTop + 100 && titleY <= cBottom;
    if (titleVisible && firstWithTitleVisible === null) {
      firstWithTitleVisible = id;
    }

    const visibleTop = Math.max(rect.top, cTop);
    const visibleBottom = Math.min(rect.bottom, cBottom);
    const visibleHeight = Math.max(visibleBottom - visibleTop, 0);
    const ratio = visibleHeight / rect.height;

    if (ratio > bestFallback.ratio) {
      bestFallback = { id, ratio };
    }
  });

  return { 
    activeSection: firstWithTitleVisible ?? bestFallback.id 
  };
};

// Scroll to a specific section
export const scrollToSection = (id: string, container: HTMLDivElement | null) => {
  if (!container) return;
  
  const el = document.getElementById(id);
  if (!el) return;
  
  const containerRect = container.getBoundingClientRect();
  const elRect = el.getBoundingClientRect();
  const offset = elRect.top - containerRect.top + container.scrollTop - SCROLL_OFFSET_TOP;
  
  container.scrollTo({ 
    top: offset, 
    behavior: "smooth" 
  });
}; 