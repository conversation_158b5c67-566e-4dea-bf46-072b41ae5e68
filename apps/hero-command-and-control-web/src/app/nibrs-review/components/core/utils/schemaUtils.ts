import {
  CreateEntityRequest,
  EntityType,
  RecordStatus,
} from "proto/hero/entity/v1/entity_pb";
import { ENTITY_TYPES } from "../constants";
import { PanelType } from "../types";

// Unified schema getter based on PanelType
export const getSchemaForPanelType = (
  panelType: PanelType | null,
  editingEntityId: string | null,
  editingEntity: any,
  specificSchema: any,
  personSchemas: any,
  propertySchemas: any,
  vehicleSchemas: any,
  organizationSchemas: any
) => {
  if (!panelType) return null;

  // If editing and specific schema is loaded, always use the specific schema
  if (editingEntityId && specificSchema && editingEntity) {
    const entityTypeMap: { [key in PanelType]?: string } = {
      [PanelType.PERSON]: ENTITY_TYPES.PERSON,
      [PanelType.PROPERTY]: ENTITY_TYPES.PROPERTY,
      [PanelType.VEHICLE]: ENTITY_TYPES.VEHICLE,
      [PanelType.ORGANIZATION]: ENTITY_TYPES.ORGANIZATION,
    };

    const expectedEntityType = entityTypeMap[panelType];
    if (editingEntity.entityType === expectedEntityType) {
      return specificSchema;
    }
  }

  // For new entities, use the latest schema for the target type
  const entityTypeMap: { [key in PanelType]?: EntityType } = {
    [PanelType.PERSON]: EntityType.PERSON,
    [PanelType.PROPERTY]: EntityType.PROPERTY,
    [PanelType.VEHICLE]: EntityType.VEHICLE,
    [PanelType.ORGANIZATION]: EntityType.ORGANIZATION,
  };
  const targetEntityType = entityTypeMap[panelType];

  switch (targetEntityType) {
    case EntityType.PERSON:
      return personSchemas?.schemas?.[0] ?? null;
    case EntityType.PROPERTY:
      return propertySchemas?.schemas?.[0] ?? null;
    case EntityType.VEHICLE:
      return vehicleSchemas?.schemas?.[0] ?? null;
    case EntityType.ORGANIZATION:
      return organizationSchemas?.schemas?.[0] ?? null;
    default:
      return null;
  }
};

// Create entity request builder
export const createEntityFromValues = (
  values: any,
  type: EntityType,
  schemaId: string = "",
  schemaVersion: number = 1
): CreateEntityRequest => {
  return {
    entity: {
      schemaId,
      schemaVersion,
      data: values,
      entityType: type,
      status: RecordStatus.ACTIVE,
    },
  } as CreateEntityRequest;
};

// Add the mapEntityTypeToProtoType function to convert internal entity types to proto types

/**
 * Maps our application entity types to the proto entity types
 * @param entityType The application entity type (e.g., "PERSON", "VEHICLE", "PROPERTY")
 * @returns The proto entity type (e.g., "ENTITY_TYPE_PERSON")
 */
export function mapEntityTypeToProtoType(entityType: string): string {
  switch (entityType) {
    case "PERSON":
      return "ENTITY_TYPE_PERSON";
    case "VEHICLE":
      return "ENTITY_TYPE_VEHICLE";
    case "PROPERTY":
      return "ENTITY_TYPE_PROPERTY";
    default:
      return `ENTITY_TYPE_${entityType}`;
  }
}
