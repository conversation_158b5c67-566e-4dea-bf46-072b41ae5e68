import { useRef, useState } from "react";
import { NotificationState, PanelType, SaveStatus } from "../types";

export const useNibrsReviewPageState = () => {
  // Sidebar scroll & active-tracking
  const [activeSection, setActiveSection] = useState<string>("");
  const scrollContainerRef = useRef<HTMLDivElement>(null);

  // Panel state
  const [sidePanelOpen, setSidePanelOpen] = useState(false);
  const [activePanelType, setActivePanelType] = useState<PanelType | null>(
    null
  );
  const [isSaveAndAddAnotherLoading, setIsSaveAndAddAnotherLoading] =
    useState(false);
  const [isSaveLoading, setIsSaveLoading] = useState(false);
  const [isUpdateLoading, setIsUpdateLoading] = useState(false);
  const panelContentRef = useRef<{ scrollToTop: () => void } | null>(null);

  // Entity editing state
  const [editingEntityId, setEditingEntityId] = useState<string | null>(null);
  const [editingEntityFormData, setEditingEntityFormData] = useState<any>(null);

  // Entities state
  const [people, setPeople] = useState<any[]>([]);
  const [vehicles, setVehicles] = useState<any[]>([]);
  const [properties, setProperties] = useState<any[]>([]);
  const [organizations, setOrganizations] = useState<any[]>([]);

  // Entity IDs to fetch
  const [entityIdsToFetch, setEntityIdsToFetch] = useState<string[]>([]);

  // Report section IDs
  const [peopleListSectionId, setPeopleListSectionId] = useState<string | null>(
    null
  );
  const [vehicleListSectionId, setVehicleListSectionId] = useState<
    string | null
  >(null);
  const [propertyListSectionId, setPropertyListSectionId] = useState<
    string | null
  >(null);
  const [organizationListSectionId, setOrganizationListSectionId] = useState<
    string | null
  >(null);
  const [offenseListSectionId, setOffenseListSectionId] = useState<
    string | null
  >(null);

  // Notification
  const [notification, setNotification] = useState<NotificationState>({
    open: false,
    message: "",
    severity: "success",
  });

  // Save statuses
  const [saveStatuses, setSaveStatuses] = useState<SaveStatus[]>([]);

  // Section ID to type mapping
  const [sectionIdToType, setSectionIdToType] = useState<
    Record<string, string>
  >({});

  // Selected entity row
  const [selectedRowId, setSelectedRowId] = useState<string | null>(null);

  // Associated cases
  const [associatedCases, setAssociatedCases] = useState<any[]>([]);

  // Offense-entity relations
  const [activeOffenseRelation, setActiveOffenseRelation] = useState<{
    offenseId: string;
    relationType:
      | "victim"
      | "offender"
      | "witness"
      | "suspect"
      | "involved_party";
  } | null>(null);

  // Vehicle offense context
  const [activeVehicleOffenseContext, setActiveVehicleOffenseContext] =
    useState<{
      offenseId: string;
    } | null>(null);

  // Property offense context
  const [activePropertyOffenseContext, setActivePropertyOffenseContext] =
    useState<{
      offenseId: string;
    } | null>(null);

  // Organization offense context
  const [
    activeOrganizationOffenseContext,
    setActiveOrganizationOffenseContext,
  ] = useState<{
    offenseId: string;
  } | null>(null);

  // Additional victim data context
  const [activeVictimAdditionalData, setActiveVictimAdditionalData] =
    useState<any>(null);

  // Arrest context
  const [activeArrestRelation, setActiveArrestRelation] = useState<{
    arrestId: string;
  } | null>(null);

  return {
    // Sidebar & navigation
    activeSection,
    setActiveSection,
    scrollContainerRef,

    // Panel state
    sidePanelOpen,
    setSidePanelOpen,
    activePanelType,
    setActivePanelType,
    isSaveAndAddAnotherLoading,
    setIsSaveAndAddAnotherLoading,
    isSaveLoading,
    setIsSaveLoading,
    isUpdateLoading,
    setIsUpdateLoading,
    panelContentRef,

    // Entity editing
    editingEntityId,
    setEditingEntityId,
    editingEntityFormData,
    setEditingEntityFormData,

    // Entities
    people,
    setPeople,
    vehicles,
    setVehicles,
    properties,
    setProperties,
    organizations,
    setOrganizations,
    entityIdsToFetch,
    setEntityIdsToFetch,

    // Section IDs
    peopleListSectionId,
    setPeopleListSectionId,
    vehicleListSectionId,
    setVehicleListSectionId,
    propertyListSectionId,
    setPropertyListSectionId,
    organizationListSectionId,
    setOrganizationListSectionId,
    offenseListSectionId,
    setOffenseListSectionId,

    // Notification
    notification,
    setNotification,

    // Save statuses
    saveStatuses,
    setSaveStatuses,

    // Misc
    sectionIdToType,
    setSectionIdToType,
    selectedRowId,
    setSelectedRowId,
    associatedCases,
    setAssociatedCases,

    // Offense contexts
    activeOffenseRelation,
    setActiveOffenseRelation,
    activeVehicleOffenseContext,
    setActiveVehicleOffenseContext,
    activePropertyOffenseContext,
    setActivePropertyOffenseContext,
    activeOrganizationOffenseContext,
    setActiveOrganizationOffenseContext,
    activeVictimAdditionalData,
    setActiveVictimAdditionalData,

    // Arrest contexts
    activeArrestRelation,
    setActiveArrestRelation,
  };
};
