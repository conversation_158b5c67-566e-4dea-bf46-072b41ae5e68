import { ListCasesByReportIdRequest } from "proto/hero/cases/v1/cases_pb";
import { EntityType } from "proto/hero/entity/v1/entity_pb";
import {
  useAddEntityRefToCase,
  useCase,
  useListCasesByReportId,
  useRemoveEntityRefFromCase,
} from "../../../../apis/services/workflow/cases/hooks";
import {
  useBatchGetLatestEntities,
  useCreateEntity,
  useGetEntitySchemaByVersion,
  useGetLatestEntity,
  useListLatestEntitySchemas,
  useUpdateEntity,
} from "../../../../apis/services/workflow/entity/hooks";
import {
  useCreateRelation,
  useDeleteRelation,
  useListReportSections,
  useReport,
  useUpdateRelation,
  useUpdateReportSection,
} from "../../../../apis/services/workflow/reports/v2/hooks";

interface UseReportingPageAPIProps {
  reportId: string | null;
  editingEntityId: string | null;
  editingEntity: any;
  entityIdsToFetch: string[];
  peopleListSectionId: string | null;
  vehicleListSectionId: string | null;
  propertyListSectionId: string | null;
  organizationListSectionId: string | null;
  associatedCases: any[];
}

export const useNibrsReviewPageAPI = ({
  reportId,
  editingEntityId,
  editingEntity,
  entityIdsToFetch,
  peopleListSectionId,
  vehicleListSectionId,
  propertyListSectionId,
  organizationListSectionId,
  associatedCases,
}: UseReportingPageAPIProps) => {
  // Report API hooks
  const reportQuery = useReport(reportId || "", 0, {
    enabled: !!reportId,
    staleTime: 0,
    refetchOnMount: "always",
    refetchOnWindowFocus: true,
  });

  const reportSectionsQuery = useListReportSections(reportId || "", {
    enabled: !!reportId,
    staleTime: 0,
    refetchOnMount: "always",
    refetchOnWindowFocus: true,
  });

  // Entity API hooks
  const personSchemasQuery = useListLatestEntitySchemas({
    entityType: EntityType.PERSON,
    pageSize: 1,
    pageToken: "",
  } as any);

  const propertySchemasQuery = useListLatestEntitySchemas({
    entityType: EntityType.PROPERTY,
    pageSize: 1,
    pageToken: "",
  } as any);

  const vehicleSchemasQuery = useListLatestEntitySchemas({
    entityType: EntityType.VEHICLE,
    pageSize: 1,
    pageToken: "",
  } as any);

  const organizationSchemasQuery = useListLatestEntitySchemas({
    entityType: EntityType.ORGANIZATION,
    pageSize: 1,
    pageToken: "",
  } as any);

  const editingEntityQuery = useGetLatestEntity(
    editingEntityId || "",
    undefined,
    {
      enabled: !!editingEntityId,
      queryKey: ["entity", editingEntityId],
    }
  );

  const specificSchemaQuery = useGetEntitySchemaByVersion(
    editingEntity?.schemaId || "",
    editingEntity?.schemaVersion || 0,
    {
      enabled: !!editingEntity?.schemaId && !!editingEntity?.schemaVersion,
      queryKey: [
        "entitySchema",
        editingEntity?.schemaId,
        editingEntity?.schemaVersion,
      ],
    }
  );

  const batchEntitiesQuery = useBatchGetLatestEntities(entityIdsToFetch, {
    queryKey: ["entity", "batch", reportId, entityIdsToFetch],
    enabled: !!reportId && entityIdsToFetch.length > 0,
    staleTime: 0,
    refetchOnMount: "always",
    refetchOnWindowFocus: true,
  });

  // Case API hooks
  const casesQuery = useListCasesByReportId(
    {
      reportId: reportId || "",
      pageSize: 50,
      pageToken: "",
    } as ListCasesByReportIdRequest,
    {
      enabled: !!reportId,
      staleTime: 0,
      refetchOnMount: "always",
      refetchOnWindowFocus: true,
    }
  );

  const firstCaseId = associatedCases?.[0]?.id;
  const fullCaseQuery = useCase(firstCaseId || "", undefined, {
    enabled: !!firstCaseId,
    staleTime: 0,
    refetchOnMount: "always",
    refetchOnWindowFocus: true,
  });

  // Mutations
  const updateReportSectionMutation = useUpdateReportSection();
  const createEntityMutation = useCreateEntity();
  const updateEntityMutation = useUpdateEntity();
  const addEntityRefToCaseMutation = useAddEntityRefToCase();
  const removeEntityRefFromCaseMutation = useRemoveEntityRefFromCase();
  const createRelationMutation = useCreateRelation();
  const updateRelationMutation = useUpdateRelation();
  const deleteRelationMutation = useDeleteRelation();

  return {
    // Queries
    reportQuery,
    reportSectionsQuery,
    personSchemasQuery,
    propertySchemasQuery,
    vehicleSchemasQuery,
    organizationSchemasQuery,
    editingEntityQuery,
    specificSchemaQuery,
    batchEntitiesQuery,
    casesQuery,
    fullCaseQuery,

    // Mutations
    updateReportSectionMutation,
    createEntityMutation,
    updateEntityMutation,
    addEntityRefToCaseMutation,
    removeEntityRefFromCaseMutation,
    createRelationMutation,
    updateRelationMutation,
    deleteRelationMutation,
  };
};
