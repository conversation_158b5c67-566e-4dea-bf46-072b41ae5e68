import { useQueryClient } from "@tanstack/react-query";
import { throttle } from "lodash";
import { useCallback, useEffect } from "react";
import { ENTITY_TYPES, SCROLL_THROTTLE_MS } from "../constants";
import { findActiveSection } from "../utils/scrollUtils";
import { processReportSections } from "../utils/sectionUtils";

interface UseNibrsReviewPageEffectsProps {
  // State
  availableNavItems: any[];
  activeSection: string;
  setActiveSection: (section: string) => void;
  scrollContainerRef: React.RefObject<HTMLDivElement | null>;
  setEditingEntityFormData: (data: any) => void;
  setEntityIdsToFetch: (ids: string[]) => void;
  setPeople: (people: any[]) => void;
  setVehicles: (vehicles: any[]) => void;
  setProperties: (properties: any[]) => void;
  setOrganizations: (organizations: any[]) => void;
  setSaveStatuses: React.Dispatch<React.SetStateAction<any[]>>;
  setAssociatedCases: (cases: any[]) => void;

  // Section ID setters
  setPeopleListSectionId: (id: string | null) => void;
  setVehicleListSectionId: (id: string | null) => void;
  setPropertyListSectionId: (id: string | null) => void;
  setOrganizationListSectionId: (id: string | null) => void;
  setOffenseListSectionId: (id: string | null) => void;
  setSectionIdToType: (mapping: Record<string, string>) => void;

  // API data
  editingEntity: any;
  reportSections: any;
  batchEntitiesData: any;
  casesData: any;

  // Props
  reportId: string | null;

  // Handlers
  handleSaveStatusChange: (status: any) => void;
}

export const useNibrsReviewPageEffects = ({
  availableNavItems,
  activeSection,
  setActiveSection,
  scrollContainerRef,
  setEditingEntityFormData,
  setEntityIdsToFetch,
  setPeople,
  setVehicles,
  setProperties,
  setOrganizations,
  setSaveStatuses,
  setAssociatedCases,
  setPeopleListSectionId,
  setVehicleListSectionId,
  setPropertyListSectionId,
  setOrganizationListSectionId,
  setOffenseListSectionId,
  setSectionIdToType,
  editingEntity,
  reportSections,
  batchEntitiesData,
  casesData,
  reportId,
  handleSaveStatusChange,
}: UseNibrsReviewPageEffectsProps) => {
  const queryClient = useQueryClient();

  // Update active section based on available sections
  useEffect(() => {
    if (availableNavItems.length > 0) {
      // Only update if current activeSection is not in available items or is empty
      if (
        !activeSection ||
        !availableNavItems.some((item) => item.id === activeSection)
      ) {
        setActiveSection(availableNavItems[0].id);
      }
    } else {
      // Clear active section if no items are available
      setActiveSection("");
    }
  }, [availableNavItems, activeSection, setActiveSection]);

  // Process entity data when it's loaded
  useEffect(() => {
    if (editingEntity && editingEntity.data) {
      const formattedData: any = {};
      if (typeof editingEntity.data === "object") {
        Object.keys(editingEntity.data).forEach((sectionKey) => {
          formattedData[sectionKey] = editingEntity.data?.[sectionKey] || {};
        });
      }
      setEditingEntityFormData(formattedData);
    }
  }, [editingEntity, setEditingEntityFormData]);

  // Process report sections
  useEffect(() => {
    if (reportSections) {
      const setSectionCallbacks = {
        setPeopleListSectionId,
        setVehicleListSectionId,
        setPropertyListSectionId,
        setOrganizationListSectionId,
        setOffenseListSectionId,
        setSectionIdToType,
      };

      const { entityIds } = processReportSections(
        reportSections.sections,
        setSectionCallbacks
      );
      setEntityIdsToFetch(entityIds);

      // Clear entity arrays if no entities to fetch
      if (entityIds.length === 0) {
        setPeople([]);
        setVehicles([]);
        setProperties([]);
      }
    }
  }, [
    reportSections,
    setPeopleListSectionId,
    setVehicleListSectionId,
    setPropertyListSectionId,
    setOrganizationListSectionId,
    setOffenseListSectionId,
    setSectionIdToType,
    setEntityIdsToFetch,
    setPeople,
    setVehicles,
    setProperties,
  ]);

  // Process batch fetched entities
  useEffect(() => {
    if (batchEntitiesData?.entities) {
      const fetchedEntities = batchEntitiesData.entities;
      const peopleEntities: any[] = [];
      const vehicleEntities: any[] = [];
      const propertyEntities: any[] = [];
      const organizationEntities: any[] = [];

      fetchedEntities.forEach((entity: any) => {
        switch (entity.entityType as string) {
          case ENTITY_TYPES.PERSON:
            peopleEntities.push(entity);
            break;
          case ENTITY_TYPES.VEHICLE:
            vehicleEntities.push(entity);
            break;
          case ENTITY_TYPES.PROPERTY:
            propertyEntities.push(entity);
            break;
          case ENTITY_TYPES.ORGANIZATION:
            organizationEntities.push(entity);
            break;
        }
      });

      setPeople(peopleEntities);
      setVehicles(vehicleEntities);
      setProperties(propertyEntities);
      setOrganizations(organizationEntities);
    }
  }, [
    batchEntitiesData,
    setPeople,
    setVehicles,
    setProperties,
    setOrganizations,
  ]);

  // Store associated cases in state when data is loaded
  useEffect(() => {
    if (casesData?.cases) {
      setAssociatedCases(casesData.cases);
    }
  }, [casesData, setAssociatedCases]);

  // Scroll handling with throttling
  const handleScroll = useCallback(() => {
    if (!scrollContainerRef.current || availableNavItems.length === 0) return;

    const { activeSection: newActiveSection } = findActiveSection(
      scrollContainerRef.current,
      availableNavItems
    );

    if (newActiveSection) {
      setActiveSection(newActiveSection);
    }
  }, [availableNavItems, scrollContainerRef, setActiveSection]);

  useEffect(() => {
    const container = scrollContainerRef.current;
    if (!container || availableNavItems.length === 0) return;

    const onScroll = throttle(handleScroll, SCROLL_THROTTLE_MS);
    container.addEventListener("scroll", onScroll);
    handleScroll(); // Initial call to set active section

    return () => container.removeEventListener("scroll", onScroll);
  }, [handleScroll, availableNavItems.length]);

  // Handler for save status changes from child components
  const internalHandleSaveStatusChange = useCallback(
    (status: any) => {
      setSaveStatuses((prev: any[]) => {
        const existingIndex = prev.findIndex((s) => s.source === status.source);

        if (existingIndex >= 0) {
          const newStatuses = [...prev];
          newStatuses[existingIndex] = status;
          return newStatuses;
        } else {
          return [...prev, status];
        }
      });
    },
    [setSaveStatuses]
  );

  // Return the internal handler if no external handler is provided
  return {
    handleSaveStatusChange:
      handleSaveStatusChange || internalHandleSaveStatusChange,
  };
};
