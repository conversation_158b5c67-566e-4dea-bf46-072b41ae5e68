import { Button } from "@/design-system/components/Button";
import {
  DropdownOption,
  InputType,
  TextInput,
} from "@/design-system/components/TextInput";
import { Typography } from "@/design-system/components/Typography";
import { colors } from "@/design-system/tokens";
import AddIcon from "@mui/icons-material/Add";
import DeleteIcon from "@mui/icons-material/Delete";
import EditIcon from "@mui/icons-material/Edit";
import ExpandLessIcon from "@mui/icons-material/ExpandLess";
import ExpandMoreIcon from "@mui/icons-material/ExpandMore";
import MoreVertIcon from "@mui/icons-material/MoreVert";
import OpenInNewIcon from "@mui/icons-material/OpenInNew";
import {
  Box,
  Collapse,
  Dialog,
  DialogActions,
  DialogContent,
  Divider,
  IconButton,
  Menu,
  MenuItem,
} from "@mui/material";
import React, { useState } from "react";
import { PropertyData } from "./constants";

interface PropertiesSectionProps {
  properties: PropertyData[];
  relations?: any[];
  addedOffenses?: any[];
  readOnly?: boolean;
  onSetActivePropertyOffenseContext?: (
    context: { offenseId: string } | null
  ) => void;
  onOpenSidePanel?: (panelType: "PROPERTY") => void;
  onEntityEdit?: (entityId: string, entityType: "property") => void;
  onRemovePropertyFromReport?: (
    propertyId: string,
    entityType: "property"
  ) => void;
  onAddPropertyToOffense?: (propertyId: string, offenseId: string) => void;
  onRemovePropertyFromOffense?: (propertyId: string, offenseId: string) => void;
}

// Individual Property Card Component
interface PropertyCardProps {
  property: PropertyData;
  relations?: any[];
  addedOffenses?: any[];
  readOnly?: boolean;
  onEntityEdit?: (entityId: string, entityType: "property") => void;
  onRemovePropertyFromReport?: (
    propertyId: string,
    entityType: "property"
  ) => void;
  onAddPropertyToOffense?: (propertyId: string, offenseId: string) => void;
  onRemovePropertyFromOffense?: (propertyId: string, offenseId: string) => void;
}

const PropertyCard: React.FC<PropertyCardProps> = ({
  property,
  relations = [],
  addedOffenses = [],
  readOnly = false,
  onEntityEdit,
  onRemovePropertyFromReport,
  onAddPropertyToOffense,
  onRemovePropertyFromOffense,
}) => {
  const [isExpanded, setIsExpanded] = useState(true);
  const [menuAnchorEl, setMenuAnchorEl] = useState<null | HTMLElement>(null);
  const [removeModalOpen, setRemoveModalOpen] = useState(false);

  // Helper function to get related offenses for this property
  const getRelatedOffenses = (propertyId: string): string[] => {
    if (!relations || !addedOffenses) return [];

    const offenseIds = relations
      .filter((rel: any) => {
        const isPropertyRelation =
          rel.relationType === "RELATION_TYPE_OFFENSE_PROPERTY";
        const isPropertyInvolved =
          (rel.objectA?.objectType === "entity" &&
            rel.objectA?.globalId === propertyId) ||
          (rel.objectB?.objectType === "entity" &&
            rel.objectB?.globalId === propertyId);

        return isPropertyRelation && isPropertyInvolved;
      })
      .map((rel: any) => {
        if (rel.objectA?.objectType === "offense")
          return rel.objectA.reportScopedId;
        if (rel.objectB?.objectType === "offense")
          return rel.objectB.reportScopedId;
        return null;
      })
      .filter(Boolean);

    return offenseIds;
  };

  // Helper function to create offense dropdown options
  const getOffenseOptions = (): DropdownOption[] => {
    if (!addedOffenses) return [];

    return addedOffenses.map((addedOffense) => ({
      value: addedOffense.id,
      label: `${addedOffense.offense.citation} ${addedOffense.offense.literal}`,
    }));
  };

  // Helper function to get selected offense options for this property
  const getSelectedOffenseOptions = (propertyId: string): DropdownOption[] => {
    const relatedOffenseIds = getRelatedOffenses(propertyId);
    const offenseOptions = getOffenseOptions();

    return offenseOptions.filter((option) =>
      relatedOffenseIds.includes(option.value)
    );
  };

  // Handle offense selection changes
  const handleOffenseSelectionChange = (selectedOptions: DropdownOption[]) => {
    if (readOnly) return;

    const currentOffenseIds = getRelatedOffenses(property.id);
    const newOffenseIds = selectedOptions.map((option) => option.value);

    // Find offenses to add (in new selection but not in current)
    const offensesToAdd = newOffenseIds.filter(
      (offenseId) => !currentOffenseIds.includes(offenseId)
    );

    // Find offenses to remove (in current but not in new selection)
    const offensesToRemove = currentOffenseIds.filter(
      (offenseId) => !newOffenseIds.includes(offenseId)
    );

    // Add new offense relations
    offensesToAdd.forEach((offenseId) => {
      if (onAddPropertyToOffense) {
        onAddPropertyToOffense(property.id, offenseId);
      }
    });

    // Remove offense relations
    offensesToRemove.forEach((offenseId) => {
      if (onRemovePropertyFromOffense) {
        onRemovePropertyFromOffense(property.id, offenseId);
      }
    });
  };

  const selectedOffenseOptions = getSelectedOffenseOptions(property.id);
  const offenseOptions = getOffenseOptions();

  const handleMenuClick = (event: React.MouseEvent<HTMLElement>) => {
    event.stopPropagation();
    setMenuAnchorEl(event.currentTarget);
  };

  const handleMenuClose = () => {
    setMenuAnchorEl(null);
  };

  const handleOpenRecord = () => {
    window.open(`/entity?entityId=${property.id}`, "_blank");
    setMenuAnchorEl(null);
  };

  const handleEditEntity = () => {
    onEntityEdit?.(property.id, "property");
    setMenuAnchorEl(null);
  };

  const handleRemoveClick = () => {
    setRemoveModalOpen(true);
    setMenuAnchorEl(null);
  };

  const handleRemoveCancel = () => {
    setRemoveModalOpen(false);
  };

  const handleRemoveConfirm = () => {
    onRemovePropertyFromReport?.(property.id, "property");
    setRemoveModalOpen(false);
  };

  return (
    <Box
      sx={{
        display: "flex",
        width: "100%",
        flexDirection: "column",
        alignItems: "flex-start",
        borderRadius: "12px",
        border: `1px solid ${colors.grey[200]}`,
        background: "#FFF",
        overflow: "hidden",
        boxShadow: "0px 0px 16px 0px rgba(0, 0, 0, 0.04)",
        mb: 3,
      }}
    >
      {/* Header */}
      <Box
        sx={{
          display: "flex",
          width: "100%",
          justifyContent: "space-between",
          alignItems: "center",
          padding: "16px 24px",
          borderBottom: `1px solid ${colors.grey[200]}`,
          backgroundColor: colors.grey[50],
        }}
      >
        <Box sx={{ display: "flex", alignItems: "center", gap: 2 }}>
          <Typography style="h3" color={colors.grey[900]}>
            {property.category || property.description || "Unknown Property"}
          </Typography>
        </Box>

        <Box sx={{ display: "flex", alignItems: "center", gap: 1 }}>
          {!readOnly && (
            <IconButton
              onClick={handleMenuClick}
              size="small"
              sx={{
                color: colors.grey[600],
                zIndex: 10,
                "&:hover": {
                  bgcolor: colors.grey[100],
                },
              }}
            >
              <MoreVertIcon />
            </IconButton>
          )}
          <IconButton
            onClick={() => setIsExpanded(!isExpanded)}
            size="small"
            sx={{
              color: colors.grey[600],
              "&:hover": {
                bgcolor: colors.grey[100],
              },
            }}
          >
            {isExpanded ? <ExpandLessIcon /> : <ExpandMoreIcon />}
          </IconButton>
        </Box>
      </Box>

      {/* Form Content */}
      <Collapse in={isExpanded} sx={{ width: "100%" }}>
        <Box sx={{ padding: "24px" }}>
          {/* Related Offenses Section */}
          <Box sx={{ mb: 4 }}>
            <TextInput
              title="Related Offenses"
              placeholder="Select offenses..."
              type={InputType.Dropdown}
              isMultiSelect
              options={offenseOptions}
              selectedItems={selectedOffenseOptions}
              onChangeSelectedItems={handleOffenseSelectionChange}
              readOnly={readOnly}
              chipColor="grey"
              hideSelectedFromOptions={true}
              minRequiredItems={1}
            />
          </Box>
        </Box>
      </Collapse>

      {/* Menu for property actions */}
      <Menu
        anchorEl={menuAnchorEl}
        open={Boolean(menuAnchorEl)}
        onClose={handleMenuClose}
        anchorOrigin={{
          vertical: "bottom",
          horizontal: "right",
        }}
        transformOrigin={{
          vertical: "top",
          horizontal: "right",
        }}
        sx={{
          mt: 0.25,
          "& .MuiPaper-root": {
            borderRadius: "8px",
            minWidth: "200px",
          },
          "& .MuiMenuItem-root": {
            py: 1.5,
            display: "flex",
            alignItems: "center",
            gap: 1.5,
          },
        }}
      >
        <MenuItem onClick={handleEditEntity}>
          <EditIcon fontSize="small" sx={{ color: colors.grey[500] }} />
          <Typography style="body2" color={colors.grey[500]}>
            Edit entity
          </Typography>
        </MenuItem>
        <MenuItem onClick={handleOpenRecord}>
          <OpenInNewIcon fontSize="small" sx={{ color: colors.grey[500] }} />
          <Typography style="body2" color={colors.grey[500]}>
            View entity in new tab
          </Typography>
        </MenuItem>
        <MenuItem onClick={handleRemoveClick}>
          <DeleteIcon fontSize="small" sx={{ color: colors.grey[500] }} />
          <Typography style="body2" color={colors.grey[500]}>
            Remove entity
          </Typography>
        </MenuItem>
      </Menu>

      {/* Remove confirmation modal */}
      <Dialog
        open={removeModalOpen}
        onClose={handleRemoveCancel}
        maxWidth="sm"
        fullWidth
        PaperProps={{
          sx: {
            borderRadius: "12px",
            p: 1,
          },
        }}
      >
        <DialogContent sx={{ p: 3 }}>
          <Box sx={{ mb: 2 }}>
            <Typography style="h2" color={colors.grey[900]}>
              Delete{" "}
              {property.category || property.description || "Unknown Property"}
            </Typography>
          </Box>
          <Box sx={{ mb: 3 }}>
            <Typography style="body3" color={colors.grey[700]}>
              {property.category || property.description || "Unknown Property"}{" "}
              will be deleted from the report and all offenses
            </Typography>
          </Box>
        </DialogContent>

        <DialogActions sx={{ p: 3, pt: 0 }}>
          <Button
            label="Cancel"
            color="grey"
            prominence={false}
            onClick={handleRemoveCancel}
          />
          <Button
            label="Delete"
            color="blue"
            prominence={true}
            onClick={handleRemoveConfirm}
          />
        </DialogActions>
      </Dialog>
    </Box>
  );
};

const PropertiesSection: React.FC<PropertiesSectionProps> = ({
  properties,
  relations,
  addedOffenses,
  readOnly = false,
  onSetActivePropertyOffenseContext,
  onOpenSidePanel,
  onEntityEdit,
  onRemovePropertyFromReport,
  onAddPropertyToOffense,
  onRemovePropertyFromOffense,
}) => {
  const [addPropertyMenuAnchor, setAddPropertyMenuAnchor] =
    useState<null | HTMLElement>(null);

  const handleAddPropertyClick = (event: React.MouseEvent<HTMLElement>) => {
    if (!addedOffenses || addedOffenses.length === 0) return;

    if (addedOffenses.length === 1) {
      // If only one offense, directly set the active property offense context
      onSetActivePropertyOffenseContext?.({
        offenseId: addedOffenses[0].id,
      });
      // Open the side panel
      onOpenSidePanel?.("PROPERTY");
    } else {
      // If multiple offenses, show menu to select
      setAddPropertyMenuAnchor(event.currentTarget);
    }
  };

  const handleAddPropertyMenuClose = () => {
    setAddPropertyMenuAnchor(null);
  };

  const handleSelectOffenseForProperty = (offenseId: string) => {
    onSetActivePropertyOffenseContext?.({
      offenseId,
    });
    // Open the side panel
    onOpenSidePanel?.("PROPERTY");
    setAddPropertyMenuAnchor(null);
  };

  return (
    <>
      <Divider sx={{ borderColor: colors.grey[300], mt: 4, mb: 3 }} />
      <Box sx={{ mb: 4 }}>
        <Box
          sx={{
            display: "flex",
            justifyContent: "space-between",
            alignItems: "center",
            mb: 3,
          }}
        >
          <Typography style="h1" color={colors.grey[900]}>
            All Properties
          </Typography>
          {!readOnly && addedOffenses && addedOffenses.length > 0 && (
            <Button
              onClick={handleAddPropertyClick}
              label="Add Property"
              leftIcon={<AddIcon />}
              size="small"
            />
          )}
        </Box>
        {properties.map((property) => (
          <PropertyCard
            key={property.id}
            property={property}
            relations={relations}
            addedOffenses={addedOffenses}
            readOnly={readOnly}
            onEntityEdit={onEntityEdit}
            onRemovePropertyFromReport={onRemovePropertyFromReport}
            onAddPropertyToOffense={onAddPropertyToOffense}
            onRemovePropertyFromOffense={onRemovePropertyFromOffense}
          />
        ))}
      </Box>

      {/* Add Property Offense Selection Menu */}
      <Menu
        anchorEl={addPropertyMenuAnchor}
        open={Boolean(addPropertyMenuAnchor)}
        onClose={handleAddPropertyMenuClose}
        anchorOrigin={{
          vertical: "bottom",
          horizontal: "left",
        }}
        transformOrigin={{
          vertical: "top",
          horizontal: "left",
        }}
        sx={{
          mt: 0.25,
          "& .MuiPaper-root": {
            borderRadius: "8px",
            minWidth: "300px",
          },
          "& .MuiMenuItem-root": {
            py: 1.5,
            display: "flex",
            alignItems: "center",
            gap: 1.5,
          },
        }}
      >
        {addedOffenses?.map((addedOffense) => (
          <MenuItem
            key={addedOffense.id}
            onClick={() => handleSelectOffenseForProperty(addedOffense.id)}
          >
            <Typography style="body2" color={colors.grey[900]}>
              {addedOffense.offense.citation} {addedOffense.offense.literal}
            </Typography>
          </MenuItem>
        ))}
      </Menu>
    </>
  );
};

export default PropertiesSection;
