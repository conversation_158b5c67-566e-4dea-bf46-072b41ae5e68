import { Button } from "@/design-system/components/Button";
import { Dropdown } from "@/design-system/components/Dropdown";
import {
  DropdownOption,
  TextInput,
} from "@/design-system/components/TextInput";
import { Typography } from "@/design-system/components/Typography";
import { colors } from "@/design-system/tokens";
import AddIcon from "@mui/icons-material/Add";
import DeleteIcon from "@mui/icons-material/Delete";
import EditIcon from "@mui/icons-material/Edit";
import ExpandLessIcon from "@mui/icons-material/ExpandLess";
import ExpandMoreIcon from "@mui/icons-material/ExpandMore";
import MoreVertIcon from "@mui/icons-material/MoreVert";
import OpenInNewIcon from "@mui/icons-material/OpenInNew";
import {
  Box,
  Collapse,
  Dialog,
  DialogActions,
  DialogContent,
  Divider,
  IconButton,
  Menu,
  MenuItem,
} from "@mui/material";
import React, { useState } from "react";

interface OrganizationData {
  id: string;
  name: string;
  type: string;
  streetAddress: string;
  state: string;
  zIP: string;
}

interface OrganizationsSectionProps {
  organizations: OrganizationData[];
  relations?: any[];
  addedOffenses?: any[];
  readOnly?: boolean;
  onSetActiveOrganizationOffenseContext?: (
    context: { offenseId: string } | null
  ) => void;
  onOpenSidePanel?: (panelType: "ORGANIZATION") => void;
  onEntityEdit?: (entityId: string, entityType: "organization") => void;
  onRemoveOrganizationFromReport?: (
    organizationId: string,
    entityType: "organization"
  ) => void;
  onAddOrganizationToOffense?: (
    organizationId: string,
    offenseId: string
  ) => void;
  onRemoveOrganizationFromOffense?: (
    organizationId: string,
    offenseId: string
  ) => void;
}

// Individual Organization Card Component
interface OrganizationCardProps {
  organization: OrganizationData;
  relations?: any[];
  addedOffenses?: any[];
  readOnly?: boolean;
  onEntityEdit?: (entityId: string, entityType: "organization") => void;
  onRemoveOrganizationFromReport?: (
    organizationId: string,
    entityType: "organization"
  ) => void;
  onAddOrganizationToOffense?: (
    organizationId: string,
    offenseId: string
  ) => void;
  onRemoveOrganizationFromOffense?: (
    organizationId: string,
    offenseId: string
  ) => void;
}

const OrganizationCard: React.FC<OrganizationCardProps> = ({
  organization,
  relations = [],
  addedOffenses = [],
  readOnly = false,
  onEntityEdit,
  onRemoveOrganizationFromReport,
  onAddOrganizationToOffense,
  onRemoveOrganizationFromOffense,
}) => {
  const [isExpanded, setIsExpanded] = useState(true);
  const [menuAnchorEl, setMenuAnchorEl] = useState<null | HTMLElement>(null);
  const [removeModalOpen, setRemoveModalOpen] = useState(false);

  // Helper function to get related offenses for this organization
  const getRelatedOffenses = (organizationId: string): string[] => {
    if (!relations || !addedOffenses) return [];

    const offenseIds = relations
      .filter((rel: any) => {
        const isOrganizationRelation =
          rel.relationType === "RELATION_TYPE_OFFENSE_ORGANIZATION";
        const isOrganizationInvolved =
          (rel.objectA?.objectType === "entity" &&
            rel.objectA?.globalId === organizationId) ||
          (rel.objectB?.objectType === "entity" &&
            rel.objectB?.globalId === organizationId);

        return isOrganizationRelation && isOrganizationInvolved;
      })
      .map((rel: any) => {
        if (rel.objectA?.objectType === "offense")
          return rel.objectA.reportScopedId;
        if (rel.objectB?.objectType === "offense")
          return rel.objectB.reportScopedId;
        return null;
      })
      .filter(Boolean);

    return offenseIds;
  };

  // Helper function to create offense dropdown options
  const getOffenseOptions = (): DropdownOption[] => {
    if (!addedOffenses) return [];

    return addedOffenses.map((addedOffense) => ({
      value: addedOffense.id,
      label: `${addedOffense.offense.citation} ${addedOffense.offense.literal}`,
    }));
  };

  // Helper function to get selected offense options for this organization
  const getSelectedOffenseOptions = (
    organizationId: string
  ): DropdownOption[] => {
    const relatedOffenseIds = getRelatedOffenses(organizationId);
    return getOffenseOptions().filter((option) =>
      relatedOffenseIds.includes(option.value)
    );
  };

  const handleMenuClick = (event: React.MouseEvent<HTMLElement>) => {
    event.stopPropagation();
    setMenuAnchorEl(event.currentTarget);
  };

  const handleMenuClose = () => {
    setMenuAnchorEl(null);
  };

  const handleOpenRecord = () => {
    window.open(`/entity?entityId=${organization.id}`, "_blank");
    setMenuAnchorEl(null);
  };

  const handleEditEntity = () => {
    onEntityEdit?.(organization.id, "organization");
    setMenuAnchorEl(null);
  };

  const handleRemoveClick = () => {
    setRemoveModalOpen(true);
  };

  const handleRemoveCancel = () => {
    setRemoveModalOpen(false);
  };

  const handleRemoveConfirm = () => {
    onRemoveOrganizationFromReport?.(organization.id, "organization");
    setRemoveModalOpen(false);
  };

  const handleAddToOffense = (offenseId: string) => {
    onAddOrganizationToOffense?.(organization.id, offenseId);
    setMenuAnchorEl(null);
  };

  const handleRemoveFromOffense = (offenseId: string) => {
    onRemoveOrganizationFromOffense?.(organization.id, offenseId);
    setMenuAnchorEl(null);
  };

  const selectedOffenseOptions = getSelectedOffenseOptions(organization.id);
  const offenseOptions = getOffenseOptions();

  return (
    <Box
      sx={{
        border: `1px solid ${colors.grey[200]}`,
        borderRadius: "8px",
        backgroundColor: "#FFF",
        mb: 2,
      }}
    >
      {/* Header */}
      <Box
        sx={{
          display: "flex",
          justifyContent: "space-between",
          alignItems: "center",
          p: 2,
          cursor: "pointer",
        }}
        onClick={() => setIsExpanded(!isExpanded)}
      >
        <Box sx={{ display: "flex", alignItems: "center", gap: 1 }}>
          {isExpanded ? (
            <ExpandLessIcon sx={{ color: colors.grey[500] }} />
          ) : (
            <ExpandMoreIcon sx={{ color: colors.grey[500] }} />
          )}
          <Typography style="h3" color={colors.grey[900]}>
            {organization.name}
          </Typography>
        </Box>

        <Box sx={{ display: "flex", alignItems: "center", gap: 1 }}>
          {selectedOffenseOptions.length > 0 && (
            <Typography style="body2" color={colors.grey[600]}>
              {selectedOffenseOptions.length} offense(s)
            </Typography>
          )}
          <IconButton
            size="small"
            onClick={handleMenuClick}
            sx={{ color: colors.grey[500] }}
          >
            <MoreVertIcon />
          </IconButton>
        </Box>
      </Box>

      {/* Expanded Content */}
      <Collapse in={isExpanded}>
        <Box sx={{ px: 2, pb: 2 }}>
          {/* Organization Details */}
          <Box sx={{ mb: 2 }}>
            <TextInput
              title="Organization Type"
              value={organization.type}
              readOnly
            />
            <TextInput
              title="Street Address"
              value={organization.streetAddress}
              readOnly
            />
            <TextInput title="State" value={organization.state} readOnly />
            <TextInput title="ZIP Code" value={organization.zIP} readOnly />
          </Box>

          {/* Related Offenses */}
          {selectedOffenseOptions.length > 0 && (
            <Box sx={{ mb: 2 }}>
              <Typography style="h4" color={colors.grey[900]}>
                Related Offenses
              </Typography>
              {selectedOffenseOptions.map((option) => (
                <Box
                  key={option.value}
                  sx={{
                    display: "flex",
                    justifyContent: "space-between",
                    alignItems: "center",
                    p: 1,
                    backgroundColor: colors.grey[50],
                    borderRadius: "4px",
                    mb: 1,
                  }}
                >
                  <Typography style="body2" color={colors.grey[700]}>
                    {option.label}
                  </Typography>
                  {!readOnly && (
                    <IconButton
                      size="small"
                      onClick={() => handleRemoveFromOffense(option.value)}
                      sx={{ color: colors.rose[600] }}
                    >
                      <DeleteIcon fontSize="small" />
                    </IconButton>
                  )}
                </Box>
              ))}
            </Box>
          )}
        </Box>
      </Collapse>

      {/* Menu */}
      <Menu
        anchorEl={menuAnchorEl}
        open={Boolean(menuAnchorEl)}
        onClose={handleMenuClose}
        anchorOrigin={{
          vertical: "bottom",
          horizontal: "right",
        }}
        transformOrigin={{
          vertical: "top",
          horizontal: "right",
        }}
      >
        <MenuItem onClick={handleOpenRecord}>
          <OpenInNewIcon sx={{ mr: 1, fontSize: 16 }} />
          <Typography style="body2">Open Record</Typography>
        </MenuItem>
        {!readOnly && (
          <MenuItem onClick={handleEditEntity}>
            <EditIcon sx={{ mr: 1, fontSize: 16 }} />
            <Typography style="body2">Edit</Typography>
          </MenuItem>
        )}
        {!readOnly && offenseOptions.length > 0 && (
          <MenuItem>
            <AddIcon sx={{ mr: 1, fontSize: 16 }} />
            <Dropdown
              title="Add to Offense"
              options={offenseOptions}
              value=""
              onChange={(value: string | null) =>
                value && handleAddToOffense(value)
              }
              placeholder="Select offense..."
            />
          </MenuItem>
        )}
        {!readOnly && (
          <MenuItem onClick={handleRemoveClick}>
            <DeleteIcon sx={{ mr: 1, fontSize: 16 }} />
            <Typography style="body2" color={colors.rose[600]}>
              Remove from Report
            </Typography>
          </MenuItem>
        )}
      </Menu>

      {/* Remove Confirmation Dialog */}
      <Dialog open={removeModalOpen} onClose={handleRemoveCancel}>
        <DialogContent>
          <Typography style="h4" color={colors.grey[900]}>
            Remove Organization
          </Typography>
          <Typography style="body1" color={colors.grey[700]}>
            {organization.name || organization.type || "Unknown Organization"}{" "}
            will be deleted from the report and all offenses
          </Typography>
        </DialogContent>
        <DialogActions>
          <Button
            label="Cancel"
            color="grey"
            prominence={false}
            onClick={handleRemoveCancel}
          />
          <Button
            label="Remove"
            color="rose"
            prominence={true}
            onClick={handleRemoveConfirm}
          />
        </DialogActions>
      </Dialog>
    </Box>
  );
};

const OrganizationsSection: React.FC<OrganizationsSectionProps> = ({
  organizations,
  relations,
  addedOffenses,
  readOnly = false,
  onSetActiveOrganizationOffenseContext,
  onOpenSidePanel,
  onEntityEdit,
  onRemoveOrganizationFromReport,
  onAddOrganizationToOffense,
  onRemoveOrganizationFromOffense,
}) => {
  const [addOrganizationMenuAnchor, setAddOrganizationMenuAnchor] =
    useState<null | HTMLElement>(null);

  const handleAddOrganizationClick = (event: React.MouseEvent<HTMLElement>) => {
    if (!addedOffenses || addedOffenses.length === 0) return;

    if (addedOffenses.length === 1) {
      // If only one offense, directly set the active organization offense context
      onSetActiveOrganizationOffenseContext?.({
        offenseId: addedOffenses[0].id,
      });
      // Open the side panel
      onOpenSidePanel?.("ORGANIZATION");
    } else {
      // If multiple offenses, show menu to select
      setAddOrganizationMenuAnchor(event.currentTarget);
    }
  };

  const handleAddOrganizationMenuClose = () => {
    setAddOrganizationMenuAnchor(null);
  };

  const handleSelectOffenseForOrganization = (offenseId: string) => {
    onSetActiveOrganizationOffenseContext?.({
      offenseId,
    });
    // Open the side panel
    onOpenSidePanel?.("ORGANIZATION");
    setAddOrganizationMenuAnchor(null);
  };

  return (
    <>
      <Divider sx={{ borderColor: colors.grey[300], mt: 4, mb: 3 }} />
      <Box sx={{ mb: 4 }}>
        <Box
          sx={{
            display: "flex",
            justifyContent: "space-between",
            alignItems: "center",
            mb: 3,
          }}
        >
          <Typography style="h1" color={colors.grey[900]}>
            All Organizations
          </Typography>
          {!readOnly && addedOffenses && addedOffenses.length > 0 && (
            <Button
              onClick={handleAddOrganizationClick}
              label="Add Organization"
              leftIcon={<AddIcon />}
              size="small"
            />
          )}
        </Box>
        {organizations.map((organization) => (
          <OrganizationCard
            key={organization.id}
            organization={organization}
            relations={relations}
            addedOffenses={addedOffenses}
            readOnly={readOnly}
            onEntityEdit={onEntityEdit}
            onRemoveOrganizationFromReport={onRemoveOrganizationFromReport}
            onAddOrganizationToOffense={onAddOrganizationToOffense}
            onRemoveOrganizationFromOffense={onRemoveOrganizationFromOffense}
          />
        ))}
      </Box>

      {/* Add Organization Offense Selection Menu */}
      <Menu
        anchorEl={addOrganizationMenuAnchor}
        open={Boolean(addOrganizationMenuAnchor)}
        onClose={handleAddOrganizationMenuClose}
        anchorOrigin={{
          vertical: "bottom",
          horizontal: "right",
        }}
        transformOrigin={{
          vertical: "top",
          horizontal: "right",
        }}
      >
        {addedOffenses?.map((offense) => (
          <MenuItem
            key={offense.id}
            onClick={() => handleSelectOffenseForOrganization(offense.id)}
          >
            <Typography style="body2" color={colors.grey[700]}>
              {offense.offense.citation} {offense.offense.literal}
            </Typography>
          </MenuItem>
        ))}
      </Menu>
    </>
  );
};

export default OrganizationsSection;
