import { Button } from "@/design-system/components/Button";
import { Typography } from "@/design-system/components/Typography";
import { colors } from "@/design-system/tokens";
import AddIcon from "@mui/icons-material/Add";
import { Box } from "@mui/material";
import { useEffect, useRef, useState } from "react";
import {
  useListReportSections,
  useUpdateReportSection,
} from "../../../apis/services/workflow/reports/v2/hooks";
import ArrestFormCard from "./ArrestFormCard";
import { PersonData } from "./constants";

interface ArrestSectionProps {
  reportId?: string;
  onSaveStatusChange?: (status: {
    isSaving: boolean;
    hasUnsavedChanges: boolean;
    source: string;
  }) => void;
  readOnly?: boolean;
  people?: PersonData[];
  relations?: any[];
  associatedCases?: any[];
  onOpenSidePanel?: (panelType: "PERSON") => void;
  onAddPersonToArrest?: (personId: string, arrestId: string) => void;
  onRemovePersonFromArrest?: (personId: string, arrestId: string) => void;
  onRemovePersonFromReport?: (personId: string, entityType: "person") => void;
  onSetActiveArrestRelation?: (context: { arrestId: string } | null) => void;
  onEntityEdit?: (entityId: string, entityType: "person") => void;
}

interface AddedArrest {
  id: string;
  data?: any;
}

export default function ArrestSection({
  reportId,
  onSaveStatusChange,
  readOnly = false,
  people,
  relations,
  associatedCases,
  onOpenSidePanel,
  onAddPersonToArrest,
  onRemovePersonFromArrest,
  onRemovePersonFromReport,
  onSetActiveArrestRelation,
  onEntityEdit,
}: ArrestSectionProps) {
  const [addedArrests, setAddedArrests] = useState<AddedArrest[]>([]);
  const [arrestSectionId, setArrestSectionId] = useState<string | null>(null);
  const [arrestSection, setArrestSection] = useState<any>(null);
  const [isSaving, setIsSaving] = useState(false);
  const [hasUnsavedChanges, setHasUnsavedChanges] = useState(false);

  // Ref for scrolling to new arrest
  const arrestsContainerRef = useRef<HTMLDivElement>(null);

  // Use a ref to track previous status
  const lastStatusRef = useRef<{
    isSaving: boolean;
    hasUnsavedChanges: boolean;
  } | null>(null);

  // Notify parent on save/dirty changes
  useEffect(() => {
    if (!onSaveStatusChange) return;

    const currentStatus = { isSaving, hasUnsavedChanges };
    const lastStatus = lastStatusRef.current;
    const changed =
      !lastStatus ||
      lastStatus.isSaving !== currentStatus.isSaving ||
      lastStatus.hasUnsavedChanges !== currentStatus.hasUnsavedChanges;

    if (changed) {
      lastStatusRef.current = { ...currentStatus };
      onSaveStatusChange({ ...currentStatus, source: "arrests" });
    }
  }, [isSaving, hasUnsavedChanges, onSaveStatusChange]);

  // Fetch report sections to get the arrest section
  const { data: reportSections } = useListReportSections(reportId || "", {
    enabled: !!reportId,
    staleTime: 5 * 60 * 1000,
    refetchOnWindowFocus: true,
  });

  // Update report section mutation
  const updateReportSectionMutation = useUpdateReportSection({
    onSuccess: (data) => {
      setArrestSection(data);
      setArrestSectionId(data.id);
      setIsSaving(false);
      setHasUnsavedChanges(false);
    },
    onError: (error) => {
      console.error("Error updating arrest section:", error);
      setIsSaving(false);
    },
  });

  // Load existing arrests from the report section
  useEffect(() => {
    if (reportSections?.sections) {
      const foundSection = reportSections.sections.find(
        // @ts-expect-error TODO: Fix type issue
        (section) => section.type === "SECTION_TYPE_ARREST"
      );

      if (foundSection) {
        setArrestSection(foundSection);
        setArrestSectionId(foundSection.id);

        // Load existing arrests
        // @ts-expect-error TODO: Fix type issue
        const arrestList = foundSection.arrestList;
        if (arrestList?.arrests && Array.isArray(arrestList.arrests)) {
          const existingArrests = arrestList.arrests.map((arrest: any) => ({
            id: arrest.id,
            data: arrest.data || {},
          }));
          setAddedArrests(existingArrests);
        }
      }
    }
  }, [reportSections]);

  const handleAddArrest = () => {
    const newArrestId = `arrest_${Date.now()}`;
    const newArrest: AddedArrest = {
      id: newArrestId,
      data: {},
    };

    setAddedArrests((prev) => [...prev, newArrest]);
    setHasUnsavedChanges(true);

    // Auto-save the new arrest list
    const newArrestsList = [...addedArrests, newArrest];
    saveArrestListToBackend(newArrestsList);

    // Scroll to new arrest after state update (leave 100px above)
    setTimeout(() => {
      const elem = arrestsContainerRef.current
        ?.lastElementChild as HTMLElement | null;
      if (!elem) return;
      const getScrollParent = (node: HTMLElement | null): HTMLElement => {
        let p: HTMLElement | null = node;
        while (p && p !== document.body) {
          const style = getComputedStyle(p);
          const hasScrollableContent = p.scrollHeight > p.clientHeight;
          const overflowYScrollable = /(auto|scroll)/.test(style.overflowY);
          if (hasScrollableContent && overflowYScrollable) {
            return p;
          }
          p = p.parentElement;
        }
        return document.documentElement;
      };

      const scrollParent = getScrollParent(elem);
      const elemTop = elem.getBoundingClientRect().top + window.scrollY;
      const targetTop = Math.max(elemTop - 100, 0);

      if (scrollParent === document.documentElement) {
        window.scrollTo({ top: targetTop, behavior: "smooth" });
      } else {
        scrollParent.scrollTo({
          top:
            targetTop -
            scrollParent.getBoundingClientRect().top +
            scrollParent.scrollTop,
          behavior: "smooth",
        });
      }
    }, 100);
  };

  const saveArrestListToBackend = (arrestsList: AddedArrest[]) => {
    if (!reportId || !arrestSectionId) return;

    setIsSaving(true);

    const arrestsData = arrestsList.map((arrest) => ({
      id: arrest.id,
      arrestType: "default",
      data: arrest.data || {},
      schema: {},
    }));

    const updatedSection = {
      id: arrestSectionId,
      type: "SECTION_TYPE_ARREST",
      arrestList: {
        id: `arrest_content_${Date.now()}`,
        arrests: arrestsData,
        metadata: {},
      },
      reportId,
    };

    updateReportSectionMutation.mutate({
      reportId,
      // @ts-expect-error TODO: Fix type issue
      section: updatedSection,
    });
  };

  const handleSaveArrest = (arrestId: string, formData: any) => {
    const updatedArrests = addedArrests.map((arrest) =>
      arrest.id === arrestId ? { ...arrest, data: formData } : arrest
    );
    setAddedArrests(updatedArrests);
    setHasUnsavedChanges(true);
    saveArrestListToBackend(updatedArrests);
  };

  const handleDeleteArrest = (arrestId: string) => {
    const updatedArrests = addedArrests.filter(
      (arrest) => arrest.id !== arrestId
    );
    setAddedArrests(updatedArrests);
    setHasUnsavedChanges(true);
    saveArrestListToBackend(updatedArrests);
  };

  const handleArrestFormSaveStatusChange = (status: {
    isSaving: boolean;
    hasUnsavedChanges: boolean;
    source: string;
  }) => {
    // Update local state based on child component status
    setIsSaving(status.isSaving);
    setHasUnsavedChanges(status.hasUnsavedChanges);
  };

  return (
    <Box>
      {/* Header */}
      <Box
        sx={{
          display: "flex",
          justifyContent: "space-between",
          alignItems: "center",
          py: 2,
        }}
      >
        <Typography style="h1" color={colors.grey[900]}>
          Arrestees
        </Typography>

        {!readOnly && (
          <Button
            onClick={handleAddArrest}
            label="Add"
            leftIcon={<AddIcon />}
          />
        )}
      </Box>

      {/* Added Arrest Cards */}
      {addedArrests.length > 0 && (
        <Box ref={arrestsContainerRef} sx={{ mt: 3 }}>
          {addedArrests.map((arrest, index) => (
            <ArrestFormCard
              key={arrest.id}
              arrest={arrest}
              arrestId={arrest.id}
              arrestIndex={index}
              onSave={handleSaveArrest}
              onDelete={handleDeleteArrest}
              onSaveStatusChange={handleArrestFormSaveStatusChange}
              readOnly={readOnly}
              initialData={arrest.data}
              people={people}
              relations={relations}
              reportId={reportId}
              associatedCases={associatedCases}
              onAddPersonToArrest={onAddPersonToArrest}
              onRemovePersonFromArrest={onRemovePersonFromArrest}
              onRemovePersonFromReport={onRemovePersonFromReport}
              onOpenSidePanel={onOpenSidePanel}
              onQuickAddPersonToArrest={onAddPersonToArrest}
              onEntityEdit={onEntityEdit}
              onSetActiveArrestRelation={onSetActiveArrestRelation}
            />
          ))}
        </Box>
      )}
    </Box>
  );
}
