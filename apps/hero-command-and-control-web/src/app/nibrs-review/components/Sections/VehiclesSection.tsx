import { Button } from "@/design-system/components/Button";
import {
  DropdownOption,
  InputType,
  TextInput,
} from "@/design-system/components/TextInput";
import { Typography } from "@/design-system/components/Typography";
import { colors } from "@/design-system/tokens";
import AddIcon from "@mui/icons-material/Add";
import DeleteIcon from "@mui/icons-material/Delete";
import EditIcon from "@mui/icons-material/Edit";
import ExpandLessIcon from "@mui/icons-material/ExpandLess";
import ExpandMoreIcon from "@mui/icons-material/ExpandMore";
import MoreVertIcon from "@mui/icons-material/MoreVert";
import OpenInNewIcon from "@mui/icons-material/OpenInNew";
import {
  Box,
  Collapse,
  Dialog,
  DialogActions,
  DialogContent,
  Divider,
  IconButton,
  Menu,
  MenuItem,
} from "@mui/material";
import React, { useState } from "react";
import { VehicleData } from "./constants";

interface VehiclesSectionProps {
  vehicles: VehicleData[];
  relations?: any[];
  addedOffenses?: any[];
  readOnly?: boolean;
  onSetActiveVehicleOffenseContext?: (
    context: { offenseId: string } | null
  ) => void;
  onOpenSidePanel?: (panelType: "VEHICLE") => void;
  onEntityEdit?: (entityId: string, entityType: "vehicle") => void;
  onRemoveVehicleFromReport?: (
    vehicleId: string,
    entityType: "vehicle"
  ) => void;
  onAddVehicleToOffense?: (vehicleId: string, offenseId: string) => void;
  onRemoveVehicleFromOffense?: (vehicleId: string, offenseId: string) => void;
}

// Individual Vehicle Card Component
interface VehicleCardProps {
  vehicle: VehicleData;
  relations?: any[];
  addedOffenses?: any[];
  readOnly?: boolean;
  onEntityEdit?: (entityId: string, entityType: "vehicle") => void;
  onRemoveVehicleFromReport?: (
    vehicleId: string,
    entityType: "vehicle"
  ) => void;
  onAddVehicleToOffense?: (vehicleId: string, offenseId: string) => void;
  onRemoveVehicleFromOffense?: (vehicleId: string, offenseId: string) => void;
}

const VehicleCard: React.FC<VehicleCardProps> = ({
  vehicle,
  relations = [],
  addedOffenses = [],
  readOnly = false,
  onEntityEdit,
  onRemoveVehicleFromReport,
  onAddVehicleToOffense,
  onRemoveVehicleFromOffense,
}) => {
  const [isExpanded, setIsExpanded] = useState(true);
  const [menuAnchorEl, setMenuAnchorEl] = useState<null | HTMLElement>(null);
  const [removeModalOpen, setRemoveModalOpen] = useState(false);

  // Helper function to get related offenses for this vehicle
  const getRelatedOffenses = (vehicleId: string): string[] => {
    if (!relations || !addedOffenses) return [];

    const offenseIds = relations
      .filter((rel: any) => {
        const isVehicleRelation =
          rel.relationType === "RELATION_TYPE_OFFENSE_VEHICLE";
        const isVehicleInvolved =
          (rel.objectA?.objectType === "entity" &&
            rel.objectA?.globalId === vehicleId) ||
          (rel.objectB?.objectType === "entity" &&
            rel.objectB?.globalId === vehicleId);

        return isVehicleRelation && isVehicleInvolved;
      })
      .map((rel: any) => {
        if (rel.objectA?.objectType === "offense")
          return rel.objectA.reportScopedId;
        if (rel.objectB?.objectType === "offense")
          return rel.objectB.reportScopedId;
        return null;
      })
      .filter(Boolean);

    return offenseIds;
  };

  // Helper function to create offense dropdown options
  const getOffenseOptions = (): DropdownOption[] => {
    if (!addedOffenses) return [];

    return addedOffenses.map((addedOffense) => ({
      value: addedOffense.id,
      label: `${addedOffense.offense.citation} ${addedOffense.offense.literal}`,
    }));
  };

  // Helper function to get selected offense options for this vehicle
  const getSelectedOffenseOptions = (vehicleId: string): DropdownOption[] => {
    const relatedOffenseIds = getRelatedOffenses(vehicleId);
    const offenseOptions = getOffenseOptions();

    return offenseOptions.filter((option) =>
      relatedOffenseIds.includes(option.value)
    );
  };

  // Handle offense selection changes
  const handleOffenseSelectionChange = (selectedOptions: DropdownOption[]) => {
    if (readOnly) return;

    const currentOffenseIds = getRelatedOffenses(vehicle.id);
    const newOffenseIds = selectedOptions.map((option) => option.value);

    // Find offenses to add (in new selection but not in current)
    const offensesToAdd = newOffenseIds.filter(
      (offenseId) => !currentOffenseIds.includes(offenseId)
    );

    // Find offenses to remove (in current but not in new selection)
    const offensesToRemove = currentOffenseIds.filter(
      (offenseId) => !newOffenseIds.includes(offenseId)
    );

    // Add new offense relations
    offensesToAdd.forEach((offenseId) => {
      if (onAddVehicleToOffense) {
        onAddVehicleToOffense(vehicle.id, offenseId);
      }
    });

    // Remove offense relations
    offensesToRemove.forEach((offenseId) => {
      if (onRemoveVehicleFromOffense) {
        onRemoveVehicleFromOffense(vehicle.id, offenseId);
      }
    });
  };

  const selectedOffenseOptions = getSelectedOffenseOptions(vehicle.id);
  const offenseOptions = getOffenseOptions();

  const handleMenuClick = (event: React.MouseEvent<HTMLElement>) => {
    event.stopPropagation();
    setMenuAnchorEl(event.currentTarget);
  };

  const handleMenuClose = () => {
    setMenuAnchorEl(null);
  };

  const handleOpenRecord = () => {
    window.open(`/entity?entityId=${vehicle.id}`, "_blank");
    setMenuAnchorEl(null);
  };

  const handleEditEntity = () => {
    onEntityEdit?.(vehicle.id, "vehicle");
    setMenuAnchorEl(null);
  };

  const handleRemoveClick = () => {
    setRemoveModalOpen(true);
    setMenuAnchorEl(null);
  };

  const handleRemoveCancel = () => {
    setRemoveModalOpen(false);
  };

  const handleRemoveConfirm = () => {
    onRemoveVehicleFromReport?.(vehicle.id, "vehicle");
    setRemoveModalOpen(false);
  };

  return (
    <Box
      sx={{
        display: "flex",
        width: "100%",
        flexDirection: "column",
        alignItems: "flex-start",
        borderRadius: "12px",
        border: `1px solid ${colors.grey[200]}`,
        background: "#FFF",
        overflow: "hidden",
        boxShadow: "0px 0px 16px 0px rgba(0, 0, 0, 0.04)",
        mb: 3,
      }}
    >
      {/* Header */}
      <Box
        sx={{
          display: "flex",
          width: "100%",
          justifyContent: "space-between",
          alignItems: "center",
          padding: "16px 24px",
          borderBottom: `1px solid ${colors.grey[200]}`,
          backgroundColor: colors.grey[50],
        }}
      >
        <Box sx={{ display: "flex", alignItems: "center", gap: 2 }}>
          <Typography style="h3" color={colors.grey[900]}>
            {vehicle.year || vehicle.make || vehicle.model
              ? `${vehicle.year || ""} ${vehicle.make || ""} ${
                  vehicle.model || ""
                }`.trim()
              : "Unknown vehicle"}
          </Typography>
        </Box>

        <Box sx={{ display: "flex", alignItems: "center", gap: 1 }}>
          {!readOnly && (
            <IconButton
              onClick={handleMenuClick}
              size="small"
              sx={{
                color: colors.grey[600],
                zIndex: 10,
                "&:hover": {
                  bgcolor: colors.grey[100],
                },
              }}
            >
              <MoreVertIcon />
            </IconButton>
          )}
          <IconButton
            onClick={() => setIsExpanded(!isExpanded)}
            size="small"
            sx={{
              color: colors.grey[600],
              "&:hover": {
                bgcolor: colors.grey[100],
              },
            }}
          >
            {isExpanded ? <ExpandLessIcon /> : <ExpandMoreIcon />}
          </IconButton>
        </Box>
      </Box>

      {/* Form Content */}
      <Collapse in={isExpanded} sx={{ width: "100%" }}>
        <Box sx={{ padding: "24px" }}>
          {/* Related Offenses Section */}
          <Box sx={{ mb: 4 }}>
            <TextInput
              title="Related Offenses"
              placeholder="Select offenses..."
              type={InputType.Dropdown}
              isMultiSelect
              options={offenseOptions}
              selectedItems={selectedOffenseOptions}
              onChangeSelectedItems={handleOffenseSelectionChange}
              readOnly={readOnly}
              chipColor="grey"
              hideSelectedFromOptions={true}
              minRequiredItems={1}
            />
          </Box>
        </Box>
      </Collapse>

      {/* Menu for vehicle actions */}
      <Menu
        anchorEl={menuAnchorEl}
        open={Boolean(menuAnchorEl)}
        onClose={handleMenuClose}
        anchorOrigin={{
          vertical: "bottom",
          horizontal: "right",
        }}
        transformOrigin={{
          vertical: "top",
          horizontal: "right",
        }}
        sx={{
          mt: 0.25,
          "& .MuiPaper-root": {
            borderRadius: "8px",
            minWidth: "200px",
          },
          "& .MuiMenuItem-root": {
            py: 1.5,
            display: "flex",
            alignItems: "center",
            gap: 1.5,
          },
        }}
      >
        <MenuItem onClick={handleEditEntity}>
          <EditIcon fontSize="small" sx={{ color: colors.grey[500] }} />
          <Typography style="body2" color={colors.grey[500]}>
            Edit entity
          </Typography>
        </MenuItem>
        <MenuItem onClick={handleOpenRecord}>
          <OpenInNewIcon fontSize="small" sx={{ color: colors.grey[500] }} />
          <Typography style="body2" color={colors.grey[500]}>
            View entity in new tab
          </Typography>
        </MenuItem>
        <MenuItem onClick={handleRemoveClick}>
          <DeleteIcon fontSize="small" sx={{ color: colors.grey[500] }} />
          <Typography style="body2" color={colors.grey[500]}>
            Remove entity
          </Typography>
        </MenuItem>
      </Menu>

      {/* Remove confirmation modal */}
      <Dialog
        open={removeModalOpen}
        onClose={handleRemoveCancel}
        maxWidth="sm"
        fullWidth
        PaperProps={{
          sx: {
            borderRadius: "12px",
            p: 1,
          },
        }}
      >
        <DialogContent sx={{ p: 3 }}>
          <Box sx={{ mb: 2 }}>
            <Typography style="h2" color={colors.grey[900]}>
              Delete{" "}
              {vehicle.year || vehicle.make || vehicle.model
                ? `${vehicle.year || ""} ${vehicle.make || ""} ${
                    vehicle.model || ""
                  }`.trim()
                : "Unknown vehicle"}
            </Typography>
          </Box>
          <Box sx={{ mb: 3 }}>
            <Typography style="body3" color={colors.grey[700]}>
              {vehicle.year || vehicle.make || vehicle.model
                ? `${vehicle.year || ""} ${vehicle.make || ""} ${
                    vehicle.model || ""
                  }`.trim()
                : "Unknown vehicle"}{" "}
              will be deleted from the report and all offenses
            </Typography>
          </Box>
        </DialogContent>

        <DialogActions sx={{ p: 3, pt: 0 }}>
          <Button
            label="Cancel"
            color="grey"
            prominence={false}
            onClick={handleRemoveCancel}
          />
          <Button
            label="Delete"
            color="blue"
            prominence={true}
            onClick={handleRemoveConfirm}
          />
        </DialogActions>
      </Dialog>
    </Box>
  );
};

const VehiclesSection: React.FC<VehiclesSectionProps> = ({
  vehicles,
  relations,
  addedOffenses,
  readOnly = false,
  onSetActiveVehicleOffenseContext,
  onOpenSidePanel,
  onEntityEdit,
  onRemoveVehicleFromReport,
  onAddVehicleToOffense,
  onRemoveVehicleFromOffense,
}) => {
  const [addVehicleMenuAnchor, setAddVehicleMenuAnchor] =
    useState<null | HTMLElement>(null);

  const handleAddVehicleClick = (event: React.MouseEvent<HTMLElement>) => {
    if (!addedOffenses || addedOffenses.length === 0) return;

    if (addedOffenses.length === 1) {
      // If only one offense, directly set the active vehicle offense context
      onSetActiveVehicleOffenseContext?.({
        offenseId: addedOffenses[0].id,
      });
      // Open the side panel
      onOpenSidePanel?.("VEHICLE");
    } else {
      // If multiple offenses, show menu to select
      setAddVehicleMenuAnchor(event.currentTarget);
    }
  };

  const handleAddVehicleMenuClose = () => {
    setAddVehicleMenuAnchor(null);
  };

  const handleSelectOffenseForVehicle = (offenseId: string) => {
    onSetActiveVehicleOffenseContext?.({
      offenseId,
    });
    // Open the side panel
    onOpenSidePanel?.("VEHICLE");
    setAddVehicleMenuAnchor(null);
  };

  return (
    <>
      <Divider sx={{ borderColor: colors.grey[300], mt: 4, mb: 3 }} />
      <Box sx={{ mb: 4 }}>
        <Box
          sx={{
            display: "flex",
            justifyContent: "space-between",
            alignItems: "center",
            mb: 3,
          }}
        >
          <Typography style="h1" color={colors.grey[900]}>
            All Vehicles
          </Typography>
          {!readOnly && addedOffenses && addedOffenses.length > 0 && (
            <Button
              onClick={handleAddVehicleClick}
              label="Add Vehicle"
              leftIcon={<AddIcon />}
              size="small"
            />
          )}
        </Box>
        {vehicles.map((vehicle) => (
          <VehicleCard
            key={vehicle.id}
            vehicle={vehicle}
            relations={relations}
            addedOffenses={addedOffenses}
            readOnly={readOnly}
            onEntityEdit={onEntityEdit}
            onRemoveVehicleFromReport={onRemoveVehicleFromReport}
            onAddVehicleToOffense={onAddVehicleToOffense}
            onRemoveVehicleFromOffense={onRemoveVehicleFromOffense}
          />
        ))}
      </Box>

      {/* Add Vehicle Offense Selection Menu */}
      <Menu
        anchorEl={addVehicleMenuAnchor}
        open={Boolean(addVehicleMenuAnchor)}
        onClose={handleAddVehicleMenuClose}
        anchorOrigin={{
          vertical: "bottom",
          horizontal: "left",
        }}
        transformOrigin={{
          vertical: "top",
          horizontal: "left",
        }}
        sx={{
          mt: 0.25,
          "& .MuiPaper-root": {
            borderRadius: "8px",
            minWidth: "300px",
          },
          "& .MuiMenuItem-root": {
            py: 1.5,
            display: "flex",
            alignItems: "center",
            gap: 1.5,
          },
        }}
      >
        {addedOffenses?.map((addedOffense) => (
          <MenuItem
            key={addedOffense.id}
            onClick={() => handleSelectOffenseForVehicle(addedOffense.id)}
          >
            <Typography style="body2" color={colors.grey[900]}>
              {addedOffense.offense.citation} {addedOffense.offense.literal}
            </Typography>
          </MenuItem>
        ))}
      </Menu>
    </>
  );
};

export default VehiclesSection;
