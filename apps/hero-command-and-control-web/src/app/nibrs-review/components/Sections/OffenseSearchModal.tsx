import React, { useState, useMemo, useEffect, useCallback } from "react";
import {
  Box,
  Modal,
  IconButton,
} from "@mui/material";
import CloseIcon from "@mui/icons-material/Close";
import ChevronRightIcon from "@mui/icons-material/ChevronRight";
import { Typography } from "@/design-system/components/Typography";
import { TextInput } from "@/design-system/components/TextInput";
import { colors } from "@/design-system/tokens";
import { matchSorter } from "match-sorter";

export interface OffenseData {
  code: string;
  literal: string;
  citation: string;
  statute: string;
  level: string;
}

interface OffenseSearchModalProps {
  open: boolean;
  onClose: () => void;
  onSelectOffense: (offense: OffenseData) => void;
}

// Parse CSV data - this will be done once when the module loads
const parseCSVData = (csvContent: string): OffenseData[] => {
  const lines = csvContent.split('\n');
  // Skip header lines (first two lines)
  const dataLines = lines.slice(2).filter(line => line.trim());
  
  return dataLines.map(line => {
    const columns = line.split(',');
    return {
      code: columns[0] || '',
      literal: columns[1] || '',
      citation: columns[2] || '',
      statute: columns[3] || '',
      level: columns[4] || '',
    };
  }).filter(offense => offense.literal.trim()); // Filter out empty literals
};

// Parse data once at module level for efficiency
let offensesCache: OffenseData[] | null = null;

const getOffensesData = async (): Promise<OffenseData[]> => {
  if (offensesCache) {
    return offensesCache;
  }
  
  try {
    // Fetch the CSV file from the public directory
    const response = await fetch('/NIBRS-Offenses.csv');
    const csvContent = await response.text();
    offensesCache = parseCSVData(csvContent);
    return offensesCache;
  } catch (error) {
    console.error('Error loading NIBRS offenses data:', error);
    return [];
  }
};

// Custom debounce hook
const useDebounce = (value: string, delay: number) => {
  const [debouncedValue, setDebouncedValue] = useState(value);

  useEffect(() => {
    const handler = setTimeout(() => {
      setDebouncedValue(value);
    }, delay);

    return () => {
      clearTimeout(handler);
    };
  }, [value, delay]);

  return debouncedValue;
};

// Convert text to title case
const toTitleCase = (str: string): string => {
  return str.toLowerCase().replace(/\b\w/g, (l) => l.toUpperCase());
};

export const OffenseSearchModal: React.FC<OffenseSearchModalProps> = ({
  open,
  onClose,
  onSelectOffense,
}) => {
  const [searchQuery, setSearchQuery] = useState("");
  const [offenses, setOffenses] = useState<OffenseData[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  
  // Debounce the search query to improve performance
  const debouncedSearchQuery = useDebounce(searchQuery, 200);

  // Load offenses data when modal opens
  useEffect(() => {
    if (open) {
      setIsLoading(true);
      getOffensesData().then(data => {
        setOffenses(data);
        setIsLoading(false);
      });
    }
  }, [open]);

  // Efficiently filter offenses using match-sorter with fuzzy matching
  const filteredOffenses = useMemo(() => {
    if (!debouncedSearchQuery.trim()) {
      return offenses.slice(0, 50); 
    }
    
    // Use match-sorter for fuzzy matching on the literal field
    const results = matchSorter(offenses, debouncedSearchQuery, {
      keys: ['literal'],
      threshold: matchSorter.rankings.CONTAINS,
    });
    
    return results.slice(0, 50); // Limit to 50 results for better performance
  }, [offenses, debouncedSearchQuery]);

  const handleOffenseSelect = useCallback((offense: OffenseData) => {
    onSelectOffense(offense);
    onClose();
    setSearchQuery(""); // Reset search when closing
  }, [onSelectOffense, onClose]);

  const handleClose = useCallback(() => {
    onClose();
    setSearchQuery(""); // Reset search when closing
  }, [onClose]);

  const handleSearchChange = useCallback((e: React.ChangeEvent<HTMLInputElement>) => {
    setSearchQuery(e.target.value);
  }, []);

  return (
    <Modal
      open={open}
      onClose={handleClose}
      aria-labelledby="offense-search-modal"
      sx={{
        display: "flex",
        alignItems: "center",
        justifyContent: "center",
      }}
    >
      <Box
        sx={{
          width: 600,
          maxWidth: "90vw",
          height: 600,
          bgcolor: "background.paper",
          borderRadius: "12px",
          boxShadow: 24,
          display: "flex",
          flexDirection: "column",
          overflow: "hidden",
        }}
        onClick={(e) => e.stopPropagation()}
      >
        {/* Header */}
        <Box
          sx={{
            display: "flex",
            alignItems: "center",
            justifyContent: "space-between",
            p: 3,
            borderBottom: `1px solid ${colors.grey[200]}`,
            flexShrink: 0,
          }}
        >
          <Typography style="h2" color={colors.grey[900]}>
            Select Offense
          </Typography>
          <IconButton
            onClick={handleClose}
            size="small"
            sx={{
              color: colors.grey[600],
              "&:hover": {
                bgcolor: colors.grey[100],
              },
            }}
          >
            <CloseIcon />
          </IconButton>
        </Box>

        {/* Search */}
        <Box sx={{ p: 3, pb: 2, flexShrink: 0 }}>
          <TextInput
            placeholder="Search offenses..."
            value={searchQuery}
            onChange={handleSearchChange}
            autoFocus
          />
        </Box>

        {/* Results - Scrollable */}
        <Box
          sx={{
            flex: 1,
            overflow: "auto",
            px: 3,
            minHeight: 0,
          }}
        >
          {isLoading ? (
            <Box sx={{ p: 3, textAlign: "center" }}>
              <Typography style="body2" color={colors.grey[500]}>
                Loading offenses...
              </Typography>
            </Box>
          ) : filteredOffenses.length === 0 ? (
            <Box sx={{ p: 3, textAlign: "center" }}>
              <Typography style="body2" color={colors.grey[500]}>
                {debouncedSearchQuery.trim() ? "No offenses found" : "Enter a search term to find offenses"}
              </Typography>
            </Box>
          ) : (
            <Box sx={{ display: "flex", flexDirection: "column", gap: 2, pb: 2 }}>
              {filteredOffenses.map((offense) => (
                <Box
                  key={offense.code}
                  sx={{
                    padding: 2,
                    border: `1px solid ${colors.grey[200]}`,
                    borderRadius: '8px',
                    cursor: "pointer",
                    backgroundColor: colors.grey[50],
                    "&:hover": { 
                      backgroundColor: colors.grey[100] 
                    },
                    "&:active": { 
                      backgroundColor: colors.grey[200] 
                    },
                    display: "flex",
                    justifyContent: "space-between",
                    alignItems: "center",
                  }}
                  onClick={() => handleOffenseSelect(offense)}
                >
                  <Box sx={{ flex: 1 }}>
                    <Box sx={{ mb: 1 }}>
                      <Typography style="body3" color={colors.grey[900]}>
                        {toTitleCase(offense.literal)}
                      </Typography>
                    </Box>
                    <Box sx={{ display: "flex", gap: 1, flexWrap: "wrap" }}>
                      <Typography style="tag2" color={colors.grey[500]}>
                        Code:
                      </Typography>
                      <Typography style="tag2" color={colors.grey[900]}>
                        {offense.code}
                      </Typography>
                      {offense.citation && (
                        <>
                          <Typography style="tag2" color={colors.grey[500]}>
                            ,
                          </Typography>
                          <Typography style="tag2" color={colors.grey[500]}>
                            Citation:
                          </Typography>
                          <Typography style="tag2" color={colors.grey[900]}>
                            {offense.citation}
                          </Typography>
                        </>
                      )}
                      {offense.level && (
                        <>
                          <Typography style="tag2" color={colors.grey[500]}>
                            ,
                          </Typography>
                          <Typography style="tag2" color={colors.grey[500]}>
                            Level:
                          </Typography>
                          <Typography style="tag2" color={colors.grey[900]}>
                            {offense.level}
                          </Typography>
                        </>
                      )}
                    </Box>
                  </Box>
                  <ChevronRightIcon
                    sx={{ color: colors.grey[500], ml: 2 }}
                  />
                </Box>
              ))}
            </Box>
          )}
        </Box>

        {/* Footer */}
        <Box
          sx={{
            p: 2,
            borderTop: `1px solid ${colors.grey[200]}`,
            bgcolor: colors.grey[50],
            textAlign: "center",
            flexShrink: 0,
          }}
        >
          <Typography style="body3" color={colors.grey[500]}>
            {debouncedSearchQuery.trim() 
              ? `Showing ${filteredOffenses.length} results`
              : `${offenses.length} total offenses available`
            }
          </Typography>
        </Box>
      </Box>
    </Modal>
  );
}; 