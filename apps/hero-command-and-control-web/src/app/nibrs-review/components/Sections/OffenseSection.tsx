import { Button } from "@/design-system/components/Button";
import { Typography } from "@/design-system/components/Typography";
import { colors } from "@/design-system/tokens";
import AddIcon from "@mui/icons-material/Add";
import { Box, Divider, Menu, MenuItem } from "@mui/material";
import React, { useEffect, useRef, useState } from "react";
import {
  useListReportSections,
  useUpdateReportSection,
} from "../../../apis/services/workflow/reports/v2/hooks";
import { PersonData, PropertyData, VehicleData } from "./constants";
import OffendersSection from "./OffendersSection";
import OffenseFormCard from "./OffenseFormCard";
import OffenseRelationsCard from "./OffenseRelationsCard";
import { OffenseData, OffenseSearchModal } from "./OffenseSearchModal";
import OrganizationsSection from "./OrganizationsSection";
import PropertiesSection from "./PropertiesSection";
import VehiclesSection from "./VehiclesSection";
import VictimsSection from "./VictimsSection";

interface OffenseSectionProps {
  reportId?: string;
  onSaveStatusChange?: (status: {
    isSaving: boolean;
    hasUnsavedChanges: boolean;
    source: string;
  }) => void;
  readOnly?: boolean;
  people?: PersonData[];
  vehicles?: VehicleData[];
  properties?: PropertyData[];
  organizations?: any[];
  relations?: any[];
  associatedCases?: any[];
  onOpenSidePanel?: (
    panelType: "PERSON" | "VEHICLE" | "PROPERTY" | "ORGANIZATION"
  ) => void;
  onAddPersonToOffense?: (
    personId: string,
    offenseId: string,
    relationType:
      | "victim"
      | "offender"
      | "witness"
      | "suspect"
      | "involved_party"
  ) => void;
  onAddVehicleToOffense?: (vehicleId: string, offenseId: string) => void;
  onAddPropertyToOffense?: (propertyId: string, offenseId: string) => void;
  onAddOrganizationToOffense?: (
    organizationId: string,
    offenseId: string
  ) => void;
  onRemovePersonFromOffense?: (personId: string, offenseId: string) => void;
  onRemoveVehicleFromOffense?: (vehicleId: string, offenseId: string) => void;
  onRemovePropertyFromOffense?: (propertyId: string, offenseId: string) => void;
  onRemoveOrganizationFromOffense?: (
    organizationId: string,
    offenseId: string
  ) => void;
  onRemovePersonFromReport?: (personId: string, entityType: "person") => void;
  onRemoveVehicleFromReport?: (
    vehicleId: string,
    entityType: "vehicle"
  ) => void;
  onRemovePropertyFromReport?: (
    propertyId: string,
    entityType: "property"
  ) => void;
  onRemoveOrganizationFromReport?: (
    organizationId: string,
    entityType: "organization"
  ) => void;
  onSetActiveOffenseRelation?: (
    context: {
      offenseId: string;
      relationType:
        | "victim"
        | "offender"
        | "witness"
        | "suspect"
        | "involved_party";
    } | null
  ) => void;
  onSetActiveVehicleOffenseContext?: (
    context: { offenseId: string } | null
  ) => void;
  onSetActivePropertyOffenseContext?: (
    context: { offenseId: string } | null
  ) => void;
  onSetActiveOrganizationOffenseContext?: (
    context: { offenseId: string } | null
  ) => void;
  onEntityEdit?: (
    entityId: string,
    entityType: "person" | "vehicle" | "property" | "organization"
  ) => void;
  onCreateVictimReportRelation?: (
    personId: string,
    reportId: string,
    entity?: any,
    victimData?: any
  ) => void;
  onUpdateVictimReportRelation?: (relationId: string, victimData: any) => void;
}

interface AddedOffense {
  id: string;
  offense: OffenseData;
  data?: any;
}

export default function OffenseSection({
  reportId,
  onSaveStatusChange,
  readOnly = false,
  people,
  vehicles,
  properties,
  organizations,
  relations,
  associatedCases,
  onOpenSidePanel,
  onAddPersonToOffense,
  onAddVehicleToOffense,
  onAddPropertyToOffense,
  onAddOrganizationToOffense,
  onRemovePersonFromOffense,
  onRemoveVehicleFromOffense,
  onRemovePropertyFromOffense,
  onRemoveOrganizationFromOffense,
  onRemovePersonFromReport,
  onRemoveVehicleFromReport,
  onRemovePropertyFromReport,
  onRemoveOrganizationFromReport,
  onSetActiveOffenseRelation,
  onSetActiveVehicleOffenseContext,
  onSetActivePropertyOffenseContext,
  onSetActiveOrganizationOffenseContext,
  onEntityEdit,
  onCreateVictimReportRelation,
  onUpdateVictimReportRelation,
}: OffenseSectionProps) {
  const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null);
  const [isOffenseModalOpen, setIsOffenseModalOpen] = useState(false);
  const [addedOffenses, setAddedOffenses] = useState<AddedOffense[]>([]);
  const [offenseSectionId, setOffenseSectionId] = useState<string | null>(null);
  const [offenseSection, setOffenseSection] = useState<any>(null);
  const [isSaving, setIsSaving] = useState(false);
  const [hasUnsavedChanges, setHasUnsavedChanges] = useState(false);

  // Store which offense is currently adding a person for relation creation
  const [activeOffenseForPerson, setActiveOffenseForPerson] = useState<{
    offenseId: string;
    relationType:
      | "victim"
      | "offender"
      | "witness"
      | "suspect"
      | "involved_party";
  } | null>(null);

  // Ref for scrolling to new offense
  const offensesContainerRef = useRef<HTMLDivElement>(null);

  const menuOpen = Boolean(anchorEl);

  // Use a ref to track previous status like IncidentDetailsCard
  const lastStatusRef = useRef<{
    isSaving: boolean;
    hasUnsavedChanges: boolean;
  } | null>(null);

  // Notify parent on save/dirty changes like IncidentDetailsCard
  useEffect(() => {
    if (!onSaveStatusChange) return;

    const currentStatus = { isSaving, hasUnsavedChanges };
    const lastStatus = lastStatusRef.current;
    const changed =
      !lastStatus ||
      lastStatus.isSaving !== currentStatus.isSaving ||
      lastStatus.hasUnsavedChanges !== currentStatus.hasUnsavedChanges;

    if (changed) {
      lastStatusRef.current = { ...currentStatus };
      onSaveStatusChange({ ...currentStatus, source: "offenses" });
    }
  }, [isSaving, hasUnsavedChanges, onSaveStatusChange]);

  // Fetch report sections to get the offense section
  const { data: reportSections } = useListReportSections(reportId || "", {
    enabled: !!reportId,
    staleTime: 5 * 60 * 1000,
    refetchOnWindowFocus: true,
  });

  // Update report section mutation
  const updateReportSectionMutation = useUpdateReportSection({
    onSuccess: (data) => {
      setOffenseSection(data);
      setOffenseSectionId(data.id);
      setIsSaving(false);
      setHasUnsavedChanges(false);
    },
    onError: (error) => {
      console.error("Error updating offense section:", error);
      setIsSaving(false);
    },
  });

  // Load existing offenses from the report section
  useEffect(() => {
    if (reportSections?.sections) {
      const foundSection = reportSections.sections.find(
        // @ts-expect-error TODO: Fix type issue
        (section) => section.type === "SECTION_TYPE_OFFENSE"
      );

      if (foundSection) {
        setOffenseSection(foundSection);
        setOffenseSectionId(foundSection.id);

        // Load existing offenses
        // @ts-expect-error TODO: Fix type issue
        const offenseList = foundSection.offenseList;
        if (offenseList?.offenses && Array.isArray(offenseList.offenses)) {
          // Load offenses using NIBRS data from the data object
          const existingOffenses = offenseList.offenses.map((offense: any) => ({
            id: offense.id,
            offense: {
              code:
                offense.data?.offense_code || offense.offense_type || "UNKNOWN",
              literal:
                offense.data?.offense_literal ||
                offense.offense_type ||
                "Unknown Offense",
              citation: offense.data?.offense_citation || "",
              statute: offense.data?.offense_statute || "",
              level: offense.data?.offense_level || "",
            },
            data: offense.data || {},
          }));
          setAddedOffenses(existingOffenses);
        }
      }
    }
  }, [reportSections]);

  const handleAddClick = (event: React.MouseEvent<HTMLElement>) => {
    if (readOnly) return;
    setAnchorEl(event.currentTarget);
  };

  const handleClose = () => {
    setAnchorEl(null);
  };

  const handleAddOffense = () => {
    handleClose();
    setIsOffenseModalOpen(true);
  };

  const handleAddIncident = () => {
    handleClose();
  };

  const handleOffenseSelect = (offense: OffenseData) => {
    // Check if offense is already added
    const isAlreadyAdded = addedOffenses.some(
      (addedOffense) => addedOffense.offense.code === offense.code
    );

    if (isAlreadyAdded) {
      console.log("Offense already added");
      return;
    }

    // Add new offense to the list
    const newOffense: AddedOffense = {
      id: `offense_${Date.now()}`,
      offense,
      data: {
        // Include NIBRS fields in data object
        offense_code: offense.code,
        offense_literal: offense.literal,
        offense_citation: offense.citation,
        offense_statute: offense.statute,
        offense_level: offense.level,
        // Default form values
        offenseWas: "completed",
        cargoTheftRelated: false,
        offenseSpecialCircumstance: "",
        justifiableHomicideCircumstance: "",
        biasMotivation: "",
        numberOfPremisesEntered: "",
        methodOfEntry: "",
        offenderSuspectedOfUsing: {
          alcohol: false,
          computerEquipment: false,
          drugsNarcotics: false,
        },
        attributes: {
          domesticAbuse: false,
          gangRelated: false,
          gamblingRelated: false,
        },
        weapons: [
          {
            id: "weapon_1",
            weaponType: "",
            isAutomatic: false,
          },
        ],
        criminalActivity: {
          buyingReceiving: false,
          cultivatingManufacturing: false,
          distributing: false,
          allocating: false,
          sharing: false,
          delivering: false,
          apportioning: false,
          dividing: false,
          dispensing: false,
          transferring: false,
        },
        gangInformation: "",
      },
    };

    setAddedOffenses((prev) => [...prev, newOffense]);
    setHasUnsavedChanges(true);

    // Immediately save the new offense
    if (reportId && offenseSectionId && offenseSection) {
      const updatedOffensesList = [...addedOffenses, newOffense];
      saveOffenseListToBackend(updatedOffensesList);
    }

    // Trigger scroll to the newly added offense (leave 100px above)
    setTimeout(() => {
      const elem = offensesContainerRef.current
        ?.lastElementChild as HTMLElement | null;
      if (!elem) return;
      const getScrollParent = (node: HTMLElement | null): HTMLElement => {
        let p: HTMLElement | null = node;
        while (p && p !== document.body) {
          const style = getComputedStyle(p);
          const hasScrollableContent = p.scrollHeight > p.clientHeight;
          const overflowYScrollable = /(auto|scroll)/.test(style.overflowY);
          if (hasScrollableContent && overflowYScrollable) {
            return p;
          }
          p = p.parentElement;
        }
        return document.documentElement;
      };

      const scrollParent = getScrollParent(elem);
      const elemTop = elem.getBoundingClientRect().top + window.scrollY;
      const targetTop = Math.max(elemTop - 100, 0);

      if (scrollParent === document.documentElement) {
        window.scrollTo({ top: targetTop, behavior: "smooth" });
      } else {
        scrollParent.scrollTo({
          top:
            targetTop -
            scrollParent.getBoundingClientRect().top +
            scrollParent.scrollTop,
          behavior: "smooth",
        });
      }
    }, 100);
  };

  // Helper function to save offense list to backend
  const saveOffenseListToBackend = (offensesList: AddedOffense[]) => {
    setIsSaving(true);

    // Prepare the offense data for backend
    const offensesData = offensesList.map((addedOffense) => ({
      id: addedOffense.id,
      offense_type: addedOffense.offense.literal,
      data: addedOffense.data || {},
    }));

    // Update the report section
    const updatedSection = {
      ...offenseSection,
      id: offenseSectionId,
      offenseList: {
        id: offenseSection?.offenseList?.id || "offense_content_01",
        offenses: offensesData,
      },
    };

    updateReportSectionMutation.mutate({
      // @ts-expect-error TODO: Fix type issue
      reportId,
      section: updatedSection,
    });
  };

  const handleSaveOffense = (offenseId: string, formData: any) => {
    if (!reportId || !offenseSectionId || readOnly) return;

    setIsSaving(true);
    setHasUnsavedChanges(false);

    // Create updated offenses list with the new form data
    const updatedOffensesList = addedOffenses.map((offense) =>
      offense.id === offenseId ? { ...offense, data: formData } : offense
    );

    // Update the local state
    setAddedOffenses(updatedOffensesList);

    // Use the helper function to save
    saveOffenseListToBackend(updatedOffensesList);
  };

  const handleDeleteOffense = (offenseId: string) => {
    if (readOnly) return;

    setIsSaving(true);

    // Remove from local state
    const updatedOffenses = addedOffenses.filter(
      (offense) => offense.id !== offenseId
    );
    setAddedOffenses(updatedOffenses);

    // Update backend if we have a section
    if (reportId && offenseSectionId && offenseSection) {
      saveOffenseListToBackend(updatedOffenses);
    } else {
      setIsSaving(false);
    }
  };

  // Handle save status changes from child components
  const handleOffenseFormSaveStatusChange = (status: {
    isSaving: boolean;
    hasUnsavedChanges: boolean;
    source: string;
  }) => {
    setIsSaving(status.isSaving);
    setHasUnsavedChanges(status.hasUnsavedChanges);
  };

  // Handle person addition from offense card
  const handleAddPersonFromOffense =
    (offenseId: string) =>
    (
      relationType:
        | "victim"
        | "offender"
        | "witness"
        | "suspect"
        | "involved_party"
    ) => {
      setActiveOffenseForPerson({ offenseId, relationType });

      // Set the active offense relation context in the parent
      if (onSetActiveOffenseRelation) {
        onSetActiveOffenseRelation({ offenseId, relationType });
      }

      if (onOpenSidePanel) {
        onOpenSidePanel("PERSON");
      }
    };

  // Handle vehicle addition from offense card
  const handleAddVehicleFromOffense = (offenseId: string) => () => {
    console.log(
      "handleAddVehicleFromOffense called with offenseId:",
      offenseId
    );
    // Set the vehicle offense context in the parent
    if (onSetActiveVehicleOffenseContext) {
      console.log("Calling onSetActiveVehicleOffenseContext");
      onSetActiveVehicleOffenseContext({ offenseId });
    } else {
      console.error("onSetActiveVehicleOffenseContext is not available");
    }

    if (onOpenSidePanel) {
      onOpenSidePanel("VEHICLE");
    }
  };

  // Handle property addition from offense card
  const handleAddPropertyFromOffense = (offenseId: string) => () => {
    console.log(
      "handleAddPropertyFromOffense called with offenseId:",
      offenseId
    );
    // Set the property offense context in the parent
    if (onSetActivePropertyOffenseContext) {
      console.log("Calling onSetActivePropertyOffenseContext");
      onSetActivePropertyOffenseContext({ offenseId });
    } else {
      console.error("onSetActivePropertyOffenseContext is not available");
    }

    if (onOpenSidePanel) {
      onOpenSidePanel("PROPERTY");
    }
  };

  // Handle organization addition from offense card
  const handleAddOrganizationFromOffense = (offenseId: string) => () => {
    console.log(
      "handleAddOrganizationFromOffense called with offenseId:",
      offenseId
    );
    // Set the organization offense context in the parent
    if (onSetActiveOrganizationOffenseContext) {
      console.log("Calling onSetActiveOrganizationOffenseContext");
      onSetActiveOrganizationOffenseContext({ offenseId });
    } else {
      console.error("onSetActiveOrganizationOffenseContext is not available");
    }

    if (onOpenSidePanel) {
      onOpenSidePanel("ORGANIZATION");
    }
  };

  // Calculate victim and offender data
  const victimIds = !relations
    ? ([] as string[])
    : relations
        .filter(
          (rel: any) => rel.relationType === "RELATION_TYPE_OFFENSE_VICTIM"
        )
        .map((rel: any) => {
          if (rel.objectA?.objectType === "entity") return rel.objectA.globalId;
          if (rel.objectB?.objectType === "entity") return rel.objectB.globalId;
          return null;
        })
        .filter(Boolean);

  const offenderIds = !relations
    ? ([] as string[])
    : relations
        .filter(
          (rel: any) => rel.relationType === "RELATION_TYPE_OFFENSE_OFFENDER"
        )
        .map((rel: any) => {
          if (rel.objectA?.objectType === "entity") return rel.objectA.globalId;
          if (rel.objectB?.objectType === "entity") return rel.objectB.globalId;
          return null;
        })
        .filter(Boolean);

  // Calculate vehicle and property data
  const vehicleIds = !relations
    ? ([] as string[])
    : relations
        .filter(
          (rel: any) => rel.relationType === "RELATION_TYPE_OFFENSE_VEHICLE"
        )
        .map((rel: any) => {
          if (rel.objectA?.objectType === "entity") return rel.objectA.globalId;
          if (rel.objectB?.objectType === "entity") return rel.objectB.globalId;
          return null;
        })
        .filter(Boolean);

  const propertyIds = !relations
    ? ([] as string[])
    : relations
        .filter(
          (rel: any) => rel.relationType === "RELATION_TYPE_OFFENSE_PROPERTY"
        )
        .map((rel: any) => {
          if (rel.objectA?.objectType === "entity") return rel.objectA.globalId;
          if (rel.objectB?.objectType === "entity") return rel.objectB.globalId;
          return null;
        })
        .filter(Boolean);

  const organizationIds = !relations
    ? ([] as string[])
    : relations
        .filter(
          (rel: any) =>
            rel.relationType === "RELATION_TYPE_OFFENSE_ORGANIZATION"
        )
        .map((rel: any) => {
          if (rel.objectA?.objectType === "entity") return rel.objectA.globalId;
          if (rel.objectB?.objectType === "entity") return rel.objectB.globalId;
          return null;
        })
        .filter(Boolean);

  const victimPeople = (people || []).filter((p) => victimIds.includes(p.id));

  const offenderPeople = (people || []).filter((p) =>
    offenderIds.includes(p.id)
  );

  const relatedVehicles = (vehicles || []).filter((v) =>
    vehicleIds.includes(v.id)
  );

  const relatedProperties = (properties || []).filter((p) =>
    propertyIds.includes(p.id)
  );

  const relatedOrganizations = (organizations || []).filter((o) =>
    organizationIds.includes(o.id)
  );

  // Calculate victim-offender pairs
  const victimOffenderPairs = (() => {
    const pairs: any[] = [];

    // Group victim & offender person IDs by offenseId
    const offenseMap: Record<
      string,
      { victims: string[]; offenders: string[] }
    > = {};

    (relations || []).forEach((rel: any) => {
      const isVictimRel = rel.relationType === "RELATION_TYPE_OFFENSE_VICTIM";
      const isOffenderRel =
        rel.relationType === "RELATION_TYPE_OFFENSE_OFFENDER";
      if (!isVictimRel && !isOffenderRel) return;

      // Determine offenseId and personId from relation
      let offenseId: string | null = null;
      let personId: string | null = null;

      if (rel.objectA?.objectType === "offense")
        offenseId = rel.objectA.reportScopedId;
      if (rel.objectB?.objectType === "offense")
        offenseId = rel.objectB.reportScopedId;

      if (rel.objectA?.objectType === "entity") personId = rel.objectA.globalId;
      if (rel.objectB?.objectType === "entity") personId = rel.objectB.globalId;

      if (!offenseId || !personId) return;

      if (!offenseMap[offenseId]) {
        offenseMap[offenseId] = { victims: [], offenders: [] };
      }

      if (isVictimRel) offenseMap[offenseId].victims.push(personId);
      if (isOffenderRel) offenseMap[offenseId].offenders.push(personId);
    });

    const existingVO = (relations || []).filter(
      (rel: any) =>
        rel.objectA?.objectType === "entity" &&
        rel.objectB?.objectType === "entity"
    );

    Object.entries(offenseMap).forEach(([offenseId, group]) => {
      group.victims.forEach((vId) => {
        group.offenders.forEach((oId) => {
          const victim = (people || []).find((p) => p.id === vId);
          const offender = (people || []).find((p) => p.id === oId);
          if (!victim || !offender) return;

          const existing = existingVO.find(
            (rel: any) =>
              (rel.objectA.globalId === vId && rel.objectB.globalId === oId) ||
              (rel.objectA.globalId === oId && rel.objectB.globalId === vId)
          );

          pairs.push({
            victim,
            offender,
            existingRelationId: existing?.id,
            relationType: existing?.relationType,
            existingRelation: existing || null,
            offenseId,
          });
        });
      });
    });

    return pairs;
  })();

  return (
    <Box>
      {/* Top Divider */}
      <Divider sx={{ borderColor: colors.grey[300], mb: 3 }} />

      {/* Header */}
      <Box
        sx={{
          display: "flex",
          justifyContent: "space-between",
          alignItems: "center",
          py: 2,
        }}
      >
        <Typography style="h1" color={colors.grey[900]}>
          Offenses
        </Typography>

        {!readOnly && (
          <Button onClick={handleAddClick} label="Add" leftIcon={<AddIcon />} />
        )}
      </Box>

      {/* Added Offense Cards - WITHOUT embedded entity lists */}
      {addedOffenses.length > 0 && (
        <Box ref={offensesContainerRef} sx={{ mt: 3 }}>
          {addedOffenses.map((addedOffense) => (
            <OffenseFormCard
              key={addedOffense.id}
              offense={addedOffense.offense}
              offenseId={addedOffense.id}
              onSave={handleSaveOffense}
              onDelete={handleDeleteOffense}
              onSaveStatusChange={handleOffenseFormSaveStatusChange}
              readOnly={readOnly}
              initialData={addedOffense.data}
            />
          ))}
        </Box>
      )}

      {/* Separate Victims Section */}
      <Box id="all-victims">
        {victimPeople.length > 0 && (
          <VictimsSection
            people={victimPeople}
            relations={relations}
            addedOffenses={addedOffenses}
            readOnly={readOnly}
            reportId={reportId}
            onSaveStatusChange={onSaveStatusChange}
            onSetActiveOffenseRelation={onSetActiveOffenseRelation}
            onOpenSidePanel={onOpenSidePanel}
            onEntityEdit={onEntityEdit}
            onRemovePersonFromReport={onRemovePersonFromReport}
            onAddPersonToOffense={onAddPersonToOffense}
            onRemovePersonFromOffense={onRemovePersonFromOffense}
            onCreateVictimReportRelation={onCreateVictimReportRelation}
            onUpdateVictimReportRelation={onUpdateVictimReportRelation}
          />
        )}
      </Box>

      {/* Separate Offenders Section */}
      <Box id="all-offenders">
        {offenderPeople.length > 0 && (
          <OffendersSection
            people={offenderPeople}
            relations={relations}
            addedOffenses={addedOffenses}
            readOnly={readOnly}
            onSetActiveOffenseRelation={onSetActiveOffenseRelation}
            onOpenSidePanel={onOpenSidePanel}
            onEntityEdit={onEntityEdit}
            onRemovePersonFromReport={onRemovePersonFromReport}
            onAddPersonToOffense={onAddPersonToOffense}
            onRemovePersonFromOffense={onRemovePersonFromOffense}
          />
        )}
      </Box>

      {/* VictimOffender Relations Card */}
      <Box id="victim-offender-relationships">
        {victimOffenderPairs.length > 0 && (
          <>
            <Divider sx={{ borderColor: colors.grey[300], mt: 4, mb: 3 }} />
            <OffenseRelationsCard
              reportId={reportId || ""}
              victimOffenderPairs={victimOffenderPairs}
              readOnly={readOnly}
            />
          </>
        )}
      </Box>

      {/* Separate Vehicles Section */}
      <Box id="all-vehicles">
        <VehiclesSection
          vehicles={relatedVehicles}
          relations={relations}
          addedOffenses={addedOffenses}
          readOnly={readOnly}
          onSetActiveVehicleOffenseContext={onSetActiveVehicleOffenseContext}
          onOpenSidePanel={onOpenSidePanel}
          onEntityEdit={onEntityEdit}
          onRemoveVehicleFromReport={onRemoveVehicleFromReport}
          onAddVehicleToOffense={onAddVehicleToOffense}
          onRemoveVehicleFromOffense={onRemoveVehicleFromOffense}
        />
      </Box>

      {/* Separate Properties Section */}
      <Box id="all-properties">
        <PropertiesSection
          properties={relatedProperties}
          relations={relations}
          addedOffenses={addedOffenses}
          readOnly={readOnly}
          onSetActivePropertyOffenseContext={onSetActivePropertyOffenseContext}
          onOpenSidePanel={onOpenSidePanel}
          onEntityEdit={onEntityEdit}
          onRemovePropertyFromReport={onRemovePropertyFromReport}
          onAddPropertyToOffense={onAddPropertyToOffense}
          onRemovePropertyFromOffense={onRemovePropertyFromOffense}
        />
      </Box>

      {/* Separate Organizations Section */}
      <Box id="all-organizations">
        {relatedOrganizations.length > 0 && (
          <OrganizationsSection
            organizations={relatedOrganizations}
            relations={relations}
            addedOffenses={addedOffenses}
            readOnly={readOnly}
            onSetActiveOrganizationOffenseContext={
              onSetActiveOrganizationOffenseContext
            }
            onOpenSidePanel={onOpenSidePanel}
            onEntityEdit={onEntityEdit}
            onRemoveOrganizationFromReport={onRemoveOrganizationFromReport}
            onAddOrganizationToOffense={onAddOrganizationToOffense}
            onRemoveOrganizationFromOffense={onRemoveOrganizationFromOffense}
          />
        )}
      </Box>

      {/* Bottom Divider */}
      <Divider sx={{ borderColor: colors.grey[300], mt: 3 }} />

      {/* Add Menu */}
      <Menu
        anchorEl={anchorEl}
        open={menuOpen}
        onClose={handleClose}
        anchorOrigin={{
          vertical: "bottom",
          horizontal: "right",
        }}
        transformOrigin={{
          vertical: "top",
          horizontal: "right",
        }}
        sx={{
          mt: 0.25,
          "& .MuiPaper-root": {
            borderRadius: "8px",
            minWidth: "200px",
          },
          "& .MuiMenuItem-root": {
            py: 1.5,
          },
        }}
      >
        <MenuItem onClick={handleAddOffense}>
          <Typography style="body2" color={colors.grey[500]}>
            Add an Offense
          </Typography>
        </MenuItem>
        <MenuItem onClick={handleAddIncident}>
          <Typography style="body2" color={colors.grey[500]}>
            Add an Incident
          </Typography>
        </MenuItem>
      </Menu>

      {/* Offense Search Modal */}
      <OffenseSearchModal
        open={isOffenseModalOpen}
        onClose={() => setIsOffenseModalOpen(false)}
        onSelectOffense={handleOffenseSelect}
      />
    </Box>
  );
}
