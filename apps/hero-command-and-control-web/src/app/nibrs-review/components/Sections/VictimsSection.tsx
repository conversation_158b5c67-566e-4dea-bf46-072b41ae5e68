import { Button } from "@/design-system/components/Button";
import { Checkbox } from "@/design-system/components/Checkbox";
import { Dropdown } from "@/design-system/components/Dropdown";
import {
  DropdownOption,
  InputType,
  TextInput,
} from "@/design-system/components/TextInput";
import { Typography } from "@/design-system/components/Typography";
import { colors } from "@/design-system/tokens";
import AddIcon from "@mui/icons-material/Add";
import DeleteIcon from "@mui/icons-material/Delete";
import EditIcon from "@mui/icons-material/Edit";
import ExpandLessIcon from "@mui/icons-material/ExpandLess";
import ExpandMoreIcon from "@mui/icons-material/ExpandMore";
import MoreVertIcon from "@mui/icons-material/MoreVert";
import OpenInNewIcon from "@mui/icons-material/OpenInNew";
import {
  Box,
  Collapse,
  Dialog,
  DialogActions,
  DialogContent,
  Divider,
  IconButton,
  <PERSON>u,
  MenuItem,
} from "@mui/material";
import React, { useState } from "react";
import { PersonData } from "./constants";

interface VictimsSectionProps {
  people: PersonData[];
  relations?: any[];
  addedOffenses?: any[];
  readOnly?: boolean;
  reportId?: string;
  onSaveStatusChange?: (status: {
    isSaving: boolean;
    hasUnsavedChanges: boolean;
    source: string;
  }) => void;
  onSetActiveOffenseRelation?: (
    context: {
      offenseId: string;
      relationType: "victim";
    } | null
  ) => void;
  onOpenSidePanel?: (panelType: "PERSON") => void;
  onEntityEdit?: (entityId: string, entityType: "person") => void;
  onRemovePersonFromReport?: (personId: string, entityType: "person") => void;
  onAddPersonToOffense?: (
    personId: string,
    offenseId: string,
    relationType: "victim"
  ) => void;
  onRemovePersonFromOffense?: (personId: string, offenseId: string) => void;
  onCreateVictimReportRelation?: (
    personId: string,
    reportId: string,
    entity?: any,
    victimData?: any
  ) => void;
  onUpdateVictimReportRelation?: (relationId: string, victimData: any) => void;
}

// Individual Victim Card Component
interface VictimCardProps {
  person: PersonData;
  relations?: any[];
  addedOffenses?: any[];
  readOnly?: boolean;
  reportId?: string;
  onSaveStatusChange?: (status: {
    isSaving: boolean;
    hasUnsavedChanges: boolean;
    source: string;
  }) => void;
  onEntityEdit?: (entityId: string, entityType: "person") => void;
  onRemovePersonFromReport?: (personId: string, entityType: "person") => void;
  onAddPersonToOffense?: (
    personId: string,
    offenseId: string,
    relationType: "victim"
  ) => void;
  onRemovePersonFromOffense?: (personId: string, offenseId: string) => void;
  onCreateVictimReportRelation?: (
    personId: string,
    reportId: string,
    entity?: any,
    victimData?: any
  ) => void;
  onUpdateVictimReportRelation?: (relationId: string, victimData: any) => void;
}

// Victim form data interface
interface VictimFormData {
  victimType: string;
  residentStatus: string;
  isReportingParty: boolean;
  injuries: string[];
}

const VictimCard: React.FC<VictimCardProps> = ({
  person,
  relations = [],
  addedOffenses = [],
  readOnly = false,
  reportId,
  onSaveStatusChange,
  onEntityEdit,
  onRemovePersonFromReport,
  onAddPersonToOffense,
  onRemovePersonFromOffense,
  onCreateVictimReportRelation,
  onUpdateVictimReportRelation,
}) => {
  const [isExpanded, setIsExpanded] = useState(true);
  const [isSaving, setIsSaving] = useState(false);
  const [menuAnchorEl, setMenuAnchorEl] = useState<null | HTMLElement>(null);
  const [removeModalOpen, setRemoveModalOpen] = useState(false);

  // Helper function to get existing victim-report relation
  const getVictimReportRelation = (personId: string) => {
    if (!relations || !reportId) return null;

    return relations.find((rel: any) => {
      const isVictimReportRelation =
        rel.relationType === "RELATION_TYPE_VICTIM_REPORT";
      const isPersonInvolved =
        (rel.objectA?.objectType === "entity" &&
          rel.objectA?.globalId === personId) ||
        (rel.objectB?.objectType === "entity" &&
          rel.objectB?.globalId === personId);
      const isReportInvolved =
        (rel.objectA?.objectType === "report" &&
          rel.objectA?.reportScopedId === reportId) ||
        (rel.objectB?.objectType === "report" &&
          rel.objectB?.reportScopedId === reportId);

      return isVictimReportRelation && isPersonInvolved && isReportInvolved;
    });
  };

  // Initialize form data from existing relation or defaults
  const existingRelation = getVictimReportRelation(person.id);
  const [victimFormData, setVictimFormData] = useState<VictimFormData>(() => {
    const metadata = existingRelation?.metadata || {};
    return {
      victimType: metadata.victimType || "",
      residentStatus: metadata.residentStatus || "",
      isReportingParty: metadata.isReportingParty || false,
      injuries: metadata.injuries || [],
    };
  });

  // Helper function to save victim form data
  const saveVictimFormData = async (updatedData: Partial<VictimFormData>) => {
    const newFormData = { ...victimFormData, ...updatedData };
    setVictimFormData(newFormData);

    if (!reportId) return;

    // Set saving state
    setIsSaving(true);
    onSaveStatusChange?.({
      isSaving: true,
      hasUnsavedChanges: false,
      source: `victim-${person.id}`,
    });

    try {
      if (existingRelation) {
        // Update existing relation
        await onUpdateVictimReportRelation?.(existingRelation.id, newFormData);
      } else {
        // Create new relation
        await onCreateVictimReportRelation?.(
          person.id,
          reportId,
          person,
          newFormData
        );
      }
    } finally {
      // Reset saving state
      setIsSaving(false);
      onSaveStatusChange?.({
        isSaving: false,
        hasUnsavedChanges: false,
        source: `victim-${person.id}`,
      });
    }
  };

  // Helper function to add a new injury
  const handleAddInjury = () => {
    if (victimFormData.injuries.length >= 5) return; // Max 5 injuries

    const newInjuries = [...victimFormData.injuries, ""];
    saveVictimFormData({ injuries: newInjuries });
  };

  // Helper function to update an injury at a specific index
  const handleUpdateInjury = (index: number, value: string | null) => {
    const newInjuries = [...victimFormData.injuries];
    if (value) {
      newInjuries[index] = value;
    } else {
      // Remove empty injury
      newInjuries.splice(index, 1);
    }
    saveVictimFormData({ injuries: newInjuries });
  };

  // Helper function to remove an injury at a specific index
  const handleRemoveInjury = (index: number) => {
    const newInjuries = [...victimFormData.injuries];
    newInjuries.splice(index, 1);
    saveVictimFormData({ injuries: newInjuries });
  };

  // Helper function to get person role numbers (V/O/W/S/I) and RP tag
  const getVictimOffenderNumbers = (personId: string): string[] => {
    if (!relations) return [];

    const personRelations = relations.filter((rel: any) => {
      const isPersonRelation =
        rel.relationType === "RELATION_TYPE_OFFENSE_VICTIM" ||
        rel.relationType === "RELATION_TYPE_OFFENSE_OFFENDER" ||
        rel.relationType === "RELATION_TYPE_OFFENSE_WITNESS" ||
        rel.relationType === "RELATION_TYPE_OFFENSE_SUSPECT" ||
        rel.relationType === "RELATION_TYPE_OFFENSE_INVOLVED_PARTY";

      if (!isPersonRelation) return false;

      const isPersonInvolved =
        (rel.objectA?.objectType === "entity" &&
          rel.objectA?.globalId === personId) ||
        (rel.objectB?.objectType === "entity" &&
          rel.objectB?.globalId === personId);

      return isPersonInvolved && rel.metadata?.victimOffenderNumber;
    });

    const numbers = personRelations
      .map((rel) => rel.metadata?.victimOffenderNumber)
      .filter(Boolean);

    const uniqueNumbers = [];
    const seenTypes = new Set();

    for (const number of numbers) {
      const type = number.charAt(0);
      if (!seenTypes.has(type)) {
        uniqueNumbers.push(number);
        seenTypes.add(type);
      }
    }

    // Check for reporting party status from victim report relations
    if (reportId) {
      const victimReportRelation = relations.find((rel: any) => {
        const isVictimReportRelation =
          rel.relationType === "RELATION_TYPE_VICTIM_REPORT";
        const isPersonInvolved =
          (rel.objectA?.objectType === "entity" &&
            rel.objectA?.globalId === personId) ||
          (rel.objectB?.objectType === "entity" &&
            rel.objectB?.globalId === personId);
        const isReportInvolved =
          (rel.objectA?.objectType === "report" &&
            rel.objectA?.reportScopedId === reportId) ||
          (rel.objectB?.objectType === "report" &&
            rel.objectB?.reportScopedId === reportId);

        return isVictimReportRelation && isPersonInvolved && isReportInvolved;
      });

      // Add RP tag if person is reporting party
      if (victimReportRelation?.metadata?.isReportingParty) {
        uniqueNumbers.push("RP");
      }
    }

    return uniqueNumbers;
  };

  // Helper function to get related offenses for this person
  const getRelatedOffenses = (personId: string): string[] => {
    if (!relations || !addedOffenses) return [];

    const offenseIds = relations
      .filter((rel: any) => {
        const isVictimRelation =
          rel.relationType === "RELATION_TYPE_OFFENSE_VICTIM";
        const isPersonInvolved =
          (rel.objectA?.objectType === "entity" &&
            rel.objectA?.globalId === personId) ||
          (rel.objectB?.objectType === "entity" &&
            rel.objectB?.globalId === personId);

        return isVictimRelation && isPersonInvolved;
      })
      .map((rel: any) => {
        if (rel.objectA?.objectType === "offense")
          return rel.objectA.reportScopedId;
        if (rel.objectB?.objectType === "offense")
          return rel.objectB.reportScopedId;
        return null;
      })
      .filter(Boolean);

    return offenseIds;
  };

  // Helper function to format role number as 3-digit string
  const formatRoleNumber = (roleNumber: string): string => {
    // Extract just the number part (remove V, O, W, S, I prefix)
    const numberPart = roleNumber.slice(1);
    // Pad with zeros to make it 3 digits
    return numberPart.padStart(3, "0");
  };

  // Helper function to create offense dropdown options
  const getOffenseOptions = (): DropdownOption[] => {
    if (!addedOffenses) return [];

    return addedOffenses.map((addedOffense) => ({
      value: addedOffense.id,
      label: `${addedOffense.offense.citation} ${addedOffense.offense.literal}`,
    }));
  };

  // Helper function to get selected offense options for this person
  const getSelectedOffenseOptions = (personId: string): DropdownOption[] => {
    const relatedOffenseIds = getRelatedOffenses(personId);
    const offenseOptions = getOffenseOptions();

    return offenseOptions.filter((option) =>
      relatedOffenseIds.includes(option.value)
    );
  };

  // Handle offense selection changes
  const handleOffenseSelectionChange = (selectedOptions: DropdownOption[]) => {
    if (readOnly) return;

    const currentOffenseIds = getRelatedOffenses(person.id);
    const newOffenseIds = selectedOptions.map((option) => option.value);

    // Find offenses to add (in new selection but not in current)
    const offensesToAdd = newOffenseIds.filter(
      (offenseId) => !currentOffenseIds.includes(offenseId)
    );

    // Find offenses to remove (in current but not in new selection)
    const offensesToRemove = currentOffenseIds.filter(
      (offenseId) => !newOffenseIds.includes(offenseId)
    );

    // Add new offense relations
    offensesToAdd.forEach((offenseId) => {
      if (onAddPersonToOffense) {
        onAddPersonToOffense(person.id, offenseId, "victim");
      }
    });

    // Remove offense relations
    offensesToRemove.forEach((offenseId) => {
      if (onRemovePersonFromOffense) {
        onRemovePersonFromOffense(person.id, offenseId);
      }
    });
  };

  // Helper function to get victim type display text
  const getVictimTypeDisplay = (personId: string): string => {
    const victimReportRelation = getVictimReportRelation(personId);
    if (!victimReportRelation?.metadata?.victimType) return "";

    const victimType = victimReportRelation.metadata.victimType;

    // Map the victim type values to display labels
    const typeLabels: Record<string, string> = {
      individual: "Individual",
      business: "Business",
      government: "Government",
      religious_organization: "Religious Organization",
      society_public: "Society/Public",
      other: "Other",
    };

    return typeLabels[victimType] || "";
  };

  const roleNumbers = getVictimOffenderNumbers(person.id);
  const selectedOffenseOptions = getSelectedOffenseOptions(person.id);
  const offenseOptions = getOffenseOptions();
  const victimTypeDisplay = getVictimTypeDisplay(person.id);

  const handleMenuClick = (event: React.MouseEvent<HTMLElement>) => {
    event.stopPropagation();
    setMenuAnchorEl(event.currentTarget);
  };

  const handleMenuClose = () => {
    setMenuAnchorEl(null);
  };

  const handleOpenRecord = () => {
    window.open(`/entity?entityId=${person.id}`, "_blank");
    setMenuAnchorEl(null);
  };

  const handleEditEntity = () => {
    onEntityEdit?.(person.id, "person");
    setMenuAnchorEl(null);
  };

  const handleRemoveClick = () => {
    setRemoveModalOpen(true);
  };

  const handleRemoveCancel = () => {
    setRemoveModalOpen(false);
  };

  const handleRemoveConfirm = () => {
    onRemovePersonFromReport?.(person.id, "person");
    setRemoveModalOpen(false);
  };

  return (
    <Box
      sx={{
        display: "flex",
        width: "100%",
        flexDirection: "column",
        alignItems: "flex-start",
        borderRadius: "12px",
        border: `1px solid ${colors.grey[200]}`,
        background: "#FFF",
        overflow: "hidden",
        boxShadow: "0px 0px 16px 0px rgba(0, 0, 0, 0.04)",
        mb: 3,
      }}
    >
      {/* Header */}
      <Box
        sx={{
          display: "flex",
          width: "100%",
          justifyContent: "space-between",
          alignItems: "center",
          padding: "16px 24px",
          borderBottom: `1px solid ${colors.grey[200]}`,
          backgroundColor: colors.grey[50],
        }}
      >
        <Box sx={{ display: "flex", alignItems: "center", gap: 2 }}>
          {roleNumbers.length > 0 && (
            <Typography style="h3" color={colors.grey[900]}>
              <span style={{ fontWeight: "bold" }}>
                {formatRoleNumber(roleNumbers[0])}
              </span>
            </Typography>
          )}
          <Typography style="h3" color={colors.grey[900]}>
            {person.name}
            {victimTypeDisplay ? `, ${victimTypeDisplay}` : ""}
          </Typography>
        </Box>

        <Box sx={{ display: "flex", alignItems: "center", gap: 1 }}>
          {!readOnly && (
            <IconButton
              onClick={handleMenuClick}
              size="small"
              sx={{
                color: colors.grey[600],
                zIndex: 10,
                "&:hover": {
                  bgcolor: colors.grey[100],
                },
              }}
            >
              <MoreVertIcon />
            </IconButton>
          )}
          <IconButton
            onClick={() => setIsExpanded(!isExpanded)}
            size="small"
            sx={{
              color: colors.grey[600],
              "&:hover": {
                bgcolor: colors.grey[100],
              },
            }}
          >
            {isExpanded ? <ExpandLessIcon /> : <ExpandMoreIcon />}
          </IconButton>
        </Box>
      </Box>

      {/* Form Content */}
      <Collapse in={isExpanded} sx={{ width: "100%" }}>
        <Box sx={{ padding: "24px" }}>
          {/* Related Offenses Section */}
          <Box sx={{ mb: 4 }}>
            <TextInput
              title="Related Offenses"
              placeholder="Select offenses..."
              type={InputType.Dropdown}
              isMultiSelect
              options={offenseOptions}
              selectedItems={selectedOffenseOptions}
              onChangeSelectedItems={handleOffenseSelectionChange}
              readOnly={readOnly}
              chipColor="grey"
              hideSelectedFromOptions={true}
              minRequiredItems={1}
            />
          </Box>

          {/* Victim Type Section */}
          <Box sx={{ mb: 4 }}>
            <Box sx={{ mb: 3 }}>
              <Typography style="caps1" color={colors.grey[700]}>
                Victim Classification
              </Typography>
            </Box>
            <Box sx={{ display: "flex", gap: 2, mb: 3 }}>
              <Box sx={{ width: "50%" }}>
                <Dropdown
                  title="Victim Type"
                  placeholder="Select Type"
                  options={[
                    { value: "individual", label: "Individual" },
                    { value: "business", label: "Business" },
                    { value: "government", label: "Government" },
                    {
                      value: "religious_organization",
                      label: "Religious Organization",
                    },
                    { value: "society_public", label: "Society/Public" },
                    { value: "other", label: "Other" },
                  ]}
                  value={victimFormData.victimType}
                  onChange={(value) =>
                    saveVictimFormData({ victimType: value || "" })
                  }
                  readOnly={readOnly}
                />
              </Box>
              <Box sx={{ width: "50%" }}>
                <Dropdown
                  title="Resident Status"
                  placeholder="Select Status"
                  options={[
                    { value: "resident", label: "Resident" },
                    { value: "nonresident", label: "Non-resident" },
                    { value: "unknown", label: "Unknown" },
                  ]}
                  value={victimFormData.residentStatus}
                  onChange={(value) =>
                    saveVictimFormData({ residentStatus: value || "" })
                  }
                  readOnly={readOnly}
                />
              </Box>
            </Box>
          </Box>

          {/* Reporting Party Section */}
          <Box sx={{ mb: 4 }}>
            <Box sx={{ mb: 3 }}>
              <Typography style="caps1" color={colors.grey[700]}>
                Reporting Party
              </Typography>
            </Box>
            <Checkbox
              label="Victim was also Reporting Party"
              checked={victimFormData.isReportingParty}
              onChange={(e) =>
                saveVictimFormData({ isReportingParty: e.target.checked })
              }
              readOnly={readOnly}
            />
          </Box>

          {/* Incident Related Injuries Section */}
          <Box sx={{ mb: 4 }}>
            <Box sx={{ mb: 3 }}>
              <Typography style="caps1" color={colors.grey[700]}>
                Incident Related Injuries
              </Typography>
            </Box>
            <Box sx={{ mb: 3 }}>
              <Typography style="body4" color={colors.grey[500]}>
                Add up to 5
              </Typography>
            </Box>

            {/* Render existing injuries */}
            {victimFormData.injuries.map((injury, index) => (
              <Box
                key={index}
                sx={{ mb: 3, display: "flex", gap: 1, alignItems: "flex-end" }}
              >
                <Box sx={{ width: "50%" }}>
                  <Dropdown
                    title={index === 0 ? "Injury Type" : ""}
                    placeholder="Select Type"
                    options={[
                      { value: "none", label: "None" },
                      {
                        value: "apparent_minor_injuries",
                        label: "Apparent Minor Injuries",
                      },
                      {
                        value: "apparent_major_injuries",
                        label: "Apparent Major Injuries",
                      },
                      {
                        value: "possible_internal_injury",
                        label: "Possible Internal Injury",
                      },
                      {
                        value: "severe_laceration",
                        label: "Severe Laceration",
                      },
                      { value: "loss_of_teeth", label: "Loss of Teeth" },
                      { value: "broken_bones", label: "Broken Bones" },
                      { value: "unconsciousness", label: "Unconsciousness" },
                      {
                        value: "other_major_injury",
                        label: "Other Major Injury",
                      },
                    ]}
                    value={injury}
                    onChange={(value) => handleUpdateInjury(index, value)}
                    readOnly={readOnly}
                    enableSearch
                  />
                </Box>
                {!readOnly && victimFormData.injuries.length > 1 && (
                  <IconButton
                    onClick={() => handleRemoveInjury(index)}
                    size="small"
                    sx={{
                      color: colors.grey[600],
                      "&:hover": {
                        bgcolor: colors.grey[100],
                      },
                    }}
                  >
                    <DeleteIcon />
                  </IconButton>
                )}
              </Box>
            ))}

            {/* Show first injury dropdown if no injuries exist */}
            {victimFormData.injuries.length === 0 && (
              <Box sx={{ mb: 3 }}>
                <Box sx={{ width: "50%" }}>
                  <Dropdown
                    title="Injury Type"
                    placeholder="Select Type"
                    options={[
                      { value: "none", label: "None" },
                      {
                        value: "apparent_minor_injuries",
                        label: "Apparent Minor Injuries",
                      },
                      {
                        value: "apparent_major_injuries",
                        label: "Apparent Major Injuries",
                      },
                      {
                        value: "possible_internal_injury",
                        label: "Possible Internal Injury",
                      },
                      {
                        value: "severe_laceration",
                        label: "Severe Laceration",
                      },
                      { value: "loss_of_teeth", label: "Loss of Teeth" },
                      { value: "broken_bones", label: "Broken Bones" },
                      { value: "unconsciousness", label: "Unconsciousness" },
                      {
                        value: "other_major_injury",
                        label: "Other Major Injury",
                      },
                    ]}
                    value=""
                    onChange={(value) => handleUpdateInjury(0, value)}
                    readOnly={readOnly}
                    enableSearch
                  />
                </Box>
              </Box>
            )}

            {!readOnly && victimFormData.injuries.length < 5 && (
              <Button
                label="Add Injury"
                style="ghost"
                color="blue"
                size="small"
                leftIcon={<AddIcon />}
                onClick={handleAddInjury}
              />
            )}
          </Box>
        </Box>
      </Collapse>

      {/* Menu for victim actions */}
      <Menu
        anchorEl={menuAnchorEl}
        open={Boolean(menuAnchorEl)}
        onClose={handleMenuClose}
        anchorOrigin={{
          vertical: "bottom",
          horizontal: "right",
        }}
        transformOrigin={{
          vertical: "top",
          horizontal: "right",
        }}
        sx={{
          mt: 0.25,
          "& .MuiPaper-root": {
            borderRadius: "8px",
            minWidth: "200px",
          },
          "& .MuiMenuItem-root": {
            py: 1.5,
            display: "flex",
            alignItems: "center",
            gap: 1.5,
          },
        }}
      >
        <MenuItem onClick={handleEditEntity}>
          <EditIcon fontSize="small" sx={{ color: colors.grey[500] }} />
          <Typography style="body2" color={colors.grey[500]}>
            Edit entity
          </Typography>
        </MenuItem>
        <MenuItem onClick={handleOpenRecord}>
          <OpenInNewIcon fontSize="small" sx={{ color: colors.grey[500] }} />
          <Typography style="body2" color={colors.grey[500]}>
            View entity in new tab
          </Typography>
        </MenuItem>
        <MenuItem onClick={handleRemoveClick}>
          <DeleteIcon fontSize="small" sx={{ color: colors.grey[500] }} />
          <Typography style="body2" color={colors.grey[500]}>
            Remove entity
          </Typography>
        </MenuItem>
      </Menu>

      {/* Remove confirmation modal */}
      <Dialog
        open={removeModalOpen}
        onClose={handleRemoveCancel}
        maxWidth="sm"
        fullWidth
        PaperProps={{
          sx: {
            borderRadius: "12px",
            p: 1,
          },
        }}
      >
        <DialogContent sx={{ p: 3 }}>
          <Box sx={{ mb: 2 }}>
            <Typography style="h2" color={colors.grey[900]}>
              Delete {person.name}
            </Typography>
          </Box>
          <Box sx={{ mb: 3 }}>
            <Typography style="body3" color={colors.grey[700]}>
              {person.name} will be deleted from the report and all offenses
            </Typography>
          </Box>
        </DialogContent>

        <DialogActions sx={{ p: 3, pt: 0 }}>
          <Button
            label="Cancel"
            color="grey"
            prominence={false}
            onClick={handleRemoveCancel}
          />
          <Button
            label="Delete"
            color="blue"
            prominence={true}
            onClick={handleRemoveConfirm}
          />
        </DialogActions>
      </Dialog>
    </Box>
  );
};

const VictimsSection: React.FC<VictimsSectionProps> = ({
  people,
  relations,
  addedOffenses,
  readOnly = false,
  reportId,
  onSaveStatusChange,
  onSetActiveOffenseRelation,
  onOpenSidePanel,
  onEntityEdit,
  onRemovePersonFromReport,
  onAddPersonToOffense,
  onRemovePersonFromOffense,
  onCreateVictimReportRelation,
  onUpdateVictimReportRelation,
}) => {
  const [addVictimMenuAnchor, setAddVictimMenuAnchor] =
    useState<null | HTMLElement>(null);

  const handleAddVictimClick = (event: React.MouseEvent<HTMLElement>) => {
    if (!addedOffenses || addedOffenses.length === 0) return;

    if (addedOffenses.length === 1) {
      // If only one offense, directly set the active offense relation
      onSetActiveOffenseRelation?.({
        offenseId: addedOffenses[0].id,
        relationType: "victim",
      });
      // Open the side panel
      onOpenSidePanel?.("PERSON");
    } else {
      // If multiple offenses, show menu to select
      setAddVictimMenuAnchor(event.currentTarget);
    }
  };

  const handleAddVictimMenuClose = () => {
    setAddVictimMenuAnchor(null);
  };

  const handleSelectOffenseForVictim = (offenseId: string) => {
    onSetActiveOffenseRelation?.({
      offenseId,
      relationType: "victim",
    });
    // Open the side panel
    onOpenSidePanel?.("PERSON");
    setAddVictimMenuAnchor(null);
  };

  return (
    <>
      <Divider sx={{ borderColor: colors.grey[300], mt: 4, mb: 3 }} />
      <Box sx={{ mb: 4 }}>
        <Box
          sx={{
            display: "flex",
            justifyContent: "space-between",
            alignItems: "center",
            mb: 3,
          }}
        >
          <Typography style="h1" color={colors.grey[900]}>
            All Victims
          </Typography>
          {!readOnly && addedOffenses && addedOffenses.length > 0 && (
            <Button
              onClick={handleAddVictimClick}
              label="Add Victim"
              leftIcon={<AddIcon />}
              size="small"
            />
          )}
        </Box>
        {people.map((person) => (
          <VictimCard
            key={person.id}
            person={person}
            relations={relations}
            addedOffenses={addedOffenses}
            readOnly={readOnly}
            reportId={reportId}
            onSaveStatusChange={onSaveStatusChange}
            onEntityEdit={onEntityEdit}
            onRemovePersonFromReport={onRemovePersonFromReport}
            onAddPersonToOffense={onAddPersonToOffense}
            onRemovePersonFromOffense={onRemovePersonFromOffense}
            onCreateVictimReportRelation={onCreateVictimReportRelation}
            onUpdateVictimReportRelation={onUpdateVictimReportRelation}
          />
        ))}
      </Box>

      {/* Add Victim Offense Selection Menu */}
      <Menu
        anchorEl={addVictimMenuAnchor}
        open={Boolean(addVictimMenuAnchor)}
        onClose={handleAddVictimMenuClose}
        anchorOrigin={{
          vertical: "bottom",
          horizontal: "left",
        }}
        transformOrigin={{
          vertical: "top",
          horizontal: "left",
        }}
        sx={{
          mt: 0.25,
          "& .MuiPaper-root": {
            borderRadius: "8px",
            minWidth: "300px",
          },
          "& .MuiMenuItem-root": {
            py: 1.5,
            display: "flex",
            alignItems: "center",
            gap: 1.5,
          },
        }}
      >
        {addedOffenses?.map((addedOffense) => (
          <MenuItem
            key={addedOffense.id}
            onClick={() => handleSelectOffenseForVictim(addedOffense.id)}
          >
            <Typography style="body2" color={colors.grey[900]}>
              {addedOffense.offense.citation} {addedOffense.offense.literal}
            </Typography>
          </MenuItem>
        ))}
      </Menu>
    </>
  );
};

export default VictimsSection;
