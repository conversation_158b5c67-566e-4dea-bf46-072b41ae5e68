import { TextInput } from "@/design-system/components/TextInput";
import { Typography } from "@/design-system/components/Typography";
import { colors } from "@/design-system/tokens";
import { Box } from "@mui/material";
import React, { useEffect, useRef, useState } from "react";

interface AdministrativeSectionProps {
  reportId?: string;
  onSaveStatusChange?: (status: {
    isSaving: boolean;
    hasUnsavedChanges: boolean;
    source: string;
  }) => void;
  readOnly?: boolean;
  initialData?: AdminFormData;
}

interface AdminFormData {
  incidentNumber: string;
  departmentORI: string;
  incidentDate: string;
  incidentTime: string;
  clearedExceptionally: string;
  exceptionalClearanceDate: string;
  field1: string;
  field2: string;
}

const AdministrativeSection: React.FC<AdministrativeSectionProps> = ({
  reportId,
  onSaveStatusChange,
  readOnly = false,
  initialData,
}) => {
  const [formData, setFormData] = useState<AdminFormData>({
    incidentNumber: "20251005001",
    departmentORI: "20251005001",
    incidentDate: "04/17/2022",
    incidentTime: "13:35",
    clearedExceptionally: "04/17/2022",
    exceptionalClearanceDate: "13:35",
    field1: "",
    field2: "",
    ...initialData,
  });

  const [isSaving, setIsSaving] = useState(false);
  const [hasUnsavedChanges, setHasUnsavedChanges] = useState(false);

  // Use a ref to track previous status
  const lastStatusRef = useRef<{
    isSaving: boolean;
    hasUnsavedChanges: boolean;
  } | null>(null);

  // Notify parent on save/dirty changes
  useEffect(() => {
    if (!onSaveStatusChange) return;

    const currentStatus = { isSaving, hasUnsavedChanges };
    const lastStatus = lastStatusRef.current;
    const changed =
      !lastStatus ||
      lastStatus.isSaving !== currentStatus.isSaving ||
      lastStatus.hasUnsavedChanges !== currentStatus.hasUnsavedChanges;

    if (changed) {
      lastStatusRef.current = { ...currentStatus };
      onSaveStatusChange({ ...currentStatus, source: "administrative" });
    }
  }, [isSaving, hasUnsavedChanges, onSaveStatusChange]);

  const handleInputChange = (field: keyof AdminFormData, value: string) => {
    if (readOnly) return;

    setFormData((prev) => ({
      ...prev,
      [field]: value,
    }));
    setHasUnsavedChanges(true);
  };

  const handleBlur = () => {
    if (hasUnsavedChanges && !readOnly) {
      // Simulate saving
      setIsSaving(true);
      setTimeout(() => {
        setIsSaving(false);
        setHasUnsavedChanges(false);
      }, 500);
    }
  };

  return (
    <Box
      sx={{
        border: `1px solid ${colors.grey[200]}`,
        borderRadius: "12px",
        backgroundColor: "#FFF",
        boxShadow: "0px 0px 16px 0px rgba(0, 0, 0, 0.04)",
        mb: 3,
      }}
    >
      {/* Title inside the card */}
      <Box sx={{ px: 3, pt: 3 }}>
        <Typography style="h2" color={colors.grey[900]}>
          Administrative
        </Typography>
      </Box>

      {/* Form Content */}
      <Box sx={{ px: 3, py: 2 }}>
        {/* First Row */}
        <Box
          sx={{
            display: "grid",
            gridTemplateColumns: "1fr 1fr",
            gap: 3,
            mb: 3,
          }}
        >
          <TextInput
            title="Incident Number"
            value={formData.incidentNumber}
            onChange={(e: React.ChangeEvent<HTMLInputElement>) =>
              handleInputChange("incidentNumber", e.target.value)
            }
            onBlur={handleBlur}
            readOnly={readOnly}
          />
          <TextInput
            title="Department ORI"
            value={formData.departmentORI}
            onChange={(e: React.ChangeEvent<HTMLInputElement>) =>
              handleInputChange("departmentORI", e.target.value)
            }
            onBlur={handleBlur}
            readOnly={readOnly}
          />
        </Box>

        {/* Second Row */}
        <Box
          sx={{
            display: "grid",
            gridTemplateColumns: "1fr 1fr",
            gap: 3,
            mb: 3,
          }}
        >
          <TextInput
            title="Incident Date"
            value={formData.incidentDate}
            onChange={(e: React.ChangeEvent<HTMLInputElement>) =>
              handleInputChange("incidentDate", e.target.value)
            }
            onBlur={handleBlur}
            readOnly={readOnly}
            placeholder="MM/DD/YYYY"
          />
          <TextInput
            title="Incident Time"
            value={formData.incidentTime}
            onChange={(e: React.ChangeEvent<HTMLInputElement>) =>
              handleInputChange("incidentTime", e.target.value)
            }
            onBlur={handleBlur}
            readOnly={readOnly}
            placeholder="HH:MM"
          />
        </Box>

        {/* Third Row */}
        <Box
          sx={{
            display: "grid",
            gridTemplateColumns: "1fr 1fr",
            gap: 3,
            mb: 3,
          }}
        >
          <TextInput
            title="Cleared Exceptionally"
            value={formData.clearedExceptionally}
            onChange={(e: React.ChangeEvent<HTMLInputElement>) =>
              handleInputChange("clearedExceptionally", e.target.value)
            }
            onBlur={handleBlur}
            readOnly={readOnly}
            placeholder="MM/DD/YYYY"
          />
          <TextInput
            title="Exceptional Clearance Date"
            value={formData.exceptionalClearanceDate}
            onChange={(e: React.ChangeEvent<HTMLInputElement>) =>
              handleInputChange("exceptionalClearanceDate", e.target.value)
            }
            onBlur={handleBlur}
            readOnly={readOnly}
            placeholder="HH:MM"
          />
        </Box>

        {/* Fourth Row */}
        <Box
          sx={{
            display: "grid",
            gridTemplateColumns: "1fr 1fr",
            gap: 3,
          }}
        >
          <TextInput
            title="Field"
            value={formData.field1}
            onChange={(e: React.ChangeEvent<HTMLInputElement>) =>
              handleInputChange("field1", e.target.value)
            }
            onBlur={handleBlur}
            readOnly={readOnly}
          />
          <TextInput
            title="Field"
            value={formData.field2}
            onChange={(e: React.ChangeEvent<HTMLInputElement>) =>
              handleInputChange("field2", e.target.value)
            }
            onBlur={handleBlur}
            readOnly={readOnly}
          />
        </Box>
      </Box>
    </Box>
  );
};

export default AdministrativeSection;
