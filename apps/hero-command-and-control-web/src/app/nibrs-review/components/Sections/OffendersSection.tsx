import { Button } from "@/design-system/components/Button";
import {
  DropdownOption,
  InputType,
  TextInput,
} from "@/design-system/components/TextInput";
import { Typography } from "@/design-system/components/Typography";
import { colors } from "@/design-system/tokens";
import AddIcon from "@mui/icons-material/Add";
import DeleteIcon from "@mui/icons-material/Delete";
import EditIcon from "@mui/icons-material/Edit";
import ExpandLessIcon from "@mui/icons-material/ExpandLess";
import ExpandMoreIcon from "@mui/icons-material/ExpandMore";
import MoreVertIcon from "@mui/icons-material/MoreVert";
import OpenInNewIcon from "@mui/icons-material/OpenInNew";
import {
  Box,
  Collapse,
  Dialog,
  DialogActions,
  DialogContent,
  Divider,
  IconButton,
  Menu,
  MenuItem,
} from "@mui/material";
import React, { useState } from "react";
import { PersonData } from "./constants";

interface OffendersSectionProps {
  people: PersonData[];
  relations?: any[];
  addedOffenses?: any[];
  readOnly?: boolean;
  onSetActiveOffenseRelation?: (
    context: {
      offenseId: string;
      relationType: "offender";
    } | null
  ) => void;
  onOpenSidePanel?: (panelType: "PERSON") => void;
  onEntityEdit?: (entityId: string, entityType: "person") => void;
  onRemovePersonFromReport?: (personId: string, entityType: "person") => void;
  onAddPersonToOffense?: (
    personId: string,
    offenseId: string,
    relationType: "offender"
  ) => void;
  onRemovePersonFromOffense?: (personId: string, offenseId: string) => void;
}

// Individual Offender Card Component
interface OffenderCardProps {
  person: PersonData;
  relations?: any[];
  addedOffenses?: any[];
  readOnly?: boolean;
  onEntityEdit?: (entityId: string, entityType: "person") => void;
  onRemovePersonFromReport?: (personId: string, entityType: "person") => void;
  onAddPersonToOffense?: (
    personId: string,
    offenseId: string,
    relationType: "offender"
  ) => void;
  onRemovePersonFromOffense?: (personId: string, offenseId: string) => void;
}

const OffenderCard: React.FC<OffenderCardProps> = ({
  person,
  relations = [],
  addedOffenses = [],
  readOnly = false,
  onEntityEdit,
  onRemovePersonFromReport,
  onAddPersonToOffense,
  onRemovePersonFromOffense,
}) => {
  const [isExpanded, setIsExpanded] = useState(true);
  const [menuAnchorEl, setMenuAnchorEl] = useState<null | HTMLElement>(null);
  const [removeModalOpen, setRemoveModalOpen] = useState(false);

  // Helper function to get person role numbers (V/O/W/S/I)
  const getVictimOffenderNumbers = (personId: string): string[] => {
    if (!relations) return [];

    const personRelations = relations.filter((rel: any) => {
      const isPersonRelation =
        rel.relationType === "RELATION_TYPE_OFFENSE_VICTIM" ||
        rel.relationType === "RELATION_TYPE_OFFENSE_OFFENDER" ||
        rel.relationType === "RELATION_TYPE_OFFENSE_WITNESS" ||
        rel.relationType === "RELATION_TYPE_OFFENSE_SUSPECT" ||
        rel.relationType === "RELATION_TYPE_OFFENSE_INVOLVED_PARTY";

      if (!isPersonRelation) return false;

      const isPersonInvolved =
        (rel.objectA?.objectType === "entity" &&
          rel.objectA?.globalId === personId) ||
        (rel.objectB?.objectType === "entity" &&
          rel.objectB?.globalId === personId);

      return isPersonInvolved && rel.metadata?.victimOffenderNumber;
    });

    const numbers = personRelations
      .map((rel) => rel.metadata?.victimOffenderNumber)
      .filter(Boolean);

    const uniqueNumbers = [];
    const seenTypes = new Set();

    for (const number of numbers) {
      const type = number.charAt(0);
      if (!seenTypes.has(type)) {
        uniqueNumbers.push(number);
        seenTypes.add(type);
      }
    }

    return uniqueNumbers;
  };

  // Helper function to get related offenses for this person
  const getRelatedOffenses = (personId: string): string[] => {
    if (!relations || !addedOffenses) return [];

    const offenseIds = relations
      .filter((rel: any) => {
        const isOffenderRelation =
          rel.relationType === "RELATION_TYPE_OFFENSE_OFFENDER";
        const isPersonInvolved =
          (rel.objectA?.objectType === "entity" &&
            rel.objectA?.globalId === personId) ||
          (rel.objectB?.objectType === "entity" &&
            rel.objectB?.globalId === personId);

        return isOffenderRelation && isPersonInvolved;
      })
      .map((rel: any) => {
        if (rel.objectA?.objectType === "offense")
          return rel.objectA.reportScopedId;
        if (rel.objectB?.objectType === "offense")
          return rel.objectB.reportScopedId;
        return null;
      })
      .filter(Boolean);

    return offenseIds;
  };

  // Helper function to format role number as 3-digit string
  const formatRoleNumber = (roleNumber: string): string => {
    // Extract just the number part (remove V, O, W, S, I prefix)
    const numberPart = roleNumber.slice(1);
    // Pad with zeros to make it 3 digits
    return numberPart.padStart(3, "0");
  };

  // Helper function to create offense dropdown options
  const getOffenseOptions = (): DropdownOption[] => {
    if (!addedOffenses) return [];

    return addedOffenses.map((addedOffense) => ({
      value: addedOffense.id,
      label: `${addedOffense.offense.citation} ${addedOffense.offense.literal}`,
    }));
  };

  // Helper function to get selected offense options for this person
  const getSelectedOffenseOptions = (personId: string): DropdownOption[] => {
    const relatedOffenseIds = getRelatedOffenses(personId);
    const offenseOptions = getOffenseOptions();

    return offenseOptions.filter((option) =>
      relatedOffenseIds.includes(option.value)
    );
  };

  // Handle offense selection changes
  const handleOffenseSelectionChange = (selectedOptions: DropdownOption[]) => {
    if (readOnly) return;

    const currentOffenseIds = getRelatedOffenses(person.id);
    const newOffenseIds = selectedOptions.map((option) => option.value);

    // Find offenses to add (in new selection but not in current)
    const offensesToAdd = newOffenseIds.filter(
      (offenseId) => !currentOffenseIds.includes(offenseId)
    );

    // Find offenses to remove (in current but not in new selection)
    const offensesToRemove = currentOffenseIds.filter(
      (offenseId) => !newOffenseIds.includes(offenseId)
    );

    // Add new offense relations
    offensesToAdd.forEach((offenseId) => {
      if (onAddPersonToOffense) {
        onAddPersonToOffense(person.id, offenseId, "offender");
      }
    });

    // Remove offense relations
    offensesToRemove.forEach((offenseId) => {
      if (onRemovePersonFromOffense) {
        onRemovePersonFromOffense(person.id, offenseId);
      }
    });
  };

  const roleNumbers = getVictimOffenderNumbers(person.id);
  const selectedOffenseOptions = getSelectedOffenseOptions(person.id);
  const offenseOptions = getOffenseOptions();

  const handleMenuClick = (event: React.MouseEvent<HTMLElement>) => {
    event.stopPropagation();
    setMenuAnchorEl(event.currentTarget);
  };

  const handleMenuClose = () => {
    setMenuAnchorEl(null);
  };

  const handleOpenRecord = () => {
    window.open(`/entity?entityId=${person.id}`, "_blank");
    setMenuAnchorEl(null);
  };

  const handleEditEntity = () => {
    onEntityEdit?.(person.id, "person");
    setMenuAnchorEl(null);
  };

  const handleRemoveClick = () => {
    setRemoveModalOpen(true);
    setMenuAnchorEl(null);
  };

  const handleRemoveCancel = () => {
    setRemoveModalOpen(false);
  };

  const handleRemoveConfirm = () => {
    onRemovePersonFromReport?.(person.id, "person");
    setRemoveModalOpen(false);
  };

  return (
    <Box
      sx={{
        display: "flex",
        width: "100%",
        flexDirection: "column",
        alignItems: "flex-start",
        borderRadius: "12px",
        border: `1px solid ${colors.grey[200]}`,
        background: "#FFF",
        overflow: "hidden",
        boxShadow: "0px 0px 16px 0px rgba(0, 0, 0, 0.04)",
        mb: 3,
      }}
    >
      {/* Header */}
      <Box
        sx={{
          display: "flex",
          width: "100%",
          justifyContent: "space-between",
          alignItems: "center",
          padding: "16px 24px",
          borderBottom: `1px solid ${colors.grey[200]}`,
          backgroundColor: colors.grey[50],
        }}
      >
        <Box sx={{ display: "flex", alignItems: "center", gap: 2 }}>
          {roleNumbers.length > 0 && (
            <Typography style="h3" color={colors.grey[900]}>
              <span style={{ fontWeight: "bold" }}>
                {formatRoleNumber(roleNumbers[0])}
              </span>
            </Typography>
          )}
          <Typography style="h3" color={colors.grey[900]}>
            {person.name}, Individual
          </Typography>
        </Box>

        <Box sx={{ display: "flex", alignItems: "center", gap: 1 }}>
          {!readOnly && (
            <IconButton
              onClick={handleMenuClick}
              size="small"
              sx={{
                color: colors.grey[600],
                zIndex: 10,
                "&:hover": {
                  bgcolor: colors.grey[100],
                },
              }}
            >
              <MoreVertIcon />
            </IconButton>
          )}
          <IconButton
            onClick={() => setIsExpanded(!isExpanded)}
            size="small"
            sx={{
              color: colors.grey[600],
              "&:hover": {
                bgcolor: colors.grey[100],
              },
            }}
          >
            {isExpanded ? <ExpandLessIcon /> : <ExpandMoreIcon />}
          </IconButton>
        </Box>
      </Box>

      {/* Form Content */}
      <Collapse in={isExpanded} sx={{ width: "100%" }}>
        <Box sx={{ padding: "24px" }}>
          {/* Related Offenses Section */}
          <Box sx={{ mb: 4 }}>
            <TextInput
              title="Related Offenses"
              placeholder="Select offenses..."
              type={InputType.Dropdown}
              isMultiSelect
              options={offenseOptions}
              selectedItems={selectedOffenseOptions}
              onChangeSelectedItems={handleOffenseSelectionChange}
              readOnly={readOnly}
              chipColor="grey"
              hideSelectedFromOptions={true}
              minRequiredItems={1}
            />
          </Box>
        </Box>
      </Collapse>

      {/* Menu for offender actions */}
      <Menu
        anchorEl={menuAnchorEl}
        open={Boolean(menuAnchorEl)}
        onClose={handleMenuClose}
        anchorOrigin={{
          vertical: "bottom",
          horizontal: "right",
        }}
        transformOrigin={{
          vertical: "top",
          horizontal: "right",
        }}
        sx={{
          mt: 0.25,
          "& .MuiPaper-root": {
            borderRadius: "8px",
            minWidth: "200px",
          },
          "& .MuiMenuItem-root": {
            py: 1.5,
            display: "flex",
            alignItems: "center",
            gap: 1.5,
          },
        }}
      >
        <MenuItem onClick={handleEditEntity}>
          <EditIcon fontSize="small" sx={{ color: colors.grey[500] }} />
          <Typography style="body2" color={colors.grey[500]}>
            Edit entity
          </Typography>
        </MenuItem>
        <MenuItem onClick={handleOpenRecord}>
          <OpenInNewIcon fontSize="small" sx={{ color: colors.grey[500] }} />
          <Typography style="body2" color={colors.grey[500]}>
            View entity in new tab
          </Typography>
        </MenuItem>
        <MenuItem onClick={handleRemoveClick}>
          <DeleteIcon fontSize="small" sx={{ color: colors.grey[500] }} />
          <Typography style="body2" color={colors.grey[500]}>
            Remove entity
          </Typography>
        </MenuItem>
      </Menu>

      {/* Remove confirmation modal */}
      <Dialog
        open={removeModalOpen}
        onClose={handleRemoveCancel}
        maxWidth="sm"
        fullWidth
        PaperProps={{
          sx: {
            borderRadius: "12px",
            p: 1,
          },
        }}
      >
        <DialogContent sx={{ p: 3 }}>
          <Box sx={{ mb: 2 }}>
            <Typography style="h2" color={colors.grey[900]}>
              Delete {person.name}
            </Typography>
          </Box>
          <Box sx={{ mb: 3 }}>
            <Typography style="body3" color={colors.grey[700]}>
              {person.name} will be deleted from the report and all offenses
            </Typography>
          </Box>
        </DialogContent>

        <DialogActions sx={{ p: 3, pt: 0 }}>
          <Button
            label="Cancel"
            color="grey"
            prominence={false}
            onClick={handleRemoveCancel}
          />
          <Button
            label="Delete"
            color="blue"
            prominence={true}
            onClick={handleRemoveConfirm}
          />
        </DialogActions>
      </Dialog>
    </Box>
  );
};

const OffendersSection: React.FC<OffendersSectionProps> = ({
  people,
  relations,
  addedOffenses,
  readOnly = false,
  onSetActiveOffenseRelation,
  onOpenSidePanel,
  onEntityEdit,
  onRemovePersonFromReport,
  onAddPersonToOffense,
  onRemovePersonFromOffense,
}) => {
  const [addOffenderMenuAnchor, setAddOffenderMenuAnchor] =
    useState<null | HTMLElement>(null);

  const handleAddOffenderClick = (event: React.MouseEvent<HTMLElement>) => {
    if (!addedOffenses || addedOffenses.length === 0) return;

    if (addedOffenses.length === 1) {
      // If only one offense, directly set the active offense relation
      onSetActiveOffenseRelation?.({
        offenseId: addedOffenses[0].id,
        relationType: "offender",
      });
      // Open the side panel
      onOpenSidePanel?.("PERSON");
    } else {
      // If multiple offenses, show menu to select
      setAddOffenderMenuAnchor(event.currentTarget);
    }
  };

  const handleAddOffenderMenuClose = () => {
    setAddOffenderMenuAnchor(null);
  };

  const handleSelectOffenseForOffender = (offenseId: string) => {
    onSetActiveOffenseRelation?.({
      offenseId,
      relationType: "offender",
    });
    // Open the side panel
    onOpenSidePanel?.("PERSON");
    setAddOffenderMenuAnchor(null);
  };

  return (
    <>
      <Divider sx={{ borderColor: colors.grey[300], mt: 4, mb: 3 }} />
      <Box sx={{ mb: 4 }}>
        <Box
          sx={{
            display: "flex",
            justifyContent: "space-between",
            alignItems: "center",
            mb: 3,
          }}
        >
          <Typography style="h1" color={colors.grey[900]}>
            All Offenders
          </Typography>
          {!readOnly && addedOffenses && addedOffenses.length > 0 && (
            <Button
              onClick={handleAddOffenderClick}
              label="Add Offender"
              leftIcon={<AddIcon />}
              size="small"
            />
          )}
        </Box>
        {people.map((person) => (
          <OffenderCard
            key={person.id}
            person={person}
            relations={relations}
            addedOffenses={addedOffenses}
            readOnly={readOnly}
            onEntityEdit={onEntityEdit}
            onRemovePersonFromReport={onRemovePersonFromReport}
            onAddPersonToOffense={onAddPersonToOffense}
            onRemovePersonFromOffense={onRemovePersonFromOffense}
          />
        ))}
      </Box>

      {/* Add Offender Offense Selection Menu */}
      <Menu
        anchorEl={addOffenderMenuAnchor}
        open={Boolean(addOffenderMenuAnchor)}
        onClose={handleAddOffenderMenuClose}
        anchorOrigin={{
          vertical: "bottom",
          horizontal: "left",
        }}
        transformOrigin={{
          vertical: "top",
          horizontal: "left",
        }}
        sx={{
          mt: 0.25,
          "& .MuiPaper-root": {
            borderRadius: "8px",
            minWidth: "300px",
          },
          "& .MuiMenuItem-root": {
            py: 1.5,
            display: "flex",
            alignItems: "center",
            gap: 1.5,
          },
        }}
      >
        {addedOffenses?.map((addedOffense) => (
          <MenuItem
            key={addedOffense.id}
            onClick={() => handleSelectOffenseForOffender(addedOffense.id)}
          >
            <Typography style="body2" color={colors.grey[900]}>
              {addedOffense.offense.citation} {addedOffense.offense.literal}
            </Typography>
          </MenuItem>
        ))}
      </Menu>
    </>
  );
};

export default OffendersSection;
