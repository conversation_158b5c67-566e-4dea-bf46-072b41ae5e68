import { Dropdown } from "@/design-system/components/Dropdown";
import { TextInput } from "@/design-system/components/TextInput";
import { Typography } from "@/design-system/components/Typography";
import { colors } from "@/design-system/tokens";
import { create } from "@bufbuild/protobuf";
import { Box } from "@mui/material";
import {
  CreateRelationRequestSchema,
  ObjectReferenceSchema,
  RelationSchema,
  UpdateRelationRequestSchema,
} from "proto/hero/reports/v2/reports_pb";
import React, { useEffect, useState } from "react";
import {
  useCreateRelation,
  useDeleteRelation,
  useUpdateRelation,
} from "../../../apis/services/workflow/reports/v2/hooks";
import { PersonData, RELATIONSHIP_OPTIONS } from "./constants";

interface RelationshipRow {
  victim: PersonData;
  offender: PersonData;
  existingRelationId?: string;
  relationType?: string;
  existingRelation?: any | null;
}

interface OffenseRelationsCardProps {
  reportId: string;
  victimOffenderPairs: RelationshipRow[];
  readOnly?: boolean;
}

const OffenseRelationsCard: React.FC<OffenseRelationsCardProps> = ({
  reportId,
  victimOffenderPairs,
  readOnly = false,
}) => {
  // Mutations
  const createRelationMutation = useCreateRelation();
  const updateRelationMutation = useUpdateRelation();
  const deleteRelationMutation = useDeleteRelation();

  const [relationshipRows, setRelationshipRows] =
    useState<RelationshipRow[]>(victimOffenderPairs);

  // Sync local relationshipRows when prop changes (e.g., after backend refetch)
  useEffect(() => {
    setRelationshipRows(victimOffenderPairs);
  }, [victimOffenderPairs]);

  const handleRelationshipChange = (
    pair: RelationshipRow,
    newType: string | null
  ) => {
    if (readOnly) return;

    // Store the previous relationship type so we can revert if the API call fails
    const prevType = pair.relationType;

    // Step 1: Optimistically update the UI immediately to provide instant feedback
    // This updates the dropdown selection before we know if the backend operation succeeds
    setRelationshipRows((prev) =>
      prev.map((r) =>
        r.victim.id === pair.victim.id && r.offender.id === pair.offender.id
          ? { ...r, relationType: newType ?? undefined }
          : r
      )
    );

    // Helper function to revert the UI back to the previous state if API call fails
    const revert = () =>
      setRelationshipRows((prev) =>
        prev.map((r) =>
          r.victim.id === pair.victim.id && r.offender.id === pair.offender.id
            ? { ...r, relationType: prevType }
            : r
        )
      );

    // Case 1: User cleared the selection (selected "No relationship" or equivalent)
    if (newType === null) {
      // If there's no existing relation in the backend, just revert the UI since there's nothing to delete
      if (!pair.existingRelationId) {
        revert();
        return;
      }

      // Delete the existing relation from the backend
      const req = {
        reportId: reportId,
        relationId: pair.existingRelationId,
      } as any;

      deleteRelationMutation.mutate(req, { onError: () => revert() });
      return;
    }

    // Case 2: There's already a relation in the backend, so we need to update it
    if (pair.existingRelationId && pair.existingRelation) {
      // Create an updated version of the existing relation with the new relationship type
      const updatedRelation = {
        ...pair.existingRelation,
        relationType: newType,
      };

      const req = create(UpdateRelationRequestSchema, {
        reportId: reportId,
        relation: updatedRelation,
      }) as any;

      console.log("Updating relation", JSON.stringify(req));

      updateRelationMutation.mutate(req as any, {
        onError: () => revert(),
      });
    } else {
      // Case 3: No existing relation, so we need to create a new one

      // Create object references for both the victim and offender
      const victimRef = create(ObjectReferenceSchema, {
        objectType: "entity",
        globalId: pair.victim.id,
        displayName: pair.victim.name,
      });
      const offenderRef = create(ObjectReferenceSchema, {
        objectType: "entity",
        globalId: pair.offender.id,
        displayName: pair.offender.name,
      });

      // Create the new relation object
      const relation = create(RelationSchema, {
        reportId: reportId,
        objectA: victimRef, // The victim
        objectB: offenderRef, // The offender
        relationType: newType,
        description: ``, // Empty description for now
      });

      // Submit the create request to the backend
      createRelationMutation.mutate(
        create(CreateRelationRequestSchema, {
          reportId: reportId,
          relation,
        }) as any,
        {
          onError: () => revert(), // Revert UI if creation fails
        }
      );
    }
  };

  // Empty state UI when no victim-offender pairs exist
  if (relationshipRows.length === 0) {
    return (
      <Box
        sx={{
          border: `1px solid ${colors.grey[200]}`,
          borderRadius: "12px",
          backgroundColor: "#FFF",
          boxShadow: "0px 0px 16px 0px rgba(0, 0, 0, 0.04)",
          mb: 3,
        }}
      >
        <Box sx={{ px: 3, pt: 3 }}>
          <Typography style="h2" color={colors.grey[900]}>
            Victim–Offender Relationships
          </Typography>
        </Box>
        
        <Box 
          sx={{ 
            px: 3, 
            py: 4, 
            textAlign: "center",
            mx: 3,
            mb: 3,
          }}
        >
          <Typography style="body2" color={colors.grey[600]}>
            No victims or offenders have been added to this report yet.
          </Typography>
        </Box>
      </Box>
    );
  }

  return (
    <Box
      sx={{
        border: `1px solid ${colors.grey[200]}`,
        borderRadius: "12px",
        backgroundColor: "#FFF",
        boxShadow: "0px 0px 16px 0px rgba(0, 0, 0, 0.04)",
        mb: 3,
      }}
    >
      <Box sx={{ px: 3, pt: 3 }}>
        <Typography style="h2" color={colors.grey[900]}>
          Victim–Offender Relationships
        </Typography>
      </Box>

      <Box sx={{ px: 3, py: 2 }}>
        {relationshipRows.map((row, idx) => {
          const prevVictimId =
            idx > 0 ? relationshipRows[idx - 1].victim.id : null;
          const showVictim = row.victim.id !== prevVictimId;

          const uniqueKey = row.existingRelationId
            ? `${row.victim.id}-${row.offender.id}-${row.existingRelationId}`
            : `${row.victim.id}-${row.offender.id}-${idx}`;

          return (
            <Box
              key={uniqueKey}
              sx={{
                display: "grid",
                gridTemplateColumns: "1fr 1fr 1fr",
                gap: 2,
                alignItems: "center",
                px: 1,
                py: 1.5,
              }}
            >
              {showVictim ? (
                <TextInput
                  title="Victim"
                  value={row.victim.name}
                  readOnly={true}
                />
              ) : (
                <Box />
              )}
              <TextInput
                title={showVictim ? "Offender" : undefined}
                value={row.offender.name}
                readOnly={true}
              />
              <Dropdown
                options={RELATIONSHIP_OPTIONS}
                value={row.relationType ?? null}
                onChange={(val) => handleRelationshipChange(row, val)}
                readOnly={readOnly}
                title={showVictim ? "Relationship to Victim" : undefined}
                placeholder="Select Relationship"
              />
            </Box>
          );
        })}
      </Box>
    </Box>
  );
};

export default OffenseRelationsCard;
