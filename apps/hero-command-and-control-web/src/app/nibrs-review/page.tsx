"use client";
import { ThemeProvider, createTheme } from "@mui/material/styles";
import { useSearchParams } from "next/navigation";
import { Order } from "proto/hero/orders/v2/orders_pb";
import { useEffect, useState } from "react";
import Sidebar from "../components/Sidebar";
import { OrderProvider, useOrders } from "../contexts/OrderContext";
import styles from "../reports/styles.module.css";
import NibrsReviewPage from "./components/core/NibrsReviewPage";

const THEME = createTheme({
  typography: {
    fontFamily: `Roboto`,
  },
});

function NibrsReviewContent() {
  const searchParams = useSearchParams();
  const reportId = searchParams?.get("reportId") || null;
  const [currentOrder, setCurrentOrder] = useState<Order | undefined>(
    undefined
  );
  const { getOrderByReportId, ordersData } = useOrders();

  useEffect(() => {
    if (reportId) {
      const matchingOrder = getOrderByReportId(reportId);
      setCurrentOrder(matchingOrder);
    } else {
      setCurrentOrder(undefined);
    }
  }, [reportId, getOrderByReportId, ordersData]);

  // If no reportId is provided, don't render anything
  if (!reportId) {
    return null;
  }

  return (
    <div className={styles.content}>
      <NibrsReviewPage currentOrder={currentOrder} />
    </div>
  );
}

export default function NibrsReviewPageMain() {
  return (
    <ThemeProvider theme={THEME}>
      <OrderProvider>
        <div className={styles.mainContainer}>
          <Sidebar />
          <NibrsReviewContent />
        </div>
      </OrderProvider>
    </ThemeProvider>
  );
}
